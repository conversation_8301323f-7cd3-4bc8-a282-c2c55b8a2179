package com.easylive.pay.model;

import org.junit.Test;

import java.util.Arrays;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * RechargeSearchCriteria 测试类
 * 测试 briefString 方法的功能
 * 
 * <AUTHOR>
 */
public class RechargeSearchCriteriaTest {

    @Test
    public void testBriefStringWithAllFields() {
        RechargeSearchCriteria criteria = new RechargeSearchCriteria();
        criteria.setOrderId("ORDER123");
        criteria.setPlatformOrderId("PLATFORM456");
        criteria.setAppId(Arrays.asList(1, 2, 3));
        criteria.setName(Arrays.asList("张三", "李四"));
        criteria.setUid(Arrays.asList(1001L, 1002L, 1003L));
        criteria.setPlatform(Arrays.asList(1, 2));
        criteria.setStartDate(new Date());
        criteria.setEndDate(new Date());
        criteria.setStart(0);
        criteria.setCount(10);

        String result = criteria.briefString();
        
        assertNotNull(result);
        assertTrue(result.contains("订单号: ORDER123"));
        assertTrue(result.contains("平台订单号: PLATFORM456"));
        assertTrue(result.contains("应用ID: [1, 2, 3]"));
        assertTrue(result.contains("用户名: [张三, 李四]"));
        assertTrue(result.contains("用户ID: [1001, 1002, 1003]"));
        assertTrue(result.contains("平台: [1, 2]"));
        assertTrue(result.contains("起始位置: 0"));
        assertTrue(result.contains("数量: 10"));
    }

    @Test
    public void testBriefStringWithLongNameList() {
        RechargeSearchCriteria criteria = new RechargeSearchCriteria();
        criteria.setName(Arrays.asList("张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"));

        String result = criteria.briefString();
        
        assertNotNull(result);
        assertTrue(result.contains("用户名: [张三, 李四, 王五, 赵六, 钱七]等8个"));
    }

    @Test
    public void testBriefStringWithLongUidList() {
        RechargeSearchCriteria criteria = new RechargeSearchCriteria();
        criteria.setUid(Arrays.asList(1001L, 1002L, 1003L, 1004L, 1005L, 1006L, 1007L));

        String result = criteria.briefString();
        
        assertNotNull(result);
        assertTrue(result.contains("用户ID: [1001, 1002, 1003, 1004, 1005]等7个"));
    }

    @Test
    public void testBriefStringWithExactlyFiveNames() {
        RechargeSearchCriteria criteria = new RechargeSearchCriteria();
        criteria.setName(Arrays.asList("张三", "李四", "王五", "赵六", "钱七"));

        String result = criteria.briefString();
        
        assertNotNull(result);
        assertTrue(result.contains("用户名: [张三, 李四, 王五, 赵六, 钱七]"));
        assertFalse(result.contains("等"));
    }

    @Test
    public void testBriefStringWithExactlyFiveUids() {
        RechargeSearchCriteria criteria = new RechargeSearchCriteria();
        criteria.setUid(Arrays.asList(1001L, 1002L, 1003L, 1004L, 1005L));

        String result = criteria.briefString();
        
        assertNotNull(result);
        assertTrue(result.contains("用户ID: [1001, 1002, 1003, 1004, 1005]"));
        assertFalse(result.contains("等"));
    }

    @Test
    public void testBriefStringWithEmptyFields() {
        RechargeSearchCriteria criteria = new RechargeSearchCriteria();

        String result = criteria.briefString();
        
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    public void testBriefStringWithOnlyOrderId() {
        RechargeSearchCriteria criteria = new RechargeSearchCriteria();
        criteria.setOrderId("ORDER123");

        String result = criteria.briefString();
        
        assertNotNull(result);
        assertEquals("订单号: ORDER123", result);
    }

    @Test
    public void testBriefStringWithSingleNameAndUid() {
        RechargeSearchCriteria criteria = new RechargeSearchCriteria();
        criteria.setName(Arrays.asList("张三"));
        criteria.setUid(Arrays.asList(1001L));

        String result = criteria.briefString();
        
        assertNotNull(result);
        assertTrue(result.contains("用户名: [张三]"));
        assertTrue(result.contains("用户ID: [1001]"));
        assertFalse(result.contains("等"));
    }
}
