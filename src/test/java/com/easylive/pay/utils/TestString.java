package com.easylive.pay.utils;

import com.easylive.pay.enums.MobilePlatform;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.Currency;

/**
 * <AUTHOR>
 * @date 2019-08-20
 */
@Slf4j
public class TestString {
    @Test
    public void testSplit() {
        String s =  "ws_ccc";
        String[] ss = s.split("_");
        if (ss.length > 1) {
            String[] newSS = Arrays.copyOfRange(ss, 0, ss.length - 1);
            String parentCid = StringUtils.join(newSS, "_");
            System.out.println(parentCid);
            Assert.assertEquals(parentCid, "ws");
        }
    }


    @Test
    public void testCurrency() {
        for(Currency currency: Currency.getAvailableCurrencies()) {
            log.info("{},{},{}",currency.getCurrencyCode(), currency.getDisplayName(), currency.getSymbol());
        }
    }


    @Test
    public void testUserAgent() {
        UAgentInfo info = new UAgentInfo("ylivetool 5.7.0 rv:202002200 ylivetool Develop (iPhone 6; iOS 12.4.3; en_CN)");
        Assert.assertEquals(info.getAppName(), "ylivetool");
        Assert.assertEquals(info.getAppVersion(),"5.7.0");
        Assert.assertEquals(info.getDeviceType(), MobilePlatform.MPF_IOS);

        info = new UAgentInfo("oupaienterprise 5.7.0 rv:202001130 enterpriseios Release (iPhone Superior; iOS 13.3.1; zh_CN)");
        Assert.assertEquals(info.getAppName(), "oupaienterprise");
        Assert.assertEquals(info.getAppVersion(),"5.7.0");
        Assert.assertEquals(info.getDeviceType(), MobilePlatform.MPF_IOS);


        info = new UAgentInfo("en_CN)");
        Assert.assertEquals(info.getAppName(), "yizhibo");
        Assert.assertEquals(info.getDeviceType(), MobilePlatform.MPF_UNKNOWN);
        info = new UAgentInfo("Dalvik/2.1.0 (Linux; U; Android 9; PAFM00 Build/PKQ1.190319.001); ellite android v5.5.0.0926-anellite_wandoujia7");
        Assert.assertEquals(info.getAppName(), "ellite");
        Assert.assertEquals(info.getDeviceType(), MobilePlatform.MPF_ANDROID);


        info = new UAgentInfo("Dalvik/2.1.0 (Linux; U; Android 15 Build/AP31.240322.016); gargantua android v6.15.2-laileoppo");
        Assert.assertEquals(info.getAppName(), "gargantua");
        Assert.assertEquals(info.getDeviceType(), MobilePlatform.MPF_ANDROID);

        info = new UAgentInfo("dalvik/2.1.0 (linux, u, android 11, gm1910 build/rkq1.201022.002), gargantua android v6.15.5-laileoppo");
        Assert.assertEquals(info.getAppName(), "gargantua");
        Assert.assertEquals(info.getDeviceType(), MobilePlatform.MPF_ANDROID);

    }
}
