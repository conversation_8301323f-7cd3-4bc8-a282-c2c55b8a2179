package com.easylive.pay.utils;

import lombok.extern.slf4j.Slf4j;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Assert;
import org.junit.Test;

import java.util.Date;

@Slf4j
public class TestDateUtils {

    @Test
    public void testParse() {
        String date = "2019-01-01";
        Date dt = DateUtils.parse(date);
        Date dt2 = DateUtils.parse2(date);
        Assert.assertEquals(dt, dt2);
    }

    @Test
    public void testParse2(){
        String d1 = "20:59";
        String d2 = "25:01";
        String d3 = "21:67";
        DateTimeFormatter dateFormat = DateTimeFormat.forPattern("HH:mm");

        log.info(dateFormat.parseDateTime(d1).toString());
        log.info(dateFormat.parseDateTime(d2).toString());
        log.info(dateFormat.parseDateTime(d3).toString());
    }

    @Test
    public void testTomorrow() {

        System.out.println( DateUtils.getTomorrowDate() );
    }


    @Test
    public void testWeek() {
        System.out.println(DateUtils.getStartOfNextWeek());

        long time = DateUtils.getStartOfNextWeek().getTime();
        long curr = System.currentTimeMillis();
        System.out.println(time + " - " + curr);
        System.out.println( time - curr);
    }


    @Test
    public void testMonth() {

        System.out.println(DateUtils.getStartOfNextMonth());

        long time = DateUtils.getStartOfNextMonth().getTime();
        long curr = System.currentTimeMillis();
        System.out.println(time + " - " + curr);
        System.out.println( time - curr);

    }
}
