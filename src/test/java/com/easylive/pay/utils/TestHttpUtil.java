package com.easylive.pay.utils;

import com.alibaba.fastjson.JSON;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.cashout.jiuhe.JiuHeRequestBaseDTO;
import com.easylive.pay.model.cashout.jiuhe.JiuHeRequestUploadDTO;
import com.easylive.pay.model.cashout.xinyun.WithdrawRequestParam;
import com.easylive.pay.model.cashout.xinyun.WithdrawRequestParamData;
import com.easylive.pay.service.factory.BankCashout;
import com.easylive.pay.service.factory.JiuHeBankCashout;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.EncoderException;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.codec.net.URLCodec;
import org.apache.http.entity.ContentType;
import org.junit.Test;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;

@Slf4j
public class TestHttpUtil {

    @Test
    public void testHttpPost() throws Exception {

        String priKey= "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCTfe2sZOkBtCIOpqIVkp4zwK5cOOckeyCmV1OJt4LnLPFkpFi4zFSWiXO2OgB6aliDK7gBRyVMDXPFs5Qn66e1lEKnGINO4EHFq9n81ZYZcozoZ7DWFGNBS0aEuWQKOlAuJ9WRgBeNekwHCgdrpLD/VE0i6vqkCmodGCaArUFMm61Rtd56axSl0QT3dJd3559rGUikOGvUCmI0+aSN2auMHQxDvqocFGeaN8WAEnrEud2MtuosOFf1AQ8oly84vLSnmeYPSOTJ5fn/xEgUvU6qR5u7Iukcx4hqLrVsRnl8gpF1UAga4A0ftNWdAbeilsD86nHpbpjUp4FQ/iPocoC9AgMBAAECggEAAeiPOrvTiAIcrrPZRQ4wndrQZsEVvJMbSQu4OP/24abjOLonq7x5G7mLlcAKYYseuhPGvb197g3+gYBhWmLiOKdCdYcLq+ma8EPY/jyFRK8Gw7sTvrFk12Nx9iWv9bHrd61sRgWySFJcyXqHvgzTfXpJxTnlJFo+JUI6vU+AmM0l1v1JDJAeE4H2qfmme8IhDxMXAEJbkfiuRau8Ksqpj+voGqQrC8pAtjSnx9krdQgHp1Hf4a1NJwhHsk4mXJNJLEuapF02F3LYEMMFhwPpt9nUlAzzWNGkhJMCS2uZs8SpdhG0vcM8gxFpvpuwI6wHNc586rjb9HJ+jd8a3/dUwQKBgQDPMcD9VzWQ6+i7p3BKTJ6w96Vfp1VtqhzqsaGwp2Jy1NrR8tk7L7V+OgC4Cif7Z81NkJVD0eb7YvICujZ2p+S/4EgeRP5etPa+d3O7dSv0AY3x/iUfcHILbrec/c87edASBRVmpZ68iwCyZ2HtY/0ObI56B1MDew2uHj25asKexwKBgQC2O/6HOJe8mBWEgDlLiUnkbV7BVdXC5QWLVNnQHl3BmKBopCKCQbbpPMiQ7mJBHRQ0aFlwJglDHH6G0gv9x+S2HNIZ6btKtnaf4L2+ubLxrHvy1K5FhC+mk8JOCarT797OT+BsfIHV7EYBZEk5iDXO/bu/+IPU9KKUS5v22MtwWwKBgQCPl3CjYowaCpR5cLouk9kq1gYDO6nF3a46rK5dCjP9Vyzrcb1A+nhyqWoUId3BqxeqWLhJjy/gKpuEbl8Obp7mp13ztGcBZbLXH1vSn4q9iVJBUpuRpVE0QR/5KiEzm+Eqi+FgCQnwqvjpqyI/7hrTo0I+qdLLR72cXRLz7FxCwwKBgQCGsleTKVB2tM8KziALZiG49uJX8xL6aH+tmf0ZKKjSJPdEATYEo4AIa8Yv4hO73zuE9TBOhb7I3THkgzMb1PmHZkwhnTKzqaSKzfpSFD5hBcXkGjHp7ieub37qYZjj6GP+iOnwJiNttKOV+cNqA/UJ12BEJ/OBn4TgKfbRATN2CwKBgQCQu7Z8yJ2SPzU/NXEGgHL9alKm9kVPUPgOkIVhsZVCtnb4kTKmg7eqJXHW+ZidCsjDzBG/EYUV7S1SKMy9TL8o3K0aLbTzrIorECoigVhvOLSbG+QN5bFAkSrVs9fOEWY7H9TWwkwrIcbGIR35XZRz/QLOI9gm+n4nH5kk8OyHbg==";

        /**
         * $adata = [
         *             'trxseq' => '********',
         *             'identity_card' => '51392219920817575X',
         *             'real_name' => '丁强',
         *             'bank_name' => '招商银行',
         *             'bank_account' => '****************',
         *             // 'zfbzh' => "",
         *             'trsamt' => '8.0'
         *         ];
         *
         * $param_data = [
         *     'enterprise_unique_no' => $enterprise_unique_no,
         *     'ttlcnt' => 1,
         *     'ttlamt' => '8.0',
         *     'enterprise_management_unique_no' => '2023111417535421420850',
         *     'task_unique_no' => '202311151148451412',
         *     'fk_type' => 1,
         *     'remark' => '3412341',
         *     'data' => json_encode($adata),
         *     'nonce_str' => '*************'
         * ];
         */
        Map<String, String> adata = new HashMap<>();
        adata.put("trxseq", "********");
        adata.put("identity_card", "51392219920817575X");
        adata.put("real_name", "丁强");
        adata.put("bank_name", "成都分行营业部");
        adata.put("bank_account", "****************");
        adata.put("trsamt", "8.01");

        ArrayList<Map> list = new ArrayList<>(1);
        list.add(adata);

        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("enterprise_unique_no", "2023111417384919512342");
        hashMap.put("ttlcnt", "1");
        hashMap.put("ttlamt", "8.01");
        hashMap.put("enterprise_management_unique_no", "2023111417535421420850");
        hashMap.put("task_unique_no", "202311151148451412");
        hashMap.put("fk_type", "1");
        hashMap.put("remark", "3412341");
        hashMap.put("data", JsonUtils.toString(list));
        hashMap.put("nonce_str", "*************");

        String signStr = UrlUtils.createLinkString(hashMap);
        System.out.println("signStr:    "+signStr);
        String sign = RSA2.sign(priKey, signStr);
        hashMap.put("sign", sign);
        String body = JsonUtils.toString(hashMap);
        System.out.println("body:    "+body);
        String a = HttpUtils.doPost("https://lhyg.xinzhanghu.cn/apienterprise/Undertaker/paymentorderdata", body, ContentType.APPLICATION_JSON);

        System.out.println(a);
    }


    @Test
    public void paymentorderdata() throws Exception {
        WithdrawRequestParamData data = new WithdrawRequestParamData("********", "51392219920817575X", "丁强", "8.01");
        data.setBank_name("成都分行营业部");
        data.setBank_account("****************");
        WithdrawRequestParam requestParam = WithdrawRequestParam
                .builder()
                .enterprise_unique_no("2023111417384919512342")
                .ttlamt("8.01")
                .ttlcnt(1)
                .enterprise_management_unique_no("2023111417535421420850")
                .task_unique_no("202311151148451412")
                .fk_type(1)
                .remark("3412341")
                .data(JsonUtils.toString(List.of(data)))
                .nonce_str(String.valueOf(System.currentTimeMillis()))
//                .nonce_str("*************")
                .build();
        String signStr = UrlUtils.createLinkString(requestParam.toHashMap());
        System.out.println("signStr:" + signStr);
        String sign = RSA2.sign("MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCTfe2sZOkBtCIOpqIVkp4zwK5cOOckeyCmV1OJt4LnLPFkpFi4zFSWiXO2OgB6aliDK7gBRyVMDXPFs5Qn66e1lEKnGINO4EHFq9n81ZYZcozoZ7DWFGNBS0aEuWQKOlAuJ9WRgBeNekwHCgdrpLD/VE0i6vqkCmodGCaArUFMm61Rtd56axSl0QT3dJd3559rGUikOGvUCmI0+aSN2auMHQxDvqocFGeaN8WAEnrEud2MtuosOFf1AQ8oly84vLSnmeYPSOTJ5fn/xEgUvU6qR5u7Iukcx4hqLrVsRnl8gpF1UAga4A0ftNWdAbeilsD86nHpbpjUp4FQ/iPocoC9AgMBAAECggEAAeiPOrvTiAIcrrPZRQ4wndrQZsEVvJMbSQu4OP/24abjOLonq7x5G7mLlcAKYYseuhPGvb197g3+gYBhWmLiOKdCdYcLq+ma8EPY/jyFRK8Gw7sTvrFk12Nx9iWv9bHrd61sRgWySFJcyXqHvgzTfXpJxTnlJFo+JUI6vU+AmM0l1v1JDJAeE4H2qfmme8IhDxMXAEJbkfiuRau8Ksqpj+voGqQrC8pAtjSnx9krdQgHp1Hf4a1NJwhHsk4mXJNJLEuapF02F3LYEMMFhwPpt9nUlAzzWNGkhJMCS2uZs8SpdhG0vcM8gxFpvpuwI6wHNc586rjb9HJ+jd8a3/dUwQKBgQDPMcD9VzWQ6+i7p3BKTJ6w96Vfp1VtqhzqsaGwp2Jy1NrR8tk7L7V+OgC4Cif7Z81NkJVD0eb7YvICujZ2p+S/4EgeRP5etPa+d3O7dSv0AY3x/iUfcHILbrec/c87edASBRVmpZ68iwCyZ2HtY/0ObI56B1MDew2uHj25asKexwKBgQC2O/6HOJe8mBWEgDlLiUnkbV7BVdXC5QWLVNnQHl3BmKBopCKCQbbpPMiQ7mJBHRQ0aFlwJglDHH6G0gv9x+S2HNIZ6btKtnaf4L2+ubLxrHvy1K5FhC+mk8JOCarT797OT+BsfIHV7EYBZEk5iDXO/bu/+IPU9KKUS5v22MtwWwKBgQCPl3CjYowaCpR5cLouk9kq1gYDO6nF3a46rK5dCjP9Vyzrcb1A+nhyqWoUId3BqxeqWLhJjy/gKpuEbl8Obp7mp13ztGcBZbLXH1vSn4q9iVJBUpuRpVE0QR/5KiEzm+Eqi+FgCQnwqvjpqyI/7hrTo0I+qdLLR72cXRLz7FxCwwKBgQCGsleTKVB2tM8KziALZiG49uJX8xL6aH+tmf0ZKKjSJPdEATYEo4AIa8Yv4hO73zuE9TBOhb7I3THkgzMb1PmHZkwhnTKzqaSKzfpSFD5hBcXkGjHp7ieub37qYZjj6GP+iOnwJiNttKOV+cNqA/UJ12BEJ/OBn4TgKfbRATN2CwKBgQCQu7Z8yJ2SPzU/NXEGgHL9alKm9kVPUPgOkIVhsZVCtnb4kTKmg7eqJXHW+ZidCsjDzBG/EYUV7S1SKMy9TL8o3K0aLbTzrIorECoigVhvOLSbG+QN5bFAkSrVs9fOEWY7H9TWwkwrIcbGIR35XZRz/QLOI9gm+n4nH5kk8OyHbg==", signStr);
        System.out.println("sign:  "+ sign);
        requestParam.setSign(sign);
        String body = JsonUtils.toString(requestParam);
        System.out.println("body:" + body);
        String ret = HttpUtils.doPost("https://lhyg.xinzhanghu.cn/apienterprise/Undertaker/paymentorderdata", body, ContentType.APPLICATION_JSON);
        System.out.println("response:" + ret);
    }


    @Test
    public void verify() throws Exception {
        String paramStr ="{\"result_code\":\"FAIL\",\"err_code_des\":\"付款账户钱包余额不足\",\"return_code\":\"SUCCESS\",\"err_code\":\"\",\"nonce_str\":\"Ci0OBoMh80Xf9j14dTcEvpFaYVgCXJRE\",\"sign_type\":\"RSA2\",\"sign\":\"bdcd5xd9p2wSG4SXiNQEcwWJeGLFYmwI4Otzw6Bgv1UkwexNkHaIlIuxnXd5PBNa9mdLSL\\/VJQMe\\/KzmOyystwNgQ07reSvJsR0J7RmCFq6i7UWm47ynS175GJNV9GI7Ll7yYTkPc9HJR276yzhGHYDUJy\\/4S5LJryCZo8b4cqI4fUB7ZBhCQYhQeFosNRpjicVFZ5jTJjhLZWpe\\/kv0Cqf5SE9dkj3rVltBNGwxwyhxYyXfYhDvbP6BYmkyMso0YA5Wy54R9RwwJzXsLR5c97+9Aeo\\/HVhLRP9cpox8dbz3thG4w+lpGkKek1kJY\\/gIhguLOYfWFO18aLp8eZ30NQ==\"}";
        Map<String, String> map = JSON.parseObject(paramStr, Map.class);
        String sign = map.get("sign");
        System.out.println("sign:  " + sign);
        String signStr = UrlUtils.createLinkString(map);
        System.out.println("signStr:    " + signStr);
        boolean verify = RSA2.verify("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgyZkyQ0pPhBPILM0uwb4EL6fexiRE8+NCqGRKPdLRzp8OKz/n70SbbY2JgnvlocNugLeLAiaE0S5zmyawNhtQVCfejaHyITKCIPQjmG0mVZdQDZmWUjU/wGwQYoOFb0uA4hno4ZqgnZwY6UaF2Tyt1qT/h+jZ2jkZb1ilGx+s07TGh7xlyKd4u6Re9cgYC7Il4qRewHsG6Le9mjkd0wTQXDoC6agyHyGPvU/iLUaLvC3T6uyuCWo/OcJRY0nsaK0XfoM99jlstNHfoXogMZHWT29J9R71lAucSqgxV91XapbUKl8jO2G7gBcxyoF5Q3qnWvPBa0syhaM2CdIQelxGQIDAQAB", signStr, sign);
        System.out.println("sign result => "+ verify);
    }


    @Test
    public void testParse() {
        String str = "https://openapi.alipay.com/gateway.do?alipay_sdk=alipay-sdk-java-3.4.27.ALL&app_id=2016051301395927&biz_content=%7B%22body%22%3A%22APP%E5%85%85%E5%80%BC20.00%E5%85%83%22%2C%22out_trade_no%22%3A%2220181128140900778061763636033045%22%2C%22product_code%22%3A%22QUICK_MSECURITY_PAY%22%2C%22subject%22%3A%22APP%E5%85%85%E5%80%BC20.00%E5%85%83%22%2C%22total_amount%22%3A%2220.00%22%7D&charset=UTF-8&format=json&method=alipay.trade.app.pay&notify_url=https%3A%2F%2Fdev.yizhibo.tv%2Fdev%2Fappgw%2Fpay%2Frecharge%2Fali%2Fnotify%2Fv2&sign=pzXVoPKZRpVaPM5xq7QQyAaefN4sZtzXyso0oQY%2BZQCrrCsAdYKdAp0WEvSHI1urHMiDYjJ%2FRrjsfCwz6LOVDehTILWM%2FSQscTjfDVW6yNW61xsBB8FxNOa2MtlPwwmh5EJc8SqhH93yqQaEkBfbWArviMoMREw8CY6n4h5kfT0wYJBTFnA0wCKIBc3%2BgKwo6FFcOPDtH4d0WAgsL%2BMa0VilBAGvZBUr0f2bT%2FUDBvklJgCX9DsvXpSzV664zCtHtI0dSFIo8vqqI%2FEB7J46gRYqKNm4NlNBP1fWzq3OR%2B7YZ8F0mlo0xMXtcbxymoGZqCa6Le%2Fi4oJAF1ocgOQLI%2F2BBges63Wr5PqmMxdSEy6tB%2B%2F%2FjlfeXxPG2T2cSae9PLdmpgZQu2FcIkDcTvZ1LYnNaBY2NsOQT765B%2B7dmGbWUNxCFt6Vom05aXuQUh5HVBgtGy31Qis0SDOEf3Px%2Fl%2BSy8Ag6n92oFV%2Fz1o0QmYse06iVlBNIJBns%2FjhPdrd&sign_type=RSA2&timestamp=2018-11-28+14%3A09%3A00&version=1.0";
        MultiValueMap<String, String> parameters =
                UriComponentsBuilder.fromUriString(str).build().getQueryParams();
        parameters.forEach((k, v) -> {
            System.out.println(k + "==> " + v);
        });

        str = "http://yzb.tv/recharge.php?result=success";
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(str);
        builder.queryParam("name","12345678");
        log.info(builder.toUriString());
    }

    @Test
    public void testURLEncode() throws EncoderException, UnsupportedEncodingException {
        URLCodec codec = new URLCodec();
        System.out.println(codec.encode("隨 風"));
        System.out.println(UriUtils.encode("隨 風","utf-8"));
    }

    @Test
    public void test3() throws Exception {
        String priKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCqEcz0BqpxkfKbwU+9ZUEfCPtaT2AmpIo0e1gUdnwi6XIdGxzc2okHab6ROB+9s/Myp5o9iRCP1GHuu/4lqrONuuLZmj6EW3uaVq/pJ4FnVPlraiMoGTHxZVCJcyPIcEhcdArX6Y3lhKFRZEiOj3Z97OjBUSdQKrpf+1SWPDzJTQJKQ9W3NFhRlrUkWSD68t53yuHgjIJPai79iKYshN73Epo29COpfh+IXnUtbZ5VCtFZ5QKRNOteH7rUpek6i8aoyvzvoMM79Dxf0Ny6+qRGANJGejd93eBCtB6niDQ8b2nMAewbVsYzcPeLT45bx5wsMZIquBqQ6TH1Oh6Q6oUZAgMBAAECggEAAPDLCxPVF5d2+oBU1lm8vpaJ6NtloFNtqn8IJK2cJ5Di0TJYJIvHBLnX5aiXyA5AtMqygxPh8JEeje5REnl6jvEECAdVmvyThFtGbiIy5hGsqIuMiWYqFC7Bb8lerPorK+UxrJkN9pvoUyHd57GZ0lbc7Fji+HVwIuJY2IPFT3c1hv6QUyv0A/v5mjuKqrL4OHnew3N85l3KP48q4CDXZH73nyzsWHgSDdYW2V3FLcECn8eWhURP9kjH3+5zhEraur8a2KTpgbDnrNSne0cb6plhk9kRA79t5lCOVBc1dL4AcoqXDX2aZpZjNZPaLr3qvr7zqoRk/4CAACJND+DkUQKBgQDZx2mg2ngOB6vJ67YNEnHBFkmA8UNuBtbok/JRnsRUlyGiLbwQC0Q+AoPqFnXtligR8qJYxBRuloIVQoGP5a3pYdNL9/CL/+7h2LrOK/VZIPZv27jDsDPJnm7+huPWkqWCuPcZBHfOZudDjnTTH8gqvCABDjP9UfrVtKQRaPXxJQKBgQDH6toi0tuhYJyA9evPMdoybT3Evasf5MLeK6zUsq20wR571kH5ubxGvdL2RvzaM20MiTxjOW0RpDU6raD98AUoyUxRn/zVzqLWEmdeUBJ0gSD/lT75XpX1cB1B6qvrzC0lr/bwdHI1fVNtw3bvxLRNd1hwDNlZCuIaI0uQct7j5QKBgQC3H5uOiqZRBdpKXE4lB9Fnj5oXNsiAiHfF8kHcqChnCP5bJSHyS8cCbZMHEAPx2EWxcdlZ0udrZlvUDKxtl7B5gqzuQHqg/oCcoGXUX9pbLACNZrEiu9OQQg0LMdn74CwUk/wTqVIk2Rq2D9/kd+EVLkOrH4pQBNNrSA5+JUhJkQKBgQCs3nmtryNX0lDCeuRjj7ob7LaCz3eP5qJStvWGHtNx/6Q6dTUrmd4iVbZ8Wzn9/IYJVwouytNzc8jLaqRRsSDk+dvSoUNiJoJMFjHoLdkngRllqEqCG7YUO9Qw0ZnEFhPcCOLBXg/8DNj9iw3nmvJGIjMJATNYeb6111taFtqBuQKBgCBeo5YBPyxgkVeWD/JdvOiegD3HYlvrGEPcKANa2qQQxZle7Y9eZoAame5y5igCKZ47XcILiNQtgdWOZF0MD5iNDEw0Bu4p00yzBo3sLYWSEnGIus2fSJsp4tIBmEjf16nEe5arpVlllOzoLO+vGF4uSaBa/ntUkCyLggfnMaPK";
        String uri = "/v2/merchant/fund/list/";
        String domain = "https://api.jiuhedigital.com/api/";
        Map<String, Object> mapRequest = new HashMap<>();
        mapRequest.put("page", 1);
        mapRequest.put("pageSize", 20);
        String jsonString = JSON.toJSONString(mapRequest);
        String data = Base64.encode(jsonString.getBytes(StandardCharsets.UTF_8));
        JiuHeRequestBaseDTO baseDTO = new JiuHeRequestBaseDTO("Mb0d2a8b1a638cb9", data);
        String sortString = UrlUtils.createSortString(baseDTO.toHashMap());

        String signData = RSA2.sign(priKey, uri + "&" + sortString);
        baseDTO.setSign(signData);

        String response = HttpUtils.doPost(domain + uri, JSON.toJSONString(baseDTO), ContentType.APPLICATION_JSON);

        if (response == null || response.isEmpty()) {
            throw new ResponseException(ResponseError.E_GASH_ERROR, "提现服务器错误");
        }
        Map<String, Object> map = JsonUtils.fromString(response, Map.class);
        String respSign = (String) map.get("sign");
        String respData = (String) map.get("data");
        if (respSign == null || respData == null) {
            log.error("[JiuHe][getResponseData] error code : {}, msg: {}", map.get("code"), map.get("msg"));
            throw new ResponseException(ResponseError.E_GASH_ERROR, (String) map.get("msg"));
        }

        String responseJsonData = new String(Base64.decode(respData), StandardCharsets.UTF_8);
        log.info("[JiuHe][getResponseData] responseJsonData: {}", responseJsonData);
    }



    @Test
    public void test1() {
        final HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("page", 1);
        requestMap.put("pageSize", 20);
        String uri = "/v2/project/list/";

        String requestDataJson = JsonUtils.toString(requestMap);
        String data = Base64.encode(requestDataJson.getBytes(StandardCharsets.UTF_8));


//        String responseData = buildJiuHeRequest(uri, data);
        HashMap response = parseResponse("{\n" +
                "    \"reqid\": \"1700730016204\",\n" +
                "    \"data\": \"eyJjb2RlIjowLCJkYXRhIjp7Imxpc3QiOlt7ImlkIjoiOTI2NGQwOTYiLCJuYW1lIjoi5biC5Zy65Lq65ZGY5Y+R6JaqLeeugOaYk+etvue6pua1i+ivlSIsInNpZ25UeXBlIjoiU0lMRU5DRSIsImludlR5cGUiOiJHRU5FUkFMIiwiaW52T3B0aW9uIjoiKuS/oeaBr+aKgOacr+acjeWKoSrlhbbku5bova/ku7bmnI3liqEiLCJsb2NUeXBlIjoiQU5ZIiwibG9jYXRpb24iOiJBTlkiLCJkZW1hbmRTZXgiOiJBTlkiLCJkZW1hbmRBZ2UiOiJBTlkiLCJkZW1hbmRFZHUiOiJBTlkiLCJub3RlIjoi5YiG5YyF5Lu75Yqh5Yib5bu65rWL6K+V5YiG5YyF5Lu75Yqh5Yib5bu65rWL6K+V5YiG5YyF5Lu75Yqh5Yib5bu65rWL6K+V5YiG5YyF5Lu75Yqh5Yib5bu65rWL6K+V5YiG5YyF5Lu75Yqh5Yib5bu65rWL6K+VIiwiaGVhZGNvdW50IjoxMDAwLCJpc1B1YmxpYyI6ZmFsc2UsImJpeklkIjoiIiwic2VydmljZU1vZGUiOiJDUk9XRCIsInN0YXR1cyI6Ik9OR09JTkciLCJsZXZpZXJJZCI6IjM3NTY5MzY0Iiwic3RhcnREYXRlIjoiMjAyMy0wOS0xMSIsImVuZERhdGUiOiIyMDIzLTEyLTExIiwiZmluaXNoRGF0ZSI6IiJ9LHsiaWQiOiI3NTY5ZDhkNiIsIm5hbWUiOiLluILlnLrkurrlkZjmmJPnrb7lrp3lj5Holqot566A5piT562+57qmIiwic2lnblR5cGUiOiJTSUxFTkNFIiwiaW52VHlwZSI6IkdFTkVSQUwiLCJpbnZPcHRpb24iOiIq5L+h5oGv5oqA5pyv5pyN5YqhKuWFtuS7lui9r+S7tuacjeWKoSIsImxvY1R5cGUiOiJBTlkiLCJkZW1hbmRTZXgiOiJBTlkiLCJkZW1hbmRBZ2UiOiJBTlkiLCJkZW1hbmRFZHUiOiJBTlkiLCJub3RlIjoi5YiG5YyF5Lu75Yqh5Yib5bu65rWL6K+VIiwiaGVhZGNvdW50IjoxMDAwLCJpc1B1YmxpYyI6ZmFsc2UsImJpeklkIjoiIiwic2VydmljZU1vZGUiOiJDUk9XRCIsInN0YXR1cyI6IkRPTkUiLCJsZXZpZXJJZCI6IjM3NTY5MzY0Iiwic3RhcnREYXRlIjoiMjAyMy0wOS0xMCIsImVuZERhdGUiOiIyMDI0LTAzLTEwIiwiZmluaXNoRGF0ZSI6IiJ9LHsiaWQiOiJkNTYwNTU0NiIsIm5hbWUiOiIxMTExMTEyMjMzMyIsInNpZ25UeXBlIjoiT05MSU5FIiwiaW52VHlwZSI6IkdFTkVSQUwiLCJpbnZPcHRpb24iOiIq5L+h5oGv5oqA5pyv5pyN5YqhKuWFtuS7lui9r+S7tuacjeWKoSIsImxvY1R5cGUiOiJBTlkiLCJsb2NhdGlvbiI6IkFOWSIsImRlbWFuZFNleCI6IkFOWSIsImRlbWFuZEFnZSI6IkFOWSIsImRlbWFuZEVkdSI6IkFOWSIsIm5vdGUiOiLliIbljIXku7vliqHliJvlu7rmtYvor5XliIbljIXku7vliqHliJvlu7rmtYvor5XliIbljIXku7vliqHliJvlu7rmtYvor5XliIbljIXku7vliqHliJvlu7rmtYvor5XliIbljIXku7vliqHliJvlu7rmtYvor5UiLCJoZWFkY291bnQiOjEwMDAsImlzUHVibGljIjpmYWxzZSwiYml6SWQiOiIiLCJzZXJ2aWNlTW9kZSI6IkNST1dEIiwic3RhdHVzIjoiRE9ORSIsImxldmllcklkIjoiMzc1NjkzNjQiLCJzdGFydERhdGUiOiIyMDIzLTA2LTE5IiwiZW5kRGF0ZSI6IjIwMjMtMDktMTkiLCJmaW5pc2hEYXRlIjoiIn0seyJpZCI6Ijk4NjgyMmQ2IiwibmFtZSI6IuWOu+eOqeWEvyIsInNpZ25UeXBlIjoiT05MSU5FIiwiaW52VHlwZSI6IiIsImludk9wdGlvbiI6IirnjrDku6PmnI3liqEq5o6o5bm/5pyN5Yqh6LS5IiwibG9jVHlwZSI6IkFOWSIsImxvY2F0aW9uIjoiQU5ZIiwiZGVtYW5kU2V4IjoiQU5ZIiwiZGVtYW5kQWdlIjoiQU5ZIiwiZGVtYW5kRWR1IjoiQU5ZIiwibm90ZSI6IuaYr+eahOmYsuaZkuacjeeahOaWueW8j+WFrOWPuOeahOWFrOWPuOWkp+mUhemlreesrOS4ieaWuWciLCJoZWFkY291bnQiOjEwLCJpc1B1YmxpYyI6dHJ1ZSwiYml6SWQiOiIiLCJzZXJ2aWNlTW9kZSI6IkNST1dEIiwic3RhdHVzIjoiRkFJTCIsImxldmllcklkIjoiMzc1NjkzNjQiLCJzdGFydERhdGUiOiIyMDIzLTA2LTE5IiwiZW5kRGF0ZSI6IjIwMjMtMDktMTkiLCJmaW5pc2hEYXRlIjoiIn0seyJpZCI6Ijc0NjVkNDA2IiwibmFtZSI6ImFzZGZhc2RmYXNmYSIsInNpZ25UeXBlIjoiT05MSU5FIiwiaW52VHlwZSI6IkdFTkVSQUwiLCJpbnZPcHRpb24iOiIq5L+h5oGv5oqA5pyv5pyN5YqhKuWFtuS7lui9r+S7tuacjeWKoSIsImxvY1R5cGUiOiJBTlkiLCJsb2NhdGlvbiI6Ik9OTElORSIsImRlbWFuZFNleCI6IkFOWSIsImRlbWFuZEFnZSI6IkFOWSIsImRlbWFuZEVkdSI6IkFOWSIsIm5vdGUiOiLliIbljIXku7vliqHliJvlu7rmtYvor5Vhc2RmIGFzZGYgYXNkIGZhcyBmIiwiaGVhZGNvdW50IjoxMDAwLCJpc1B1YmxpYyI6ZmFsc2UsImJpeklkIjoiIiwic2VydmljZU1vZGUiOiJDUk9XRCIsInN0YXR1cyI6IkRPTkUiLCJsZXZpZXJJZCI6IjM3NTY5MzY0Iiwic3RhcnREYXRlIjoiMjAyMy0wNS0zMCIsImVuZERhdGUiOiIyMDIzLTA4LTMwIiwiZmluaXNoRGF0ZSI6IiJ9LHsiaWQiOiJlNTY3N2U4NiIsIm5hbWUiOiLluILlnLrkurrlkZjmmJPnrb7lrp3lj5HolqoiLCJzaWduVHlwZSI6Ik9OTElORSIsImludlR5cGUiOiJHRU5FUkFMIiwiaW52T3B0aW9uIjoiKuS/oeaBr+aKgOacr+acjeWKoSrlhbbku5bova/ku7bmnI3liqEiLCJsb2NUeXBlIjoiQU5ZIiwibG9jYXRpb24iOiJPTkxJTkUiLCJkZW1hbmRTZXgiOiJBTlkiLCJkZW1hbmRBZ2UiOiJBTlkiLCJkZW1hbmRFZHUiOiJBTlkiLCJub3RlIjoi5YiG5YyF5Lu75Yqh5Yib5bu65rWL6K+VYXNkZiBhc2RmIGFzZCBmYXMgZiIsImhlYWRjb3VudCI6MTAwMCwiaXNQdWJsaWMiOmZhbHNlLCJiaXpJZCI6IiIsInNlcnZpY2VNb2RlIjoiQ1JPV0QiLCJzdGF0dXMiOiJET05FIiwibGV2aWVySWQiOiIzNzU2OTM2NCIsInN0YXJ0RGF0ZSI6IjIwMjMtMDUtMzAiLCJlbmREYXRlIjoiMjAyMy0wNS0zMCIsImZpbmlzaERhdGUiOiIifSx7ImlkIjoiOTI2NDI5NzYiLCJuYW1lIjoi5biC5Zy65Lq65ZGY5Y+R6JaqIiwic2lnblR5cGUiOiJPTkxJTkUiLCJpbnZUeXBlIjoiR0VORVJBTCIsImludk9wdGlvbiI6Iirkv6Hmga/mioDmnK/mnI3liqEq5YW25LuW6L2v5Lu25pyN5YqhIiwibG9jVHlwZSI6IkFOWSIsImxvY2F0aW9uIjoiQU5ZIiwiZGVtYW5kU2V4IjoiQU5ZIiwiZGVtYW5kQWdlIjoiQU5ZIiwiZGVtYW5kRWR1IjoiQU5ZIiwibm90ZSI6IuWIhuWMheS7u+WKoeWIm+W7uua1i+ivleWIhuWMheS7u+WKoeWIm+W7uua1i+ivleWIhuWMheS7u+WKoeWIm+W7uua1i+ivleWIhuWMheS7u+WKoeWIm+W7uua1i+ivleWIhuWMheS7u+WKoeWIm+W7uua1i+ivlSIsImhlYWRjb3VudCI6MTAwMCwiaXNQdWJsaWMiOmZhbHNlLCJiaXpJZCI6IiIsInNlcnZpY2VNb2RlIjoiQ1JPV0QiLCJzdGF0dXMiOiJET05FIiwibGV2aWVySWQiOiIzNzU2OTM2NCIsInN0YXJ0RGF0ZSI6IjIwMjMtMDUtMTEiLCJlbmREYXRlIjoiMjAyMy0xMS0xMSIsImZpbmlzaERhdGUiOiIifV0sInN1bSI6eyJ0b3RhbCI6N30sInRvdGFsIjowLCJwYWdlIjoxLCJwYWdlU2l6ZSI6MjB9LCJtc2ciOiLmk43kvZzmiJDlip8ifQo=\",\n" +
                "    \"sign\": \"eJMTM0ssXaR/Bf7qxedesoQ5FanOUcQOkxUUZKBJmSLAPyFzRQUhwfXE3bfEiDlIpuDfeTjTCc53f0i+p++mv71dHSMM5xwZhgkAF1V0HiOYmQzkPEmUCGDGuyjX8Wiy/gWKr4skJoJQ8nZwDB1IWXimHP3iJLmN+pDJZAQzdIrEsX1+qrpBw9dWnPpfX78AUOeTbU/Ycq5cWIpUnnUGf5xmynh8LiRBdDWpTnl8nnU7/LNU0Wr/a2aNnVEKjRltggu765S6cCYMwH0Wy/ZZ8jZhzkQewHu4r0zml6qo5A4sEnGhxi2xnKla5aL3UGsF8u0hiaROY+IzS5fDRqQUXA==\"\n" +
                "}", false);
        log.info("{}", response);
    }

    @Test
    public void test2() {
        String url = "http://img.devel.scmagic.net/test/user/96/6e/phpe4wITu@1080h_1080w_90Q_0e_1c_1o";
        byte[] urlBinaryContent = FileUtils.getURLBinaryContentNIO(url);

        String md5 = DigestUtils.md5Hex(urlBinaryContent);

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("scene", "");
        requestMap.put("md5", md5);

        String requestDataJson = JsonUtils.toString(requestMap);
        log.info("[JiuHe][signContract] upload file. requestDataJson={}", requestDataJson);
        String data = Base64.encode(requestDataJson.getBytes(StandardCharsets.UTF_8));

        String uri = "/v2/file/upload/";

        try {
            String responseData = buildJiuHeUploadRequest(uri, data, urlToFile(url));
            HashMap response = parseResponse(responseData, false);

            log.info("[JiuHe][signContract] upload file. response:{}", response);
        } catch (Exception e) {
            log.error("{}", e);
        }
    }

    /**
     * 构建玖合请求
     *
     * @param uri
     * @param requestData
     * @return
     */
    public  String buildJiuHeRequest(String uri, String requestData) {
        JiuHeRequestBaseDTO baseDTO = new JiuHeRequestBaseDTO("Md3cf5c793040049", requestData);
        String sortString = UrlUtils.createSortString(baseDTO.toHashMap());
        String sign = paramSign(uri, sortString);
        baseDTO.setSign(sign);
        String requestBody = JsonUtils.toString(baseDTO);
        log.info("[JiuHe][buildJiuHeRequest] uri: {} requestBody: {}", uri, requestBody);
        String response = HttpUtils.doPost("https://test.jiuhedigital.com/" + uri, requestBody, ContentType.APPLICATION_JSON);
        log.info("[JiuHe][buildJiuHeRequest] uri: {} responseBody: {}", uri, response);
        return response;
    }

    public <T> T parseResponse(String response, boolean isVerifySign) {
        if (response == null || response.isEmpty()) {
            throw new ResponseException(ResponseError.E_GASH_ERROR, "提现服务器错误");
        }
        Map<String, Object> map = JsonUtils.fromString(response, Map.class);
        String respSign = (String) map.get("sign");
        String respData = (String) map.get("data");
        if (respSign == null || respData == null) {
            log.error("[JiuHe][buildJiuHeRequest] error code : {}, msg: {}", map.get("code"), map.get("msg"));
            throw new ResponseException(ResponseError.E_GASH_ERROR, (String) map.get("msg"));
        }
        if (isVerifySign) {
        }
        String responseJsonData = new String(Base64.decode(respData), StandardCharsets.UTF_8);
        log.info("[JiuHe][parseResponse] responseJsonData: {}", responseJsonData);
        Map<String, Object> dataMap = JsonUtils.fromString(responseJsonData, Map.class);
        if ((int) dataMap.get("code") != 0) {
            throw new ResponseException(ResponseError.E_GASH_ERROR, (String) dataMap.get("msg"));
        }

        return (T) dataMap.get("data");
    }

    /**
     * 签名方法
     *
     * @param signStr 要签名的字符串
     * @return 签名数据
     * @throws ResponseException 当签名出现异常时抛出异常
     */
    private String paramSign(String uri, String signStr) {
        String signData = "";
        try {
            String data = StringUtils.isEmpty(uri) ? signStr : uri + "&" + signStr;
            signData = RSA2.sign("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC8EpNkQX0bA7nQRSC7sblMlpVD6kUqS8FnVxlK+zjOYZQuxVTWuoQMVlRu3H5/t5JpT+VUldUUwU+Amzq95Q5b61ZlfEKRC8OBTKSdhY3UXCLI/Sa+hnpmK/8WlwOx0bayiiXhTmKlPS82MZGZl86VQ6syGnEzDYBW2DUc9C9qIkpuBcYBaojowos2xA3QywAOJhGUNyCFY6ppmti4318qNo5wBzy41V5oKxdtk7tpZxU1SYpVkQF2ggabvF2G0lF2hEEo1DEY8UFVvkO2WXUhqG5Fnbu3zosmHO3Fcel6owx5uBjXR6FhJEZvkHBJq7SsLmYzIcGVSUwaT9HFTrEhAgMBAAECggEAfZlOM3sEyKDBW9R+mrqCzCIYRwXR8QAshg4Fxyxk8Hb8FgwUr15aRz70HiFEnZiDv67qsYVqivTaWkHQhfbNkxdkWFwWvHUFuHjtyoDhifuKA34EfOKkJU2miniJsBwkjbFywLevazikKeYKnW9jOA2767b6+CRBHD5Bojd03sQoHBhlYem08pCqA5fi2+gRPW/MltaWPSuK+n5GA0KJ34oVOJrHwt5aLVzcX1djKSor/qvKOogLRbHV3BHzRZ7b9lA9gEuR8Q3s/EmqVgoqSDwoTz6AAROVValBwpRL4uKA9ifxQUchpNEo+ltKQaHe1PP5HKvRcELT2EBG+ffrQQKBgQDpaPB2yECG8qdbrprhwgLFkEDYKy2VDKrzzBpX14bkVDouBchMciOynkzV2RxNkYvXxNS3eC03Xc6FWQmCUEYRvY2aZWlRxfaCsK+Bz0YPKo57LnvLz3atqUYgTGtosJHeLUdHwaGEVsrvfjRaQyuL1Ny3CSOv46HnaSttGMxSyQKBgQDORlbdL7SarmRX8TNXVL0MxZaZiZY474BqEq6289xfJUD9FiQNwImBL1nPN1B0BcO1OZ2svnU3GNdH7XvBWEL5H0gVnohDUtEEP5soK8brMGon5mkDvpsmxrFiE84wxxTuWg1fciUp1TRnRUgmBfY12ESNcJwW2jpYzlcmoWP/mQKBgG8TdXvRiIOv7lz/Fs3g2hjSsUkuIx8PTDuBIfgzpQn720QTlGJs3vgdxUrvazyuyvH/xYsaMcprZeCnopLVSMMghca4BAraPpmrI5qO3TRSeEO+t0Mik8PJDopIU8UaYhBtU8KeLxMfiqu2T4u084Az6GVigA0wMthBgF0GtBWhAoGBAKyPKVGl+/c+lpD/JY0URYausNrGv9Xnfcy+zkwc2yj5OFHAuO6MODvLCd2Qj4xP2jX7+gyt088Ax+/TGqlXFcfkRGBZm/J+EH72Wu0TGjhb6yJv3UAVryOUKoY6vU4O7VKsIO3MxowlYM5XphCxAhRvVIHiR6HSc+cgzsVxU/9ZAoGAFa7ZfuFy9/+gZHw59LkYQ5PTj9bZA/qohg1CI//oyMNBs2QlWGb+JzX3QgnZqqKF8IxdyHXJNXfHzdO5zfAgBf+WrkHiHEfGq/K4Nqf4WG+ld07hi6uAj0ehGbUscRy06Lh2UzNxNiHP7Kh3tG5xwcPFD4hgTrzcVnX7N9kmSnI=", data);
        } catch (Exception e) {
            log.error("[JiuHe][signContract] 签名异常", e);
            throw new ResponseException(ResponseError.E_CASHOUT_SIGN);
        }
        return signData;
    }

    public static File urlToFile(String urlString) {
        try {
            URL url = new URL(urlString);

            // 打开URL连接
            try (InputStream in = url.openStream()) {
                // 使用 Files 类将 InputStream 写入到本地文件
                Path tempFile = Files.createTempFile("tempFile", ".png");
                Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);

                // 获取 File 对象
                return tempFile.toFile();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    public String buildJiuHeUploadRequest(String uri, String requestData, File file) {
        JiuHeRequestUploadDTO uploadDTO = new JiuHeRequestUploadDTO("Md3cf5c793040049", requestData, file);
        String sortString = UrlUtils.createSortString(uploadDTO.toHashMap());
        String sign = paramSign(uri, sortString);
        uploadDTO.setSign(sign);
        String requestBody = JsonUtils.toString(uploadDTO);
        log.info("[JiuHe][buildJiuHeRequest] uri: {} requestBody: {}", uri, requestBody);
        String response = HttpUtils.doPost("https://test.jiuhedigital.com/" + uri, requestBody, ContentType.MULTIPART_FORM_DATA);
        log.info("[JiuHe][buildJiuHeRequest] uri: {} responseBody: {}", uri, response);
        return response;
    }

}
