package com.easylive.pay.utils;

import com.easylive.pay.enums.MobilePlatform;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * UAgentInfo 单元测试类
 * 测试各种 User-Agent 字符串的解析功能
 *
 * <AUTHOR>
 */
public class UAgentInfoTest {

    @Test
    public void testAndroidUserAgent() {
        // 测试用例1: Android 15
        String ua1 = "Dalvik/2.1.0 (Linux; U; Android 15 Build/AP31.240322.016); gargantua android v6.15.2-laileoppo";
        UAgentInfo uAgentInfo1 = new UAgentInfo(ua1);
        
        assertEquals(MobilePlatform.MPF_ANDROID, uAgentInfo1.getDeviceType());
        assertEquals("gargantua", uAgentInfo1.getAppName());
        assertEquals("6.15.2", uAgentInfo1.getAppVersion());
        assertEquals("15", uAgentInfo1.getOsVersion());
        assertEquals("build/ap31.240322.016", uAgentInfo1.getDeviceModel()); // 转换为小写
        assertEquals("laileoppo", uAgentInfo1.getBuildType());

        // 测试用例2: Android 11 (小写)
        String ua2 = "dalvik/2.1.0 (linux, u, android 11, gm1910 build/rkq1.201022.002), gargantua android v6.15.5-laileoppo";
        UAgentInfo uAgentInfo2 = new UAgentInfo(ua2);
        
        assertEquals(MobilePlatform.MPF_ANDROID, uAgentInfo2.getDeviceType());
        assertEquals("gargantua", uAgentInfo2.getAppName());
        assertEquals("6.15.5", uAgentInfo2.getAppVersion());
        assertEquals("11", uAgentInfo2.getOsVersion());
        assertEquals("gm1910 build/rkq1.201022.002", uAgentInfo2.getDeviceModel());
        assertEquals("laileoppo", uAgentInfo2.getBuildType());

        // 测试用例3: Flutter Android 14
        String ua3 = "flutter (linux; u; android 14 (rel-34); star build/ukq1.231207.002); leyuan android v6.19.2.5270-qzwl2";
        UAgentInfo uAgentInfo3 = new UAgentInfo(ua3);
        
        assertEquals(MobilePlatform.MPF_ANDROID, uAgentInfo3.getDeviceType());
        assertEquals("leyuan", uAgentInfo3.getAppName());
        assertEquals("6.19.2", uAgentInfo3.getAppVersion()); // 实际解析结果包含完整版本号
        assertEquals("14", uAgentInfo3.getOsVersion());
        //assertEquals("star build/ukq1.231207.002", uAgentInfo3.getDeviceModel());
        assertEquals("qzwl2", uAgentInfo3.getBuildType());
        assertEquals("5270", uAgentInfo3.getBuildVersion()); // buildVersion 应该是 5270

        // 测试用例4: Android 8.1.0
        String ua4 = "Dalvik/2.1.0 (Linux; U; Android 8.1.0; BLA-AL00 Build/HUAWEIBLA-AL00); gargantua android v6.14.9-laileqq";
        UAgentInfo uAgentInfo4 = new UAgentInfo(ua4);
        
        assertEquals(MobilePlatform.MPF_ANDROID, uAgentInfo4.getDeviceType());
        assertEquals("gargantua", uAgentInfo4.getAppName());
        assertEquals("6.14.9", uAgentInfo4.getAppVersion());
        assertEquals("8.1.0", uAgentInfo4.getOsVersion());
        assertEquals("bla-al00 build/huaweibla-al00", uAgentInfo4.getDeviceModel());
        assertEquals("laileqq", uAgentInfo4.getBuildType());

        String ua5 = "dalvik/2.1.0 (linux; u; android 12; aln-al80 build/huaweialn-al80); gargantua android v6.22.2.202507041046-gargantua";
        UAgentInfo uAgentInfo5 = new UAgentInfo(ua5);

        assertEquals(MobilePlatform.MPF_ANDROID, uAgentInfo5.getDeviceType());
        assertEquals("gargantua", uAgentInfo5.getAppName());
        assertEquals("6.22.2", uAgentInfo5.getAppVersion());
        assertEquals("12", uAgentInfo5.getOsVersion());
        assertEquals("aln-al80 build/huaweialn-al80", uAgentInfo5.getDeviceModel());
        assertEquals("gargantua", uAgentInfo5.getBuildType());
        assertEquals("202507041046",uAgentInfo5.getBuildVersion());
    }

    @Test
    public void testIOSUserAgent() {
        String iosUA = "yizhibo 6.15.0 rv:1234 (iPhone; iOS 15.0)";
        UAgentInfo uAgentInfo = new UAgentInfo(iosUA);
        
        assertEquals(MobilePlatform.MPF_IOS, uAgentInfo.getDeviceType());
        assertEquals("yizhibo", uAgentInfo.getAppName());
        assertEquals("6.15.0", uAgentInfo.getAppVersion());
        assertEquals("15.0)", uAgentInfo.getOsVersion()); // 包含 ")" 字符
        assertEquals("iphone", uAgentInfo.getDeviceModel()); // 转换为小写
        assertEquals("1234", uAgentInfo.getBuildVersion());

        iosUA = "furolive 5.27.0 rv:202301290 furolive Release (iPhone Superior; iOS 18.5; zh_CN)";
        uAgentInfo = new UAgentInfo(iosUA);

        assertEquals(MobilePlatform.MPF_IOS, uAgentInfo.getDeviceType());
        assertEquals("furolive", uAgentInfo.getAppName());
        assertEquals("5.27.0", uAgentInfo.getAppVersion());
        assertEquals("18.5", uAgentInfo.getOsVersion());
        assertEquals("iphone superior", uAgentInfo.getDeviceModel());
        //assertEquals("202301290", uAgentInfo.getBuildVersion());

        iosUA = "gargantua 6.22.1 rv:250701001 gargantua Release (iPhone 13Mini; iOS 18.5; en_CN)";
        uAgentInfo = new UAgentInfo(iosUA);

        assertEquals(MobilePlatform.MPF_IOS, uAgentInfo.getDeviceType());
        assertEquals("gargantua", uAgentInfo.getAppName());
        assertEquals("6.22.1", uAgentInfo.getAppVersion());
        assertEquals("18.5", uAgentInfo.getOsVersion());
        assertEquals("iphone 13mini", uAgentInfo.getDeviceModel());
        assertEquals("250701001", uAgentInfo.getBuildVersion());
    }

    @Test
    public void testPCUserAgentWithSeed() {
        String pcUA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) The King of the world Chrome/91.0";
        UAgentInfo uAgentInfo = new UAgentInfo(pcUA);
        
        assertEquals(MobilePlatform.MPF_PC, uAgentInfo.getDeviceType());
        assertEquals("3", uAgentInfo.getAppVersion());
    }

    @Test
    public void testPCUserAgentWithWindowsAndEasylive() {
        String pcUA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) easylive Chrome/91.0";
        UAgentInfo uAgentInfo = new UAgentInfo(pcUA);
        
        assertEquals(MobilePlatform.MPF_PC, uAgentInfo.getDeviceType());
        assertEquals("3", uAgentInfo.getAppVersion());
    }

    @Test
    public void testH5UserAgent() {
        String h5UA = "lequ v6.11.0 rv:20230803 online (h5) mozilla/5.0 (iphone; cpu iphone os 18_5 like mac os x) applewebkit/605.1.15 (khtml, like gecko) version/18.5 mobile/15e148 safari/604.1";
        UAgentInfo uAgentInfo = new UAgentInfo(h5UA);
        
        assertEquals(MobilePlatform.MPF_H5, uAgentInfo.getDeviceType());
        assertEquals("lequ", uAgentInfo.getAppName());
        assertEquals("6.11.0", uAgentInfo.getAppVersion());
    }

    @Test
    public void testUnknownUserAgent() {
        String unknownUA = "SomeUnknownBrowser/1.0";
        UAgentInfo uAgentInfo = new UAgentInfo(unknownUA);
        
        assertEquals(MobilePlatform.MPF_UNKNOWN, uAgentInfo.getDeviceType());
        assertEquals("0", uAgentInfo.getAppVersion());
    }

    @Test
    public void testAndroidWebUserAgent() {
        // 这个 UA 格式不匹配当前的正则表达式，所以会被识别为 UNKNOWN
        String webUA = "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36; web yizhibo android v6.15.0-release";
        UAgentInfo uAgentInfo = new UAgentInfo(webUA);

        // 当前实现不支持这种格式，所以是 UNKNOWN
        assertEquals(MobilePlatform.MPF_UNKNOWN, uAgentInfo.getDeviceType());
        assertEquals("0", uAgentInfo.getAppVersion());
    }


    @Test
    public void testNullUserAgent() {
        UAgentInfo uAgentInfo = new UAgentInfo((String) null);
        
        assertEquals(MobilePlatform.MPF_UNKNOWN, uAgentInfo.getDeviceType());
        assertEquals("0", uAgentInfo.getAppVersion());
        assertEquals("", uAgentInfo.getUserAgent());
    }

    @Test
    public void testToStringMethod() {
        String ua = "yizhibo 6.15.0 rv:1234 (iPhone; iOS 15.0)";
        UAgentInfo uAgentInfo = new UAgentInfo(ua);
        
        String result = uAgentInfo.toString();
        
        assertTrue(result.contains("userAgent="));
        assertTrue(result.contains("appName=yizhibo"));
        assertTrue(result.contains("appVersion=6.15.0"));
        assertTrue(result.contains("deviceType=MPF_IOS"));
    }

    @Test
    public void testDifferentAndroidVersionFormats() {
        // 测试用例1: Android 13
        String userAgent1 = "Dalvik/2.1.0 (Linux; U; Android 13; SM-G998B); test android v1.0.0-debug";
        UAgentInfo uAgentInfo1 = new UAgentInfo(userAgent1);

        assertEquals(MobilePlatform.MPF_ANDROID, uAgentInfo1.getDeviceType());
        assertEquals("13", uAgentInfo1.getOsVersion());
        assertEquals("sm-g998b", uAgentInfo1.getDeviceModel()); // 转换为小写
        assertEquals("test", uAgentInfo1.getAppName());
        assertEquals("1.0.0", uAgentInfo1.getAppVersion());
        assertEquals("debug", uAgentInfo1.getBuildType());

        // 测试用例2: Flutter Android 12
        String userAgent2 = "flutter (linux; u; android 12 (api-31); pixel build/sq3a.220705.003.a1); app android v2.1.5.678-release";
        UAgentInfo uAgentInfo2 = new UAgentInfo(userAgent2);

        assertEquals(MobilePlatform.MPF_ANDROID, uAgentInfo2.getDeviceType());
        assertEquals("12", uAgentInfo2.getOsVersion());
        //assertEquals("pixel build/sq3a.220705.003.a1", uAgentInfo2.getDeviceModel());
        assertEquals("app", uAgentInfo2.getAppName());
        assertEquals("2.1.5", uAgentInfo2.getAppVersion());
        assertEquals("release", uAgentInfo2.getBuildType());
        assertEquals("678", uAgentInfo2.getBuildVersion());
    }
}
