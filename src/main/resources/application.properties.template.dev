server.port = 8080
spring.profiles.active=dev

# Database
db.master.url = ***********************************************************************************************************************************************************************
db.master.username = yizhibo
db.master.password = Yizhibo@2015
db.slave.url = ***********************************************************************************************************************************************************************
db.slave.username = yizhibo
db.slave.password = Yizhibo@2015
hibernate.show_sql = false

# REDIS (RedisProperties)
# database name
spring.redis.database=0
# server host
spring.redis.host=localhost
# server password
spring.redis.password=
# connection port
spring.redis.port=6379
# pool settings ...
spring.redis.pool.max-idle=8
spring.redis.pool.min-idle=1
spring.redis.pool.max-active=64
spring.redis.pool.max-wait=-1
#spring.redis.sentinel.master= # name of Redis server
#spring.redis.sentinel.nodes= # comma-separated list of host:port pairs

# Cash
cash.limit = 20000
cash.fee = 0
cash.rate.rmb.to.riceroll = 25
cash.rate.ecoin.to.riceroll = 250
cash.gift.ecoin.to.riceroll = 145
cash.payliving.ecoin.to.riceroll = 120
cash.payrecord.ecoin.to.riceroll = 75
cash.rate.ecoin.to.rmb = 10
cash.riceuid = 1371,1372,1377
cash.rate.with.draw.extra = 0.2
cash.with.draw.extra.begin.time = 2022-01-01

# Static URL
url.serverip = **************
url.appgate = http://devel.easyvaas.com/test/appgw/pay
url.appgate.yaomer = http://devel.easyvaas.com/test/app/yaomer/pay
url.redpack.icon=http://img.devel.easyvaas.com/online/goods/redpack_logo.png
url.redpack.icon.yaomer=http://img.yaomer.cn/magiclive/redpack_logo.png
url.service.user=http://devel.easyvaas.com/test/mgs/user
url.service.chat=http://devel.easyvaas.com/test/mgs/chat
url.service.imsever=http://devel.easyvaas.com/test/imserver
url.service.lotus=http://devel.easyvaas.com/test/lotus
url.service.ieasy=http://devel.easyvaas.com/test/mgs/ieasy
url.service.bizcore=http://devel.easyvaas.com/test/mgs/bizcore
url.service.asset=http://devel.easyvaas.com/test/mgs/asset
url.service.center=http://devel.easyvaas.com/test/mgs/center
url.service.provider=http://proxy.provider.lailer.net


# Cache
cache.prefix = dev_

# alipay
pay.ali.rsakey = /zebra/data/pay/rsa_private_key.pem
pay.ali.public.key.path=/zebra/data/pay/alipay/public
pay.ali.private.key.path=/zebra/data/pay/alipay/private
pay.ali.notify.path=/notify/pay/ali/notify
pay.ali.return.path = /notify/pay/ali/web-return


# Weixin

weixin.send_name = \u6613\u76f4\u64ad
weixin.wishing = \u606d\u559c\u60a8\u83b7\u5f97\u7ea2\u5305
weixin.act_name = \u6613\u76f4\u64ad\u7ea2\u5305\u6d3b\u52a8
weixin.remark = \u73a9\u6613\u76f4\u64ad\u5f97\u5927\u7ea2\u5305

pay.weixin.notify.path = /notify/pay/wx
pay.weixin.redpack.cert.file = /zebra/data/pay/wx/**********.p12
pay.weixin.redpack.cert.pass = **********

pay.weixin.mp.appid=wxf8a573fb0b091fa7
pay.weixin.app.appid=wxc2589d8b2b06c4e5
pay.weixin.code.appid=wxf8a573fb0b091fa7
pay.weixin.h5.appid=wxba75ece251c6b98f
pay.weixin.miniapp.appid=wx82758b3c327b0b92
pay.weixin.miniapp.secret=8b4c9aa61562ce28223ea255f01024ed
pay.weixin.miniapp.app.notify=http://devel.easyvaas.com/test/wx_shop/api.php?mod=wxpay&act=callback&

pay.weixin.h5.path = /yzb-recharge/pay

pay.reapal.merchantid=10000********47
pay.reapal.key=g0be2385657fa355af68b74e9913a1320af82gb7ae5f580g79bffd04a402ba8f
pay.reapal.seller-email=<EMAIL>
pay.reapal.rongpay-api=http://testapi.reapal.com/web/portal
pay.reapal.privatekey=/zebra/data/pay/rongbaotest/itrus001.pfx
pay.reapal.privatekey-pwd=123456
pay.reapal.pubkey=/zebra/data/pay/rongbaotest/itrus001.cer


#google
pay.google.key.dir = /zebra/data/pay/google


#
pay.oppo.notify.key = MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCmreYIkPwVovKR8rLHWlFVw7YDfm9uQOJKL89Smt6ypXGVdrAKKl0wNYc3/jecAoPi2ylChfa2iRu5gunJyNmpWZzlCNRIau55fxGW0XEu553IiprOZcaw5OuYGlf60ga8QT6qToP0/dpiL/ZbmNUO9kUhosIjEu22uFgR+5cYyQIDAQAB
pay.oppo.app.key = 4a95fa4f69fe4b0aa4cba1f8c56c7d8a
pay.oppo.app.secret = 8f8fdec120664e71a2eb25c3fd0c21eb
pay.oppo.user.check.url = https://iopen.game.oppomobile.com/sdkopen/user/fileIdInfo


#game
game.sign.key = 3834309564feffafb6a9712


kafka.server.bootstrap=dev001:9092,dev002:9092,dev003:9092

# 新郑银企提现相关
xzyq.mac.secret_key=AB7E309F4DBB6DD8E002A69B0129DC75
xzyq.bank.url=http://**************:24133
xzyq.bank.enterprise.code=********
xzyq.bank.enterprise.code.new=********
xzyq.bank.bank-no=********
xzyq.bank.channel.type=0001

# 税优地提现相关
cashout.syd.pubKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDQKz3GhUlcqnHBeuviFZuQVASqQCD9MswooA4/qiKlTnHoO5oz//QL0S+jHBlJxGcod8SVlFxZpgpjzBAGnajIfbQ5kfVJxCT+3+ZO1Xb2z0OBaSs60TvibiZWyu1WosP8rMCwf3yj6gro3RXPuSzBhOmyubLp2/h0Tu3QMv19wQIDAQAB
cashout.syd.priKey=MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBANArPcaFSVyqccF66+IVm5BUBKpAIP0yzCigDj+qIqVOceg7mjP/9AvRL6McGUnEZyh3xJWUXFmmCmPMEAadqMh9tDmR9UnEJP7f5k7VdvbPQ4FpKzrRO+JuJlbK7Vaiw/yswLB/fKPqCujdFc+5LMGE6bK5sunb+HRO7dAy/X3BAgMBAAECgYApjZglOpHsV3Ghp4HSfXGyG7NasLZlFHDAY0K03qdPzBrcupkLbDow6a7Splc97GCTKWUqXpUYqBZG2YMRbwMztfAkNvUzwEEDvSFSYfgSUFzNjjKnCnrF6i5fjPDHZp6e9CFSLj6MaSS5ptUEDTKxqnIRsCsxZCPvVsPCKW9IAQJBAPIEQzhsY1omVuhWWZ4S//H71xcQ+RDuoJ4v+Kl8mYS+ShafbmTl5Xnc3ecwE+gHxZAoa2hSbJq+nJ9FmroieSECQQDcMlPAAkCZGgN33870GpNUHpKBYHwfXvqQH4G4Y2LWmG7xFKZYGV90+Li9pLVk6fyZX6CWH+us6DYcolv11VChAkEAlT/LtKAjJRU/w0h9O967uJ92peLxtl9y7hrnXp5YjmRLjGgFzHN6cQykTE55+D3SuHQ0yMEL+OJT9GUYZnGcAQJBANKS6lZ3kPEY2HI9vLrh4zyRlAdXjrutZ66GAtajbLTNx2KTHdSoRwokhP8O0U11GlROB9MpKYcL9huCBLFeUqECQQCSnm76IVaIM1mVF5t5OyyThgFjr08CZgx4ZDuvA1TxZrHnArQmo9Fv6CiZfCfFMsdxZ8ObW6sI4PWweMYa2Jnp
cashout.syd.merId=89900000240015933379
cashout.syd.interKey=2008508A6609072DA64E136743178D9E
cashout.syd.domain=http://pay.lxxinfubao.com
cashout.syd.development=true

# 税筹2.0
cashout.sc.pubKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjenLqsNg+Hemdzsm2WYPIoB6blnBjGlChq6Qqn5PAZfTU63s0Q2DLDDmwVhLtb2aSpON7wZuhk+t03S3/iZzxGilqWRS8Gs24u+VXNXKGxzphxyVTYvQKcqXUe0zahSYdTuWa2VB9N6eHP/4Y/LYKZcx+ZS18gxOavlOerxnGV/f1fNWVLMAr/N0JeaapwkIaLdhJ0Tr+IdM8zhI0ug63+ABEgB0ALXbGPULvbnuVZ9GTcQ/j7Zrfqa1k1cMj1Jb391tn6MvR3SKCXbww4W9x1WDixbwHy1OF850uAD5d40BCfge5FvD+ZcAhCUwVoMwPuoiJKGujehu6i5m1LtQUwIDAQAB
cashout.sc.priKey=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCN6cuqw2D4d6Z3OybZZg8igHpuWcGMaUKGrpCqfk8Bl9NTrezRDYMsMObBWEu1vZpKk43vBm6GT63TdLf+JnPEaKWpZFLwazbi75Vc1cobHOmHHJVNi9ApypdR7TNqFJh1O5ZrZUH03p4c//hj8tgplzH5lLXyDE5q+U56vGcZX9/V81ZUswCv83Ql5pqnCQhot2EnROv4h0zzOEjS6Drf4AESAHQAtdsY9Qu9ue5Vn0ZNxD+Ptmt+prWTVwyPUlvf3W2foy9HdIoJdvDDhb3HVYOLFvAfLU4XznS4APl3jQEJ+B7kW8P5lwCEJTBWgzA+6iIkoa6N6G7qLmbUu1BTAgMBAAECggEAL51jJX96eMOEifrB1S2GJHhhG8dnxlnG5XOKvHm2vn0XfbsceyQCgABEXFjoxijArQcHF0zcJppzY0CFP3PdYegOtWAg5+PBPbFPNx/QOPqhbCDuA1/Gz07XKLjiiFQytxrwJEaMM8HB4U8NI1PmrUW6kdgQmA8C/Kd7anjuSre5QEkKKSQvCx7t4wF717rzNxlcIEP3YOxtL+3ON9txSr7xG96mR3vsVRRjioSGn4a1SdXK5j/Lcsr5yzvtFCdWJgfIQnIwoxfDj6k4KKpnLdPCCPXszKVRBmdBWQc5RSiSdE8USMPxb+SP1etqsVDQbAtfz0C1DPlR4gS1RMfvUQKBgQD2LZN1DrJ+tE84X7ks/hQ7Z6X1WnLuUNY2IkoNUXyrH8F1fo0pAyB7NP/sAmiAEfXDinHzZ4zzyk7mBzCZgWUI1vewce7eq4p6twIKrlOhVKaK4uuCItJdIulnwuIk3nWf6RsifmwEfflIUoikVMWTJy+56wXwa/C09NghOJEkqQKBgQCTk0aHnWhRpxinvpgVjKgRWtiL4O/YdaTu8vvsBEJMxVIWPJFLCRtd2mEWGs53VTztIZxPWKq4/6a/4uNCPOAJf5oYqQBiSkxEBXUxITE6ZpCQQ8SV2oWOMwClPuDv5/gOByC2l+znqXaI+qhPXaNgDeqJoXxOJnUOYi8+dL3umwKBgQCUxOQoyHH7+hEp20c3tnAZmkCDrl5oQLfDTSN9OvJhI7awv4Kk9zvI2TWTtbzbMH9Z5S+Jzdkzdt4JSu0Gx1h8aBSVL7UQIZB1KmV0ePcXNuXSZuG16j9Ag5mT2AT8DOf+dEuuo8G5UTu1hhMHZCdmDzdbXnlfWovDf3ehU+L6yQKBgAc8KDAL4opmQ8otiO09iRvYtVcePwQAuIZSeVFX4Bw9bEnUzFa+ao4OX8YiqE6naiZCCJTHRw9s0TMFH5kmvOEaVWayGme3Nsf68ITpGK3BS4MXH3MtVHDlRTnUvFhmqf2eEdeB2UZ+Q2Zxy4CgmZri88WtmheGwSRkYeaQ+KLTAoGBANYeJwnYdpDum9midFZN85fTPvoqK7ugudNcWHxXJV33ENRD7njxlu9mclA0eI9+WcUQL31h129+xl3mmjX3AlOUFU9ogVZsf1v8pmmlM5fC/e/cJSzroPz5R4qmPrQPdOQLVwKmfT4X6Pzuk30VGa7Hh73CXwP2BT+SR7e6F9kZ
cashout.sc.merId=860000110600397
cashout.sc.domain=https://api.huiqi365.com/MerApi
cashout.sc.interKey=cbad83569a4441a38040d69291d2453d
cashout.sc.callback.ip=*************

# 薪云提现配置
cashout.xinyun.pubKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgyZkyQ0pPhBPILM0uwb4EL6fexiRE8+NCqGRKPdLRzp8OKz/n70SbbY2JgnvlocNugLeLAiaE0S5zmyawNhtQVCfejaHyITKCIPQjmG0mVZdQDZmWUjU/wGwQYoOFb0uA4hno4ZqgnZwY6UaF2Tyt1qT/h+jZ2jkZb1ilGx+s07TGh7xlyKd4u6Re9cgYC7Il4qRewHsG6Le9mjkd0wTQXDoC6agyHyGPvU/iLUaLvC3T6uyuCWo/OcJRY0nsaK0XfoM99jlstNHfoXogMZHWT29J9R71lAucSqgxV91XapbUKl8jO2G7gBcxyoF5Q3qnWvPBa0syhaM2CdIQelxGQIDAQAB
cashout.xinyun.priKey=MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCTfe2sZOkBtCIOpqIVkp4zwK5cOOckeyCmV1OJt4LnLPFkpFi4zFSWiXO2OgB6aliDK7gBRyVMDXPFs5Qn66e1lEKnGINO4EHFq9n81ZYZcozoZ7DWFGNBS0aEuWQKOlAuJ9WRgBeNekwHCgdrpLD/VE0i6vqkCmodGCaArUFMm61Rtd56axSl0QT3dJd3559rGUikOGvUCmI0+aSN2auMHQxDvqocFGeaN8WAEnrEud2MtuosOFf1AQ8oly84vLSnmeYPSOTJ5fn/xEgUvU6qR5u7Iukcx4hqLrVsRnl8gpF1UAga4A0ftNWdAbeilsD86nHpbpjUp4FQ/iPocoC9AgMBAAECggEAAeiPOrvTiAIcrrPZRQ4wndrQZsEVvJMbSQu4OP/24abjOLonq7x5G7mLlcAKYYseuhPGvb197g3+gYBhWmLiOKdCdYcLq+ma8EPY/jyFRK8Gw7sTvrFk12Nx9iWv9bHrd61sRgWySFJcyXqHvgzTfXpJxTnlJFo+JUI6vU+AmM0l1v1JDJAeE4H2qfmme8IhDxMXAEJbkfiuRau8Ksqpj+voGqQrC8pAtjSnx9krdQgHp1Hf4a1NJwhHsk4mXJNJLEuapF02F3LYEMMFhwPpt9nUlAzzWNGkhJMCS2uZs8SpdhG0vcM8gxFpvpuwI6wHNc586rjb9HJ+jd8a3/dUwQKBgQDPMcD9VzWQ6+i7p3BKTJ6w96Vfp1VtqhzqsaGwp2Jy1NrR8tk7L7V+OgC4Cif7Z81NkJVD0eb7YvICujZ2p+S/4EgeRP5etPa+d3O7dSv0AY3x/iUfcHILbrec/c87edASBRVmpZ68iwCyZ2HtY/0ObI56B1MDew2uHj25asKexwKBgQC2O/6HOJe8mBWEgDlLiUnkbV7BVdXC5QWLVNnQHl3BmKBopCKCQbbpPMiQ7mJBHRQ0aFlwJglDHH6G0gv9x+S2HNIZ6btKtnaf4L2+ubLxrHvy1K5FhC+mk8JOCarT797OT+BsfIHV7EYBZEk5iDXO/bu/+IPU9KKUS5v22MtwWwKBgQCPl3CjYowaCpR5cLouk9kq1gYDO6nF3a46rK5dCjP9Vyzrcb1A+nhyqWoUId3BqxeqWLhJjy/gKpuEbl8Obp7mp13ztGcBZbLXH1vSn4q9iVJBUpuRpVE0QR/5KiEzm+Eqi+FgCQnwqvjpqyI/7hrTo0I+qdLLR72cXRLz7FxCwwKBgQCGsleTKVB2tM8KziALZiG49uJX8xL6aH+tmf0ZKKjSJPdEATYEo4AIa8Yv4hO73zuE9TBOhb7I3THkgzMb1PmHZkwhnTKzqaSKzfpSFD5hBcXkGjHp7ieub37qYZjj6GP+iOnwJiNttKOV+cNqA/UJ12BEJ/OBn4TgKfbRATN2CwKBgQCQu7Z8yJ2SPzU/NXEGgHL9alKm9kVPUPgOkIVhsZVCtnb4kTKmg7eqJXHW+ZidCsjDzBG/EYUV7S1SKMy9TL8o3K0aLbTzrIorECoigVhvOLSbG+QN5bFAkSrVs9fOEWY7H9TWwkwrIcbGIR35XZRz/QLOI9gm+n4nH5kk8OyHbg==
cashout.xinyun.merId=2023111417384919512342
cashout.xinyun.domain=https://lhyg.xinzhanghu.cn


# 玖合云提现配置
cashout.jiuhe.platform.pubKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtQz9xK6B7z72klA/LKZxjC089ZZB4M4DoPjzyM3dK1eVh3R5E96n/czcHvzMA2ZZS7Dn2vUKW/IAjBd55frOBo19EIa2AAU67qOsh7Vam4lxF0Wfxsow0u6PpkIDFBQA+MaOzqidnFVU6Jo4fthtkScvjK20EMYEJGNPJDdLYMTgp+0YKbN+HB6eRcakQPdOqqykzuh5RxNaPP/vpv2lDN6cO6Eb+HHJ6dgINdrS0zO/gmxyGALfexYQpJfjiFWXahcPTSC3gH91shNjyK9831OQNnoLxdtZpC/diAx+aV5xYNDVpvJ7BeoCFyoRg23E9JtNiEboZYpzrts20QtP+wIDAQAB
cashout.jiuhe.client.priKey=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC8EpNkQX0bA7nQRSC7sblMlpVD6kUqS8FnVxlK+zjOYZQuxVTWuoQMVlRu3H5/t5JpT+VUldUUwU+Amzq95Q5b61ZlfEKRC8OBTKSdhY3UXCLI/Sa+hnpmK/8WlwOx0bayiiXhTmKlPS82MZGZl86VQ6syGnEzDYBW2DUc9C9qIkpuBcYBaojowos2xA3QywAOJhGUNyCFY6ppmti4318qNo5wBzy41V5oKxdtk7tpZxU1SYpVkQF2ggabvF2G0lF2hEEo1DEY8UFVvkO2WXUhqG5Fnbu3zosmHO3Fcel6owx5uBjXR6FhJEZvkHBJq7SsLmYzIcGVSUwaT9HFTrEhAgMBAAECggEAfZlOM3sEyKDBW9R+mrqCzCIYRwXR8QAshg4Fxyxk8Hb8FgwUr15aRz70HiFEnZiDv67qsYVqivTaWkHQhfbNkxdkWFwWvHUFuHjtyoDhifuKA34EfOKkJU2miniJsBwkjbFywLevazikKeYKnW9jOA2767b6+CRBHD5Bojd03sQoHBhlYem08pCqA5fi2+gRPW/MltaWPSuK+n5GA0KJ34oVOJrHwt5aLVzcX1djKSor/qvKOogLRbHV3BHzRZ7b9lA9gEuR8Q3s/EmqVgoqSDwoTz6AAROVValBwpRL4uKA9ifxQUchpNEo+ltKQaHe1PP5HKvRcELT2EBG+ffrQQKBgQDpaPB2yECG8qdbrprhwgLFkEDYKy2VDKrzzBpX14bkVDouBchMciOynkzV2RxNkYvXxNS3eC03Xc6FWQmCUEYRvY2aZWlRxfaCsK+Bz0YPKo57LnvLz3atqUYgTGtosJHeLUdHwaGEVsrvfjRaQyuL1Ny3CSOv46HnaSttGMxSyQKBgQDORlbdL7SarmRX8TNXVL0MxZaZiZY474BqEq6289xfJUD9FiQNwImBL1nPN1B0BcO1OZ2svnU3GNdH7XvBWEL5H0gVnohDUtEEP5soK8brMGon5mkDvpsmxrFiE84wxxTuWg1fciUp1TRnRUgmBfY12ESNcJwW2jpYzlcmoWP/mQKBgG8TdXvRiIOv7lz/Fs3g2hjSsUkuIx8PTDuBIfgzpQn720QTlGJs3vgdxUrvazyuyvH/xYsaMcprZeCnopLVSMMghca4BAraPpmrI5qO3TRSeEO+t0Mik8PJDopIU8UaYhBtU8KeLxMfiqu2T4u084Az6GVigA0wMthBgF0GtBWhAoGBAKyPKVGl+/c+lpD/JY0URYausNrGv9Xnfcy+zkwc2yj5OFHAuO6MODvLCd2Qj4xP2jX7+gyt088Ax+/TGqlXFcfkRGBZm/J+EH72Wu0TGjhb6yJv3UAVryOUKoY6vU4O7VKsIO3MxowlYM5XphCxAhRvVIHiR6HSc+cgzsVxU/9ZAoGAFa7ZfuFy9/+gZHw59LkYQ5PTj9bZA/qohg1CI//oyMNBs2QlWGb+JzX3QgnZqqKF8IxdyHXJNXfHzdO5zfAgBf+WrkHiHEfGq/K4Nqf4WG+ld07hi6uAj0ehGbUscRy06Lh2UzNxNiHP7Kh3tG5xwcPFD4hgTrzcVnX7N9kmSnI=
cashout.jiuhe.appId=Md3cf5c793040049
cashout.jiuhe.domain=https://test.jiuhedigital.com/api/
cashout.jiuhe.projectId=9264d096
cashout.jiuhe.fundId=1563126d


# 新app的礼物价值换算比例
goods.sizhibo.cost.factor = 1
goods.sizhibo.riceroll.factor = 1

# 粉丝团礼物固定ID
goods.fans.group.gift.ids = 718,719,720

# OSS配置
ali.oss.img.url=http://img.devel.easyvaas.com
ali.oss.app=test

system.zookeeper.hosts = dev001:2181,dev002:2181,dev003:2181

# 应用id
app.id = pay
apollo.meta = http://el-dev002:10102
apollo.cacheDir= /zebra/data/apollo
apollo.cluster = dev001-bj

# gash 支付配置
pay.gash.recharge.return_path = /appgw/pay/recharge/gash/return
pay.gash.recharge.scale = 0.2

# gash 第三方配置
pay.gash.pcode = 300000
pay.gash.key = F2U2rC4DapOf44dEUC0je4DspFY1wPQb
pay.gash.iv = azHt/xMSZu4=
pay.gash.pwd = 1Q1Q6Y6Y
pay.gash.cid = C009350001457
pay.gash.mid = M1000935

# 连连支付
pay.third.lianlian.notify-url = /notify/pay/lianlian
pay.third.huifu.notify-url = /notify/pay/huifu
pay.third.huifu.cash.app-id=202312221109
pay.third.huifu.cash.member.prefix=test



#资产系统配置
asset.signature.name = 54914578
asset.signature.key = X6X14J7bbq9byV2mJYj8aK4bB6r8R74xCziou1ynjhon1Xg3xKHlN79rDQ3Sw4mF

# 云账户
cashout.yzh.key.root-path=/zebra/data/pay/yzh/
cashout.yzh.notify-url=/notify/pay/yzh
