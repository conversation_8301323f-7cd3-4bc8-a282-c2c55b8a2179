package com.easylive.pay.model;


import com.easylive.pay.config.CacheConfig;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

import java.util.HashMap;

/**
 * Created by c on 2015/12/12.
 */
@Repository
public class UserCache {

    private static final String prefix_key = "uidbaseinfo_";

    @Autowired
    private StringRedisTemplate template;

    @Autowired
    private CacheConfig cacheConfig;

    public User getUserByUid(long uid) {
        ValueOperations<String, String> ops = this.template.opsForValue();
        String key = cacheConfig.getPrefix() + prefix_key + ((Long) uid).toString();
        String value = "";
        if (this.template.hasKey(key)) {
            value = ops.get(key);
        } else {
            return null;
        }

        return this.getUserByJson(uid, value);
    }

    private User getUserByJson(long uid, String value) {
        HashMap jsonValue = new HashMap<>();
        GsonBuilder builder = new GsonBuilder();
        Gson gson = builder.create();
        jsonValue = gson.fromJson(value, jsonValue.getClass());

        User user = new User();
        user.setId(uid);
        user.setLogoUrl((String) jsonValue.get("logourl"));
        user.setName((String) jsonValue.get("name"));
        user.setNickname((String) jsonValue.get("nickname"));
        user.setGender((String) jsonValue.get("gender"));
        user.setCid((String) jsonValue.get("cid"));
        Double appSourceId = (Double) jsonValue.get("app_source_id");
        user.setAppSourceId(appSourceId == null ? null : appSourceId.longValue());
        return user;
    }


}
