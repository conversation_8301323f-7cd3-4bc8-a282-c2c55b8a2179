package com.easylive.pay.model;

import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.PayPlatform;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/12/19
 */

@Data
public class OrderInfo {
    private int appId;
    private long uid;
    private long rmb;
    private PayPlatform payPlatform;
    private PayPlatform realPayPlatform;
    private MobilePlatform mobilePlatform;
    private String clientIp;

    private String productId;
    private String mchId;
    private String merchantCurrency;

    private Long amount;
    private String currency;


    public OrderInfo(int appId, long uid, long rmb, PayPlatform payPlatform, MobilePlatform mobilePlatform, String clientIp) {
        this.appId = appId;
        this.uid = uid;
        this.rmb = rmb;
        this.payPlatform = payPlatform;
        this.realPayPlatform = payPlatform;
        this.mobilePlatform = mobilePlatform;
        this.clientIp = clientIp;
    }

    public OrderInfo(int appId, long uid, String productId, PayPlatform payPlatform, MobilePlatform mobilePlatform, String clientIp) {
        this.appId = appId;
        this.uid = uid;
        this.payPlatform = payPlatform;
        this.realPayPlatform = payPlatform;
        this.mobilePlatform = mobilePlatform;
        this.clientIp = clientIp;
        this.productId = productId;
    }
}
