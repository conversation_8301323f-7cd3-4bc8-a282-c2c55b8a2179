package com.easylive.pay.model;

import com.easylive.pay.entity.RechargeOption;
import lombok.Data;

@Data
public class RechargeOptionItem {
    private long id;

    private long rmb;
    private long ecoin;
    private long free;
    private long active;
//    private long total;
//    private long donate;
    private long platform;
    private int device;
    private int tagType;
    private String productid;
    private String currency;
    private Long lotteryTime = 0L;

    public RechargeOptionItem(RechargeOption option) {
        setActive(option.getActive());
        setCurrency(option.getCurrency());
        setDevice(option.getDevice());
        setEcoin(option.getEcoin());
        setFree(option.getFree());
        setId(option.getId());
        setPlatform(option.getPlatform());
        setProductid(option.getProductId());
        setRmb(option.getRmb());
        setTagType(option.getTagType());
//        setDonate(option.getFree());
//        setTotal( option.getEcoin() + option.getFree() );
    }
}
