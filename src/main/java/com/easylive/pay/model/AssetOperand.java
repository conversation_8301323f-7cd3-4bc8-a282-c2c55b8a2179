package com.easylive.pay.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * <AUTHOR>
 * @date 2019-03-15
 */
public class AssetOperand {
    private long uid;
    private long value;

    @JsonIgnore
    private User user;

    public AssetOperand() {

    }

    public AssetOperand(User user, long value) {
        this.user = user;
        this.uid = user.getId();
        this.value = value;
    }

    public AssetOperand(long uid, long value) {
        this.uid = uid;
        this.value = value;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
