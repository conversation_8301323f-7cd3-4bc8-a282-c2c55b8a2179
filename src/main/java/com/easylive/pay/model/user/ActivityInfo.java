package com.easylive.pay.model.user;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
public class ActivityInfo {
    private int watchTime;
    private int liveTime;
    private int commentCount;
    private int costEcoin;
    private int recvRiceroll;
    private int sentGift;
    private int recvGift;

    public int getWatchTime() {
        return watchTime;
    }

    public void setWatchTime(int watchTime) {
        this.watchTime = watchTime;
    }

    public int getLiveTime() {
        return liveTime;
    }

    public void setLiveTime(int liveTime) {
        this.liveTime = liveTime;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public int getCostEcoin() {
        return costEcoin;
    }

    public void setCostEcoin(int costEcoin) {
        this.costEcoin = costEcoin;
    }

    public int getRecvRiceroll() {
        return recvRiceroll;
    }

    public void setRecvRiceroll(int recvRiceroll) {
        this.recvRiceroll = recvRiceroll;
    }

    public int getSentGift() {
        return sentGift;
    }

    public void setSentGift(int sentGift) {
        this.sentGift = sentGift;
    }

    public int getRecvGift() {
        return recvGift;
    }

    public void setRecvGift(int recvGift) {
        this.recvGift = recvGift;
    }
}
