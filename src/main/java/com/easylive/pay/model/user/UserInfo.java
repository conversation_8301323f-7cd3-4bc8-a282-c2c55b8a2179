package com.easylive.pay.model.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-08
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserInfo {

    private long uid;
    private String name;

    private EditInfo edit;
    private AttributeInfo attr;
    private StateInfo state;
    private LevelInfo level;

    private Edit2Info edit2;
    private RegisterInfo reg;
    private TopicInfo topic;

    private NobleInfo noble;
    private SocialInfo social;
    private GuildInfo guild;
    private List<AuthInfo> auth;
    private IdentityInfo identity;
    private TitleInfo title;
    private List<ImageInfo> image;
    private RoomImageInfo roomImage;
    private List<TagInfo> tag;
    private Certification cert;
    private AnchorInfo anchor;

    private SettingInfo setting;
    private ListingInfo listing;

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public EditInfo getEdit() {
        return edit;
    }

    public void setEdit(EditInfo edit) {
        this.edit = edit;
    }

    public Edit2Info getEdit2() {
        return edit2;
    }

    public void setEdit2(Edit2Info edit2) {
        this.edit2 = edit2;
    }

    public AttributeInfo getAttr() {
        return attr;
    }

    public void setAttr(AttributeInfo attr) {
        this.attr = attr;
    }

    public RegisterInfo getReg() {
        return reg;
    }

    public void setReg(RegisterInfo reg) {
        this.reg = reg;
    }

    public StateInfo getState() {
        return state;
    }

    public void setState(StateInfo state) {
        this.state = state;
    }

    public GuildInfo getGuild() {
        return guild;
    }

    public void setGuild(GuildInfo guild) {
        this.guild = guild;
    }

    public LevelInfo getLevel() {
        return level;
    }

    public void setLevel(LevelInfo level) {
        this.level = level;
    }

    public TopicInfo getTopic() {
        return topic;
    }

    public void setTopic(TopicInfo topic) {
        this.topic = topic;
    }

    public NobleInfo getNoble() {
        return noble;
    }

    public void setNoble(NobleInfo noble) {
        this.noble = noble;
    }

    public SocialInfo getSocial() {
        return social;
    }

    public void setSocial(SocialInfo social) {
        this.social = social;
    }

    public List<AuthInfo> getAuth() {
        return auth;
    }

    public void setAuth(List<AuthInfo> auth) {
        this.auth = auth;
    }


    public IdentityInfo getIdentity() {
        return identity;
    }

    public void setIdentity(IdentityInfo identity) {
        this.identity = identity;
    }

    public List<TagInfo> getTag() {
        return tag;
    }

    public void setTag(List<TagInfo> tag) {
        this.tag = tag;
    }

    public TitleInfo getTitle() {
        return title;
    }

    public void setTitle(TitleInfo title) {
        this.title = title;
    }

    public List<ImageInfo> getImage() {
        return image;
    }

    public void setImage(List<ImageInfo> image) {
        this.image = image;
    }

    public RoomImageInfo getRoomImage() {
        return roomImage;
    }

    public void setRoomImage(RoomImageInfo roomImage) {
        this.roomImage = roomImage;
    }

    public Certification getCert() {
        return cert;
    }

    public void setCert(Certification cert) {
        this.cert = cert;
    }

    public SettingInfo getSetting() {
        return setting;
    }

    public void setSetting(SettingInfo setting) {
        this.setting = setting;
    }

    public ListingInfo getListing() { return listing; }

    public void setListing(ListingInfo listing) { this.listing = listing; }

    public static class EditInfo {
        private String nickname;
        private String logoUrl;
        private String gender;
        private String location;
        private String gpsLocation = "";

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getLogoUrl() {
            return logoUrl;
        }

        public void setLogoUrl(String logoUrl) {
            this.logoUrl = logoUrl;
        }

        public String getGender() {
            return gender;
        }

        public void setGender(String gender) {
            this.gender = gender;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getGpsLocation() {
            return gpsLocation;
        }

        public void setGpsLocation(String gpsLocation) {
            this.gpsLocation = gpsLocation;
        }

        @Override
        public String toString() {
            return "EditInfo{" +
                    "nickname='" + nickname + '\'' +
                    ", logoUrl='" + logoUrl + '\'' +
                    ", gender='" + gender + '\'' +
                    ", location='" + location + '\'' +
                    ", gpsLocation='" + gpsLocation + '\'' +
                    '}';
        }
    }

    public static class Edit2Info {

        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date birthday;
        private String signature;

        public Date getBirthday() {
            return birthday;
        }

        public void setBirthday(Date birthday) {
            this.birthday = birthday;
        }

        public String getSignature() {
            return signature;
        }

        public void setSignature(String signature) {
            this.signature = signature;
        }

        @Override
        public String toString() {
            return "Edit2Info{" +
                    "birthday=" + birthday +
                    ", signature='" + signature + '\'' +
                    '}';
        }
    }

    public static class AttributeInfo {
        private String cid;
        private int appSourceId;
        private int appId;

        public String getCid() {
            return cid;
        }

        public void setCid(String cid) {
            this.cid = cid;
        }

        public int getAppSourceId() {
            return appSourceId;
        }

        public void setAppSourceId(int appSourceId) {
            this.appSourceId = appSourceId;
        }

        public int getAppId() {
            return appId;
        }

        public void setAppId(int appId) {
            this.appId = appId;
        }
    }

    public static class RegisterInfo {
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
        private Date registerTime;
        private String registerIp;
        private String type;

        public Date getRegisterTime() {
            return registerTime;
        }

        public void setRegisterTime(Date registerTime) {
            this.registerTime = registerTime;
        }

        public String getRegisterIp() {
            return registerIp;
        }

        public void setRegisterIp(String registerIp) {
            this.registerIp = registerIp;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        @Override
        public String toString() {
            return "RegisterInfo{" +
                    "registerTime=" + registerTime +
                    ", registerIp='" + registerIp + '\'' +
                    ", type='" + type + '\'' +
                    '}';
        }
    }

    public static class StateInfo {
        private boolean freeze;
        private boolean shutup;

        public boolean isFreeze() {
            return freeze;
        }

        public void setFreeze(boolean freeze) {
            this.freeze = freeze;
        }

        public boolean isShutup() {
            return shutup;
        }

        public void setShutup(boolean shutup) {
            this.shutup = shutup;
        }
    }


    public static class GuildInfo {
        private String name = "";
        private int type = 0;
        private String level = "";

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }
    }

    public static class LevelInfo {
        private long exp;
        private int level;
        private int vipLevel;
        private int anchorLevel;

        public long getExp() {
            return exp;
        }

        public void setExp(long exp) {
            this.exp = exp;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public int getVipLevel() {
            return vipLevel;
        }

        public void setVipLevel(int vipLevel) {
            this.vipLevel = vipLevel;
        }

        public int getAnchorLevel() {
            return anchorLevel;
        }

        public void setAnchorLevel(int anchorLevel) {
            this.anchorLevel = anchorLevel;
        }
    }

    public static class TopicInfo {
        private int topicId;
        private int highTopicId;

        public int getTopicId() {
            return topicId;
        }

        public void setTopicId(int topicId) {
            this.topicId = topicId;
        }

        public int getHighTopicId() {
            return highTopicId;
        }

        public void setHighTopicId(int highTopicId) {
            this.highTopicId = highTopicId;
        }
    }

    public static class SocialInfo {
        private long fansCount;
        private long followerCount;

        public long getFansCount() {
            return fansCount;
        }

        public void setFansCount(long fansCount) {
            this.fansCount = fansCount;
        }

        public long getFollowerCount() {
            return followerCount;
        }

        public void setFollowerCount(long followerCount) {
            this.followerCount = followerCount;
        }
    }

    public static class ImageInfo {
        private long id;
        private String url;
        private int type;
        private int status;
        private long watchCount;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public long getWatchCount() {
            return watchCount;
        }

        public void setWatchCount(long watchCount) {
            this.watchCount = watchCount;
        }
    }

    public static class RoomImageInfo {
        private int imageId;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
        private Date expiredTime;

        public int getImageId() {
            return imageId;
        }

        public void setImageId(int imageId) {
            this.imageId = imageId;
        }

        public Date getExpiredTime() {
            return expiredTime;
        }

        public void setExpiredTime(Date expiredTime) {
            this.expiredTime = expiredTime;
        }
    }

    public static class TagInfo {
        private int id;
        private String name;

        public long getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class TitleInfo {

        private String name = "";
        private int color = 0;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getColor() {
            return color;
        }

        public void setColor(int color) {
            this.color = color;
        }
    }

    public static class SettingInfo {

        /**
         * 夜间推送消息
         */
        private boolean nightNotification = false;

        /**
         * 直播消息提醒
         */
        private boolean liveNotification = true;

        /**
         * 关注收到提醒
         */
        private boolean followNotification = true;

        public boolean isNightNotification() {
            return nightNotification;
        }

        public void setNightNotification(boolean nightNotification) {
            this.nightNotification = nightNotification;
        }

        public boolean isLiveNotification() {
            return liveNotification;
        }

        public void setLiveNotification(boolean liveNotification) {
            this.liveNotification = liveNotification;
        }

        public boolean isFollowNotification() {
            return followNotification;
        }

        public void setFollowNotification(boolean followNotification) {
            this.followNotification = followNotification;
        }
    }

    public static class AuthInfo {
        private String authType;
        private String token;

        public String getAuthType() {
            return authType;
        }

        public void setAuthType(String authType) {
            this.authType = authType;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }
    }

    public static class AnchorInfo {
        private Integer attribute;
        public Integer getAttribute() {
            return attribute;
        }

        public void setAttribute(Integer attribute) {
            this.attribute = attribute;
        }
    }


    public AnchorInfo getAnchor() {
        return anchor;
    }

    public void setAnchor(AnchorInfo anchor) {
        this.anchor = anchor;
    }

    public static class ListingInfo {
        private boolean black;
        private boolean white;
        private boolean certWhite;

        @Override
        public String toString() {
            return "ListingInfo{" +
                    "black=" + black +
                    ", white=" + white +
                    ", certWhite=" + certWhite +
                    '}';
        }

        public boolean isBlack() {
            return black;
        }

        public void setBlack(boolean black) {
            this.black = black;
        }

        public boolean isWhite() {
            return white;
        }

        public void setWhite(boolean white) {
            this.white = white;
        }

        public boolean isCertWhite() {
            return certWhite;
        }

        public void setCertWhite(boolean certWhite) {
            this.certWhite = certWhite;
        }
    }
}
