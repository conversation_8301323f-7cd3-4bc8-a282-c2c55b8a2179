package com.easylive.pay.model.user;

import java.util.Date;

public class NobleInfo {
    private int level;
    private boolean liveStealth;
    private boolean listStealth;
    private boolean continued;
    private Date endTime;

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public boolean isLiveStealth() {
        return liveStealth;
    }

    public void setLiveStealth(boolean liveStealth) {
        this.liveStealth = liveStealth;
    }

    public boolean isListStealth() {
        return listStealth;
    }

    public void setListStealth(boolean listStealth) {
        this.listStealth = listStealth;
    }

    public boolean isContinued() {
        return continued;
    }

    public void setContinued(boolean continued) {
        this.continued = continued;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
