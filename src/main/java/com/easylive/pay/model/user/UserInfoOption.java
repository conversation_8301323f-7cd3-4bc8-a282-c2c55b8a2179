package com.easylive.pay.model.user;

import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 用户信息获取选项
 * <AUTHOR>
 * @date 2019-08-09
 */
public enum UserInfoOption {
    /**
     * 常见可编辑属性
     */
    EDIT('E'),

    /**
     * AppID等属性
     */
    ATTR('A'),

    /**
     * 用户状态
     */
    STATE('S'),

    /**
     * 等级属性
     */
    LEVEL('L'),

    EDIT2('D'),

    REG('R'),

    TOPIC('T'),

    /**
     * 公会属性
     */
    GUILD('G'),

    /**
     * 标签属性
     */
    TAG('B'),

    /**
     * 称号属性
     */
    TITLE('I'),

    NOBLE('N'),

    SOCIAL('O'),

    AUTH('U'),

    IDENTITY('Y'),

    CERT('C'),

    IMAGE('M'),

    ROOM_IMAGE('F'),

    SETTING('I'),

    /**
     * 主播信息
     */
    ANCHOR('H'),

    /**
     * 用户黑白名单
     */
    LISTING('K'),

    SECURITY('Q'),

    ;

    private char abbr;
    UserInfoOption(char abbr) {
        this.abbr = abbr;
    }

    public static final Set<UserInfoOption> BASE = EnumSet.of(EDIT, ATTR, STATE, LEVEL );

    public static final Set<UserInfoOption> ALL_REG = EnumSet.of( EDIT, EDIT2, REG);

    public static final Set<UserInfoOption> EXTEND = EnumSet.of(EDIT2, REG, TOPIC, ANCHOR);

    public static final Set<UserInfoOption> CENTER = EnumSet.of(TITLE, IMAGE, GUILD, CERT);

    public static final Set<UserInfoOption> ROOM = EnumSet.of(TITLE, ROOM_IMAGE);

    public static final Set<UserInfoOption> ALL = EnumSet.allOf(UserInfoOption.class);

    private static Map<String,Set<UserInfoOption>> optionSetMap = new HashMap<>();
    static {
        optionSetMap.put("BASE", BASE);
        optionSetMap.put("ALL_REG",ALL_REG);
        optionSetMap.put("EXTEND", EXTEND);
        optionSetMap.put("CENTER", CENTER);
        optionSetMap.put("ROOM", ROOM);
        optionSetMap.put("ALL",ALL);
    }

    private static final String OPTION_SEPARATOR = ",";
    public static Set<UserInfoOption> parse(String option) {
        Set<UserInfoOption> options = new HashSet<>();
        if(StringUtils.isEmpty(option))
            return options;

        for( String s : option.split(OPTION_SEPARATOR) ) {
            try {
               options.add( UserInfoOption.valueOf(s.toUpperCase()) );
            } catch (IllegalArgumentException ignored) {
                Set<UserInfoOption> optionSet = optionSetMap.get(s);
                if( optionSet != null)
                    options.addAll(optionSet);
            }
        }
        return options;
    }

    public static String buildOptionString(Set<UserInfoOption> options) {
        if( options.isEmpty() )
            return "";

        StringBuilder sb = new StringBuilder();
        for( UserInfoOption option : options) {
            sb.append(option.name()).append(",");
        }

        sb.deleteCharAt( sb.length() -1 );
        return sb.toString();
    }

    public static String buildAbbrOptionString(Set<UserInfoOption> options) {
        if( options.isEmpty() )
            return "";

        StringBuilder sb = new StringBuilder();
        for( UserInfoOption option : options) {
            sb.append(option.abbr);
        }
        return sb.toString();
    }
}
