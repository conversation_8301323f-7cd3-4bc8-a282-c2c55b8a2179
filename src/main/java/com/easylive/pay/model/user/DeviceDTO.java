package com.easylive.pay.model.user;

/**
 * <AUTHOR>
 * @date 2020/3/23
 */
public class DeviceDTO {
    private String userAgent;
    private String appType;
    private String clientIp;
    private String pushId;
    private String traceId;
    private Double gpsLongitude;
    private Double gpsLatitude;

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }


    public Double getGpsLongitude() {
        return gpsLongitude;
    }

    public void setGpsLongitude(Double gpsLongitude) {
        this.gpsLongitude = gpsLongitude;
    }

    public Double getGpsLatitude() {
        return gpsLatitude;
    }

    public void setGpsLatitude(Double gpsLatitude) {
        this.gpsLatitude = gpsLatitude;
    }


    @Override
    public String toString() {
        return "DeviceDTO{" +
                "userAgent='" + userAgent + '\'' +
                ", appType='" + appType + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", pushId='" + pushId + '\'' +
                ", traceId='" + traceId + '\'' +
                ", gpsLongitude='" + gpsLongitude + '\'' +
                ", gpsLatitude='" + gpsLatitude + '\'' +
                '}';
    }

}
