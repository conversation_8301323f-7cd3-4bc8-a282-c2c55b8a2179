package com.easylive.pay.model;

public class UserBeanUpdate {

    private String unames;

    /**
     * type:
     * 1:抽水，2:中大奖
     */
    private int type;

    private long bean;

    private long timestamp;

    private long gameid;

    private String sign;

    public UserBeanUpdate() {
    }

    public long getGameid() {
        return gameid;
    }

    public void setGameid(long gameid) {
        this.gameid = gameid;
    }

    public String getUnames() {
        return unames;
    }

    public void setUnames(String unames) {
        this.unames = unames;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getBean() {
        return bean;
    }

    public void setBean(long bean) {
        this.bean = bean;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    @Override
    public String toString() {
        return "UserBeanUpdate{" +
                "unames='" + unames + '\'' +
                ", type=" + type +
                ", bean=" + bean +
                ", timestamp=" + timestamp +
                ", gameid=" + gameid +
                ", sign='" + sign + '\'' +
                '}';
    }
}
