package com.easylive.pay.model;

import com.easylive.pay.model.event.AssetTransactionRecord;

import java.util.List;


public class CenterAssetTransaction {

    private int bizId;
    private int bizType;
    private String tid;
    private List<AssetTransactionRecord> records;

    public int getBizId() {
        return bizId;
    }

    public void setBizId(int bizId) {
        this.bizId = bizId;
    }

    public int getBizType() {
        return bizType;
    }

    public void setBizType(int bizType) {
        this.bizType = bizType;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public List<AssetTransactionRecord> getRecords() {
        return records;
    }

    public void setRecords(List<AssetTransactionRecord> records) {
        this.records = records;
    }

    public AssetTransactionRecord getRecord(int index) {
        return this.records.get(index);
    }


    @Override
    public String toString() {
        return "CenterAssetTransaction{" +
                "bizId=" + bizId +
                ", bizType=" + bizType +
                ", tid='" + tid + '\'' +
                ", records=" + records +
                '}';
    }
}
