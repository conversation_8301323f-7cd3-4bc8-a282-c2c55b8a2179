package com.easylive.pay.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/4
 */
@Data
public class RechargeSearchResultItem {
    private long uid;
    private String name;
    private String nickname;
    private String cid;
    private Date registerTime;
    private String phone;
    private int appId;
    private long rmb;
    private long ecoin;
    private long amount;
    private String currency;
    private String mcurrency;
    private int status;
    private int primaryPlatform;
    private int subPlatform;
    private int clientPlatform;
    private String orderId;
    private String platformOrderId;
    private String tradeNo;
    private Date commitTime;
    private Date completeTime;

}
