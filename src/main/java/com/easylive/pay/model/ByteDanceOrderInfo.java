package com.easylive.pay.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
@Data
public class ByteDanceOrderInfo {
    @JsonProperty(value = "app_id")
    private String appId;

    @JsonProperty(value = "merchant_id")
    private String merchantId;

    private String timestamp;

    @JsonProperty(value = "out_order_no")
    private String outOrderNo;
    private String uid;

    @JsonProperty(value = "total_amount")
    private String totalAmount;

    @JsonProperty(value = "alipay_url")
    private String alipayUrl;

    @JsonProperty(value = "wx_url")
    private String wxUrl;

    @JsonProperty(value = "risk_info")
    private String riskInfo;

    private String subject;
    private String body;

    @JsonProperty(value = "trade_time")
    private String tradeTime;
    private String sign;
    private String currency = "CNY";

    @JsonProperty(value = "valid_time")
    private String validTime = "300";

    @JsonProperty(value = "notify_url")
    private String notifyUrl = "https://tp-pay.snssdk.com/paycallback";

    @JsonProperty(value = "sign_type")
    private String signType = "MD5";

    private String version = "2.0";

    @JsonProperty(value = "trade_type")
    private String tradeType = "H5";

    @JsonProperty(value = "product_code")
    private String productCode = "pay";

    @JsonProperty(value = "payment_type")
    private String paymentType = "direct";

    @JsonProperty(value = "wx_type")
    private String wxType = "MWEB";
}
