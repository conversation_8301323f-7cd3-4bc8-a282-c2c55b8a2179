package com.easylive.pay.model.cashout.jiuhe;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 提现批次参数
 *
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JiuHeWithdrawLotDTO {

    @ApiModelProperty(value = "代发批次 ID，本值非空时指先提交后支付")
    private String batchId;

    @ApiModelProperty(value = "分包任务 ID，本值与 batchId 为二选一")
    private String projectId;

    @ApiModelProperty(value = "用于支付该批次的商户账户ID，众包模式下，账户必须为代征子户，撮合模式下，账户必须为商户自有账户，如商户的企业支付宝", required = true)
    private String fundId;

    @ApiModelProperty(value = "外部商户的批次号，客户端需保证唯一",required = true)
    private String outBatchNo;

    @ApiModelProperty(value = "默认：服务费，测试环境下：填“FAIL”则本批次支付返回异步失败，否则为成功")
    private String note;

    @ApiModelProperty(value = "代发订单列表，最多支持2000笔若 batchId 为空，则必填，若 batchId 非空，则不可填值。")
    private List<JiuHeWithdrawOrderDTO> orders;

    public JiuHeWithdrawLotDTO(String projectId, String fundId, String outBatchNo){
        this.projectId = projectId;
        this.fundId = fundId;
        this.outBatchNo = outBatchNo;
        this.note = "服务费";
    }
}
