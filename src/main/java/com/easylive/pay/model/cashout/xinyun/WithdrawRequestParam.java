package com.easylive.pay.model.cashout.xinyun;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 提现参数
 *
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WithdrawRequestParam {

    @ApiModelProperty(value = "企业标识", required = true)
    private String enterprise_unique_no;

    @ApiModelProperty(value = "总笔数 非空，大于0", required = true)
    private Integer ttlcnt;

    @ApiModelProperty(value = "总金额 非空，大于0，13位整数，2位小数", required = true)
    private String ttlamt;

    @ApiModelProperty(value = "钱包唯一编号（注意：接口不支持线下付款方式，钱包所属税地必须与项目绑定的税地一致）", required = true)
    private String enterprise_management_unique_no;

    @ApiModelProperty(value = "项目唯一编号", required = true)
    private String task_unique_no;

    @ApiModelProperty(value = "个人收款账户类型(1:个人银行卡（默认为1） 2个人支付宝余额)")
    private Integer fk_type;

    @ApiModelProperty(value = "付款备注 （注意：备注内容不得出现工资、薪金、报酬、补贴、奖金、年终奖、津贴、分红等字样）")
    private String remark;

    @ApiModelProperty(value = "支付参数（一次最多支持500笔支付数据）", required = true)
    private String data;

    @ApiModelProperty(value = "随机字符串", required = true)
    private String nonce_str;

    @ApiModelProperty(value = "签名", required = true)
    private String sign;
    public Map<String, String> toHashMap() {
        Map<String, String> map = new HashMap<>();
        map.put("enterprise_unique_no", enterprise_unique_no);
        map.put("ttlcnt", String.valueOf(ttlcnt));
        map.put("ttlamt", ttlamt);
        map.put("enterprise_management_unique_no", enterprise_management_unique_no);
        map.put("task_unique_no", task_unique_no);
        map.put("fk_type", String.valueOf(fk_type));
        map.put("remark", remark);
        map.put("data", data);
        map.put("nonce_str", nonce_str);
        return map;
    }
}
