package com.easylive.pay.model.cashout.jiuhe;

import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.enums.WithdrawChannelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提现订单参数
 *
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JiuHeWithdrawOrderDTO {

    @ApiModelProperty(value = "外部商户的订单号，客户端需保证唯一", required = true)
    private String outOrderNo;

    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;

    @ApiModelProperty(value = "自由职业者身份证号", required = true)
    private String idNo;

    @ApiModelProperty(value = "银行账户名，必须与注册时的用户姓名一致",required = true)
    private String accountName;

    @ApiModelProperty(value = "账号类型，支持“CARD”（银行卡），可选 ALIPAY”（支付宝），默认为“CARD”", required = true)
    private String accountType;

    @ApiModelProperty(value = "收款的银行卡或支付宝账号", required = true)
    private String accountNo;

    @ApiModelProperty(value = "发放金额,测试环境下：金额填 9.99 则本笔代发返回异步失败，否则为成功", required = true)
    private Double amount;

    public JiuHeWithdrawOrderDTO (CashoutRecordQueue recordQueue){
        this.outOrderNo = recordQueue.getOrderid();
        this.mobile = recordQueue.getMobile();
        this.idNo = recordQueue.getId_no();
        this.accountName = recordQueue.getAccount_name();
        this.accountType = WithdrawChannelTypeEnum.fromValue(recordQueue.getWithdraw_type()).name();
        this.accountNo = recordQueue.getWithdraw_type() == WithdrawChannelTypeEnum.CARD.getCode() ? recordQueue.getAccount_no() : recordQueue.getZfb_account();
    }

}
