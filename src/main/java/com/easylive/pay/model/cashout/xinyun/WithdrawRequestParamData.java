package com.easylive.pay.model.cashout.xinyun;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提现参数
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawRequestParamData {

    @ApiModelProperty(value = "交易序号,需要客户自行保证批次范围内的序号唯一性，代发代扣系统要求格式为全数字，如‘********’,’********’,不允许'********'", required = true)
    private String trxseq;

    @ApiModelProperty(value = "收款人身份证", required = true)
    private String identity_card;

    @ApiModelProperty(value = "姓名", required = true)
    private String real_name;

    @ApiModelProperty(value = "用户银行卡所属银行名称")
    private String bank_name;

    @ApiModelProperty(value = "用户银行卡卡号/支付宝账户（注意：参数fk_type=1时，必须传银卡卡号）")
    private String bank_account;

    @ApiModelProperty(value = "支付宝账户（注意：参数fk_type=2时，必须传个人支付宝收款账户）")
    private String zfbzh;

    @ApiModelProperty(value = "交易金额 非空，大于0，13位整数，2位小数", required = true)
    private String trsamt;

    public WithdrawRequestParamData(String trxseq, String identityCard, String realName, String trsamt){
        this.trxseq = trxseq;
        this.identity_card = identityCard;
        this.real_name = realName;
        this.trsamt = trsamt;
    }
}
