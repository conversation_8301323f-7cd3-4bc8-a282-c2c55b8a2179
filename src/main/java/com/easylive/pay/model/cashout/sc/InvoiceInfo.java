package com.easylive.pay.model.cashout.sc;

import java.math.BigDecimal;

/**
 * @auther jingHu
 * @date 2020/8/15
 **/
public class InvoiceInfo {

    private String batchNo;//商户系统下，票务中心-发票申请-充值流水号
    private String result;//发票申请结果 0：受理成功  2：受理失败
    private BigDecimal amount;//申请发票金额


    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
