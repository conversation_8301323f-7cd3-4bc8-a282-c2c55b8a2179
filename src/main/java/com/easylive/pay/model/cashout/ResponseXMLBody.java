package com.easylive.pay.model.cashout;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户工资提取请求参数
 *
 * <AUTHOR>
 * @date 2019/8/12
 */
@Data
@NoArgsConstructor
@XStreamAlias("body")
public class ResponseXMLBody {

    /**
     * 请求参数格式如下(除了txSts，其他都是非必须)：
     * <body>
     * <txSts>0</txSts>
     * <oriTxsts>0</oriTxsts>
     * <employeeNum>1000520203920005345</employeeNum>
     * <acctountNo>6216520203920005345</acctountNo>
     * <txAmt>100.00</txAmt>
     * <acctName>李三一</acctName>
     * <bankNo>************</bankNo>
     * <bankName>淇县中原村镇银行鹤淇支行</bankName>
     * </body>
     */

    //交易状态
    private String txSts;
    //原交易状态
    private String oriTxsts;
    //员工编号
    private String employeeNum;
    //客户银行账号
    private String acctountNo;
    //交易金额
    private String txAmt;
    //银行账号户名
    private String acctName;
    //收款银行行号
    private String bankNo;
    //收款银行行名
    private String bankName;

}
