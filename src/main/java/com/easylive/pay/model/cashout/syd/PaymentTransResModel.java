package com.easylive.pay.model.cashout.syd;

import java.io.Serializable;
import java.util.List;


/**
 * 商户批量代付接口响应实体类
 * <AUTHOR>
 *
 */
public class PaymentTransResModel implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	public static final String CODE_0000 = "0000";
	public static final String MSG_0000 = "受理成功";
	
	public static final String CODE_0001 = "0001";
	public static final String MSG_0001 = "参数错误";
	
	public static final String CODE_0002 = "0002";
	public static final String MSG_0002 = "结算信息在黑名单不允许做结算";
	
	public static final String CODE_0003 = "0003";
	public static final String MSG_0003 = "结算信息风控验证未通过";
	
	public static final String CODE_0004 = "0004";
	public static final String MSG_0004 = "商户限额验证未通过";
	
	public static final String CODE_0005 = "0005";
	public static final String MSG_0005 = "计算商户手续费出错或商户手续费率不存在";
	
	public static final String CODE_0006 = "0006";
	public static final String MSG_0006 = "结算用户未在平台签约";

	
	private int successNum;//成功数
	
	private int failureNum;//失败数
	
	private List<PayResultList> payResultList;//返回数据集合
	
	 // 商户批次号
    private String merBatchId;
	
	public static class PayResultList{
		
		private String merOrderId;//商户订单号
		
		private String payItemId;//订单流水号
		
		private long amt;//代付金额
		
		private int splitFlag;//订单拆分标识 0：未拆分，1：已拆分
		
		private String resCode = CODE_0000;//响应码
		
		private String resMsg = MSG_0000;//响应信息

		public String getMerOrderId() {
			return merOrderId;
		}

		public void setMerOrderId(String merOrderId) {
			this.merOrderId = merOrderId;
		}

		public String getPayItemId() {
			return payItemId;
		}

		public void setPayItemId(String payItemId) {
			this.payItemId = payItemId;
		}

		public long getAmt() {
			return amt;
		}

		public void setAmt(long amt) {
			this.amt = amt;
		}

		public int getSplitFlag() {
			return splitFlag;
		}

		public void setSplitFlag(int splitFlag) {
			this.splitFlag = splitFlag;
		}

		public String getResCode() {
			return resCode;
		}

		public void setResCode(String resCode) {
			this.resCode = resCode;
		}

		public String getResMsg() {
			return resMsg;
		}

		public void setResMsg(String resMsg) {
			this.resMsg = resMsg;
		}
		
	}

	public int getSuccessNum() {
		return successNum;
	}

	public void setSuccessNum(int successNum) {
		this.successNum = successNum;
	}

	public int getFailureNum() {
		return failureNum;
	}

	public void setFailureNum(int failureNum) {
		this.failureNum = failureNum;
	}

	public List<PayResultList> getPayResultList() {
		return payResultList;
	}

	public void setPayResultList(List<PayResultList> payResultList) {
		this.payResultList = payResultList;
	}

	public String getMerBatchId() {
		return merBatchId;
	}

	public void setMerBatchId(String merBatchId) {
		this.merBatchId = merBatchId;
	}

}
