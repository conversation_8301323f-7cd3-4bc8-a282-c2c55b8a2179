package com.easylive.pay.model.cashout.jiuhe;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提现订单参数
 *
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JiuHeWithdrawOrderVO {

    @ApiModelProperty(value = "创建的代发订单ID")
    private String id;

    @ApiModelProperty(value = "外部商户的订单号")
    private String outOrderNo;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "订单状态")
    private String status;

    @ApiModelProperty(value = "有效代发金额")
    private Double grossAmount;

    @ApiModelProperty(value = "实得金额")
    private Double netAmount;

    @ApiModelProperty(value = "服务费金额")
    private Double feeAmount;

    @ApiModelProperty(value = "失败原因")
    private String note;

    @ApiModelProperty(value = "结算订单的电子回单 OSS 下载链接")
    private String receipt;
}
