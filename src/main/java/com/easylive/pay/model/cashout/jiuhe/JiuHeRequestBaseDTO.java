package com.easylive.pay.model.cashout.jiuhe;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-11-16 10:21
 */
@Data
public class JiuHeRequestBaseDTO {

    /**
     * 平台为各 App 分配的唯一 id
     */
    protected String appid;

    /**
     * 请求流水号，App 级别唯一
     */
    protected String reqid;

    /**
     * 发起请求的时间戳 毫秒
     */
    protected long timestamp;

    /**
     * 业务数据，具体内容见对应模型，为 JSON 格式并经 base64 编码
     */
    protected String data;

    /**
     * 消息签名，通过客户私钥对报文摘要作 SHA256withRSA2 签名，并经 base64 编码
     */
    protected String sign;

    public JiuHeRequestBaseDTO(String appid, String data) {
        this.appid = appid;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
        this.reqid = String.valueOf(System.currentTimeMillis());
    }

    public Map<String, Object> toHashMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("appid", appid);
        map.put("reqid", reqid);
        map.put("timestamp", timestamp);
        map.put("data", data);
        return map;
    }
}
