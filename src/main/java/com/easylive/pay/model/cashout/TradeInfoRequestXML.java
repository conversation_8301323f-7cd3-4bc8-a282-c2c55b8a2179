package com.easylive.pay.model.cashout;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 3.2.1 交易信息查询
 *
 * <AUTHOR>
 * @date 2019/8/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TradeInfoRequestXML extends RequestXMLBody {

    /**
     * 请求参数格式如下：
     * <body>
     * <selTransDate>1000520203920005345</selTransDate>
     * <selTransTime>6216520203920005345</selTransTime>
     * <selTransTraceNo>100.00</selTransTraceNo>
     * <brf>abc</brf>
     * </body>
     */

    //查询交易日期
    private String selTransDate;
    //查询交易时间
    private String selTransTime;
    //查询交易流水
    private String selTransTraceNo;
    //交易备注
    private String brf;

}
