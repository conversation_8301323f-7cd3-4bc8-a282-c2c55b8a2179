package com.easylive.pay.model.cashout.syd;

import com.easylive.pay.common.Constants;

import java.io.Serializable;
import java.util.List;


/**
 * 商户批量代付接口实体类
 * <AUTHOR>
 *
 */
public class PaymentTransModel implements Serializable{
	
	private static final long serialVersionUID = 1L;

	
	private String totalCount;//总笔数
	
	private String totalAmt;//总金额
	
	private String merId;//商户编号
	
	private List<PayItems> payItems;//代付明细数据集合
	
	 // 商户批次号
    private String merBatchId;
	
	public static class PayItems{
		
	    // 商户订单号
	    private String merOrderId;
	    // 金额
	    private Long amt;

	    // 收款人名称
	    private String payeeName;
	    
	    //收款人账号
	    private String payeeAcc;

	    // 身份证号
	    private String idCard;

	    // 手机号
	    private String mobile;

	    // 联行号
	    private String branchNo;

	    // 支行名称
	    private String branchName;

	    // 省名称
	    private String province;

	    // 市名称
	    private String city;

	    // 备注
	    private String memo;

	    // 代付类型 0：实时 1：工作日
	    private Integer payType;
	    
	    //代付方式 0：银行卡，1：支付宝，2：微信
	    private Integer paymentType;
	    
	    //结算银行卡账号类型，0：对公，1：对私
	    private Integer accType;
	    
	    //返回码
	    private String resCode = Constants.SYD_SUCCESS;
	    
	    //返回信息
	    private String resMsg  = Constants.SYD_SUCCESS_INFO;

		public String getMerOrderId() {
			return merOrderId;
		}

		public void setMerOrderId(String merOrderId) {
			this.merOrderId = merOrderId;
		}

		public Long getAmt() {
			return amt;
		}

		public void setAmt(Long amt) {
			this.amt = amt;
		}

		public String getPayeeName() {
			return payeeName;
		}

		public void setPayeeName(String payeeName) {
			this.payeeName = payeeName;
		}

		public String getPayeeAcc() {
			return payeeAcc;
		}

		public void setPayeeAcc(String payeeAcc) {
			this.payeeAcc = payeeAcc;
		}

		public String getIdCard() {
			return idCard;
		}

		public void setIdCard(String idCard) {
			this.idCard = idCard;
		}

		public String getMobile() {
			return mobile;
		}

		public void setMobile(String mobile) {
			this.mobile = mobile;
		}

		public String getBranchNo() {
			return branchNo;
		}

		public void setBranchNo(String branchNo) {
			this.branchNo = branchNo;
		}

		public String getBranchName() {
			return branchName;
		}

		public void setBranchName(String branchName) {
			this.branchName = branchName;
		}

		public String getProvince() {
			return province;
		}

		public void setProvince(String province) {
			this.province = province;
		}

		public String getCity() {
			return city;
		}

		public void setCity(String city) {
			this.city = city;
		}

		public String getMemo() {
			return memo;
		}

		public void setMemo(String memo) {
			this.memo = memo;
		}

		public Integer getPayType() {
			return payType;
		}

		public void setPayType(Integer payType) {
			this.payType = payType;
		}

		public Integer getPaymentType() {
			return paymentType;
		}

		public void setPaymentType(Integer paymentType) {
			this.paymentType = paymentType;
		}

		public Integer getAccType() {
			return accType;
		}

		public void setAccType(Integer accType) {
			this.accType = accType;
		}

		public String getResCode() {
			return resCode;
		}

		public void setResCode(String resCode) {
			this.resCode = resCode;
		}

		public String getResMsg() {
			return resMsg;
		}

		public void setResMsg(String resMsg) {
			this.resMsg = resMsg;
		}
	    
	}

	public String getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(String totalCount) {
		this.totalCount = totalCount;
	}

	public String getTotalAmt() {
		return totalAmt;
	}

	public void setTotalAmt(String totalAmt) {
		this.totalAmt = totalAmt;
	}

	public String getMerId() {
		return merId;
	}

	public void setMerId(String merId) {
		this.merId = merId;
	}

	public List<PayItems> getPayItems() {
		return payItems;
	}

	public void setPayItems(List<PayItems> payItems) {
		this.payItems = payItems;
	}
	
	public String getMerBatchId() {
		return merBatchId;
	}

	public void setMerBatchId(String merBatchId) {
		this.merBatchId = merBatchId;
	}
	

}
