package com.easylive.pay.model.cashout.sc;

import lombok.Data;

/**
 * @auther marion
 * @date 2020/12/29
 **/
@Data
public class PaymentDetailInfo {

    /**
     * 返回码
     */
    private String resultCode;

    /**
     * 返回说明
     */
    private String resultMsg;

    /**
     * 批次出款流水号
     */
    private String batchNo;

    /**
     * 子订单号
     */
    private String orderId;

    /**
     * 子订单结果状态
     * 0 – 交易成功
     * 1 – 交易失败
     * 2 – 处理处理中
     */
    private String remitResult;

    /**
     * 子订单结果说明
     */
    private String remitResultMsg;

    /**
     * 子订单银行卡成 功到账金额
     */
    private String paymentAmount;

}
