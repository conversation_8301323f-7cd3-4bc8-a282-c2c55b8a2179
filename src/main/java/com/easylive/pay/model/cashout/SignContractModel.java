package com.easylive.pay.model.cashout;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.kafka.common.protocol.types.Field;

/**
 * 签约输入
 *
 * <AUTHOR>
 * @date 2020/7/22
 */
@Data
@NoArgsConstructor
public class SignContractModel {

    private long uid;
    private int operType;
    private String accountName;
    private String accountNo;
    private String bankName;
    private String bankNo;
    private String idCard;
    private String phone;

    private String front;
    private String back;

    private String zfbAccount;

    private String platformId;

}
