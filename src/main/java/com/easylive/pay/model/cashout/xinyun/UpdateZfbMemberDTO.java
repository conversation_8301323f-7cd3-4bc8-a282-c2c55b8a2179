package com.easylive.pay.model.cashout.xinyun;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

public class UpdateZfbMemberDTO {

    /**
     * 企业标识
     */
    @JsonProperty(value = "enterprise_unique_no")
    private String enterpriseUniqueNO;

    /**
     * 会员身份证或member_id
     */
    @JsonProperty(value = "field_val")
    private String fieldVal;

    /**
     * 类型 1-身份证 2-会员member_id
     */
    @JsonProperty(value = "field_type")
    private int fieldType;

    /**
     * 支付宝账号
     */
    @JsonProperty(value = "zfbzh")
    private String zfbAccount;

    /**
     * 随机串
     */
    @JsonProperty(value = "nonce_str")
    private String nonceStr;

    /**
     * 签名
     */
    private String sign;

    public String getEnterpriseUniqueNO() {
        return enterpriseUniqueNO;
    }

    public void setEnterpriseUniqueNO(String enterpriseUniqueNO) {
        this.enterpriseUniqueNO = enterpriseUniqueNO;
    }

    public String getFieldVal() {
        return fieldVal;
    }

    public void setFieldVal(String fieldVal) {
        this.fieldVal = fieldVal;
    }

    public int getFieldType() {
        return fieldType;
    }

    public void setFieldType(int fieldType) {
        this.fieldType = fieldType;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getZfbAccount() {
        return zfbAccount;
    }

    public void setZfbAccount(String zfbAccount) {
        this.zfbAccount = zfbAccount;
    }

    // to HashMap
    public Map<String, String> toHashMap() {
        Map<String, String> map = new HashMap<>();
        map.put("zfbzh", zfbAccount);
        map.put("enterprise_unique_no", enterpriseUniqueNO);
        map.put("nonce_str", nonceStr);
        map.put("field_val", fieldVal);
        map.put("field_type", String.valueOf(fieldType));
        return map;
    }

}
