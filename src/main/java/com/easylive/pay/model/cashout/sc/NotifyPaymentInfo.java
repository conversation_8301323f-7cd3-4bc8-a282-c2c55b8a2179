package com.easylive.pay.model.cashout.sc;

import java.util.List;

/**
 * @auther jingHu
 * @date 2020/8/17
 **/
public class NotifyPaymentInfo {

    private String batchNo;//批次号
    private String totalSuccessNum;//成功笔数
    private String totalSuccessAmt;//成功打款金额
    private String totalFailNum;//失败笔数
    private String totalFailAmt;//失败金额
    private String totalNum;//总笔数
    private String totalAmt;//总金额
    private List<NotifyPaymenDetailInfo> notifyPaymenDetailInfoList;

    public void setNotifyPaymenDetailInfoList(List<NotifyPaymenDetailInfo> notifyPaymenDetailInfoList) {
        this.notifyPaymenDetailInfoList = notifyPaymenDetailInfoList;
    }

    public List<NotifyPaymenDetailInfo> getNotifyPaymenDetailInfoList() {
        return notifyPaymenDetailInfoList;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getTotalSuccessNum() {
        return totalSuccessNum;
    }

    public void setTotalSuccessNum(String totalSuccessNum) {
        this.totalSuccessNum = totalSuccessNum;
    }

    public String getTotalSuccessAmt() {
        return totalSuccessAmt;
    }

    public void setTotalSuccessAmt(String totalSuccessAmt) {
        this.totalSuccessAmt = totalSuccessAmt;
    }

    public String getTotalFailNum() {
        return totalFailNum;
    }

    public void setTotalFailNum(String totalFailNum) {
        this.totalFailNum = totalFailNum;
    }

    public String getTotalFailAmt() {
        return totalFailAmt;
    }

    public void setTotalFailAmt(String totalFailAmt) {
        this.totalFailAmt = totalFailAmt;
    }

    public String getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(String totalNum) {
        this.totalNum = totalNum;
    }

    public String getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(String totalAmt) {
        this.totalAmt = totalAmt;
    }
}
