package com.easylive.pay.model.cashout.sc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 所有接口的请求和响应都是 JSON 格式，都包含以下字段,参考:
 * {
 * 	"head":"%7B%22v2%7D",
 * 	"body":"%7B%22v2%7D",
 * 	"cipher":"2245D1A03F0CBB3A0A2552B2BB268DA8",
 * 	"sign":"L8 dkdSOBcQ14DQiz9U5w\u003d",
 * 	"reqMsgId":"4c0dd3a672fa40d684787b46511873dc"
 * 	}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonRequest {
	private String head;//报文头
	private String cipher;//密文
	private String sign;//签名
	private String body;//包文体
	private String reqMsgId;//报文编号
}
