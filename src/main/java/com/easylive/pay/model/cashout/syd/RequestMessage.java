package com.easylive.pay.model.cashout.syd;

import com.easylive.pay.common.Constants;

import java.io.Serializable;


/**
 * 公共接口请求信息实体类
 * <AUTHOR>
 *
 */
public class RequestMessage implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	//请求标识(每一次请求唯一,在返回报文是原样返回)
	private String reqId;

	//商户号
	private String merId;
	
	//接口编码
	private String funCode;
	
	//版本信息
	private String version ="V1.0";
	
	//请求数据
	private String reqData;
	
	//响应数据
	private String resData ;
	
	//响应码
	private String  resCode = Constants.SYD_SUCCESS;
	
	//响应信息
	private String resMsg = Constants.SYD_SUCCESS_INFO;
	
	//签名
	private String sign;
	
	//IP
	private String ip;

	public String getMerId() {
		return merId;
	}

	public void setMerId(String merId) {
		this.merId = merId;
	}

	public String getFunCode() {
		return funCode;
	}

	public void setFunCode(String funCode) {
		this.funCode = funCode;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getReqData() {
		return reqData;
	}

	public void setReqData(String reqData) {
		this.reqData = reqData;
	}

	public String getResData() {
		return resData;
	}

	public void setResData(String resData) {
		this.resData = resData;
	}

	public String getResCode() {
		return resCode;
	}

	public void setResCode(String resCode) {
		this.resCode = resCode;
	}

	public String getResMsg() {
		return resMsg;
	}

	public void setResMsg(String resMsg) {
		this.resMsg = resMsg;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}
	
	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}
	
	public String getReqId() {
		return reqId;
	}

	public void setReqId(String reqId) {
		this.reqId = reqId;
	}

	@Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reqId=").append(reqId);
        sb.append(", funCode=").append(funCode);
        sb.append(", merId=").append(merId);
        sb.append(", version=").append(version);
        sb.append(", reqData=").append(reqData);
        sb.append(", resData=").append(resData);
        sb.append(", ip=").append(ip);
        sb.append(", resCode=").append(resCode);
        sb.append(", resMsg=").append(resMsg);
        sb.append("]");
        return sb.toString();
    }

}
