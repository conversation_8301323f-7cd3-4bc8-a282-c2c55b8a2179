package com.easylive.pay.model.cashout.syd;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 批量付款返回信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PaymentResData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功数
     */
    private Integer successNum;

    /**
     * 失败数
     */
    private Integer failureNum;

    /**
     * 商户批次号
     */
    private String merBatchId;

    /**
     * 付款返回数据
     */
    private List<PayResult> payResultList;

    @Data
    @NoArgsConstructor
    public static class PayResult {
        /**
         * 商户订单号
         */
        private String merOrderId;

        /**
         * 订单流水号
         */
        private String payItemId;

        /**
         * 付款金额
         */
        private String amt;

        /**
         * 订单拆分标识 0：未拆分，1：已拆分
         */
        private Integer splitFlag;

        /**
         * 响应码
         */
        private String resCode;

        /**
         * 响应信息
         */
        private String resMsg;
    }

}