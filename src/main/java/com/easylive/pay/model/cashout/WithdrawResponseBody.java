package com.easylive.pay.model.cashout;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 3.1.2 客户工资提取冲正请求参数，报文头的TransCode=1122
 *
 * <AUTHOR>
 * @date 2019/8/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawResponseBody extends ResponseXMLBody {

    /**
     * 请求参数格式如下：
     * <body>
     * <txSts>0</employeeNum>
     * <oriTxsts>0</acctountNo>
     * </body>
     */

    //交易状态
    private String txSts;
    //原交易状态
    private String oriTxsts;

}
