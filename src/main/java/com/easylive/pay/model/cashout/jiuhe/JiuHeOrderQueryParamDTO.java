package com.easylive.pay.model.cashout.jiuhe;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-11-16 10:21
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JiuHeOrderQueryParamDTO {

    @ApiModelProperty(value = "代发订单 ID")
    private String id;

    @ApiModelProperty(value = "外部商户的订单号")
    private String outOrderNo;

    @ApiModelProperty(value = "代征主体 ID")
    private String levierId;

    @ApiModelProperty(value = "自由职业者 ID")
    private String sohoId;

    @ApiModelProperty(value = "结算订单状态")
    private String status;

    @ApiModelProperty(value = "支付类型")
    private String payChannel;

    @ApiModelProperty(value = "自由职业者姓名，支持模糊查询")
    private String sohoName;

    @ApiModelProperty(value = "自由职业者手机号")
    private String sohoMobile;

    @ApiModelProperty(value = "当前页码")
    private Integer page;

    @ApiModelProperty(value = "单页记录数量")
    private Integer pageSize;
}
