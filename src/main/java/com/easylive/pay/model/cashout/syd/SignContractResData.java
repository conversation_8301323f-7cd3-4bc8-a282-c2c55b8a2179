package com.easylive.pay.model.cashout.syd;

import java.io.Serializable;

import lombok.Data;

/**
 * 签约返回信息
 *
 * <AUTHOR>
 */
@Data
public class SignContractResData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求序号，每次请求保持唯一，表明报文的唯一编号
     */
    private String reqId;

    /**
     * 接口编码，该接口默认值6010
     */
    private String funCode;

    /**
     * 商户号，我司分配给客户的唯一编号
     */
    private String merId;

    /**
     * 接口版本号，目前版本为V1.0
     */
    private String version;

    /**
     * 响应码
     */
    private String resCode;

    /**
     * 响应信息
     */
    private String resMsg;

}