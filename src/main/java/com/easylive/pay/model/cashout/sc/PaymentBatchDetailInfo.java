package com.easylive.pay.model.cashout.sc;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 批量打款详细信息
 *
 * @auther jingHu
 * @date 2020/8/14
 **/
public class PaymentBatchDetailInfo {

    private String orderId;//商户打款时上传的订单号
    private String remitResult;//打款结果  S – 打款成功 F – 打款失败
    private String remitResultMsg;//打款结果说明
    private BigDecimal paymentAmount;//打款金额
    private Timestamp successTime;//打款成功：返回打款成功时间

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getRemitResult() {
        return remitResult;
    }

    public void setRemitResult(String remitResult) {
        this.remitResult = remitResult;
    }

    public String getRemitResultMsg() {
        return remitResultMsg;
    }

    public void setRemitResultMsg(String remitResultMsg) {
        this.remitResultMsg = remitResultMsg;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public Timestamp getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(Timestamp successTime) {
        this.successTime = successTime;
    }
}
