package com.easylive.pay.model.cashout;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XStreamAlias("root")
public class BaseResponseXML {
    @XStreamAlias("head")
    private ResponseXMLHead head;

    @XStreamAlias("body")
    private ResponseXMLBody body;
}
