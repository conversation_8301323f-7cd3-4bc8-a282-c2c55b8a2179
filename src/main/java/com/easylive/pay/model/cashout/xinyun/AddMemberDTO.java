package com.easylive.pay.model.cashout.xinyun;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

public class AddMemberDTO {
    /**
     * 真实姓名
     */
    @JsonProperty(value = "real_name")
    private String realName;

    /**
     * 手机号码
     */
    @JsonProperty(value = "user_tel")
    private String phone;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 身份证ID
     */
    @JsonProperty(value = "identity_card")
    private String idCard;

    /**
     * 开户银行
     */
    @JsonProperty(value = "bank_name")
    private String bankName;

    /**
     * 银行账户
     */
    @JsonProperty(value = "bank_account")
    private String bankAccount;

    /**
     * 支付宝账户 (选填)
     */
    @JsonProperty(value = "zfbzh")
    private String zfbAccount;

    /**
     * 注册提交时间 2020-01-01 11:00:00
     */
    @JsonProperty(value = "reg_time")
    private String regDate;

    /**
     * 企业标识
     */
    @JsonProperty(value = "enterprise_unique_no")
    private String enterpriseUniqueNO;

    /**
     * 随机串
     */

    @JsonProperty(value = "nonce_str")
    private String nonceStr;

    @JsonProperty(value = "front_card_url")
    private String front;

    @JsonProperty(value = "side_card_url")
    private String back;

    /**
     * 签名
     */
    private String sign;

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getZfbAccount() {
        return zfbAccount;
    }

    public void setZfbAccount(String zfbAccount) {
        this.zfbAccount = zfbAccount;
    }

    public String getRegDate() {
        return regDate;
    }

    public void setRegDate(String regDate) {
        this.regDate = regDate;
    }

    public String getEnterpriseUniqueNO() {
        return enterpriseUniqueNO;
    }

    public void setEnterpriseUniqueNO(String enterpriseUniqueNO) {
        this.enterpriseUniqueNO = enterpriseUniqueNO;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getFront() {
        return front;
    }

    public void setFront(String front) {
        this.front = front;
    }

    public String getBack() {
        return back;
    }

    public void setBack(String back) {
        this.back = back;
    }

    // to HashMap
    public Map<String, String> toHashMap() {
        Map<String, String> map = new HashMap<>();
        map.put("real_name", realName);
        map.put("user_tel", phone);
        map.put("sex", String.valueOf(sex));
        map.put("identity_card", idCard);
        map.put("bank_name", bankName);
        map.put("bank_account", bankAccount);
        map.put("zfbzh", zfbAccount);
        map.put("reg_time", regDate);
        map.put("enterprise_unique_no", enterpriseUniqueNO);
        map.put("nonce_str", nonceStr);
        map.put("front_card_url", front);
        map.put("side_card_url", back);
        return map;
    }

}
