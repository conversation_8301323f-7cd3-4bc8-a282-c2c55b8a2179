package com.easylive.pay.model.cashout.xinyun;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AddMemberResponseDTO {
    /**
     * 成员ID
     */
    @JsonProperty(value = "member_id")
    private String memberId;

    /**
     * 返回码
     */
    @JsonProperty(value = "return_code")
    private String returnCode;

    /**
     * 错误码
     */
    @JsonProperty(value = "err_code")
    private String errCode;

    /**
     * 错误码信息
     */
    @JsonProperty(value = "err_code_des")
    private String errCodeDes;

    /**
     * 返回信息
     */
    @JsonProperty(value = "result_code")
    private String resultCode;

    /**
     * 随机串
     */
    @JsonProperty(value = "nonce_str")
    private String nonceStr;

    /**
     * 加密规则
     */
    @JsonProperty(value = "sign_type")
    private String signType;

    /**
     * 加密串
     */
    @JsonProperty(value = "sign")
    private String sign;

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrCodeDes() {
        return errCodeDes;
    }

    public void setErrCodeDes(String errCodeDes) {
        this.errCodeDes = errCodeDes;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    // to Query
    public String toQuery() {
        String baseQuery = "";
        if (memberId != null) {
            baseQuery += "member_id=" + memberId + "&";
        }

        if (returnCode != null) {
            baseQuery += "return_code=" + returnCode + "&";
        }

        if (errCode != null) {
            baseQuery += "err_code=" + errCode + "&";
        }

        if (errCodeDes!= null) {
            baseQuery += "err_code_des=" + errCodeDes + "&";
        }

        if (resultCode!= null) {
            baseQuery += "result_code=" + resultCode + "&";
        }

        if (nonceStr!= null) {
            baseQuery += "nonce_str=" + nonceStr + "&";
        }

        if (signType!= null) {
            baseQuery += "sign_type=" + signType + "&";
        }

        if (baseQuery.endsWith("&")) {
            baseQuery = baseQuery.substring(0, baseQuery.length() - 1);
        }

        return baseQuery;
    }


}
