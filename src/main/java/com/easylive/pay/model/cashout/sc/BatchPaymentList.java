package com.easylive.pay.model.cashout.sc;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BatchPaymentList {

	private String orderId;
	private String realName;
	private String cardId;
	private String cardNo;
	private String phoneNo;
	private double payAmount;

	public BatchPaymentList(String orderId, String realName, String cardId, String cardNo, String phoneNo, double payAmount) {
		this.orderId = orderId;
		this.realName = realName;
		this.cardId = cardId;
		this.cardNo = cardNo;
		this.phoneNo = phoneNo;
		this.payAmount = payAmount;
	}

}
