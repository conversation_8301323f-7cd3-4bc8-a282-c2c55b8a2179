package com.easylive.pay.model.cashout.xinyun;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 付款查询参数
 *
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayQueryParam {

    @ApiModelProperty(value = "企业标识", required = true)
    private String enterprise_unique_no;

    @ApiModelProperty(value = "批次号", required = true)
    private String batch_num;

    @ApiModelProperty(value = "随机字符串", required = true)
    private String nonce_str;

    @ApiModelProperty(value = "签名", required = true)
    private String sign;

    public PayQueryParam(String enterprise_unique_no, String batch_num, String nonce_str){
        this.enterprise_unique_no = enterprise_unique_no;
        this.batch_num = batch_num;
        this.nonce_str = nonce_str;
    }

    public Map<String, String> toHashMap() {
        Map<String, String> map = new HashMap<>();
        map.put("enterprise_unique_no", enterprise_unique_no);
        map.put("batch_num", batch_num);
        map.put("nonce_str", nonce_str);
        return map;
    }
}
