package com.easylive.pay.model.cashout.sc;

import java.math.BigDecimal;
import java.util.List;

/**
 * 批量打款总信息
 *
 * @auther jingHu
 * @date 2020/8/14
 **/
public class PaymentBatchInfo {

    private String batchNo;//批次号
    private int totalSuccessNum;//成功笔数
    private BigDecimal totalSuccessAmt;//成功打款金额
    private int totalFailNum;//失败笔数
    private BigDecimal totalFailAmt;//失败金额
    private int totalNum;//总笔数
    private BigDecimal totalAmt;//总金额
    List<PaymentBatchDetailInfo> paymentBatchDetailInfoList;//详细信息

    public void setPaymentBatchDetailInfoList(List<PaymentBatchDetailInfo> paymentBatchDetailInfoList) {
        this.paymentBatchDetailInfoList = paymentBatchDetailInfoList;
    }

    public List<PaymentBatchDetailInfo> getPaymentBatchDetailInfoList() {
        return paymentBatchDetailInfoList;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public int getTotalSuccessNum() {
        return totalSuccessNum;
    }

    public void setTotalSuccessNum(int totalSuccessNum) {
        this.totalSuccessNum = totalSuccessNum;
    }

    public BigDecimal getTotalSuccessAmt() {
        return totalSuccessAmt;
    }

    public void setTotalSuccessAmt(BigDecimal totalSuccessAmt) {
        this.totalSuccessAmt = totalSuccessAmt;
    }

    public int getTotalFailNum() {
        return totalFailNum;
    }

    public void setTotalFailNum(int totalFailNum) {
        this.totalFailNum = totalFailNum;
    }

    public BigDecimal getTotalFailAmt() {
        return totalFailAmt;
    }

    public void setTotalFailAmt(BigDecimal totalFailAmt) {
        this.totalFailAmt = totalFailAmt;
    }

    public int getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(int totalNum) {
        this.totalNum = totalNum;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }
}
