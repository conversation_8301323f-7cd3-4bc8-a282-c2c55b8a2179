package com.easylive.pay.model.cashout.xinyun;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-11-16 10:21
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseBaseModel {

    /**
     * 返回码
     */
    @JsonProperty(value = "return_code")
    private String returnCode;

    /**
     * 返回消息
     */
    @JsonProperty(value = "return_msg")
    private String returnMsg;

    /**
     * 错误码
     */
    @JsonProperty(value = "err_code")
    private String errCode;

    /**
     * 错误码信息
     */
    @JsonProperty(value = "err_code_des")
    private String errCodeDes;

    /**
     * 返回信息
     */
    @JsonProperty(value = "result_code")
    private String resultCode;

    /**
     * 随机串
     */
    @JsonProperty(value = "nonce_str")
    private String nonceStr;

    /**
     * 加密规则
     */
    @JsonProperty(value = "sign_type")
    private String signType;

    /**
     * 加密串
     */
    @JsonProperty(value = "sign")
    private String sign;
}
