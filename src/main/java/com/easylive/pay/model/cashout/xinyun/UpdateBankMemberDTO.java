package com.easylive.pay.model.cashout.xinyun;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

public class UpdateBankMemberDTO {

    /**
     * 企业标识
     */
    @JsonProperty(value = "enterprise_unique_no")
    private String enterpriseUniqueNO;

    /**
     * 会员身份证或member_id
     */
    @JsonProperty(value = "field_val")
    private String fieldVal;

    /**
     * 类型 1-身份证 2-会员member_id
     */
    @JsonProperty(value = "field_type")
    private int fieldType;

    /**
     * 银行名称
     */
    @JsonProperty(value = "bank_name")
    private String bankName;

    /**
     * 银行卡号
     */
    @JsonProperty(value = "bank_account")
    private String bankAccount;

    /**
     * 随机串
     */
    @JsonProperty(value = "nonce_str")
    private String nonceStr;

    /**
     * 签名
     */
    private String sign;

    public String getEnterpriseUniqueNO() {
        return enterpriseUniqueNO;
    }

    public void setEnterpriseUniqueNO(String enterpriseUniqueNO) {
        this.enterpriseUniqueNO = enterpriseUniqueNO;
    }

    public String getFieldVal() {
        return fieldVal;
    }

    public void setFieldVal(String fieldVal) {
        this.fieldVal = fieldVal;
    }

    public int getFieldType() {
        return fieldType;
    }

    public void setFieldType(int fieldType) {
        this.fieldType = fieldType;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    // to HashMap
    public Map<String, String> toHashMap() {
        Map<String, String> map = new HashMap<>();
        map.put("bank_name", bankName);
        map.put("bank_account", bankAccount);
        map.put("enterprise_unique_no", enterpriseUniqueNO);
        map.put("nonce_str", nonceStr);
        map.put("field_val", fieldVal);
        map.put("field_type", String.valueOf(fieldType));
        return map;
    }

}
