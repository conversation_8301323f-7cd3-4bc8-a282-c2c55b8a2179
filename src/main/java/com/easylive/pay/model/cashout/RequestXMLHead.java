package com.easylive.pay.model.cashout;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/8/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RequestXMLHead {

    /**
     * XML格式如下：
     * <head>
     * <TransCode>1120</TransCode>
     * <TransDate>********</TransDate>
     * <TransTime>151902</TransTime>
     * <ChnlType>CZ01</ChnlType>
     * <TrcNo>********0008</TrcNo>
     * <EnterpriseNo>********</EnterpriseNo>
     * <BankNo>********</BankNo>
     * <Mac>************</Mac>
     * </head>
     */

    @XStreamAlias("TransCode")
    private String transCode;

    @XStreamAlias("TransDate")
    private String transDate;

    @XStreamAlias("TransTime")
    private String transTime;

    @XStreamAlias("ChnlType")
    private String chnlType;

    @XStreamAlias("TrcNo")
    private String trcNo;

    @XStreamAlias("EnterpriseNo")
    private String enterpriseNo;

    @XStreamAlias("BankNo")
    private String bankNo;

    @XStreamAlias("Mac")
    private String mac;

}
