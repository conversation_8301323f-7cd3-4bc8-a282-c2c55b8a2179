package com.easylive.pay.model.cashout.jiuhe;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 提现批次参数
 *
 * <AUTHOR>
 * @date 2023/11/15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JiuHeWithdrawLotVO {
    @ApiModelProperty(value = "创建的代发批次")
    private String id;

    @ApiModelProperty(value = "外部商户的批次号")
    private String outBatchNo;

    @ApiModelProperty(value = "代发所用的商户账户")
    private String fundId;

    @ApiModelProperty(value = "总代发订单数")
    private Integer totalNum;

    @ApiModelProperty(value = "有效代发订单数")
    private Integer availNum;

    @ApiModelProperty(value = "成功代发订单数")
    private Integer succNum;

    @ApiModelProperty(value = "有效代发总金额")
    private Double grossAmount;

    @ApiModelProperty(value = "实得总金额")
    private Double netAmount;

    @ApiModelProperty(value = "服务费总金额")
    private Double feeAmount;

    @ApiModelProperty(value = "成功代发总金额")
    private Double grossSucc;

    @ApiModelProperty(value = "成功实得总金额")
    private Double netSucc;

    @ApiModelProperty(value = "结算状态")
    private String status;

    @ApiModelProperty(value = "代发批次完结时间")
    private Double closeTime;

    @ApiModelProperty(value = "成功服务费总金额")
    private Date feeSucc;

    @ApiModelProperty(value = "订单列表")
    private List<JiuHeWithdrawOrderVO> orderList;

}
