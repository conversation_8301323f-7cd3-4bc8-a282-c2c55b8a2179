package com.easylive.pay.model.cashout.syd;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 批量付款查询返回信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PaymentQueryResData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户号，我司分配给客户的唯一编号
     */
    private String merId;

    /**
     * 商户批次号
     */
    private String merBatchId;

    /**
     * 查询数据
     */
    private List<PayQueryItem> queryItems;

    @Data
    @NoArgsConstructor
    public static class PayQueryItem {
        /**
         * 商户订单号
         */
        private String merOrderId;

        /**
         * 订单流水号
         */
        private String payItemId;

        /**
         * 交易状态 0：已受理 1：处理中 2：提现中 3：成功 4：失败
         */
        private Integer state;

        /**
         * 付款金额
         */
        private Long amt;

        /**
         * 订单拆分标识 0：未拆分，1：已拆分
         */
        private Integer splitFlag;

        /**
         * 响应码
         */
        private String resCode;

        /**
         * 响应信息
         */
        private String resMsg;
    }

}