package com.easylive.pay.model.cashout;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/8/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@XStreamAlias("head")
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseXMLHead {

    /**
     * XML格式如下：
     * <head>
     * <TransDate>********</TransDate>
     * <TransTime>175246</TransTime>
     * <TransCode>1200</TransCode>
     * <ChnlType>0001</ChnlType>
     * <TrcNo>********175246000027</TrcNo>
     * <EnterpriseNo>********</EnterpriseNo>
     * <BankNo>806000</BankNo>
     * <ResCode>0000</ResCode>
     * <ResMsg>交易成功</ResMsg>
     * <BankTxDate>********</BankTxDate>
     * <BankTxTime>175246</BankTxTime>
     * <BankTraceNo>************</BankTraceNo>
     * </head>
     */

    //交易日期
    @XStreamAlias("TransDate")
    private String transDate;
    //交易时间
    @XStreamAlias("TransTime")
    private String transTime;
    //交易代码
    @XStreamAlias("TransCode")
    private String transCode;
    //渠道类型
    @XStreamAlias("ChnlType")
    private String chnlType;
    //交易流水
    @XStreamAlias("TrcNo")
    private String trcNo;
    //企业机构代码
    @XStreamAlias("EnterpriseNo")
    private String enterpriseNo;
    //银行代码
    @XStreamAlias("BankNo")
    private String bankNo;
    //响应代码
    @XStreamAlias("ResCode")
    private String resCode;
    //返回信息
    @XStreamAlias("ResMsg")
    private String resMsg;
    //银行交易日期
    @XStreamAlias("BankTxDate")
    private String bankTxDate;
    //银行交易时间
    @XStreamAlias("BankTxTime")
    private String bankTxTime;
    //银行交易流水
    @XStreamAlias("BankTraceNo")
    private String bankTraceNo;

}
