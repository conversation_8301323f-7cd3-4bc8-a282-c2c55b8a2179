package com.easylive.pay.model.cashout;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 3.2.2 客户信息签约请求参数，报文头的TransCode=1102
 *
 * <AUTHOR>
 * @date 2019/8/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountSignRequestBody extends RequestXMLBody {

    /**
     * 请求参数格式如下：
     * <body>
     * <operType>1</operType>
     * <employeeNum>1000520203920005345</employeeNum>
     * <employeeName>员工姓名</employeeName>
     * <acctountNo>6216520203920005345</acctountNo>
     * <acctName>李三一</acctName>
     * <bankNo>************</bankNo>
     * <bankName>淇县中原村镇银行鹤淇支行</bankName>
     * <idType>01</idType>
     * <idNo>410603199001011056</idNo>
     * <cellPhone>***********</cellPhone>
     * <brf>abc</brf>
     * </body>
     */

    //操作类型
    private String operType;
    //员工编号
    private String employeeNum;
    //员工姓名
    private String employeeName;
    //客户银行账号
    private String acctountNo;
    //银行账号户名
    private String acctName;
    //收款银行行号
    private String bankNo;
    //收款银行行名
    private String bankName;
    //证件类型
    private String idType;
    //证件号码
    private String idNo;
    //手机号码
    private String cellPhone;
    //交易备注
    private String brf = "客户信息签约";

}
