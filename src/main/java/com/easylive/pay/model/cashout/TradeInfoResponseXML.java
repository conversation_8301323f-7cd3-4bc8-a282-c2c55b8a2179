package com.easylive.pay.model.cashout;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/8/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TradeInfoResponseXML {

    /**
     * 请求参数格式如下：
     * <body>
     * <txSts>0</employeeNum>
     * <employeeNum>1000520203920005345</employeeNum>
     * <acctountNo>6216520203920005345</acctountNo>
     * <txAmt>100.00</txAmt>
     * <acctName>李三一</acctName>
     * <bankNo>************</bankNo>
     * <bankName>淇县中原村镇银行鹤淇支行</bankName>
     * </body>
     */

    //交易状态
    private String txSts;
    //原交易状态
    private String oriTxsts;
    //员工编号
    private String employeeNum;
    //客户银行账号
    private String acctountNo;
    //交易金额
    private String txAmt;
    //银行账号户名
    private String acctName;
    //收款银行行号
    private String bankNo;
    //收款银行行名
    private String bankName;

}
