package com.easylive.pay.model.cashout;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 3.1.4 客户工资提取确认请求参数，报文头的TransCode=1124
 *
 * <AUTHOR>
 * @date 2019/8/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawResultConfirmRequestBody extends RequestXMLBody {

    /**
     * 请求参数格式如下：
     * <body>
     * <employeeNum>1000520203920005345</employeeNum>
     * <acctountNo>6216520203920005345</acctountNo>
     * <txAmt>100.00</txAmt>
     * <acctName>李三一</acctName>
     * <bankNo>************</bankNo>
     * <bankName>淇县中原村镇银行鹤淇支行</bankName>
     * <oriTransDate>**********</bankNo>
     * <oriTransTime>**********</bankName>
     * <oriTransTraceNo>6216520203920005345</idType>
     * <brf>abc</brf>
     * </body>
     */

    //员工编号
    private String employeeNum;
    //客户银行账号
    private String acctountNo;
    //交易金额
    private String txAmt;
    //银行账号户名
    private String acctName;
    //收款银行行号
    private String bankNo;
    //收款银行行名
    private String bankName;
    //原交易日期
    private String oriTransDate;
    //原交易时间
    private String oriTransTime;
    //原交易流水
    private String oriTransTraceNo;
    //交易备注
    private String brf;

}
