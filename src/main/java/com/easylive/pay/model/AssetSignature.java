package com.easylive.pay.model;

import org.apache.commons.codec.digest.DigestUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class AssetSignature {

    private String appId;
    private String privateKey;
    private String signature;
    private String timestamp;
    private Map<String, String> params = new HashMap<>();

    public AssetSignature(String appId, String privateKey) {
        this.appId = appId;
        this.privateKey = privateKey;
        this.timestamp = String.valueOf(System.currentTimeMillis());
        params.put("tp", timestamp);
        params.put("ak", appId);
    }

    public AssetSignature(String appId, String privateKey, Map<String, String> p) {
        this(appId, privateKey);
        this.params.putAll(p);
        this.signature = generateSignature(privateKey, params);
    }

    public String getAppId() {
        return appId;
    }

    public String getSignature() {
        return signature;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public AssetSignature addParameter(String key, String value) {
        params.put(key, value);
        return this;
    }

    public AssetSignature generate() {
        this.signature = generateSignature(privateKey, params);
        return this;
    }

    public String getQueryString() {
        return buildQueryStringWithAlphabeticKeyOrder(params);
    }

    public static String generateSignature(String privateKey, Map<String, String> params) {
        String content = buildQueryStringWithAlphabeticKeyOrder(params);
        return DigestUtils.sha256Hex(content + privateKey);
    }

    /**
     * 将Key Value形式的Map转为HTTP Query字符串格式，并且使用URL Encode以及Key按照字母顺序
     * 经常用在需要将参数进行签名的场景
     *
     * @param params KV形式HTTP参数
     * @return URL编码的HTTP Query字符串，Key按照字母表顺序
     */
    public static String buildQueryStringWithAlphabeticKeyOrder(Map<String, ?> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            sb.append(urlEncode(key)).append("=").append(urlEncode(params.get(key).toString())).append("&");
        }
        if (sb.length() > 1)
            sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    /**
     * 使用RFC3986标准进行URL编码
     *
     * @param s 输入字符串
     * @return 编码后的字符串
     */
    public static String urlEncode(String s) {
        if (s == null) {
            throw new IllegalArgumentException("Null string to encode");
        }
        try {
            return URLEncoder.encode(s, StandardCharsets.UTF_8.name())
                    .replace("+", "%20").replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (Exception e) {
            throw new IllegalArgumentException("Unable to encode");
        }
    }
}