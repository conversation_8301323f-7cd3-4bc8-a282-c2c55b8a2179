package com.easylive.pay.model;

import org.joda.time.Days;
import org.joda.time.LocalDate;

import java.util.Date;

/**
 * Created by c on 2015/12/12.
 */
public class Barley {

    private long uid;               //用户id
    private long barley;            //当天薏米数
    private long watchcount;        //观看次数
    private long watchtime;         //观看时长
    private long comments;          //评论条数
    private long livetime;          //直播时长
    private long signed;            //打卡标识
    private Date updatedate;        //缓存更新日期
    private long signdays;          //打卡连续天数
    private Date signdate;          //最后一次打卡日期

    public Barley(long _uid, long _barley, Date barleydate) {
        uid = _uid;
        barley = _barley;
        watchcount = 0;
        watchtime = 0;
        comments = 0;
        livetime = 0;
        signed = 0;
        signdays = 0;

        LocalDate localdate = new LocalDate();
        updatedate = localdate.toDate();
        if (updatedate.compareTo(barleydate) != 0) {
            barley = 0;
        }
        signdate = localdate.toDate();
    }

    public void reset() {
        barley = 0;
        watchcount = 0;
        watchtime = 0;
        comments = 0;
        livetime = 0;
        signed = 0;
        LocalDate localdate = new LocalDate();
        updatedate = localdate.toDate();
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getBarley() {
        return barley;
    }

    public void setBarley(long barley) {
        this.barley = barley;
    }

    public long getWatchCount() {
        return watchcount;
    }

    public long getWatchTime() {
        return watchtime;
    }

    public long addWatchTime(long WatchTime) {
        long curr = getWatchTimeScore(this.watchtime);
        this.watchtime += WatchTime;
        long next = getWatchTimeScore(this.watchtime);

        curr += getWatchCountScore(this.watchcount);
        this.watchcount++;
        next += getWatchCountScore(this.watchcount);

        if (next > curr) {
            return addBarley(next - curr);
        } else {
            return 0;
        }
    }

    public long getComments() {
        return comments;
    }

    public long addComments() {
        long curr = getCommentCountScore(this.comments);
        this.comments++;
        long next = getCommentCountScore(this.comments);

        if (next > curr) {
            return addBarley(next - curr);
        } else {
            return 0;
        }
    }

    public long getSigned() {
        return signed;
    }

    public long signIn() {
        if (this.signed == 1) {
            return 0;
        }

        this.signed = 1;

        //判断打卡日期是否连续
        LocalDate now = new LocalDate();
        LocalDate last = new LocalDate(this.signdate);
        int days = Days.daysBetween(last, now).getDays();
        if (days == 1) {
            this.signdays++;
        } else {
            this.signdays = 1;
        }

        this.signdate = now.toDate();

        long add = 0;
        if (this.signdays < 3) {
            add = 5;
        } else if (this.signdays < 5) {
            add = 7;
        } else if (this.signdays < 7) {
            add = 10;
        } else {
            add = 15;
        }

        return addBarley(add);
    }

    public Date getUpdatedate() {
        return updatedate;
    }

    public void setUpdatedate(Date updatedate) {
        this.updatedate = updatedate;
    }

    public long getLiveTime() {
        return livetime;
    }

    public long addLiveTime(long LiveTime) {
        long curr = getLiveTimeScore(this.livetime);
        this.livetime += LiveTime;
        long next = getLiveTimeScore(this.livetime);

        if (next > curr) {
            return addBarley(next - curr);
        } else {
            return 0;
        }
    }

    public long addBarley(long barley) {
        long add = 100 - this.barley;
        add = add > barley ? barley : add;

        this.barley += add;
        return add;
    }

    private long getWatchTimeScore(long time) {
        if (time >= 1000) {
            return 11;
        }
        if (time >= 600) {
            return 9;
        }
        if (time >= 300) {
            return 2;
        }
        return 0;
    }

    private long getLiveTimeScore(long time) {
        if (time >= 3600) {
            return 25;
        }
        if (time >= 1800) {
            return 13;
        }
        if (time >= 600) {
            return 11;
        }
        if (time >= 300) {
            return 3;
        }
        return 0;
    }

    private long getWatchCountScore(long count) {
        if (count >= 15) {
            return 14;
        }
        if (count >= 10) {
            return 5;
        }
        if (count >= 5) {
            return 3;
        }
        return 0;
    }

    private long getCommentCountScore(long count) {
        if (count >= 30) {
            return 35;
        }
        if (count >= 20) {
            return 20;
        }
        if (count >= 10) {
            return 10;
        }
        return 0;
    }

    public Date getSigndate() {
        return signdate;
    }

    public void setSigndate(Date signdate) {
        this.signdate = signdate;
    }

    public long getSigndays() {
        return signdays;
    }

    public void setSigndays(long signdays) {
        this.signdays = signdays;
    }
}
