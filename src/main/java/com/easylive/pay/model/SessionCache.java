package com.easylive.pay.model;


import com.easylive.pay.config.CacheConfig;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

import java.util.HashMap;

/**
 * Created by c on 2015/12/12.
 */
@Repository
public class SessionCache {

    private static final String prefix_key = "sessioninfo_";

    @Autowired
    private StringRedisTemplate template;

    @Autowired
    private CacheConfig cacheConfig;

    public long getUidBySession(String sessionid) {
        ValueOperations<String, String> ops = this.template.opsForValue();
        String key = cacheConfig.getPrefix() + prefix_key + sessionid;
        String value;
        if (this.template.hasKey(key)) {
            value = ops.get(key);
        } else {
            return -1;
        }

        return this.getUidByJson(value);
    }

    private long getUidByJson(String value) {
        HashMap<String, Object> jsonValue;
        GsonBuilder builder = new GsonBuilder();
        Gson gson = builder.create();
        jsonValue = gson.fromJson(value, new TypeToken<HashMap<String, Object>>() {
        }.getType());
        Object uid = jsonValue.get("uid");
        if (uid != null) {
            return ((Double) uid).longValue();
        }

        return -1;
    }


}
