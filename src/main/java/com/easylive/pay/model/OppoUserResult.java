package com.easylive.pay.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OppoUserResult {
    public static final String OK_CODE = "200";

    private String resultCode;
    private String resultMsg;
    private String loginToken;
    private String ssoid;
    private String appKey;
    private String userName;
    private String email;
    private String mobileNumber;
    private String createTime;
    private String userStatus;
}



