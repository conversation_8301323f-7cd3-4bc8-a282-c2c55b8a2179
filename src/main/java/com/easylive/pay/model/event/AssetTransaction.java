package com.easylive.pay.model.event;

import com.easylive.pay.common.Constants;
import com.easylive.pay.entity.AssetBiz;
import com.easylive.pay.entity.AssetRecord;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.enums.AssetType;
import com.easylive.pay.model.AssetOperation;
import com.easylive.pay.model.AssetUpdateResult;
import com.easylive.pay.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class AssetTransaction {
    private static final Logger logger = LoggerFactory.getLogger(AssetTransaction.class);

    private int bizId;
    private int bizType;
    private String tid;
    private List<AssetTransactionRecord> records;


    public AssetTransaction(AssetBiz biz, AssetOperation operation, AssetUpdateResult result) {
        this.bizId = (int) biz.getId();
        this.tid = result.getTid();
        this.bizType = biz.getType();
        records = new ArrayList<>(operation.operandCount());

        for (int i = 0; i < operation.operandCount(); i++) {
            AssetTransactionRecord record = new AssetTransactionRecord();
            AssetRecord r = result.getRecord(i);
            User user = result.getUser(i);
            UserCoin uc = result.getUserCoin(i);
            record.setId(r.getId());
            record.setIndex(i);
            record.setUid(r.getUid());
            record.setAppSourceId(user.getAppSourceId().intValue());
            record.setTopicId(user.getTid());
            record.setAssetType(r.getType());
            record.setBalance(r.getBalance());
            record.setValue(r.getValue());
            record.setOriginValue(r.getOriginValue());
            record.setCreateTime(r.getOperateTime());
            if (r.getType() == AssetType.ECOIN.getValue()) {
                record.setCost(uc.getCostecoin());
                record.setAccum(uc.getAccumecoin());
            } else if (r.getType() == AssetType.RICEROLL.getValue()) {
                record.setAccum(uc.getAccumriceroll());
            } else if (r.getType() == AssetType.BARLEY.getValue()) {
                record.setAccum(uc.getAccumbarley());
            }

            if (user.getAppSourceId() == null) {
                logger.error("User {} app source id not exists. Make default to 1", user.getId());
                record.setAppSourceId(Constants.DEFAULT_APP_SOURCE_ID);
            } else {
                record.setAppSourceId(user.getAppSourceId().intValue());
            }
            records.add(record);
        }
    }

    public int getBizId() {
        return bizId;
    }

    public void setBizId(int bizId) {
        this.bizId = bizId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public List<AssetTransactionRecord> getRecords() {
        return records;
    }

    public void setRecords(List<AssetTransactionRecord> records) {
        this.records = records;
    }


    public AssetTransactionRecord getRecord(int index) {
        return this.records.get(index);
    }

    public int getBizType() {
        return bizType;
    }

    @Override
    public String toString() {
        return "CenterAssetTransaction{" +
                "bizId=" + bizId +
                ", tid='" + tid + '\'' +
                "}";
    }
}
