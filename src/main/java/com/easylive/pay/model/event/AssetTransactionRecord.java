package com.easylive.pay.model.event;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-05-07
 */
public class AssetTransactionRecord {
    private long id;
    private int index;
    private long uid;
    private int appSourceId;
    private int topicId;
    private int assetType;
    private long value;
    private long originValue;
    private long balance;
    private long accum;
    private long cost;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public int getAppSourceId() {
        return appSourceId;
    }

    public void setAppSourceId(int appSourceId) {
        this.appSourceId = appSourceId;
    }

    public int getAssetType() {
        return assetType;
    }

    public void setAssetType(int assetType) {
        this.assetType = assetType;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public long getBalance() {
        return balance;
    }

    public void setBalance(long balance) {
        this.balance = balance;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public long getAccum() {
        return accum;
    }

    public void setAccum(long accum) {
        this.accum = accum;
    }

    public long getCost() {
        return cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }

    public long getOriginValue() {
        return originValue;
    }

    public void setOriginValue(long originValue) {
        this.originValue = originValue;
    }

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }
}
