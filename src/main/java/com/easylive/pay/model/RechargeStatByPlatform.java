package com.easylive.pay.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RechargeStatByPlatform {
    private long rmb;
    private long ecoin;
    private long count;
    private long countByUser;
    private String currency;
    private int platform;

    public RechargeStatByPlatform() {

    }

    public String getCurrency() {
        return currency == null ? "" : currency;
    }

    public RechargeStatByPlatform(int platform) {
        this.platform = platform;
    }

    public RechargeStatByPlatform accumulate(RechargeStatByPlatform platform) {
        this.rmb += platform.getRmb();
        this.ecoin += platform.getEcoin();
        this.count += platform.getCount();
        this.countByUser += platform.getCountByUser();
        return this;
    }
}
