package com.easylive.pay.model;

import org.springframework.http.MediaType;

/**
 * <AUTHOR>
 * @date 2019/8/14
 */
public class MediaTypeXmlT extends MediaType {

    public MediaTypeXmlT(String type) {
        super(type);
    }

    public final static String APPLICATION_XML_GBK_VALUE = APPLICATION_XML_VALUE + ";charset=GBK";

    public static MediaType APPLICATION_XML_GBK;

    static {
        APPLICATION_XML_GBK = valueOf(APPLICATION_XML_GBK_VALUE);
    }

}