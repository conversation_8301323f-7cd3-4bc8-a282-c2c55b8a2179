package com.easylive.pay.model;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-03-15
 */
public class AssetOperation {
    private List<AssetOperand> operands = new ArrayList<>();
    private String desc;

    public void addOperand(long uid, long value) {
        AssetOperand operand = new AssetOperand(uid, value);
        this.operands.add(operand);
    }

    public void addOperand(User user, long value) {
        AssetOperand operand = new AssetOperand(user, value);
        this.operands.add(operand);
    }

    public AssetOperand getOperand(int index) {
        return this.operands.get(index);
    }

    public int operandCount() {
        return this.operands.size();
    }

    public List<AssetOperand> getOperands() {
        return operands;
    }

    public void setOperands(List<AssetOperand> operands) {
        this.operands = operands;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}

