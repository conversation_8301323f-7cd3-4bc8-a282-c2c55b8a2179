package com.easylive.pay.model;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class LotusLuckMsgInput {

    @NotNull
    private String userName;

    @NotNull
    private Long rate;

    @NotNull
    private String toName;

    @NotNull
    private String vid;

    @NotNull
    private Long ecoin = 0L;

    private Long explosionStatus;

    private Long totalEcoin;

    private Long nextExplosionPool;

    private boolean triggerExplosion;

    private Long goodsId;
}