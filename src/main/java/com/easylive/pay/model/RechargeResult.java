package com.easylive.pay.model;

import com.easylive.pay.entity.RechargeRecord;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RechargeResult {
    private String merchantId;
    private int appId;
    private long rmb;
    private long ecoin;
    private String tid;
    private String orderId;
    private boolean duplicated = false;

    public RechargeResult(RechargeRecord r) {
        if (r != null) {
            this.merchantId = r.getMerchantId();
            this.appId = r.getAppId();
            this.rmb = r.getRmb();
            this.ecoin = r.getEcoin();
            this.tid = r.getTid();
            this.orderId = r.getOrderId();
        }
    }
}
