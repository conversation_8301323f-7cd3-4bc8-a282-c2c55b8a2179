package com.easylive.pay.model;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GenericMap {
    private Map map;

    public GenericMap(String[] keys, Object[] values) {
        this.map = toMap(keys, values);
    }

    public GenericMap(List<String> keys, List<Object> values) {
        this.map = toMap(keys, values);
    }

    public static Map toMap(String key, Object value) {
        return toMap(new String[]{key}, new Object[]{value});
    }

    public static Map toMap(List<String> keys, List<Object> values) {
        return toMap(keys.toArray(new String[0]), values.toArray());
    }

    public static Map toMap(String[] keys, Object[] values) {
        int keysSize = (keys != null) ? keys.length : 0;
        int valuesSize = (values != null) ? values.length : 0;

        if (keysSize == 0 && valuesSize == 0) {
            // return mutable map
            return new HashMap();
        }

        if (keysSize != valuesSize) {
            throw new IllegalArgumentException(
                    "The number of keys doesn't match the number of values.");
        }

        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < keysSize; i++) {
            map.put(keys[i], values[i]);
        }

        return map;
    }

    public Map get() {
        return map;
    }
}
