package com.easylive.pay.model;

import com.easylive.pay.entity.AssetRecord;
import com.easylive.pay.entity.UserCoin;

import java.util.ArrayList;
import java.util.List;

public class AssetUpdateResult {
    private List<AssetRecord> records = new ArrayList<>(2);
    private List<UserCoin> userCoin = new ArrayList<>(2);
    private List<User> users = new ArrayList<>(2);
    private String tid;

    public AssetUpdateResult() {
    }

    public AssetUpdateResult(String tid, UserCoin uc) {
        this.userCoin.add(uc);
        this.tid = tid;
    }

    public AssetUpdateResult(String tid) {
        this.tid = tid;
    }

    public void addRecord(AssetRecord record) {
        this.records.add(record);
    }

    public void addAsset(UserCoin uc) {
        this.userCoin.add(uc);
    }

    public void addUser(User user) {
        users.add(user);
    }

    public UserCoin getUserCoin(int index) {
        return this.userCoin.get(index);
    }

    public User getUser(int index) {
        return this.users.get(index);
    }

    public AssetRecord getRecord(int index) {
        return this.records.get(index);
    }

    public List<User> getUsers() {
        return users;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }
}
