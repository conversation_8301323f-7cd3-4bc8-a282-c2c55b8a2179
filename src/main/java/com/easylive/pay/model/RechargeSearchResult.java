package com.easylive.pay.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4
 */
@Data
public class RechargeSearchResult {
    private RechargeSearchStat successStat = new RechargeSearchStat();
    private List<RechargeSearchResultItem> items;
    private int count;
    private int start;
    private int next;
    private int total;

    public RechargeSearchResult() {

    }

    public RechargeSearchResult(int start, int total, List<RechargeSearchResultItem> items ) {
        this.start = start;
        this.count = items.size();
        this.next = start + items.size();
        this.items = items;
        this.total = total;
    }

    @Data
    public static class RechargeSearchStat {
        private long count;
        private long ecoin;
        private long rmb;
        private long countByUser;
    }
}


