package com.easylive.pay.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/3/23 18:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CashoutOfflineDTO {

    private Long uid;
    private String idCard;
    private Date createTime;
    private Date lastTime;

}
