package com.easylive.pay.model;

import java.util.Date;

public class NobleInfo {
    private int level;
    private boolean liveStealth;
    private boolean listStealth;
    private boolean continued;
    private Date endTime;

    public int getLevel() {
        return level;
    }

    public boolean isLiveStealth() {
        return liveStealth;
    }

    public boolean isListStealth() {
        return listStealth;
    }

    public boolean isContinued() {
        return continued;
    }

    public Date getEndTime() {
        return endTime;
    }
}
