package com.easylive.pay.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class User {

    @JsonProperty(value = "uid")
    private long id;

    private String nickname;
    private String name;

    private String sessionId;

    private String gender;
    private String logoUrl;

    private long level;
    private long vip;
    private long vipLevel;
    private long anchorLevel;

    private String cid;
    private Long appSourceId;
    private Integer appId;
    private Integer nobleLevel;
    private int tid;


    public User() {
    }

    public User(long id) {
        this.id = id;
    }

    public User(String name) {
        this.name = name;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public long getId() {
        return id;
    }

    public void setId(long value) {
        this.id = value;
    }


    public String getName() {
        return name;
    }

    public void setName(String value) {
        this.name = value;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public long getLevel() {
        return this.level;
    }

    public void setLevel(long level) {
        this.level = level;
    }

    public long getVip() {
        return this.vip;
    }

    public void setVip(long vip) {
        this.vip = vip;
    }

    public long getVipLevel() {
        return this.vipLevel;
    }

    public void setVipLevel(long vipLevel) {
        this.vipLevel = vipLevel;
    }

    public long getAnchorLevel() {
        return this.anchorLevel;
    }

    public void setAnchorLevel(long anchorLevel) {
        this.anchorLevel = anchorLevel;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public Long getAppSourceId() {
        return appSourceId;
    }

    public void setAppSourceId(Long appSourceId) {
        this.appSourceId = appSourceId;
    }

    public Integer getNobleLevel() {
        return nobleLevel;
    }

    public void setNobleLevel(Integer nobleLevel) {
        this.nobleLevel = nobleLevel;
    }

    public int getTid() {
        return tid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }
} // class User
