package com.easylive.pay.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4
 */
@Data
public class RechargeSearchCriteria {
    private String orderId;
    private String platformOrderId;
    private List<Integer> appId;
    private List<String> name;
    private List<Long> uid;
    private List<Integer> platform;
    private Date startDate;
    private Date endDate;
    private Integer start;
    private Integer count;

    @Override
    public String toString() {

    }
}
