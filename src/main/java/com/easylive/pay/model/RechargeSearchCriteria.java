package com.easylive.pay.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4
 */
@Data
public class RechargeSearchCriteria {
    private String orderId;
    private String platformOrderId;
    private List<Integer> appId;
    private List<String> name;
    private List<Long> uid;
    private List<Integer> platform;
    private Date startDate;
    private Date endDate;
    private Integer start;
    private Integer count;

    public String briefString() {
        StringBuilder sb = new StringBuilder();

        if (orderId != null) {
            sb.append("orderId: ").append(orderId).append(", ");
        }

        if (platformOrderId != null) {
            sb.append("platformOrderId: ").append(platformOrderId).append(", ");
        }

        if (appId != null && !appId.isEmpty()) {
            sb.append("appId: ").append(appId).append(", ");
        }

        if (name != null && !name.isEmpty()) {
            sb.append("name: ");
            if (name.size() <= 5) {
                sb.append(name);
            } else {
                sb.append(name.subList(0, 5)).append("...").append(name.size()-5).append(" more");
            }
            sb.append(", ");
        }

        if (uid != null && !uid.isEmpty()) {
            sb.append("uid: ");
            if (uid.size() <= 5) {
                sb.append(uid);
            } else {
                sb.append(uid.subList(0, 5)).append("...").append(uid.size()-5).append(" more");
            }
            sb.append(", ");
        }

        if (platform != null && !platform.isEmpty()) {
            sb.append("platform: ").append(platform).append(", ");
        }

        if (startDate != null) {
            sb.append("startDate: ").append(startDate).append(", ");
        }

        if (endDate != null) {
            sb.append("endDate: ").append(endDate).append(", ");
        }

        if (start != null) {
            sb.append("start: ").append(start).append(", ");
        }

        if (count != null) {
            sb.append("count: ").append(count).append(", ");
        }

        // 移除最后的逗号和空格
        if (sb.length() > 2) {
            sb.setLength(sb.length() - 2);
        }

        return sb.toString();
    }
}
