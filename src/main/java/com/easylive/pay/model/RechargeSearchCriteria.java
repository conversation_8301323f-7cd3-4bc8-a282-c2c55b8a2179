package com.easylive.pay.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4
 */
@Data
public class RechargeSearchCriteria {
    private String orderId;
    private String platformOrderId;
    private List<Integer> appId;
    private List<String> name;
    private List<Long> uid;
    private List<Integer> platform;
    private Date startDate;
    private Date endDate;
    private Integer start;
    private Integer count;

    public String briefString() {
        StringBuilder sb = new StringBuilder();

        if (orderId != null) {
            sb.append("订单号: ").append(orderId).append(", ");
        }

        if (platformOrderId != null) {
            sb.append("平台订单号: ").append(platformOrderId).append(", ");
        }

        if (appId != null && !appId.isEmpty()) {
            sb.append("应用ID: ").append(appId).append(", ");
        }

        if (name != null && !name.isEmpty()) {
            sb.append("用户名: ");
            if (name.size() <= 5) {
                sb.append(name);
            } else {
                sb.append(name.subList(0, 5)).append("等").append(name.size()).append("个");
            }
            sb.append(", ");
        }

        if (uid != null && !uid.isEmpty()) {
            sb.append("用户ID: ");
            if (uid.size() <= 5) {
                sb.append(uid);
            } else {
                sb.append(uid.subList(0, 5)).append("等").append(uid.size()).append("个");
            }
            sb.append(", ");
        }

        if (platform != null && !platform.isEmpty()) {
            sb.append("平台: ").append(platform).append(", ");
        }

        if (startDate != null) {
            sb.append("开始时间: ").append(startDate).append(", ");
        }

        if (endDate != null) {
            sb.append("结束时间: ").append(endDate).append(", ");
        }

        if (start != null) {
            sb.append("起始位置: ").append(start).append(", ");
        }

        if (count != null) {
            sb.append("数量: ").append(count).append(", ");
        }

        // 移除最后的逗号和空格
        if (sb.length() > 2) {
            sb.setLength(sb.length() - 2);
        }

        return sb.toString();
    }
}
