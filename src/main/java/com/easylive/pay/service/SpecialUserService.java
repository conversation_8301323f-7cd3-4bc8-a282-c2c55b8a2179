package com.easylive.pay.service;

import com.easylive.pay.entity.CashoutWhiteEntity;
import com.easylive.pay.entity.Richer;
import com.easylive.pay.enums.bank.BankType;
import com.easylive.pay.model.AffirmCashoutWhite;
import com.easylive.pay.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2016/11/16
 */
@Service
@Slf4j
public class SpecialUserService {
    private static Set<Long> users = new HashSet<>();
    private static Map<Long, AffirmCashoutWhite> cashoutWhiteMap = new HashMap<>();


    @Autowired
    private CashoutService cashoutService;

    @Autowired
    private ConfigService configService;

    private Set<String> channels = new HashSet<>();

    public AffirmCashoutWhite getCashoutWhite(long uid) {
        return cashoutWhiteMap.get(uid);
    }

    public boolean isSpecialChannel(String channelId) {
        return channels.contains(channelId);
    }

    public boolean isSpecialUser(User user) {
        return isSpecialChannel(user.getCid()) || isSpecialUser(user.getId());
    }

    public boolean isSpecialUser(long uid) {
        return users.contains(uid);
    }

    public List<String> getSpecialChannels() {
        return new ArrayList<>(channels);
    }

    public List<Long> getSpecialUsers() {
        return new ArrayList<>(users);
    }

    @PostConstruct
    public void init() {
    }


    public void load() {

        String channels = configService.getAssetSpecialChannel();
        if (channels == null) channels = "";
        Set<String> newChannels = new HashSet<>(Arrays.asList(channels.split(",")));


        HashSet<Long> tmpSet = new HashSet<>();
        for (Richer richer : cashoutService.listRicher()) {
            tmpSet.add(richer.getUid());
        }


        HashMap<Long, AffirmCashoutWhite> tmpMap = new HashMap<>();
        for (CashoutWhiteEntity cw : cashoutService.listWhiteList()) {
            AffirmCashoutWhite affirmCashoutWhite = new AffirmCashoutWhite();
            affirmCashoutWhite.setUid(cw.getUid());
            affirmCashoutWhite.setLimitRmb(cw.getLimit());
            affirmCashoutWhite.setBankType(BankType.fromValue(cw.getPlatform()));
            tmpMap.put(cw.getUid(), affirmCashoutWhite);
        }

        this.channels = newChannels;
        users = tmpSet;
        cashoutWhiteMap = tmpMap;
    }
}
