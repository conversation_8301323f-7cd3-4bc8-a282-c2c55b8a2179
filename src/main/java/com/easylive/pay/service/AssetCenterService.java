package com.easylive.pay.service;


import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.mgs.BizException;
import com.easylive.pay.common.mgs.MgsServiceBuilder;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.model.AssetSignature;
import com.easylive.pay.model.CenterAssetTransaction;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.PostConstruct;

interface MgsAssetCenterService{
    @RequestLine("POST /asset/operation/unary")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    CenterAssetTransaction unaryOperation(@RequestParam("ak") @Param("ak") String accessKey,
                                          @RequestParam("tp") @Param("tp") String tp,
                                          @RequestParam("bizId") @Param("bizId") int bizId,
                                          @RequestParam("uid") @Param("uid") long uid,
                                          @RequestParam("value") @Param("value") long value,
                                          @RequestParam("desc") @Param("desc") String desc,
                                          @RequestParam("sn") @Param("sn") String signature);

    @RequestLine("POST /asset/operation/binary")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    CenterAssetTransaction binaryOperation(@RequestParam("ak") @Param("ak") String accessKey,
                                           @RequestParam("tp") @Param("tp") String timestamp,
                                           @RequestParam("bizId") @Param("bizId") int bizId,
                                           @RequestParam("uid1") @Param("uid1") long uid1,
                                           @RequestParam("value1") @Param("value1") long value1,
                                           @RequestParam("uid2") @Param("uid2") long uid2,
                                           @RequestParam("value2") @Param("value2") long value2,
                                           @RequestParam("desc") @Param("desc") String desc,
                                           @RequestParam("sn") @Param("sn") String signature);
}

enum AssetError  {


    /**
     * 业务错误码，15000-15999
     */
    E_ASSET(15000),
    E_ASSET_APP(15001),
    E_ASSET_AUTH(15002),
    E_ASSET_OPERATION(15003),
    E_ASSET_INVALID_BIZ(15004),

    E_ASSET_ECOIN_NOT_ENOUGH(15010),
    E_ASSET_BARLEY_NOT_ENOUGH(15011),
    E_ASSET_RICEROLL_NOT_ENOUGH(15012),
    E_ASSET_GCOIN_NOT_ENOUGH(15013),
    E_ASSET_NOBLE_COIN_NOT_ENOUGH(15014),

    E_ASSET_ECOIN_INVALID(15020),
    E_ASSET_RICEROLL_INVALID(15021),
    E_ASSET_BARLEY_INVALID(15022),
    E_ASSET_GCOIN_INVALID(15023),

    E_RECHARGE(15100),
    E_RECHARGE_INVALID_PLATFORM(15101)
    ;



    private final int code;
    private HttpStatus status;

    AssetError(final int code) {
        this.code = code;
        status = HttpStatus.UNPROCESSABLE_ENTITY;
    }

    AssetError(final int value, HttpStatus status) {
        this.code = value;
        this.status = status;
    }


    public int getCode() {
        return code;
    }


    public HttpStatus getHttpStatus() {
        return status;
    }


    public String getName() {
        return null;
    }


    public String getMessage() {
        return null;
    }


    public boolean isOverrideMessage() {
        return false;
    }
}

@Service
public class AssetCenterService {

    private static Logger logger = LoggerFactory.getLogger(UserService.class);

    private MgsAssetCenterService mgsAssetCenterService;

    @Autowired
    private URLConfig urlConfig;

    @Value("${asset.signature.name}")
    public String appId;

    @Value("${asset.signature.key}")
    private String appKey;


    @PostConstruct
    public void init() {
        mgsAssetCenterService = MgsServiceBuilder.build(urlConfig.getAssetServiceUrl(), MgsAssetCenterService.class);
    }

    public CenterAssetTransaction unaryOperation(
            int bizId, Long uid, Long value, String description
    ) {
        if( description == null )
            description = "";
        AssetSignature signature = new AssetSignature(appId, appKey)
                .addParameter("bizId", String.valueOf(bizId))
                .addParameter("uid", String.valueOf(uid))
                .addParameter("value", String.valueOf(value))
                .addParameter("desc", description)
                .generate();
        try {
            return mgsAssetCenterService.unaryOperation(signature.getAppId(), signature.getTimestamp(), bizId, uid, value, description, signature.getSignature());
        } catch (BizException e) {
            if(e.getCode() == AssetError.E_ASSET_ECOIN_NOT_ENOUGH.getCode()){
                throw new ResponseException(ResponseError.E_ASSET_ECOIN_NOT_ENOUGH);
            }else{
                logger.error("Request asset unary operation error, uid {} bizid {} message:{}",uid, bizId, e.getMessage());
                throw new ResponseException(ResponseError.E_SERVICE);
            }
        } catch (Exception e) {
            logger.error("Request asset unary operation error, uid {} bizid {} message:{}",uid, bizId, e.getMessage());

            throw new ResponseException(ResponseError.E_SERVER);
        }


    }

    public CenterAssetTransaction binaryOperation(int bizId, Long uid1, Long value1, Long uid2, Long value2, String description) {
        if( description == null )
            description = "";

        AssetSignature signature = new AssetSignature(appId, appKey)
                .addParameter("bizId", String.valueOf(bizId))
                .addParameter("uid1", String.valueOf(uid1))
                .addParameter("value1", String.valueOf(value1))
                .addParameter("uid2", String.valueOf(uid2))
                .addParameter("value2", String.valueOf(value2))
                .addParameter("desc", description)
                .generate();

        try {
            return mgsAssetCenterService.binaryOperation(signature.getAppId(), signature.getTimestamp(), bizId,
                    uid1, value1, uid2, value2, description, signature.getSignature());
        } catch (BizException e) {
            if(e.getCode() == AssetError.E_ASSET_ECOIN_NOT_ENOUGH.getCode()){
                throw new ResponseException(ResponseError.E_ASSET_ECOIN_NOT_ENOUGH);
            }else{
                logger.error("Request asset binary operation error, uid {} bizid {} message:{}",uid1, bizId, e.getMessage());
                throw new ResponseException(ResponseError.E_SERVICE);
            }
        } catch (Exception e) {
            logger.error("Request asset binary operation error, uid {} bizid {} message:{}",uid1, bizId, e.getMessage());
            throw new ResponseException(ResponseError.E_SERVER);
        }

    }

}
