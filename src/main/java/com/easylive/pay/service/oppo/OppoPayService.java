package com.easylive.pay.service.oppo;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.OppoConfig;
import com.easylive.pay.entity.RechargeOption;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.model.OppoUserResult;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.service.AppService;
import com.easylive.pay.service.PayService;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.utils.HttpUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/10
 */
@Slf4j
@Service
public class OppoPayService {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private OppoConfig oppoConfig;

    @Autowired
    private AppService appService;


    @Autowired
    private PayService payService;

    private static Logger logger = LoggerFactory.getLogger(OppoPayService.class);

    /**
     * oppo服务端验签固定字段
     */
    private static final String OAUTH_CONSUMER_KEY = "oauthConsumerKey";
    private static final String OAUTH_TOKEN = "oauthToken";
    private static final String OAUTH_SIGNATURE_METHOD = "oauthSignatureMethod";
    private static final String OAUTH_TIMESTAMP = "oauthTimestamp";
    private static final String OAUTH_NONCE = "oauthNonce";
    private static final String OAUTH_VERSION = "oauthVersion";
    private static final String CONST_SIGNATURE_METHOD = "HMAC-SHA1";
    private static final String CONST_OAUTH_VERSION = "1.0";
    private static final String STR_OAUTH_NONCE = "yizhibo";

    @PostConstruct
    public void init() {
    }

    public List<RechargeOption> getRechargeOption() {
        return rechargeService.getOptionWithScale(oppoConfig.getRechargeScale());
    }

    @Transactional
    public OppoOrder createOrder(long uid, String appName, long amount, String ssoId, String token, MobilePlatform mobilePlatform, String clientIp) {
        log.info("Create oppo pay order: {}, {}, {}, {}", uid, mobilePlatform, appName, clientIp);

        if (!validateClient(ssoId, token)) {
            throw new ResponseException(ResponseError.E_OPPO_AUTH, "client validation failed");
        }

        int appId = appService.getAppIdByName(appName);
        OrderInfo order = new OrderInfo(appId, uid, amount, PayPlatform.PPF_OPPO_PAY, mobilePlatform, clientIp);
        RechargeRecord rechargeRecord = rechargeService.createRechargeRecord(order);
        OppoOrder oppoOrder = new OppoOrder();
        oppoOrder.setOrderId(rechargeRecord.getOrderId());

        //定义产品名称及描述
        String detail = payService.getProductName(PayPlatform.PPF_OPPO_PAY, appName, amount);

        oppoOrder.setProductName(detail);
        oppoOrder.setProductDesc(detail);
        oppoOrder.setNotifyUrl(oppoConfig.getNotifyUrl());
        return oppoOrder;
    }

    /**
     * 验证OPPO支付订单回调，完成支付
     */
    @Transactional
    public void validOrder(OppoNotifyRequest data) {
        log.info("[RECHARGE]validate oppo pay order: {}", data);

        if (!validateSign(data)) {
            throw new ResponseException(ResponseError.E_PARAM, "签名错误");
        }

        String orderId = data.getPartnerOrder();
        RechargeRecord rechargeRecord = rechargeService.getRecordByOrderIdWithLock(orderId);
        if (rechargeRecord == null) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "错误充值订单号");
        }

        if (rechargeRecord.getStatus() != OrderStatus.ORS_CREATE.getValue()) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_STATUS, "错误订单状态");
        }

        long rmb = rechargeRecord.getRmb();
        long ecoin = oppoConfig.getRechargeScale().multiply(new BigDecimal(rmb / 10)).longValue();
        rechargeService.rechargeTransaction(rechargeRecord, rechargeRecord.getUid(), data.getNotifyId(), rmb, ecoin);
    }


    /**
     * 用户验证
     *
     * @param ssoId
     * @param token
     * @return
     */
    private boolean validateClient(String ssoId, String token) {
        //放到用户登录验证，客户端破解了，支付可以越过验证
        String baseStr = generateBaseString(token, oppoConfig.getAppKey());
        if (baseStr.isEmpty()) {
            return false;
        }

        String rateSign = generateSign(baseStr, oppoConfig.getAppSecret());
        if (rateSign.isEmpty()) {
            return false;
        }

        Map<String, Object> args = new HashMap<>();
        args.put("fileId", ssoId);
        args.put("token", HttpUtils.URLDecode(token));

        Map<String, String> headers = new HashMap<>();
        headers.put("param", baseStr);
        headers.put("oauthSignature", rateSign);

        String response = HttpUtils.doGet(oppoConfig.getUserCheckUrl(), args, headers);

        GsonBuilder builder = new GsonBuilder().setDateFormat("yyyy-MM-dd");
        Gson gson = builder.create();
        OppoUserResult userInfo = gson.fromJson(response, OppoUserResult.class);
        if (null == userInfo) {
            logger.error("gson from json fail. response:{}", response);
            return false;
        }

        if (userInfo.getResultCode().equals(OppoUserResult.OK_CODE) && userInfo.getSsoid().equals(ssoId)) {
            return true;
        }

        return false;
    }


    private boolean validateSign(OppoNotifyRequest data) {
        String content = getBaseString(data);
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = Base64.decodeBase64(oppoConfig.getNotifyPublicKey());
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));

            java.security.Signature signature = java.security.Signature.getInstance("SHA1WithRSA");

            signature.initVerify(pubKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));
            return signature.verify(Base64.decodeBase64(data.getSign()));
        } catch (Exception e) {
            log.error(" 验证签名出错.", e);
        }
        return false;
    }

    private String getBaseString(OppoNotifyRequest ne) {

        return "notifyId=" + ne.getNotifyId() +
                "&partnerOrder=" + ne.getPartnerOrder() +
                "&productName=" + ne.getProductName() +
                "&productDesc=" + ne.getProductDesc() +
                "&price=" + ne.getPrice() +
                "&count=" + ne.getCount() +
                "&attach=" + ne.getAttach();
    }

    /**
     * 验签生成基串
     *
     * @param authToken
     * @param appKey
     * @return
     */
    private static String generateBaseString(String authToken, String appKey) {
        StringBuilder sb = new StringBuilder();
        String timestamp = String.valueOf(System.currentTimeMillis());
        try {
            sb.append(OAUTH_CONSUMER_KEY).
                    append("=").append(appKey).append("&").
                    append(OAUTH_TOKEN).
                    append("=").append(HttpUtils.URLEncode(authToken)).append("&").
                    append(OAUTH_SIGNATURE_METHOD).
                    append("=").append(CONST_SIGNATURE_METHOD).append("&").
                    append(OAUTH_TIMESTAMP).
                    append("=").append(timestamp).append("&").
                    append(OAUTH_NONCE).
                    append("=").
                    append(STR_OAUTH_NONCE).append("&").
                    append(OAUTH_VERSION).
                    append("=").append(CONST_OAUTH_VERSION).append("&");
            return sb.toString();
        } catch (Exception e) {
        }

        return "";
    }

    /**
     * @param baseStr
     * @param appSecret
     * @return
     */
    private static String generateSign(String baseStr, String appSecret) {
        String oauthSignatureKeys = appSecret + "&";
        byte[] hex = new HmacUtils(HmacAlgorithms.HMAC_SHA_1, oauthSignatureKeys.getBytes()).hmac(baseStr.getBytes());
        return HttpUtils.URLEncode(String.valueOf(com.easylive.pay.utils.Base64.encode((hex))));

    }

}
