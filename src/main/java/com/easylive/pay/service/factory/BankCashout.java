package com.easylive.pay.service.factory;

import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.CashoutRecordAppStatus;
import com.easylive.pay.model.cashout.SignContractModel;

import java.util.List;
import java.util.Map;

/**
 * 银行提现接口
 *
 * <AUTHOR>
 * @date 2020/7/14
 */
public interface BankCashout {

    /**
     * 更新会员信息
     * @param model
     * @return
     */
    default <T> T updateMember(SignContractModel model) { return null;}


    /**
     * 客户信息签约
     *
     * @return bool
     */
    <T> T signContract(SignContractModel model);

    /**
     * 提现
     */
    void doWithdraw(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType);

    /**
     * 处理提现中的订单
     */
    void processDealingOrder(CashoutRecordQueue recordQueue);

    /**
     * 处理订单状态
     */
    void processOrder(CashoutRecordQueue record);

    /**
     * 处理成功订单，退款情况处理
     */
    void processSuccessOrder(CashoutRecordQueue record,boolean isCheck);
    /**
     * 支付通知处理
     * @param params
     * @return
     */
    default String payNotifyProcess(Map<String, String> params) {
        return null;
    }

    /**
     * 批量处理提现中的订单
     */
    default void batchIntegrateOrder (List<CashoutRecordQueue> recordQueues) {
        return;
    }

    /**
     * 提现订单状态
     * @return
     */
    default int getRecordStatus(){
        return CashoutRecordAppStatus.INTO_ACCOUNTING.getValue();
    }

}
