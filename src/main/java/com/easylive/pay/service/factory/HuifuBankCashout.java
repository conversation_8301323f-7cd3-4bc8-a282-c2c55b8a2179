package com.easylive.pay.service.factory;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.huifu.HuifuCashRecord;
import com.easylive.pay.service.huifu.HuifuCashStatus;
import com.easylive.pay.service.huifu.HuifuPayService;
import com.easylive.pay.service.huifu.HuifuUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 税筹
 *
 * <AUTHOR>
 * @date 2020/7/14
 */
@Component("HuifuBankCashout")
@Slf4j
public class HuifuBankCashout extends AbstractBankCashout implements BankCashout {

    @Autowired
    private HuifuPayService huifuPayService;

    @Autowired
    private CashoutService cashoutService;

    /**
     * 添加用户到会员信息
     *
     * @param sign
     * @return
     */
    @Override
    public String signContract(SignContractModel sign) {
        log.info("[HuifuBankCashout][signContract] sign contract. sign={}", sign.toString());
        HuifuUser huifuUser = huifuPayService.getUser(sign.getUid(), sign.getPhone(), sign.getAccountName(), sign.getIdCard(), sign.getAccountNo());
        return huifuUser == null ? null : huifuUser.getMemberId();
    }

    @Override
    public Boolean updateMember(SignContractModel model) {
        log.info("[HuifuBankCashout][updateMember] update member. model={}", model.toString());
        return huifuPayService.updateUser(model.getUid(), model.getPhone(), model.getAccountName(), model.getIdCard(), model.getAccountNo());
    }

    @Override
    protected void doWithDrawActual(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {
        long rmb = getWithdrawAmount(cashoutRecord.getApp_id(), cashoutRecord.getRmb());
        log.info("[HuifuBankCashout][doWithdraw] do withdraw. cashoutRecord={}, bankCard={}, idCard={}, rmb = {}", cashoutRecord, bankCard.toString(), idCard, rmb);
        HuifuCashRecord record = huifuPayService.withdraw(cashoutRecord.getOrderid(), cashoutRecord.getUid(), rmb, phone, bankCard.getAccountName(), idCard, bankCard.getAccountNo());
        log.info("[HuifuBankCashout][doWithdraw] cash result:{}", record);
        int status = record.getStatus();

        String code = record.getErrCode();
        String msg = record.getErrMsg();

        if (status == HuifuCashStatus.CREATED.getValue()) {
            throw new ResponseException(ResponseError.E_CASHOUT, "提现失败:" + record.getErrMsg());
        }
        cashoutService.updateRecordResultByRecordId(cashoutRecord.getId(), code, msg, code);
    }

    @Override
    @Transactional
    public void processOrder(CashoutRecordQueue record) {
        log.info("HuifuBankCashout processOrder process order. record={}", record.toString());

        HuifuCashRecord cashRecord = huifuPayService.getByOrderId(record.getOrderid());
        if (cashRecord == null) {
            log.warn("Order id not found: {}", record.getOrderid());
            return;
        }

        String code = cashRecord.getErrCode();
        String msg = cashRecord.getErrMsg();
        HuifuCashStatus status = HuifuCashStatus.fromValue(cashRecord.getStatus());
        if (status == null)
            return;

        switch (status) {
            case CASH_SUCCESS:
                log.info("Cashout success:{}", record.getOrderid());
                cashoutService.updateCashoutSuccess(record, code, msg, code);
                break;
            case CASH_FAILED:
            case ERROR:
            case PAY_FAILED:
                log.info("Cashout failed with status:{}, {}", record.getOrderid(), status);
                cashoutService.updateCashoutFail(record, code, msg, code);
                break;
            default:
                break;
        }

    }

    @Override
    @Transactional
    public void processDealingOrder(CashoutRecordQueue recordQueue) {
        processOrder(recordQueue);
    }

}
