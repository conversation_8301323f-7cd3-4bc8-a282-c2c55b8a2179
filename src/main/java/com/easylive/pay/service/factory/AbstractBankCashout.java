package com.easylive.pay.service.factory;

import com.easylive.pay.common.Constants;
import com.easylive.pay.config.CashConfig;
import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Objects;

@Slf4j
public abstract class AbstractBankCashout implements BankCashout{

    @Autowired
    private CashConfig cashConfig;

    @Override
    public void doWithdraw(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {
        log.info("[doWithdraw] do withdraw. cashoutRecord={}, bankCard={}, idCard={}", cashoutRecord.toString(), bankCard.toString(), idCard);
        //计算提现金额，各自服务要实现一次计算rmb，这个坑注意下, 这里先提现后记录，所有不能获取record里面的rmb
        long withDrawAmount = getWithdrawAmount(cashoutRecord.getApp_id(), cashoutRecord.getRmb());
        doWithDrawActual(cashoutRecord, bankCard, idCard, phone, fkType);
        cashoutRecord.setRmb(withDrawAmount);
    }

    protected abstract void doWithDrawActual(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType);

    public long getWithdrawAmount(int appId, long withDrawAmount) {
        if (!Objects.equals(appId, Constants.APP_ID_HIBI)){
            long beginTime = DateUtils.parse(cashConfig.getWithDrawExtraBeginTime()).getTime();
            if (System.currentTimeMillis() >= beginTime) {
                //当提现app为足迹app时，需要按照比例额外扣除费用
                withDrawAmount = withDrawAmount - BigDecimal.valueOf(withDrawAmount).multiply(cashConfig.getRateOfWithDrawExtra()).longValue();
            }
        }
        return withDrawAmount;
    }

    @Override
    public void processSuccessOrder(CashoutRecordQueue record,boolean isCheck) {
        log.error("Default process success order,not implemented");
    }
}
