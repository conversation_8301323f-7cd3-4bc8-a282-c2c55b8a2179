package com.easylive.pay.service.factory;

import com.alibaba.fastjson.JSON;
import com.easylive.pay.dao.CashoutDao;
import com.easylive.pay.dao.jpa.YunzhanghuAppConfigRepository;
import com.easylive.pay.dao.jpa.YunzhanghuCashoutRecordRepository;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.JiuHeOrderSettleStatusEnum;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.cashout.jiuhe.JiuHeWithdrawOrderVO;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.ConfigService;
import com.easylive.pay.service.yzh.YunzhanghuService;
import com.yunzhanghu.sdk.base.YzhConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;

@Component("YZHCashout")
@Slf4j
public class YZHCashout extends AbstractBankCashout implements BankCashout {

    @Autowired
    private YunzhanghuService yunzhanghuService;

    @Autowired
    private CashoutService cashoutService;

    @Override
    public String signContract(SignContractModel model) {
        return yunzhanghuService.signUser(model.getUid(), model.getAccountName(), model.getIdCard());
    }

    @Override
    public Boolean updateMember(SignContractModel model) {
        log.info("[YzhCashout][updateMember] update member. model={}", model.toString());
        //yunzhanghuService.updateUser(model.getUid(), model.getAccountName(), model.getIdCard());
        return true;
    }

    @Override
    protected void doWithDrawActual(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {
        long rmb = getWithdrawAmount(cashoutRecord.getApp_id(), cashoutRecord.getRmb());

        log.info("[YunzhanghuCashoutService][doWithdraw] cashoutRecord={}, bankCard={}, idCard={}, phone={} rmb:{}",
                cashoutRecord, bankCard, idCard, phone, rmb);
        yunzhanghuService.withdraw(cashoutRecord, bankCard, idCard, phone, rmb);
        cashoutService.updateRecordResultByRecordId(cashoutRecord.getId(), "ok", "成功提交", "已提交");
    }

    @Override
    public void processDealingOrder(CashoutRecordQueue recordQueue) {
        processOrder(recordQueue);
    }

    /**
     * 订单查询处理
     * @param recordQueue
     */
    @Override
    public void processOrder(CashoutRecordQueue recordQueue) {
        log.info("[YunzhanghuCashoutService][processOrder] record={}", recordQueue);

        YunzhanghuService.OrderData orderData = yunzhanghuService.buildQueryOrderData(recordQueue.getOrderid());
        yunzhanghuService.syncOrderStatus(recordQueue.getOrderid(), recordQueue, orderData);
    }


    @Override
    public String payNotifyProcess(Map<String, String> params) {
        return yunzhanghuService.validOrder(params);
    }

}