package com.easylive.pay.service.factory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.JiuheCashConfig;
import com.easylive.pay.dao.CashoutDao;
import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.CashoutRecordAppStatus;
import com.easylive.pay.enums.JiuHeOrderSettleStatusEnum;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.cashout.jiuhe.*;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 玖合提现
 *
 * <AUTHOR>
 * @date 2020/7/14
 */
@Component("JiuHeBankCashout")
@Slf4j
public class JiuHeBankCashout extends AbstractBankCashout implements BankCashout {

    @Autowired
    private JiuheCashConfig config;

    @Autowired
    private CashoutService cashoutService;


    @Autowired
    private CashoutDao cashoutDao;

    @Autowired
    private StringRedisTemplate redisTemplate;

    private final String JIUHE_NOTIFY_BATCH = "jiuhe_notify_batch_";

    @Override
    public String signContract(SignContractModel model) {
        log.info("[JiuHe][signContract] sign contract. sign={}", model.toString());

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("name", model.getAccountName());
        requestMap.put("idNo", model.getIdCard());
        requestMap.put("mobile", AppStringUtils.getMobile(model.getPhone()));
        requestMap.put("accountNo", model.getAccountNo());

        String alipayAccount = model.getZfbAccount();
        if (alipayAccount != null && !alipayAccount.isEmpty()) {
            requestMap.put("alipayAccount", alipayAccount);
        }

        // 暂时不提交（上传文件是有问题的）
//        if (model.getBack() != null && model.getFront() != null) {
//            CompletableFuture<String> userFrontToken = CompletableFuture.supplyAsync(() -> getUploadFileToken(model.getFront(), "USER_IDFRONT"));
//            CompletableFuture<String> userBackToken = CompletableFuture.supplyAsync(() -> getUploadFileToken(model.getBack(), "USER_IDBACK"));
//            requestMap.put("frontImg", userFrontToken.join());
//            requestMap.put("backImg", userBackToken.join());
//        }

        String requestDataJson = JsonUtils.toString(requestMap);
        log.info("[JiuHe][signContract] sign contract. requestDataJson={}", requestDataJson);
        String data = Base64.encode(requestDataJson.getBytes(StandardCharsets.UTF_8));

        String uri = config.getAddMemberUrl();

        String responseData = buildJiuHeRequest(uri, data);
        HashMap response = parseResponse(responseData, false);

        log.info("[JiuHe][signContract] sign contract. response:{}", response);
        return (String) response.get("id");
    }

    /**
     * @param url
     * @param scene USER_IDFRONT,USER_IDBACK
     * @return
     */
    private String getUploadFileToken(String url, String scene) {
        byte[] urlBinaryContent = FileUtils.getURLBinaryContentNIO(url);

        String md5 = DigestUtils.md5Hex(urlBinaryContent);

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("scene", scene);
        requestMap.put("md5", md5);

        String requestDataJson = JsonUtils.toString(requestMap);
        log.info("[JiuHe][signContract] upload file. requestDataJson={}", requestDataJson);
        String data = Base64.encode(requestDataJson.getBytes(StandardCharsets.UTF_8));

        String uri = config.getUploadFile();

        try {
            String responseData = buildJiuHeUploadRequest(uri, data, urlToFile(url));
            HashMap response = parseResponse(responseData, false);

            log.info("[JiuHe][signContract] upload file. response:{}", response);
            return (String) response.get("file");
        } catch (Exception e) {
            log.error("{}", e);
        }

        return "";
    }

    public static File urlToFile(String urlString) {
        try {
            URL url = new URL(urlString);

            // 打开URL连接
            try (InputStream in = url.openStream()) {
                // 使用 Files 类将 InputStream 写入到本地文件
                Path tempFile = Files.createTempFile("tempFile", ".png");
                Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);

                // 获取 File 对象
                return tempFile.toFile();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }


    @Override
    public Boolean updateMember(SignContractModel model) {

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("id", model.getPlatformId());
        requestMap.put("name", model.getAccountName());
        requestMap.put("idNo", model.getIdCard());
        requestMap.put("mobile", AppStringUtils.getMobile(model.getPhone()));

        String bankAccount = model.getAccountNo();
        if (bankAccount != null && !bankAccount.isEmpty()) {
            requestMap.put("accountNo", bankAccount);
        }

        String alipayAccount = model.getZfbAccount();
        if (alipayAccount != null && !alipayAccount.isEmpty()) {
            requestMap.put("alipayAccount", alipayAccount);
        }

        String requestDataJson = JsonUtils.toString(requestMap);
        log.info("[JiuHe][updateMember] update contract. requestDataJson={}", requestDataJson);
        String data = Base64.encode(requestDataJson.getBytes(StandardCharsets.UTF_8));

        String uri = config.getAddMemberUrl();

        String responseData = buildJiuHeRequest(uri, data);
        HashMap response = parseResponse(responseData, false);

        log.info("[JiuHe][updateMember] sign contract. response:{}", response);
        return true;
    }

    /**
     * 玖合为整合订单批次提现
     */
    @Override
    protected void doWithDrawActual(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {

    }

    @Override
    public void doWithdraw(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {
        log.info("[JiuHe][doWithdraw] do withdraw. order_id={}", cashoutRecord.getOrderid());
    }

    public String buildWithdrawRequestParam(List<CashoutRecordQueue> recordQueues) {
        String tradeNo = cashoutService.getTradeNo("001");
        JiuHeWithdrawLotDTO lotDTO = new JiuHeWithdrawLotDTO(config.getProjectId(), config.getFundId(), tradeNo);
        List<JiuHeWithdrawOrderDTO> orderDTOS = recordQueues.stream().map(recordQueue -> {
            JiuHeWithdrawOrderDTO orderDTO = new JiuHeWithdrawOrderDTO(recordQueue);
            //扣出额外费用
            long withdrawAmount = getWithdrawAmount(recordQueue.getApp_id(), recordQueue.getRmb());
//            long withdrawAmount = recordQueue.getRmb();
            double amount = BigDecimal.valueOf(withdrawAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
            orderDTO.setAmount(amount);
            return orderDTO;
        }).collect(Collectors.toList());
        lotDTO.setOrders(orderDTOS);
        String dataJson = JsonUtils.toString(lotDTO);
        return Base64.encode(dataJson.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 构建玖合上传请求
     *
     * @param uri
     * @param requestData
     * @return
     */
    public String buildJiuHeUploadRequest(String uri, String requestData, File file) {
        JiuHeRequestUploadDTO uploadDTO = new JiuHeRequestUploadDTO(config.getAppId(), requestData, file);
        String sortString = UrlUtils.createSortString(uploadDTO.toHashMap());
        String sign = paramSign(uri, sortString);
        uploadDTO.setSign(sign);
        String requestBody = JsonUtils.toString(uploadDTO);
        log.info("[JiuHe][buildJiuHeUploadRequest] uri: {} requestBody: {}", uri, requestBody);
        String response = HttpUtils.doPostUpload(config.getDomain() + uri, requestBody, file);
        log.info("[JiuHe][buildJiuHeUploadRequest] uri: {} responseBody: {}", uri, response);
        return response;
    }

    /**
     * 构建玖合请求
     *
     * @param uri
     * @param requestData
     * @return
     */
    public String buildJiuHeRequest(String uri, String requestData) {
        JiuHeRequestBaseDTO baseDTO = new JiuHeRequestBaseDTO(config.getAppId(), requestData);
        String sortString = UrlUtils.createSortString(baseDTO.toHashMap());
        String sign = paramSign(uri, sortString);
        baseDTO.setSign(sign);
        String requestBody = JsonUtils.toString(baseDTO);
        log.info("[JiuHe][buildJiuHeRequest] uri: {} requestBody: {}", uri, requestBody);
        String response = HttpUtils.doPost(config.getDomain() + uri, requestBody, ContentType.APPLICATION_JSON);
        log.info("[JiuHe][buildJiuHeRequest] uri: {} responseBody: {}", uri, response);
        return response;
    }

    public <T> T parseResponse(String response, boolean isVerifySign) {
        String responseJsonData = getResponseData(response, isVerifySign);
        Map<String, Object> dataMap = JsonUtils.fromString(responseJsonData, Map.class);
        if ((int) dataMap.get("code") != 0) {
            throw new ResponseException(ResponseError.E_GASH_ERROR, (String) dataMap.get("msg"));
        }
        return (T) dataMap.get("data");
    }

    public String getResponseData(String response, boolean isVerifySign) {
        if (response == null || response.isEmpty()) {
            throw new ResponseException(ResponseError.E_GASH_ERROR, "提现服务器错误");
        }
        Map<String, Object> map = JsonUtils.fromString(response, Map.class);
        String respSign = (String) map.get("sign");
        String respData = (String) map.get("data");
        if (respSign == null || respData == null) {
            log.error("[JiuHe][getResponseData] error code : {}, msg: {}", map.get("code"), map.get("msg"));
            throw new ResponseException(ResponseError.E_GASH_ERROR, (String) map.get("msg"));
        }
        if (isVerifySign) {
            String sortString = UrlUtils.createSortString(map);
            if (!paramSignVerify(sortString, respSign)) {
                throw new ResponseException(ResponseError.E_CASHOUT_SIGN, "签名验证失败");
            }
        }
        String responseJsonData = new String(Base64.decode(respData), StandardCharsets.UTF_8);
        log.info("[JiuHe][getResponseData] responseJsonData: {}", responseJsonData);
        return responseJsonData;
    }

    @Override
    public void batchIntegrateOrder(List<CashoutRecordQueue> recordQueues) {
        //构建请求参数
        String requestParam = buildWithdrawRequestParam(recordQueues);
        //调用三方付款
        String response = buildJiuHeRequest(config.getBatchPay(), requestParam);
        //解析结果
        String responseData = getResponseData(response, false);
        JSONObject responseJson = JSON.parseObject(responseData);
        Integer code = responseJson.getInteger("code");
        String dataString = responseJson.getString("data");
        if (code != 0) {
            try {
                handleBatchError(code, dataString, responseJson.getString("msg"), recordQueues);
            } catch (Exception e) {
                log.error("[JiuHe][batchIntegrateOrder] error batch handle exception : {}", dataString, e);
                return;
            }
            if (!CollectionUtils.isEmpty(recordQueues)) {
                log.info("[JiuHe][batchIntegrateOrder] remove error queue data, try again");
                batchIntegrateOrder(recordQueues);
            } else {
                log.info("[JiuHe][batchIntegrateOrder] this batch no correct data");
            }
            return;
        }
        JSONObject dataJson = JSON.parseObject(dataString);
        log.info("[JiuHe][batchIntegrateOrder] batch_id = {}, orders = {}", dataJson.get("id"), dataJson.getString("orders"));
        List<JiuHeWithdrawOrderVO> orderVOList = JSON.parseArray(dataJson.getString("orders"), JiuHeWithdrawOrderVO.class);
        Map<String, CashoutRecordQueue> recordQueueMap = recordQueues.stream().collect(Collectors.toMap(CashoutRecordQueue::getOrderid, v -> v));
        //更新订单状态 目前所有的订单app_status都为5
        for (JiuHeWithdrawOrderVO orderVO : orderVOList) {
            CashoutRecordQueue recordQueue = recordQueueMap.get(orderVO.getOutOrderNo());
            try {
                recordQueue.setPforderid(orderVO.getId());
                if (JiuHeOrderSettleStatusEnum.SUCC.name().equals(orderVO.getStatus())) {
                    log.info("[JiuHe][batchIntegrateOrder] order_id = {} , name = {}, accountNo = {}, withdraw success", orderVO.getOutOrderNo(), recordQueue.getAccount_name(), recordQueue.getAccount_no());
                    cashoutService.transactionSuccess(recordQueue, "0", "成功", orderVO.getStatus(), true);
                } else if (isSettleFail(orderVO.getStatus())) {
                    log.error("[JiuHe][batchIntegrateOrder] order_id = {} , name = {}, accountNo = {}, withdraw fail, order_status = {}, reason = {}", orderVO.getOutOrderNo(), recordQueue.getAccount_name(), recordQueue.getAccount_no(), orderVO.getStatus(), orderVO.getNote());
                    cashoutService.transactionFail(recordQueue, "0", orderVO.getNote(), orderVO.getStatus());
                } else {
                    log.info("[JiuHe][batchIntegrateOrder] order_id = {} , name = {}, accountNo = {}, withdraw in progress", orderVO.getOutOrderNo(), recordQueue.getAccount_name(), recordQueue.getAccount_no());
                    cashoutDao.updateRecordAndQueueByRecordId(recordQueue.getRecord_id(), orderVO.getId(), CashoutRecordAppStatus.INTO_ACCOUNTING.getValue(), CashoutRecordAppStatus.ACCOUNT_REQUESTING.getValue());
                }
            } catch (Exception e) {
                log.error("[JiuHe][batchIntegrateOrder] update order status error, order_id = {}, name = {}, accountNo = {}", orderVO.getOutOrderNo(), recordQueue.getAccount_name(), recordQueue.getAccount_no(), e);
                try {
                    cashoutDao.updateRecordAndQueueByRecordId(recordQueue.getRecord_id(), orderVO.getId(), CashoutRecordAppStatus.INTO_ACCOUNTING.getValue(), CashoutRecordAppStatus.ACCOUNT_REQUESTING.getValue());
                } catch (Exception exception) {
                    log.error("[JiuHe][batchIntegrateOrder] update order status compensate fail, order_id = {}, name = {}, accountNo = {}", orderVO.getOutOrderNo(), recordQueue.getAccount_name(), recordQueue.getAccount_no(), exception);
                }
            }
        }
    }

    public void handleBatchError(int code, String dataString, String msg, List<CashoutRecordQueue> recordQueues) {
        if (StringUtils.isEmpty(dataString)) {
            log.error("[JiuHe][batchIntegrateOrder] error batch msg = {}, remove all element", msg);
            recordQueues.forEach(e -> cashoutService.transactionFail(e, String.valueOf(code), msg, JiuHeOrderSettleStatusEnum.COMMITFAIL.name()));
            recordQueues.clear();
            return;
        }
        String data = JSON.parseArray(dataString).getString(0);
        if (StringUtils.isEmpty(data)) {
            log.error("[JiuHe][batchIntegrateOrder] batch error data is error");
            return;
        }
        List<JSONObject> objectList = JSON.parseArray(data, JSONObject.class);
        List<CashoutRecordQueue> errorRecordQueues = new ArrayList<>(objectList.size());
        for (JSONObject object : objectList) {
            Integer row = object.getInteger("row");
            String accountName = object.getString("accountName");
            String errMsg = object.getString("errMsg");
            CashoutRecordQueue recordQueue = recordQueues.get(row - 1);
            if (recordQueue.getAccount_name().equals(accountName)) {
                //警告：方法内已做了（失败/成功）处理，不再处理
                if (!processNotSuccessfulOrder(recordQueue)) {
                    cashoutService.transactionFail(recordQueue, String.valueOf(code), errMsg, JiuHeOrderSettleStatusEnum.COMMITFAIL.name());
                }
                errorRecordQueues.add(recordQueue);
            }
        }
        if (!errorRecordQueues.isEmpty()) {
            recordQueues.removeAll(errorRecordQueues);
            log.info("[JiuHe][batchIntegrateOrder] remove fail order data = {}", JSON.toJSONString(errorRecordQueues));
        }
    }

    @Override
    public int getRecordStatus() {
        return CashoutRecordAppStatus.ACCOUNT_REQUESTING.getValue();
    }

    @Override
    public void processOrder(CashoutRecordQueue record) {
        log.info("[JiuHe][processOrder] process order. record={}", record.toString());
        processNotSuccessfulOrder(record);
    }


    @Override
    public void processDealingOrder(CashoutRecordQueue recordQueue) {
        log.info("[JiuHe][processDealingOrder] process order. record={}", recordQueue.toString());
        processNotSuccessfulOrder(recordQueue);
    }

    @Override
    public String payNotifyProcess(Map<String, String> params) {
        //验签
        String sign = params.get("sign");
        String dataSign = UrlUtils.createLinkString(params);
        if (!paramSignVerify(dataSign, sign)) {
            log.error("[JiuHe][payNotifyProcess] sign verify fail");
            return null;
        }
        String dataStr = params.get("data");
        String responseJsonData = new String(Base64.decode(dataStr), StandardCharsets.UTF_8);
        log.info("[JiuHe][payNotifyProcess] responseJsonData: {}", responseJsonData);

        JSONObject dataObject = JSON.parseObject(responseJsonData);
        String id = dataObject.getString("id");
        log.info("[JiuHe][payNotifyProcess] batch_id = {}", id);
        if (duplicate(id)) {
            log.info("[JiuHe][payNotifyProcess] batch_id = {}, duplicate notify", id);
            return null;
        }
        List<JiuHeWithdrawOrderVO> orderVOList = JSON.parseArray(dataObject.getString("orders"), JiuHeWithdrawOrderVO.class);
        if (CollectionUtils.isEmpty(orderVOList)) {
            log.error("[JiuHe][payNotifyProcess] orderVOList is empty");
            return null;
        }
        List<String> orderIds = orderVOList.stream().map(JiuHeWithdrawOrderVO::getOutOrderNo).collect(Collectors.toList());
        List<CashoutRecordQueue> recordQueues = cashoutDao.getRecordQueueByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(recordQueues)) {
            log.error("[JiuHe][payNotifyProcess] recordQueues is empty");
            return notifyProcessSuccess();
        }
        Map<String, JiuHeWithdrawOrderVO> orderMap = orderVOList.stream().collect(Collectors.toMap(JiuHeWithdrawOrderVO::getOutOrderNo, v -> v));
        for (CashoutRecordQueue record : recordQueues) {
            if (orderStatusIsFinished(record.getApp_status())) {
                log.error("[JiuHe][payNotifyProcess] order status is finished, order_id = {}, name = {}, accountNo = {}, order_status = {}", record.getOrderid(), record.getAccount_name(), record.getAccount_no(), record.getApp_status());
                continue;
            }
            JiuHeWithdrawOrderVO orderVO = orderMap.get(record.getOrderid());
            if (JiuHeOrderSettleStatusEnum.SUCC.name().equals(orderVO.getStatus())) {
                cashoutService.transactionSuccess(record, "0", "成功", orderVO.getStatus(), true);
                log.info("[JiuHe][payNotifyProcess] order_id = {}, name = {}, accountNo = {},  withdraw success", record.getOrderid(), record.getAccount_name(), record.getAccount_no());
            } else if (isSettleFail(orderVO.getStatus())) {
                cashoutService.transactionFail(record, "0", orderVO.getNote(), orderVO.getStatus());
                log.info("[JiuHe][payNotifyProcess] order_id = {}, name = {}, accountNo = {},  withdraw fail, order_status = {}, reason = {}", record.getOrderid(), record.getAccount_name(), record.getAccount_no(), orderVO.getStatus(), orderVO.getNote());
            } else {
                log.info("[JiuHe][payNotifyProcess] order_id = {}, name = {}, accountNo = {},  withdraw in progress, order_status = {}", record.getOrderid(), record.getAccount_name(), record.getAccount_no(), orderVO.getStatus());
            }
        }
        return notifyProcessSuccess();
    }


    public String buildOrderQueryParam(CashoutRecordQueue record) {
        JiuHeOrderQueryParamDTO orderQueryParamDTO = new JiuHeOrderQueryParamDTO();
        orderQueryParamDTO.setOutOrderNo(record.getOrderid());
        String paramJson = JSON.toJSONString(orderQueryParamDTO);
        return Base64.encode(paramJson.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 处理订单未成功的订单
     *
     * @param record 现有订单记录
     */
    public boolean processNotSuccessfulOrder(CashoutRecordQueue record) {
        JiuHeWithdrawOrderVO orderVO = null;
        try {
            String orderQueryParam = buildOrderQueryParam(record);
            String response = buildJiuHeRequest(config.getQueryOrder(), orderQueryParam);
            orderVO = JSON.parseObject(JSON.toJSONString(parseResponse(response, false)), JiuHeWithdrawOrderVO.class);
        } catch (Exception e) {
            log.error("[JiuHe][processNotSuccessfulOrder] order_id = {}, name = {}, accountNo = {}, query error", record.getOrderid(), record.getAccount_name(), record.getAccount_no(), e);
        }
        if (null == orderVO) {
            log.error("[JiuHe][processNotSuccessfulOrder] order_id = {}, name = {}, accountNo = {}, is not query data", record.getOrderid(), record.getAccount_name(), record.getAccount_no());
            //未查询到的订单，当做为提现处理，重置app_status为5
            return false;
        }
        if (JiuHeOrderSettleStatusEnum.SUCC.name().equals(orderVO.getStatus())) {
            log.info("[JiuHe][processNotSuccessfulOrder] order_id = {}, name = {}, accountNo = {}, withdraw success", orderVO.getOutOrderNo(), record.getAccount_name(), record.getAccount_no());
            cashoutService.transactionSuccess(record, "0", "成功", orderVO.getStatus(), true);
        } else if (isSettleFail(orderVO.getStatus())) {
            log.error("[JiuHe][processNotSuccessfulOrder] order_id = {}, name = {}, accountNo = {}, withdraw fail, order_status = {}, reason = {} ", orderVO.getOutOrderNo(), record.getAccount_name(), record.getAccount_no(), orderVO.getStatus(), orderVO.getNote());
            cashoutService.transactionFail(record, "0", orderVO.getNote(), orderVO.getStatus());
        } else {
            log.info("[JiuHe][processNotSuccessfulOrder] order_id = {}, name = {}, accountNo = {}, withdraw in progress, order_status = {}", orderVO.getOutOrderNo(), record.getAccount_name(), record.getAccount_no(), orderVO.getStatus());
        }
        return true;
    }


    /**
     * 签名方法
     *
     * @param signStr 要签名的字符串
     * @return 签名数据
     * @throws ResponseException 当签名出现异常时抛出异常
     */
    private String paramSign(String uri, String signStr) {
        String signData = "";
        try {
            String data = StringUtils.isEmpty(uri) ? signStr : uri + "&" + signStr;
            signData = RSA2.sign(config.getPriKey(), data);
        } catch (Exception e) {
            log.error("[JiuHe][signContract] 签名异常", e);
            throw new ResponseException(ResponseError.E_CASHOUT_SIGN);
        }
        return signData;
    }

    /**
     * 验签方法
     *
     * @param data 要验签的字符串
     * @param sign 签名数据
     * @return 验签结果
     * @throws ResponseException 当验签出现异常时抛出异常
     */
    private boolean paramSignVerify(String data, String sign) {
        boolean verify;
        try {
            verify = RSA2.verify(config.getPubKey(), data, sign);
        } catch (Exception e) {
            log.error("[JiuHe] verify exception ", e);
            throw new ResponseException(ResponseError.E_CASHOUT_SIGN);
        }
        return verify;
    }

    /**
     * 通知处理成功
     *
     * @return
     */
    public String notifyProcessSuccess() {
        Map<String, String> map = new HashMap<>(2);
        map.put("code", "0");
        map.put("msg", "OK");
        String data = Base64.encode(JsonUtils.toString(map).getBytes(StandardCharsets.UTF_8));
        String sign = paramSign("", "data" + "=" + data);
        Map<String, String> resMap = new HashMap<>(2);
        resMap.put("data", data);
        resMap.put("sign", sign);
        return JsonUtils.toString(resMap);
    }

    /**
     * 是否为结算终止状态
     *
     * @return
     */
    public boolean orderStatusIsFinished(int orderStatus) {
        return orderStatus == CashoutRecordAppStatus.ACCOUNT_SUCCESS.getValue() || orderStatus == CashoutRecordAppStatus.REFUND_SUCCESS.getValue();
    }

    /**
     * 是否结算失败
     *
     * @return
     */
    public boolean isSettleFail(String orderStatus) {
        List<String> name = List.of(JiuHeOrderSettleStatusEnum.FAIL.name(), JiuHeOrderSettleStatusEnum.COMMITFAIL.name(), JiuHeOrderSettleStatusEnum.CANCELED.name(), JiuHeOrderSettleStatusEnum.CLOSED.name());
        return name.contains(orderStatus);
    }

    /**
     * 是否重复通知
     *
     * @param batchId 商户订单批次号
     * @return
     */
    public boolean duplicate(String batchId) {
        String key = JIUHE_NOTIFY_BATCH + batchId;
        if (redisTemplate.hasKey(key)) {
            return true;
        }
        redisTemplate.opsForValue().set(key, "1", 9, TimeUnit.SECONDS);
        return false;
    }

}
