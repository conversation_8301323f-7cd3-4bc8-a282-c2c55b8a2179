package com.easylive.pay.service.factory;

import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.bank.TaxCollectHeadCode;
import com.easylive.pay.enums.bank.TaxQueryStatus;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.cashout.sc.PaymentDetailInfo;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.TaxCollectService;
import com.easylive.pay.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 税筹
 *
 * <AUTHOR>
 * @date 2020/7/14
 */
@Component("TaxCollectBankCashout")
@Slf4j
public class TaxCollectBankCashout extends AbstractBankCashout implements BankCashout {

    @Autowired
    private TaxCollectService taxCollectService;

    @Autowired
    private CashoutService cashoutService;

    @Override
    public Boolean signContract(SignContractModel sign) {
        log.info("[TaxCollect][signContract] sign contract. sign={}", sign.toString());
        return taxCollectService.signContract(sign.getAccountName(), sign.getAccountNo(), sign.getIdCard(), sign.getPhone());
    }

    @Override
    protected void doWithDrawActual(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {
        log.info("[TaxCollect][doWithdraw] do withdraw. cashoutRecord={}, bankCard={}, idCard={}", cashoutRecord.toString(), bankCard.toString(), idCard);
        taxCollectService.payment(cashoutRecord, bankCard, idCard);
    }

    @Override
    public void processOrder(CashoutRecordQueue record) {
        log.info("[TaxCollect][processOrder] process order. record={}", record.toString());

        PaymentDetailInfo info = taxCollectService.paymentQuery(record.getOrderid());
        if (info == null) {
            log.error("[processTaxAppCashout] 1. Error response null, record={}", JsonUtils.printObject(record));
            return;
        }

        if (TaxCollectHeadCode.SUCCESS.getValue().equals(info.getResultCode())) {
            Optional<TaxQueryStatus> state = Optional.ofNullable(TaxQueryStatus.fromValue(info.getRemitResult()));
            if (state.isPresent()) {
                switch (state.get()) {
                    case SUCCESS:
                        //原交易确认成功
                        cashoutService.updateCashoutSuccess(record, info.getResultCode(), info.getResultMsg(), info.getRemitResult());
                        break;
                    case FAILED:
                        //交易失败，退款逻辑
                        cashoutService.updateCashoutFail(record, info.getResultCode(), info.getResultMsg(), info.getRemitResult());
                        break;
                    default:
                        break;
                }
            }
        }
    }

    @Override
    public void processDealingOrder(CashoutRecordQueue recordQueue) {

    }
}
