package com.easylive.pay.service.factory;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.BankOriTxsts;
import com.easylive.pay.enums.BankResCodeType;
import com.easylive.pay.enums.BankTxSts;
import com.easylive.pay.model.cashout.BaseResponseXML;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.cashout.WithdrawRequestBody;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.XZYQBankService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;

/**
 * 新政银企提现
 *
 * <AUTHOR>
 * @date 2020/7/14
 */
@Component("XZYQBankCashout")
@Slf4j
public class XZYQBankCashout extends AbstractBankCashout implements BankCashout {

    @Autowired
    private XZYQBankService xzyqBankService;

    @Autowired
    private CashoutService cashoutService;

    @Override
    public Boolean signContract(SignContractModel sign) {
        log.info("[XZYQ][signContract] sign contract. sign={}", sign.toString());
        return xzyqBankService.signContract(sign.getUid(),
                sign.getOperType(),
                sign.getAccountNo(),
                sign.getAccountName(),
                sign.getBankNo(),
                sign.getBankName(),
                sign.getIdCard(),
                sign.getPhone());
    }

    @Override
    protected void doWithDrawActual(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {
        /**
         * 1.构造请求参数，请求新政银企提现接口
         * 2.提现成功返回信息，失败返回错误信息
         */
        log.info("[XZYQ][doWithdraw] do withdraw. cashoutRecord={}, bankCard={}, idNo={}", cashoutRecord.toString(), bankCard.toString(), idCard);
        WithdrawRequestBody withdrawBody = new WithdrawRequestBody();
        withdrawBody.setEmployeeNum(String.valueOf(cashoutRecord.getUid()));
        String txAmt = new DecimalFormat("#.00").format(cashoutRecord.getRmb() / 100d);
        withdrawBody.setTxAmt(txAmt);
        withdrawBody.setAcctountNo(bankCard.getAccountNo());
        withdrawBody.setAcctName(bankCard.getAccountName());
        withdrawBody.setBankName(bankCard.getOpenBankName());
        withdrawBody.setBankNo(bankCard.getOpenBankNo());
        withdrawBody.setIdType("01");
        withdrawBody.setIdNo(idCard);
        withdrawBody.setBrf("提现到银行卡");

        BaseResponseXML response = xzyqBankService.bankWithdraw(withdrawBody, cashoutRecord.getCommit_time(), cashoutRecord.getOrderid());
        if (response == null) {
            log.error("[requestThirdBankWithdraw] response error, Request:cashoutRecord={}, bankCard={}, idNo={}, Response:Info={}", cashoutRecord, bankCard, idCard, response);
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现服务器错误");
        }

        if (response.getHead().getResCode() == null || response.getHead().getResCode().compareTo(BankResCodeType.SUCCESS.getValue()) != 0) {
            //提现失败
            log.error("[requestThirdBankWithdraw] Cashout Failed, Request:cashoutRecord={}, bankCard={}, idNo={}, Response:Info={}", cashoutRecord, bankCard, idCard, response);
            //P066-账户可用余额不足
            String resMsg = response.getHead().getResMsg();
            //更新数据库返回状态信息
            if (null != response.getBody()) {
                cashoutService.updateRecordResultByRecordId(cashoutRecord.getId(), response.getHead().getResCode(), response.getHead().getResMsg(), response.getBody().getTxSts());
            }

            if (response.getHead().getResCode() != null
                    && response.getHead().getResCode().compareTo(BankResCodeType.ACCOUNT_AMOUNT_EMPTY.getValue()) == 0) {
                resMsg = BankResCodeType.ACCOUNT_AMOUNT_EMPTY.getMessage();
            }
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, resMsg);
        }
    }

    @Override
    public void processOrder(CashoutRecordQueue record) {
        log.info("[XZYQ][processOrder] process order. record={}", record.toString());
        processDealingOrder(record);
    }

    @Override
    public void processDealingOrder(CashoutRecordQueue record) {
        log.info("[XZYQ][processDealingOrder] process order. record={}", record.toString());
        /**
         * 1. 原交易状态除了1、4、5、7为成功和处理中，其余按失败处理。
         * 2. 失败处理进行更改状态和退还饭团处理。
         */
        BaseResponseXML response = xzyqBankService.getBankWithdrawConfirmResult(record);

        if (response.getBody().getTxSts() == null) {
            //错误
            log.error("[processDealingAppCashoutRecord][App Cashout]Third Bank Error record={}, response={}", record, response);
            return;
        }
        String resCode = response.getHead().getResCode();
        String resMsg = response.getHead().getResMsg();
        String txSts = response.getBody().getTxSts();

        if (txSts.compareTo(String.valueOf(BankTxSts.SUCCESS.getCode())) == 0) {
            //交易成功
            if (txSts.compareTo(String.valueOf(BankOriTxsts.SUCCESS.getCode())) == 0
                    || txSts.compareTo(String.valueOf(BankOriTxsts.CONFIRM_SUCCESS.getCode())) == 0) {
                //原交易确认成功
                cashoutService.updateCashoutSuccess(record, resCode, resMsg, txSts);

            } else if (txSts.compareTo(String.valueOf(BankOriTxsts.SEND_CONFIRM.getCode())) == 0
                    || txSts.compareTo(String.valueOf(BankOriTxsts.DEALING.getCode())) == 0) {
                //交易处理中
            } else {
                //交易失败，退款逻辑
                cashoutService.updateCashoutFail(record, resCode, resMsg, txSts);
            }
        } else if (txSts.compareTo(String.valueOf(BankTxSts.FAIL.getCode())) == 0) {
            //交易失败，退款逻辑
            log.info("[processDealingAppCashoutRecord][App Cashout] Confirm Failed, record={}, head={}, body={}", record, response.getHead(), response.getBody());
            processFailedOrder(record, response);
        } else {
            //错误
            log.error("[processDealingAppCashoutRecord][App Cashout]Third Bank Error record={}, response={}", record, response);
        }
    }

    @Override
    public void processSuccessOrder(CashoutRecordQueue record,boolean isCheck) {
        /*
          1. 原交易状态为2，做退款处理，其余修改状态已检查。
          2. 失败处理进行更改状态和退还饭团处理。
         */

        BaseResponseXML response = xzyqBankService.getBankWithdrawConfirmResult(record);

        if (response.getBody().getTxSts() == null) {
            //错误
            log.error("[Check Cashout]Third Bank Error record={}, response={}", record, response);
            return;
        }
        String resCode = response.getHead().getResCode();
        String resMsg = response.getHead().getResMsg();
        String txSts = response.getBody().getTxSts();

        if (response.getBody().getTxSts().compareTo(String.valueOf(BankTxSts.SUCCESS.getCode())) == 0) {
            //交易成功
            if (response.getBody().getOriTxsts().compareTo(String.valueOf(BankOriTxsts.REVERSAL.getCode())) == 0) {
                cashoutService.updateCashoutFailWithCheck(record, resCode, resMsg, txSts);
            } else {
                //更新状态
                if (isCheck) {
                    cashoutService.updateCashRecordCheckStatusById(record.getId());
                }
            }
        } else if (response.getBody().getTxSts().compareTo(String.valueOf(BankTxSts.FAIL.getCode())) == 0) {
            //交易失败，退款逻辑
            log.info("[Check Cashout]Failed record={}, head={}", record, response.getHead());
        } else {
            //错误
            log.error("[Check Cashout]Third Bank Error record={}, response={}", record, response);
        }
    }

    private void processFailedOrder(CashoutRecordQueue record, BaseResponseXML response) {
        if (String.valueOf(BankOriTxsts.FAIL.getCode()).equals(response.getBody().getOriTxsts())) {
            String resCode = "";
            String resMessage = "";
            if (response.getHead() != null) {
                resCode = response.getHead().getResCode();
                resMessage = response.getHead().getResMsg();
            }
            //交易失败，退款逻辑
            cashoutService.updateCashoutFail(record, resCode, resMessage, response.getBody().getOriTxsts());
        } else {
            //如果没有OriTxsts，则根据resCode退款
            if (response.getHead() != null
                    && BankResCodeType.TRADE_NOT_FOUND.getValue().equals(response.getHead().getResCode())) {
                //交易失败，退款逻辑
                cashoutService.updateCashoutFail(record, response.getHead().getResCode(), response.getHead().getResMsg(), response.getBody().getOriTxsts());
            }
        }
    }
}
