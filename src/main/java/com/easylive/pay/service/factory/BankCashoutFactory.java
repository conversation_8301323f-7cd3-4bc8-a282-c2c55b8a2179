package com.easylive.pay.service.factory;

import com.easylive.pay.enums.bank.BankType;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 银行提现工厂
 * <AUTHOR>
 * @date 2020/7/14
 */
@Component
public class BankCashoutFactory {

    @Resource(name = "SYDBankCashout")
    private BankCashout sydBankCashout;

    @Resource(name = "XZYQBankCashout")
    private BankCashout xzyqBankCashout;

    @Resource(name = "TaxCollectBankCashout")
    private BankCashout taxCollectBankCashout;

    @Resource(name = "XinYunBankCashout")
    private BankCashout xinYunBankCashout;

    @Resource(name = "JiuHeBankCashout")
    private BankCashout jiuHeBankCashout;

    @Resource(name = "HuifuBankCashout")
    private BankCashout huifuBankCashout;

    @Resource(name = "YZHCashout")
    private BankCashout yzhCashout;

    private static Map<BankType, BankCashout> bankCashoutMap;

    @PostConstruct
    public void init() {
        bankCashoutMap = new HashMap<>();
        bankCashoutMap.put(BankType.HUIFU, huifuBankCashout);
        bankCashoutMap.put(BankType.SYD, sydBankCashout);
        bankCashoutMap.put(BankType.JIUHE, jiuHeBankCashout);
        bankCashoutMap.put(BankType.TAX, taxCollectBankCashout);
        bankCashoutMap.put(BankType.XINYUN, xinYunBankCashout);
        bankCashoutMap.put(BankType.XZYQ, xzyqBankCashout);
        bankCashoutMap.put(BankType.YUNZHANGHU, yzhCashout);
    }

    public BankCashout getBankCashout(BankType bankType) {
        return bankCashoutMap.get(bankType);
    }

    public BankCashout getBankCashout(int bankType) {
        BankType type = BankType.fromValue(bankType);
        if( type == null )
            return jiuHeBankCashout;
        return getBankCashout(type);
    }

}
