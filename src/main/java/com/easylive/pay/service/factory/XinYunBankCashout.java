package com.easylive.pay.service.factory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.XinyunCashConfig;
import com.easylive.pay.dao.CashoutDao;
import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.*;
import com.easylive.pay.enums.bank.BankType;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.cashout.xinyun.*;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 薪云提现
 *
 * <AUTHOR>
 * @date 2020/7/14
 */
@Component("XinYunBankCashout")
@Slf4j
public class XinYunBankCashout extends AbstractBankCashout implements BankCashout {

    @Autowired
    private XinyunCashConfig config;

    @Autowired
    private CashoutService cashoutService;


    @Autowired
    private CashoutDao cashoutDao;

    @Override
    public Boolean signContract(SignContractModel sign) {
        log.info("[XinYun][signContract] sign contract. sign={}", sign.toString());

        AddMemberDTO addMemberDTO = new AddMemberDTO();
        addMemberDTO.setRealName(sign.getAccountName());
        addMemberDTO.setPhone(sign.getPhone());
        addMemberDTO.setSex(SexUtils.getSexByIdCardNo(sign.getIdCard()));
        addMemberDTO.setIdCard(sign.getIdCard());
        addMemberDTO.setBankName(sign.getBankName());
        addMemberDTO.setBankAccount(sign.getAccountNo());
        addMemberDTO.setRegDate(DateUtils.format(new Date()));
        addMemberDTO.setEnterpriseUniqueNO(config.getMerId());
        addMemberDTO.setNonceStr(String.valueOf(System.currentTimeMillis()));
        addMemberDTO.setZfbAccount(sign.getZfbAccount());
        addMemberDTO.setBack(sign.getBack());
        addMemberDTO.setFront(sign.getFront());

        String signStr = UrlUtils.createLinkString(addMemberDTO.toHashMap());
        String signData = paramSign(signStr);
        addMemberDTO.setSign(signData);

        String body = JsonUtils.toString(addMemberDTO);

        log.info("[XinYun][signContract] signStr:{} and body:{} and sign:{}", signStr, body, signData);
        String response = HttpUtils.doPost(config.getAddMemberUrl(), body, ContentType.APPLICATION_JSON);
        if (response == null || response.isEmpty()) {
            return false;
        }

        log.info("[XinYun][signContract] sign contract. response={}", response);
        AddMemberResponseDTO addMemberResponseDTO = JsonUtils.fromString(response, AddMemberResponseDTO.class);
        return "SUCCESS".equals(addMemberResponseDTO.getResultCode());
    }

    @Override
    public Boolean updateMember(SignContractModel sign) {
        String zfbAccount = sign.getZfbAccount();
        String bankAccount = sign.getAccountNo();

        boolean zfbBindResult = true;
        boolean bankBindResult = true;

        if (zfbAccount != null && zfbAccount.length() > 0) {
            zfbBindResult = updateZfb(sign);
        }

        if (bankAccount!= null && bankAccount.length() > 0) {
            bankBindResult = updateBankCard(sign);
        }

        return zfbBindResult && bankBindResult;
    }

    private boolean updateBankCard(SignContractModel sign) {
        UpdateBankMemberDTO updateBankMemberDTO = new UpdateBankMemberDTO();
        updateBankMemberDTO.setEnterpriseUniqueNO(config.getMerId());
        updateBankMemberDTO.setFieldType(1);
        updateBankMemberDTO.setFieldVal(sign.getIdCard());
        updateBankMemberDTO.setBankName(sign.getBankName());
        updateBankMemberDTO.setBankAccount(sign.getAccountNo());
        updateBankMemberDTO.setNonceStr(String.valueOf(System.currentTimeMillis()));

        String signStr = UrlUtils.createLinkString(updateBankMemberDTO.toHashMap());
        String signData = paramSign(signStr);
        updateBankMemberDTO.setSign(signData);

        String body = JsonUtils.toString(updateBankMemberDTO);

        log.info("[XinYun][UpdateBankCard] signStr:{} and body:{} and sign:{}", signStr, body, signData);
        String response = HttpUtils.doPost(config.getUpdateMemberBankUrl(), body, ContentType.APPLICATION_JSON);
        if (response == null || response.isEmpty()) {
            return false;
        }

        log.info("[XinYun][UpdateBankCard] sign contract. response={}", response);
        ResponseBaseModel responseBaseModel = JsonUtils.fromString(response, ResponseBaseModel.class);
        return "SUCCESS".equals(responseBaseModel.getResultCode());
    }

    /**
     * 更新支付宝信息
     * @return
     */
    private boolean updateZfb(SignContractModel sign) {
        UpdateZfbMemberDTO updateZfbMemberDTO = new UpdateZfbMemberDTO();
        updateZfbMemberDTO.setEnterpriseUniqueNO(config.getMerId());
        updateZfbMemberDTO.setFieldType(1);
        updateZfbMemberDTO.setFieldVal(sign.getIdCard());
        updateZfbMemberDTO.setZfbAccount(sign.getZfbAccount());
        updateZfbMemberDTO.setNonceStr(String.valueOf(System.currentTimeMillis()));

        String signStr = UrlUtils.createLinkString(updateZfbMemberDTO.toHashMap());
        String signData = paramSign(signStr);
        updateZfbMemberDTO.setSign(signData);

        String body = JsonUtils.toString(updateZfbMemberDTO);

        log.info("[XinYun][UpdateZfb] signStr:{} and body:{} and sign:{}", signStr, body, signData);
        String response = HttpUtils.doPost(config.getUpdateMemberZfbUrl(), body, ContentType.APPLICATION_JSON);
        if (response == null || response.isEmpty()) {
            return false;
        }

        log.info("[XinYun][UpdateZfb] sign contract. response={}", response);
        ResponseBaseModel responseBaseModel = JsonUtils.fromString(response, ResponseBaseModel.class);
        return "SUCCESS".equals(responseBaseModel.getResultCode());
    };

    @Override
    protected void doWithDrawActual(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {
        String requestParam = buildWithdrawRequestParam(cashoutRecord, bankCard, idCard, fkType);
        Map<String, String> resMap = requestXinYun(config.getWithdrawUrl(), requestParam);
        String resultCode = resMap.get("result_code");
        if(XiYunReturnStatusEnum.FAIL.name().equals(resultCode)){
            log.error("[XinYun] doWithDraw fail, Request:uid={}, card_id={}, idNo={}, fkType={}", cashoutRecord.getUid(), bankCard.getId(), idCard, fkType);
            try {
                log.info("[XinYun] doWithDraw fail, error_code: {}, err_code_des: {}", resMap.get("err_code"), resMap.get("err_code_des"));
                cashoutDao.updateQueueByRecordId(cashoutRecord.getId(), resultCode, resMap.get("err_code_des"), String.valueOf(XiYunOrderStatusEnum.FAIL.getCode()));
            } catch (Exception e) {
                log.error("[XinYun] update queue failed, recordId={}", cashoutRecord.getId());
            }
        }else {
            cashoutRecord.setOrderid(resMap.get("batch_num"));
        }
    }

    public String buildWithdrawRequestParam(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, int fkType){
        String money = BigDecimal.valueOf(cashoutRecord.getRmb()).divide(new BigDecimal(100)).setScale(2).toString();
        WithdrawRequestParamData data = new WithdrawRequestParamData("********", idCard, bankCard.getAccountName(), money);
        if (fkType == WithdrawChannelTypeEnum.CARD.getCode()){
            data.setBank_name(bankCard.getOpenBankName());
            data.setBank_account(bankCard.getAccountNo());
        }else {
            data.setZfbzh(bankCard.getZfbAccount());
        }
        WithdrawRequestParam requestParam = WithdrawRequestParam
                .builder()
                .enterprise_unique_no(config.getMerId())
                .ttlamt(money)
                .ttlcnt(1)
                .enterprise_management_unique_no("2023111417535421420850")
                .task_unique_no("202311151148451412")
                .fk_type(fkType)
//                .remark("3412341")
                .data(JsonUtils.toString(List.of(data)))
                .nonce_str(String.valueOf(System.currentTimeMillis()))
                .build();
        String signStr = UrlUtils.createLinkString(requestParam.toHashMap());
        log.info("[XinYun][buildWithdrawRequestParam] requestParam-signStr={}", signStr);
        String sign = paramSign(signStr);
        log.info("[XinYun][buildWithdrawRequestParam] requestParam-sign={}", sign);
        requestParam.setSign(sign);
        return JsonUtils.toString(requestParam);
    }


    public Map<String, String> requestXinYun(String url, String requestBody){
        log.info("[XinYun][buildWithdrawRequestParam] requestBody={}", requestBody);
        String response = HttpUtils.doPost(url, requestBody, ContentType.APPLICATION_JSON);
        log.info("[XinYun][requestXinYun] response={}", response);
        if (response == null || response.isEmpty()) {
            log.error("[XinYun][requestXinYun] response is null. url={}, requestBody={}", url, requestBody);
        }
        Map<String, String> resMap = JSON.parseObject(response, Map.class);
        //验签
        /*if (paramSignVerify(UrlUtils.createLinkString(resMap), resMap.get("sign"))){
            throw new ResponseException(ResponseError.E_CASHOUT_SIGN, "验签失败");
        }*/
        String returnCode = resMap.get("return_code");
        if (XiYunReturnStatusEnum.FAIL.name().equals(returnCode)) {
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, resMap.get("return_msg"));
        }
        return resMap;
    }

    @Override
    public void processOrder(CashoutRecordQueue record) {
        log.info("[XinYun][processOrder] process order. record={}", record.toString());
        processNotSuccessfulOrder(record);
    }


    @Override
    public void processDealingOrder(CashoutRecordQueue recordQueue) {
        log.info("[xinyun][processDealingOrder] process order. record={}", recordQueue.toString());
        processNotSuccessfulOrder(recordQueue);
    }

    @Override
    public String payNotifyProcess(Map<String, String> params){
        String batchNum = params.get("batch_num");
        String returnCode = params.get("return_code");
        String resultCode = params.get("result_code");
        log.info("[XinYun][notify] notify params => batchNum={}, returnCode={}, resultCode={}", batchNum, returnCode, resultCode);
        //验签
        String sign = params.get("sign");
        log.info("sign: {}", sign);
        String signStr = UrlUtils.createLinkString(params);
        log.info("signStr: {}", signStr);
        if (!paramSignVerify(signStr, sign)){
            log.error("[XinYun][notify] sign verify fail， batch => {}", batchNum);
            throw new ResponseException(ResponseError.E_CASHOUT_SIGN);
        }
        CashoutRecordQueue record = cashoutDao.getRecordQueueByOrderId(batchNum, BankType.XINYUN, CashoutRecordAppStatus.ACCOUNT_SUCCESS);
        if (record == null){
            log.info("[XinYun][notify] batch successful or is not exist");
            return notifyProcessSuccess();
        }
        //判断结果是否成功
        if (!XiYunReturnStatusEnum.SUCCESS.name().equals(returnCode) || !XiYunReturnStatusEnum.SUCCESS.name().equals(resultCode)){
            //失败回滚
            cashoutService.updateCashoutFail(record, resultCode, XiYunReturnStatusEnum.FAIL.getDesc(), String.valueOf(XiYunOrderStatusEnum.FAIL.getCode()));
        }else {
            //成功
            cashoutService.updateCashoutSuccess(record, resultCode, XiYunReturnStatusEnum.SUCCESS.getDesc(), String.valueOf(XiYunOrderStatusEnum.PAID.getCode()));
        }
        return notifyProcessSuccess();
    }

    public String notifyProcessSuccess(){
        //{"result_code":"SUCCESS","err_code_des":""}
        JSONObject result = new JSONObject();
        result.put("result_code", XiYunReturnStatusEnum.SUCCESS.name());
        return result.toString();
    }

    /**
     * 处理订单未成功的订单
     * @param record 现有订单记录
     */
    public void processNotSuccessfulOrder(CashoutRecordQueue record){
        PayQueryParam payQueryParam = new PayQueryParam(config.getMerId(), record.getOrderid(), String.valueOf(System.currentTimeMillis()));
        String signStr = UrlUtils.createLinkString(payQueryParam.toHashMap());
        payQueryParam.setSign(paramSign(signStr));
        Map<String, String> map = requestXinYun(config.getPaymentschedule(), JSON.toJSONString(payQueryParam));
        String resultCode = map.get("result_code");
        if (XiYunReturnStatusEnum.FAIL.name().equals(resultCode)){
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, map.get("err_code_des"));
        }
        //判断数据是否处理成功，operating_state = 3；1数据待调用  2数据处理中 3数据处理完成  4数据处理失败
        JSONObject payApiSchedule = JSON.parseObject(map.get("pay_api_schedule"));
        if (null == payApiSchedule){
            log.info("xinyun processOrder id => {}, not query data", record.getOrderid());
            return;
        }
        int state = payApiSchedule.getIntValue("operating_state");
        String stateName = payApiSchedule.getString("operating_state_name");
        if (state == XiYunOrderDataStatusEnum.UNPROCESSED.getCode() || state == XiYunOrderDataStatusEnum.PROCESSING.getCode()){
            log.info("xinyun processOrder id => {}, batch data is unprocessed or processing", record.getOrderid());
            return;
        }
        //数据处理失败则判定为订单失败
        if (state == XiYunOrderDataStatusEnum.FAIL.getCode()){
            log.info("xinyun processOrder id => {} batch data process fail, excute payFail method", record.getOrderid());
            cashoutService.updateCashoutFail(record, String.valueOf(state), stateName, String.valueOf(XiYunOrderStatusEnum.FAIL.getCode()));
            return;
        }
        //因为目前一个批次只有一个订单，所以不需要去查批次信息，直接获取订单明显即可
        JSONArray parseArray = JSON.parseArray(map.get("payment_record_list"));
        JSONObject paymentRecord = parseArray.getJSONObject(0);

        //订单状态arrival_status：1待付款 2已付款 3付款失败 4付款中 5驳回付款 7系统卡单
        int arrivalStatus = paymentRecord.getInteger("arrival_status");

        if(arrivalStatus == XiYunOrderStatusEnum.FAIL.getCode() || arrivalStatus == XiYunOrderStatusEnum.REJECT.getCode()){
            //支付失败
            cashoutService.updateCashoutFail(record,String.valueOf(state), stateName, String.valueOf(arrivalStatus));
            //transactionFail(record, String.valueOf(state), stateName, String.valueOf(arrivalStatus));
        } else if (arrivalStatus == XiYunOrderStatusEnum.PAID.getCode()) {
            //支付成功，更改订单状态
            cashoutService.updateCashoutSuccess(record,String.valueOf(state), stateName, String.valueOf(arrivalStatus) );
            //transactionSuccess(record, String.valueOf(state), stateName, String.valueOf(arrivalStatus));
        }else {
            //支付中
            log.info("xinyun processOrder id => {} ,is paying, order status => {}", record.getOrderid(), arrivalStatus);
        }
    }



    /**
     * 签名方法
     *
     * @param signStr 要签名的字符串
     * @return 签名数据
     * @throws ResponseException 当签名出现异常时抛出异常
     */
    private String paramSign(String signStr){
        String signData = "";
        try {
            signData = RSA2.sign(config.getPriKey(), signStr);
        } catch (Exception e) {
            log.error("[XinYun][signContract] 签名异常", e);
            throw new ResponseException(ResponseError.E_CASHOUT_SIGN);
        }
        return signData;
    }

    /**
     * 验签方法
     *
     * @param data 要验签的字符串
     * @param sign 签名数据
     * @return 验签结果
     * @throws ResponseException 当验签出现异常时抛出异常
     */
    private boolean paramSignVerify(String data, String sign){
        boolean verify;
        try {
            verify = RSA2.verify(config.getPubKey(), data, sign);
        } catch (Exception e) {
            log.error("[XinYun] verify exception ", e);
            throw new ResponseException(ResponseError.E_CASHOUT_SIGN);
        }
        return verify;
    }



}
