package com.easylive.pay.service.factory;

import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.bank.PaymentQueryState;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.cashout.syd.PaymentQueryResData;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.SydService;
import com.easylive.pay.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 税优地系统
 *
 * <AUTHOR>
 * @date 2020/7/14
 */
@Component("SYDBankCashout")
@Slf4j
public class SYDBankCashout extends AbstractBankCashout implements BankCashout {

    @Autowired
    private SydService sydService;

    @Autowired
    private CashoutService cashoutService;

    @Override
    public Boolean signContract(SignContractModel sign) {
        log.info("[SYD][signContract] sign contract. sign={}", sign.toString());
        return sydService.signContract(sign.getAccountName(), sign.getAccountNo(), sign.getIdCard(), sign.getPhone());
    }

    @Override
    protected void doWithDrawActual(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, int fkType) {
        log.info("[SYD][doWithdraw] do withdraw. cashoutRecord={}, bankCard={}, idCard={}", cashoutRecord.toString(), bankCard.toString(), idCard);
        sydService.payment(cashoutRecord, bankCard, idCard);
    }

    @Override
    public void processOrder(CashoutRecordQueue record) {
        log.info("[SYD][processOrder] process order. record={}", record.toString());

        PaymentQueryResData paymentQueryResData = sydService.paymentQuery(record.getOrderid(), record.getOrderid());

        if (paymentQueryResData == null) {
            log.error("[processSydAppCashout] 1. Error response null, record={}", JsonUtils.printObject(record));
            return;
        }

        if (!paymentQueryResData.getQueryItems().isEmpty()) {
            for (PaymentQueryResData.PayQueryItem queryItem : paymentQueryResData.getQueryItems()) {
                Optional<PaymentQueryState> state = Optional.ofNullable(PaymentQueryState.fromValue(queryItem.getState()));
                String resCode = queryItem.getResCode();
                String resMsg = queryItem.getResMsg();
                String resStatus = String.valueOf(queryItem.getState());
                if (state.isPresent()) {
                    switch (state.get()) {
                        //交易状态 0：已受理 1：处理中 2：提现中 3：成功 4：失败
                        case SUCCESS:
                            //原交易确认成功
                            cashoutService.updateCashoutSuccess(record, resCode, resMsg, resStatus);
                            break;
                        case FAIL:
                            //交易失败，退款逻辑
                            cashoutService.updateCashoutFail(record, resCode, resMsg, resStatus);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    @Override
    public void processDealingOrder(CashoutRecordQueue recordQueue) {

    }
}
