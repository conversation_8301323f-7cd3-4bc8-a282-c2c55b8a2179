package com.easylive.pay.service;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.model.RetNode;
import com.easylive.pay.utils.HttpUtils;
import com.easylive.pay.utils.JsonUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/09/08
 */
@Service
public class IeasyService {

    private static Logger logger = LoggerFactory.getLogger(IeasyService.class);

    @Autowired
    private URLConfig urlConfig;

    /**
     * PK下注
     */
    public boolean pkGuessInvest(long from, long to, long pkId, int amount) {
        Map<String, Object> param = new HashMap<>(4);
        param.put("from", from);
        param.put("to", to);
        param.put("pkId", pkId);
        param.put("amount", amount);
        Map<String, Object> retInfo = pushMessageToIeasy("/ieasy/service/pk/guess/invest", param);
        if (retInfo == null) {
            return false;
        }
        return (boolean) retInfo.get("data");
    }

    private Map<String, Object> pushMessageToIeasy(String cmd, Map<String, Object> args) {
        return requestIeasy(urlConfig.getIeasyServiceUrl(), cmd, args);
    }

    private Map<String, Object> requestIeasy(String urlPrefix, String cmd, Map<String, Object> args) {
        String url = urlPrefix + "/" + cmd;
        logger.info("calling ieasy for command {} ", cmd);

        String response = HttpUtils.doGet(url, args);
        GsonBuilder builder = new GsonBuilder().setDateFormat("yyyy-MM-dd");
        Gson gson = builder.create();
        RetNode retNode = gson.fromJson(response, RetNode.class);

        if (retNode.getRetval().compareTo("ok") != 0) {
            logger.error("[requestIeasy] request ieasy failed, args={}, response={}", JsonUtils.printObject(args), JsonUtils.printObject(retNode));
            return null;
        }

        return retNode.getRetinfo();
    }

}
