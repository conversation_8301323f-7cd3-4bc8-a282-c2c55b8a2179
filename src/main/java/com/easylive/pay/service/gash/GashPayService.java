package com.easylive.pay.service.gash;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.GashConfig;
import com.easylive.pay.entity.RechargeOption;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.entity.GashRechargeRecord;
import com.easylive.pay.enums.*;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.model.RechargeResult;
import com.easylive.pay.model.SubOrderInfo;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.service.SubRechargeService;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.vo.GashVO;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class GashPayService {
    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private SubRechargeService subRechargeService;

    @Autowired
    private GashConfig gashConfig;

    private String QUERY_MSG_TYPE = "0100";
    private String CREATE_MSG_TYPE = "0100";
    private String SETTLE_MSG_TYPE = "0500";

    /**
     * 订单检查时效范围sec
     */
    private Integer CHECK_ORDER_INTERVAL = 3600;

    /**
     * 成功操作CODE
     */
    private String GASH_SUC_CODE = "0000";
    private String CURRENCY = "TWD";

    public List<RechargeOption> getRechargeOption(int appId) {
        return rechargeService.getOptionListByPlatformAndAppId(PayPlatform.PPF_GASH_PAY.getValue(), appId);
    }

    @Transactional
    public String createOrder(long uid, int appId, long amount, MobilePlatform mobilePlatform, String clientIp, String extension, GashRechargeOption gashOption) {
        log.info("[RECHARGE][GASH]Create order {}, {}, {}, {}, {}, {}", uid, appId, mobilePlatform, clientIp, amount, CURRENCY);
        if (amount <= 0) {
            throw new ResponseException(ResponseError.E_RECHARGE_MIN_AMOUNT_LIMIT);
        }

        OrderInfo orderInfo = new OrderInfo(appId, uid, amount, PayPlatform.PPF_GASH_PAY, mobilePlatform, clientIp);
        orderInfo.setAmount(amount);
        orderInfo.setCurrency(CURRENCY);
        orderInfo.setMerchantCurrency(CURRENCY);
        RechargeRecord rechargeRecord = rechargeService.createRechargeRecord(orderInfo);

        SubOrderInfo subOrderInfo = new SubOrderInfo(uid, appId, amount, PayPlatform.PPF_GASH_PAY, rechargeRecord.getOrderId(), gashOption.getCode(), GashOrderStatus.SORS_CREATE.getValue(), CURRENCY);
        subRechargeService.createRechargeRecord(subOrderInfo);

        String reqData = buildGashData(rechargeRecord.getOrderId(), amount, this.CREATE_MSG_TYPE, extension, gashOption.getCode());
        log.info("Gash step 1 create order request data:{}", reqData);

        return reqData;
    }

    public void operateOrder(String reqData, GashVO gashVO) {
        String data = reqData.replaceAll(" ", "+");
        Trans trans = new Trans(data);
        trans.setKey(gashConfig.getKEY());
        trans.setIv(gashConfig.getIV());
        trans.setPwd(gashConfig.getPWD());

        Map<String, String> node = trans.getNodes();
        gashVO.setExtra(node.get("MEMO"));
        gashVO.setStatus(GashResultStatus.FAIL);

        log.info("Gash step 2 notify order data:{}, reqData{}", node.toString(), data);

        //检查数据
        String erpc = trans.getErpc(trans.getKey(), trans.getIv());
        if (!erpc.equals(node.get("ERPC"))) {
            log.error("Check sign erpc error. erpc:{}, local erpc:{}", erpc, node.get("ERPC"));
            throw new ResponseException(ResponseError.E_GASH_SIGN);
        }

        opCompleteOrder(node, gashVO);
    }

    /**
     * 同步通知
     * @param node
     * @return
     */
    private void opCompleteOrder(Map<String, String> node, GashVO gashVO) {
        String recvStatus = node.get("PAY_STATUS");
        String orderId = node.get("COID");
        String recvMsg = node.get("RMSG_CHI");

        // 更新子订单信息
        GashRechargeRecord gashRechargeRecord = updateSubRecord(orderId, recvStatus, recvMsg);

        //验证订单信息
        RechargeRecord rechargeRecord = valid(node, gashVO);

        //结算
        String tradeOrderId = gashOrder(orderId, rechargeRecord.getAmount(), SETTLE_MSG_TYPE, gashRechargeRecord.getPlatformCode());
        if (Strings.isNullOrEmpty(tradeOrderId)) {
            log.info("Gash recharge notify settle order fail. orderId:{}", orderId);
            throw new ResponseException(ResponseError.E_GASH_ORDER_NOT_EXISTS, "获取结算订单失败");
        }

        //完成订单
        complete(rechargeRecord, tradeOrderId);

        //完成子订单
        subRecordComplete(gashRechargeRecord, tradeOrderId);

        gashVO.setStatus(GashResultStatus.SUCCESS);
    }


    /**
     * 验证数据状态
     * @param node
     * @return
     */
    private RechargeRecord valid(Map<String, String> node, GashVO gashVO) {
        String recvStatus = node.get("PAY_STATUS");
        String recvRCode = node.get("RCODE");
        String orderId = node.get("COID");
        Long amount = Long.parseLong(node.get("AMOUNT")) * 100L;

        log.info("Gash notify fail. recvRCode:{}, recvStatus:{}", recvRCode, recvStatus);

        if (!CURRENCY.equals(node.get("CUID"))) {
            log.error("Gash notify currency error. {}", node.get("CUID"));
        }

        if (GashOrderStatus.SORS_WAIT.getCode().equals(recvStatus) || GashOrderStatus.SORS_INCOMPLETE.getCode().equals(recvStatus)) {
            gashVO.setStatus(GashResultStatus.WAIT);
            throw new ResponseException(ResponseError.E_GASH_WAIT, "等待确认交易");
        }

        if (!GASH_SUC_CODE.equals(recvRCode) || !GashOrderStatus.SORS_SUCCESS.getCode().equals(recvStatus)) {
            log.error("Gash recharge order error. {}, {}", orderId, node.get("RMSG_CHI"));
            throw new ResponseException(ResponseError.E_GASH_ERROR, "非正常订单状态信息:" + node.get("RMSG_CHI"));
        }

        RechargeRecord rechargeRecord = rechargeService.getRecordByOrderId(orderId);
        if (rechargeRecord == null) {
            log.error("Gash recharge query record not exists. {}", orderId);
            throw new ResponseException(ResponseError.E_GASH_ORDER, "订单不存在");
        }

        if (rechargeRecord.getStatus() != OrderStatus.ORS_CREATE.getValue()) {
            log.error("Gash recharge order status error. {}, {}", orderId, rechargeRecord.getStatus());
            throw new ResponseException(ResponseError.E_GASH_ORDER_STATUS, "订单状态不正确");
        }

        Long recordAmount = rechargeRecord.getAmount();
        if (!recordAmount.equals(amount)) {
            log.info("Gash recharge amount diff, {}, {}", recordAmount, amount);
            throw new ResponseException(ResponseError.E_GASH_ORDER_AMOUNT, "充值金额不同");
        }


        return rechargeRecord;
    }

    /**
     * 更新子订单信息
     * @param orderId
     * @param recvStatus
     */
    private GashRechargeRecord updateSubRecord(String orderId, String recvStatus, String msg) {
        GashRechargeRecord gashRechargeRecord = subRechargeService.getRecordByOrderId(orderId, PayPlatform.PPF_GASH_PAY.getValue(), GashOrderStatus.SORS_CREATE.getValue());
        if (gashRechargeRecord == null) {
            log.error("Not found sub record. orderId:{}, status:{}", orderId, recvStatus);
            throw new ResponseException(ResponseError.E_GASH_SUB_ORDER_NOT_EXISTS);
        }

        GashOrderStatus status = GashOrderStatus.of(recvStatus);
        gashRechargeRecord.setPlatformStatus(status.getValue());
        gashRechargeRecord.setUpdateTime(DateUtils.now());
        gashRechargeRecord.setPlatformMsg(msg);
        subRechargeService.updateSubRecharge(gashRechargeRecord);

        return gashRechargeRecord;
    }

    /**
     * 更新子订单信息
     * @param record
     */
    private void updateSubRecord(GashRechargeRecord record) {
        subRechargeService.updateSubRecharge(record);
    }

    private boolean complete(RechargeRecord rechargeRecord, String tradeOrderId) {

        long rmb = rechargeRecord.getRmb();
        long ecoin = gashConfig.getRechargeScale().multiply(new BigDecimal(rmb / 10)).longValue();
        RechargeResult result = rechargeService.rechargeTransaction(rechargeRecord, rechargeRecord.getUid(), tradeOrderId, rmb, ecoin);
        return result.getTid() != null;
    }

    public String checkOrder(String orderId, long amount, String paid) {
        return gashOrder(orderId, amount, QUERY_MSG_TYPE, paid);
    }

    private String gashOrder(String orderId, long amount, String type, String paid) {
        String data = buildGashData(orderId, amount, type, paid);

        String resData = "";
        if (SETTLE_MSG_TYPE.equals(type)) {
            log.info("Gash step 3 request data:{}", data);
            resData = getSettleResponse(data);
            log.info("Gash step 4 response data:{}", resData);
        } else if (QUERY_MSG_TYPE.equals(type)) {
            log.info("Gash step 5 request data:{}", data);
            resData = getOrderResponse(data);
            log.info("Gash step 6 response data:{}", resData);
        } else {
            return "";
        }

        Trans trans = new Trans(resData);
        log.error("Gash order response:{}", trans.getNodes().toString());

        //todo 这里验证数据与否？
        if (GASH_SUC_CODE.equals(trans.getNodes().get("RCODE"))) {
            return trans.getNodes().get("RRN");
        }

        return "";
    }


    public void gashCheckOrder() {

        Date time = DateUtils.beforeTime(CHECK_ORDER_INTERVAL);
        List<Integer> status = Arrays.asList(GashOrderStatus.SORS_INCOMPLETE.getValue(), GashOrderStatus.SORS_WAIT.getValue());
        List<GashRechargeRecord> gashRechargeRecords = subRechargeService.listIncompleteOrder(time, PayPlatform.PPF_GASH_PAY.getValue(), status);

        if (gashRechargeRecords.isEmpty()) {
            return;
        }

        log.info("Gash check order info count:{}", gashRechargeRecords.size());

        try {
            gashRechargeRecords.forEach(record -> {
                String orderId = record.getOrderId();
                Long amount = record.getPlatformAmount();

                String tradeOrderId = gashOrder(orderId, amount, QUERY_MSG_TYPE, record.getPlatformCode());
                if (Strings.isNullOrEmpty(tradeOrderId)) {
                    log.error("Gash query order fail {}.", record.getOrderId());
                    return;
                }

                tradeOrderId = gashOrder(orderId, amount, SETTLE_MSG_TYPE, record.getPlatformCode());
                if (Strings.isNullOrEmpty(tradeOrderId)) {
                    log.info("Check order gash recharge notify settle order fail. orderId:{}", orderId);
                    return;
                }


                RechargeRecord rechargeRecord = rechargeService.getRecordByOrderId(orderId);
                complete(rechargeRecord, tradeOrderId);

                //完成子订单
                subRecordComplete(record, tradeOrderId);
                log.info("Gash check complete order {}, {}", record.getOrderId(), tradeOrderId);
            });
        } catch (Exception e) {
            log.error("Gash check order Exception", e);
        }
    }

    private String buildGashData(String orderId, long amount, String msgType, String paid) {
        return buildGashData(orderId, amount, msgType, "", paid);
    }

    private String buildGashData(String orderId, long amount, String msgType, String extension, String paid) {
        Trans trans = new Trans();
        trans.setKey(gashConfig.getKEY());
        trans.setIv(gashConfig.getIV());
        trans.setPwd(gashConfig.getPWD());

        trans.putNode("MSG_TYPE", msgType);
        trans.putNode("PCODE", gashConfig.getPCODE());
        trans.putNode("CID", gashConfig.getCID());
        trans.putNode("COID", orderId);
        trans.putNode("CUID", "TWD");
        trans.putNode("AMOUNT", String.valueOf(amount/100));

        String erqc = trans.getErqc(trans.getPwd(), trans.getKey(), trans.getIv());
        trans.putNode("ERQC", erqc);

        if (msgType == this.CREATE_MSG_TYPE) {
            //订单内容
            trans.putNode("MID", gashConfig.getMID());
            trans.putNode("PAID", paid);
            trans.putNode("RETURN_URL", gashConfig.getReturnUrl());
            trans.putNode("ORDER_TYPE", "M");
            trans.putNode("MEMO", extension);
        } else if (msgType == this.SETTLE_MSG_TYPE) {
            //结算内容
            trans.putNode("MID", gashConfig.getMID());
            trans.putNode("PAID", paid);
        } else if (msgType == this.QUERY_MSG_TYPE) {
            //订单查询内容
        }
        return trans.getSendData();
    }

    private String getSettleResponse( String data ){
        String recvData;
        Settle ws = new Settle();
        SettleSoap port = ws.getSettleSoap();
        recvData = port.getResponse(data);
        return recvData;
    }

    public String getOrderResponse( String data ){
        String recvData;
        Checkorder ws = new Checkorder();
        CheckorderSoap port = ws.getCheckorderSoap();
        recvData = port.getResponse(data);
        return recvData;
    }

    /**
     * 完成子订单
     * @param gashRechargeRecord
     * @param tradeOrderId
     */
    private void subRecordComplete(GashRechargeRecord gashRechargeRecord, String tradeOrderId) {
        //更新结算信息
        gashRechargeRecord.setPlatformOrderId(tradeOrderId);
        gashRechargeRecord.setUpdateTime(DateUtils.now());
        gashRechargeRecord.setPlatformStatus(GashOrderStatus.SORS_SUCCESS.getValue());
        updateSubRecord(gashRechargeRecord);
    }
}
