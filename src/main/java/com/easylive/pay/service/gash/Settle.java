
package com.easylive.pay.service.gash;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.1.6 in JDK 6
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "settle", targetNamespace = "http://egsys.org/", wsdlLocation = "http://stage-api.eg.gashplus.com/CP_Module/settle.asmx?wsdl")
public class Settle
    extends Service
{

    private final static URL SETTLE_WSDL_LOCATION;
    private final static Logger logger = Logger.getLogger(Settle.class.getName());

    static {
        URL url = null;
        try {
            URL baseUrl;
            baseUrl = Settle.class.getResource(".");
            url = new URL(baseUrl, "https://api.eg.gashplus.com/CP_Module/settle.asmx");
        } catch (MalformedURLException e) {
            logger.warning("Failed to create URL for the wsdl Location: 'http://stage-api.eg.gashplus.com/CP_Module/settle.asmx?wsdl', retrying as a local file");
            logger.warning(e.getMessage());
        }
        SETTLE_WSDL_LOCATION = url;
    }

    public Settle(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public Settle() {
        super(SETTLE_WSDL_LOCATION, new QName("http://egsys.org/", "settle"));
    }

    /**
     * 
     * @return
     *     returns SettleSoap
     */
    @WebEndpoint(name = "settleSoap")
    public SettleSoap getSettleSoap() {
        return super.getPort(new QName("http://egsys.org/", "settleSoap"), SettleSoap.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns SettleSoap
     */
    @WebEndpoint(name = "settleSoap")
    public SettleSoap getSettleSoap(WebServiceFeature... features) {
        return super.getPort(new QName("http://egsys.org/", "settleSoap"), SettleSoap.class, features);
    }

}
