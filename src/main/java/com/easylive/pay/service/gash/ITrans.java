package com.easylive.pay.service.gash;

import org.dom4j.Document;

public interface ITrans {

    /**
     * 產生交易訊息之 XML 物件
     * @return XML 物件
     */
     Document generateXmlDoc();

    /***
     * 取得交易訊息之 XML 字串
     * @return XML 字串 
     */
    String getXMLString();

    /**
     * 產生欲傳遞給 GPS 之交易訊息資料
     * @return 以 BASE64 編碼後之交易訊息
     */
    String getSendData();


    /**
     * 取得交易驗證壓碼
     * @param pw Content Provider 之密碼
     * @param key 加密密鑰
     * @param iv 加密向量
     * @return Content Provider 交易驗證壓碼
     */
    String getErqc(String pw, String key, String iv);

    /**
     * 檢測交易驗證壓碼
     * @return 正確或錯誤
     */
    Boolean verifyErqc();

    /**
     * 取得 GPS 回應驗證壓碼
     * @param key 加密密鑰
     * @param iv 加密向量
     * @return GPS 回應驗證壓碼
     */
    String getErpc(String key, String iv);

    /**
     * 檢測GPS 回應驗證壓碼
     * @return 正確或錯誤
     */
    Boolean verifyErpc();

}
