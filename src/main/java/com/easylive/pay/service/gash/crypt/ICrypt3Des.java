/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.easylive.pay.service.gash.crypt;

/**
 * 3DES 加解密介面
 * <AUTHOR>
 * @version 1.0.0
 *
 */
public interface ICrypt3Des {

    /**
     * 以 3DES 加密本文
     * @param txt 欲加密之本文
     * @return 加密後之密文
     * @throws Exception
     */
    public String encrypt(String txt) throws Exception;

    /**
     * 以 3DES 解密密文
     * @param txt 欲解密之密文
     * @return 解密後本文
     * @throws Exception
     */
    public String decrypt(String txt) throws Exception;

}
