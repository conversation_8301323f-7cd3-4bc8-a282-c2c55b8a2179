package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.CashoutSyd;
import com.easylive.pay.dao.UserBankCardRepository;
import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.bank.SydFunCode;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.cashout.syd.*;
import com.easylive.pay.model.user.IdentityInfo;
import com.easylive.pay.utils.Base64;
import com.easylive.pay.utils.*;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 税优地系统
 *
 * <AUTHOR>
 * @date 2020/7/15
 */
@Service
@Slf4j
public class SydService {

    @Autowired
    private CashoutSyd sydConfig;

    @Autowired
    private UserService userService;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private XZYQBankService xzyqBankService;

    private String getSydUrl() {
        return sydConfig.getCashoutSydDomain() + "/trans-business/clientBusiness/common.do";
    }

    private String generateReqId() {
        return "yzb" + System.currentTimeMillis();
    }

    /**
     * 1. 客户签约
     *
     * @param cardName 账户名
     * @param idCard   身份证号
     * @param mobile   手机号
     * @param cardNo   银行卡号
     * @return
     */
    public boolean signContract(String cardName, String cardNo, String idCard, String mobile) {

        String json = buildSignContractJson(cardNo, idCard, mobile, cardName);
        RequestMessage requestMessage = requestSydSystem(SydFunCode.SIGN, json);
        log.info("[signContract] 1. requestMessage={}", requestMessage);
        if (requestMessage == null) {
            return false;
        }
        if (!requestMessage.getResCode().equals(Constants.SYD_SUCCESS)) {
            log.error("[signContract] 1. sign contract failed, cardNo={}, requestMessage={}", cardNo, JsonUtils.printObject(requestMessage));
            return false;
        }
        return true;
    }

    /**
     * 2. 批量付款
     *
     * @param cashoutRecord 提现信息
     * @param bankCard      银行卡号
     * @param idNo          身份证号
     */
    public void payment(CashoutRecord cashoutRecord, UserBankCard bankCard, String idNo) {
        log.info("[payment] 1. cashout start, cashoutRecord={}, bankCard={}, idNo={}",
                cashoutRecord.toString(), bankCard.toString(), idNo);

        IdentityInfo identityInfo = userService.getIdentityByUid(cashoutRecord.getUid());
        if (StringUtils.isEmpty(identityInfo.getPhone())) {
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现失败，手机号不存在");
        }
        String mobile = AppStringUtils.simplePhone(identityInfo.getPhone());

        long rmb = cashoutRecord.getRmb();
        if (sydConfig.isCashoutSydDevelopment()) {
            rmb = 1L;
        }

        String json = buildPaymentJson(rmb, idNo, cashoutRecord.getOrderid(), mobile, bankCard.getAccountNo(), bankCard.getAccountName());
        RequestMessage requestMessage = requestSydSystem(SydFunCode.MULTI_PAY, json);
        if (!requestMessage.getResCode().equals(Constants.SYD_SUCCESS)) {
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, requestMessage.getResMsg());
        }
        String detailData = parseResData(requestMessage.getResData());
        log.info("[payment] 2. payment result={}", detailData);
        PaymentResData resData = JsonUtils.fromString(detailData, new TypeReference<PaymentResData>() {
        });

        if (resData == null || resData.getPayResultList() == null) {
            log.error("[payment] 1. cashout failed, resData={}", JsonUtils.printObject(resData));
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现失败，请稍后再试");
        }

        if (resData.getPayResultList().size() > 0) {
            for (PaymentResData.PayResult payResult : resData.getPayResultList()) {
                if (payResult.getResCode().equals(Constants.SYD_SUCCESS)) {
                    return;
                } else {
                    log.error("[payment] 2. cashout failed, resData={}", JsonUtils.printObject(resData));
                    throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, payResult.getResMsg());
                }
            }
        }

        throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现失败，请稍后再试");
    }

    /**
     * 3. 付款查询
     *
     * @param merBatchId 商户批次号
     * @param merOrderId 商户订单号
     */
    public PaymentQueryResData paymentQuery(String merBatchId, String merOrderId) {
        String json = buildPaymentQuery(merBatchId, Collections.singletonList(merOrderId));
        RequestMessage requestMessage = requestSydSystem(SydFunCode.MULTI_PAY_QUERY, json);
        if (!requestMessage.getResCode().equals(Constants.SYD_SUCCESS)) {
            log.error("[paymentQuery] 1. payment query failed, error={}", requestMessage.toString());
            return null;
        }
        String detailData = parseResData(requestMessage.getResData());
        log.info("[paymentQuery] 1. payment query result={}", detailData);
        return JsonUtils.fromString(detailData, new TypeReference<PaymentQueryResData>() {
        });
    }

    private String parseResData(String resData) {
        byte[] base64bs = Base64.decode(resData);
        byte[] debs = new byte[0];
        try {
            debs = DESUtils.decrypt(base64bs, sydConfig.getCashoutSydInterKey());
        } catch (Exception e) {
            log.error("[parseResData] 1. DESUtils decrypt err={}" + e.getMessage());
        }
        return new String(debs, StandardCharsets.UTF_8);
    }

    private String buildPaymentQuery(String merBatchId, List<String> orderIds) {
        PaymentTransQueryModel ptq = new PaymentTransQueryModel();
        ptq.setMerBatchId(merBatchId);
        ptq.setMerId(sydConfig.getCashoutSydMerId());
        List<PaymentTransQueryModel.QueryItems> list = new ArrayList<>();
        for (String orderId : orderIds) {
            PaymentTransQueryModel.QueryItems pi1 = new PaymentTransQueryModel.QueryItems();
            pi1.setMerOrderId(orderId);
            list.add(pi1);
        }
        ptq.setQueryItems(list);
        return JsonUtils.printObject(ptq);
    }

    private String buildSignContractJson(String cardNo, String idCard, String mobile, String name) {
        ContractModel cm = new ContractModel();
        cm.setCardNo(cardNo);
        cm.setIdCard(idCard);
        cm.setMobile(AppStringUtils.simplePhone(mobile));
        cm.setName(name);
        cm.setSignType(0);
        return JsonUtils.printObject(cm);
    }

    private RequestMessage requestSydSystem(SydFunCode funCode, String json) {
        Map<String, Object> map = new HashMap<>(1);
        map.put("reqJson", getReqJson(funCode, json));
        log.info("[requestSydSystem] 1. funCode={}, json={}, reqJson={}", funCode, json, map.get("reqJson"));

        String resData = HttpUtils.doPost(getSydUrl(), map);
        log.info("[requestSydSystem] 2. resData={}", resData);
        if (StringUtils.isEmpty(resData)) {
            log.error("[requestSydSystem] request syd url failed");
            return null;
        }

        RequestMessage requestMessage = JsonUtils.fromString(resData, RequestMessage.class);
        if (requestMessage == null) {
            log.error("[requestSydSystem] 1. parse error, resData={}", resData);
        }
        log.info("[requestSydSystem] 3. requestMessage={}", requestMessage);

        return requestMessage;
    }

    private String getReqJson(SydFunCode funCode, String json) {
        RequestMessage rm = new RequestMessage();
        rm.setReqId(generateReqId());
        rm.setFunCode(funCode.getData());
        rm.setMerId(sydConfig.getCashoutSydMerId());
        if (funCode == SydFunCode.MULTI_PAY_QUERY) {
            rm.setVersion("V2.0");
        } else {
            rm.setVersion("V1.0");
        }
        byte[] bs = new byte[0];
        try {
            bs = DESUtils.encrypt(json.getBytes(StandardCharsets.UTF_8), sydConfig.getCashoutSydInterKey());
        } catch (Exception e) {
            log.error("[getReqJson] DESUtils encrypt error={}", e.getMessage());
        }
        String reqDataEncrypt = Base64.encode(bs);
        rm.setReqData(reqDataEncrypt);
        try {
            rm.setSign(RSA.sign(reqDataEncrypt, sydConfig.getCashoutSydPriKey()));
        } catch (Exception e) {
            log.error("[getReqJson] RSA sign error", e);
        }
        return JsonUtils.printObject(rm);
    }

    /**
     * 批量付款参数
     *
     * @param amt        金额（分）
     * @param idCard     身份证号
     * @param merOrderId 商户订单号
     * @param mobile     手机号
     * @param payeeAcc   银行卡号
     * @param payeeName  账户名
     * @return String
     */
    private String buildPaymentJson(long amt,
                                    String idCard,
                                    String merOrderId,
                                    String mobile,
                                    String payeeAcc,
                                    String payeeName) {
        PaymentTransModel pt = new PaymentTransModel();
        pt.setMerId(sydConfig.getCashoutSydMerId());
        pt.setMerBatchId(merOrderId);
        List<PaymentTransModel.PayItems> list = new ArrayList<>();
        PaymentTransModel.PayItems pi1 = getPayItems(amt, idCard, merOrderId, mobile, payeeAcc, payeeName);
        list.add(pi1);
        pt.setTotalAmt(String.valueOf((Long) list.stream().mapToLong(PaymentTransModel.PayItems::getAmt).sum()));
        pt.setTotalCount(String.valueOf(list.size()));
        pt.setPayItems(list);
        return JsonUtils.printObject(pt);
    }

    private PaymentTransModel.PayItems getPayItems(long amt,
                                                   String idCard,
                                                   String merOrderId,
                                                   String mobile,
                                                   String payeeAcc,
                                                   String payeeName) {
        PaymentTransModel.PayItems pi1 = new PaymentTransModel.PayItems();
        pi1.setMerOrderId(merOrderId);
        pi1.setAmt(amt);
        pi1.setPayeeName(payeeName);
        pi1.setPayeeAcc(payeeAcc);
        pi1.setIdCard(idCard);
        pi1.setMobile(mobile);
        pi1.setPaymentType(0);
        pi1.setPayType(0);
        pi1.setAccType(1);
        pi1.setMemo("工资代发");
        //pi1.setBranchName("北京银行");
        //pi1.setBranchNo("***********");
        //pi1.setProvince("北京");
        //pi1.setCity("北京");
        return pi1;
    }

    /**
     * 生成商户批次号
     */
    private String generateMerBatchId() {
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * 同步签约信息
     */
    public boolean sydSyncSign() {
        List<UserBankCard> list = userBankCardRepository.findAllByIsDeletedIsFalse();
        if (!list.isEmpty()) {
            int i = 0;
            for (UserBankCard u : list) {
                IdentityInfo identityInfo = userService.getIdentityByUid(u.getUid());
                if (identityInfo != null
                        && StringUtils.isNotEmpty(identityInfo.getIdCard())
                        && StringUtils.isNotEmpty(identityInfo.getPhone())) {
                    String phone = AppStringUtils.simplePhone(identityInfo.getPhone());
                    signContract(u.getAccountName(), u.getAccountNo(), identityInfo.getIdCard(), phone);
                    log.info("[sydSyncSign][{}] syd batch sign, uid={}", i, u.getUid());
                } else {
                    log.error("[sydSyncSign][{}] get user phone failed, uid={}", i, u.getUid());
                }
                i++;
            }
        }
        return true;
    }

    /**
     * 同步签约信息
     */
    public boolean xzycSign(int operType, int start, int count) {
        List<UserBankCard> list = userBankCardRepository.listUSerBankCard(start, count);
        if (!list.isEmpty()) {
            int i = 0;
            for (UserBankCard u : list) {
                IdentityInfo identityInfo = userService.getIdentityByUid(u.getUid());
                if (identityInfo != null
                        && StringUtils.isNotEmpty(identityInfo.getIdCard())
                        && StringUtils.isNotEmpty(identityInfo.getPhone())) {
                    String phone = AppStringUtils.simplePhone(identityInfo.getPhone());

                    SignContractModel sign = new SignContractModel();
                    sign.setUid(u.getUid());
                    sign.setOperType(operType);
                    sign.setAccountName(u.getAccountName());
                    sign.setAccountNo(u.getAccountNo());
                    sign.setBankName(u.getOpenBankName());
                    sign.setBankNo(u.getOpenBankNo());
                    sign.setIdCard(identityInfo.getIdCard());
                    sign.setPhone(phone);
                    boolean xzyqRet = xzyqBankService.signContractNew(sign.getUid(),
                            sign.getOperType(),
                            sign.getAccountNo(),
                            sign.getAccountName(),
                            sign.getBankNo(),
                            sign.getBankName(),
                            sign.getIdCard(),
                            sign.getPhone());
                    log.info("[xzycSign][{}] syd batch sign, uid={}, ret={}", i, u.getUid(), xzyqRet);
                } else {
                    log.error("[xzycSign][{}] get user phone failed, uid={}", i, u.getUid());
                }
                i++;
            }
        }
        return true;
    }

}
