package com.easylive.pay.service;

import com.easylive.pay.common.mgs.MgsServiceBuilder;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.entity.Redpack;
import com.easylive.pay.entity.RedpackNumber;
import com.easylive.pay.model.User;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import feign.QueryMap;
import feign.RequestLine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by c on 2016/1/22.
 */

interface MgsChatService {
    /**
     * 发送消息
     * @param input
     */
    @RequestLine("POST /service/sendToChat")
    void sendToChat(@QueryMap SendToChat input);
}

@Data
@AllArgsConstructor
@NoArgsConstructor
class SendToChat {
    @NotBlank
    private String cmd = "";

    @NotBlank
    private String args = "";

    private int groupId;

    private String vid = "";
}

@Service
public class ChatService {

    private static Logger logger = LoggerFactory.getLogger(ChatService.class);

    @Autowired
    private URLConfig urlConfig;

    private MgsChatService mgsChatService;

    @PostConstruct
    public void init() {
        mgsChatService = MgsServiceBuilder.build(urlConfig.getChatServiceUrl(), MgsChatService.class);
    }


    private void pushMessageToChatSched(String vid, String cmd, Map<String, Object> args) {
        GsonBuilder builder = new GsonBuilder().setDateFormat("yyyy-MM-dd");
        Gson gson = builder.create();
        String strArgs = gson.toJson(args);

        logger.info("vid: " + vid + ", cmd: " + cmd + ", args:" + strArgs);

        SendToChat input = new SendToChat(cmd, strArgs, 0, vid);
        mgsChatService.sendToChat(input);
    }

    @Async
    public void openRedpackToChatSched(User user, String vid, RedpackNumber redpackNumber, Redpack redpack) {
        Map<String, Object> args = new HashMap<>();
        args.put("vid", vid);
        args.put("hid", redpackNumber.getRedpackCode());
        args.put("flag", 0);
        args.put("usrname", user.getName());
        args.put("usrlogo", user.getLogoUrl());
        args.put("usrnickname", user.getNickname());
        args.put("isbest", redpackNumber.getBest());
        args.put("money", redpackNumber.getValue());
        args.put("sid", user.getSessionId());
        args.put("htype", redpack.getType());

        this.pushMessageToChatSched(vid, "redpacket", args);
    }

    @Async
    public void createRedpackToChatSched(User user, String vid, Redpack redpack) {
        Map<String, Object> args = new HashMap<>();
        args.put("vid", vid);
        args.put("hid", redpack.getCode());
        args.put("usrname", user.getName());
        args.put("usrlogo", user.getLogoUrl());
        args.put("usrnickname", user.getNickname());
        args.put("hname", redpack.getName());
        args.put("hlogo", redpack.getLogo());
        args.put("htype", redpack.getType());
        args.put("time", redpack.getDuration());
        args.put("flag", 1);
        args.put("money", redpack.getValue());
        args.put("count", redpack.getCount());
        args.put("sid", user.getSessionId());

        this.pushMessageToChatSched(vid, "redpacket", args);
    }

    public void updateRicerollToChatSched(String vid, long riceroll) {

        try {
            Map<String, Object> args = new HashMap<>();
            args.put("riceroll", riceroll);
            args.put("vid", vid);

            this.pushMessageToChatSched(vid, "modifyvideoinfo", args);
        } catch (Exception e) {
            logger.error("error sending riceroll info to chat server, {}, {}", vid, riceroll);
        }
    }

}
