package com.easylive.pay.service.reapal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.ReapalConfig;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.model.RechargeResult;
import com.easylive.pay.service.PayService;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.utils.Md5Utils;
import com.easylive.pay.utils.ReapalDecipher;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by liwd on 2016/12/25.
 */
@Service
public class ReapalService {

    private static Logger logger = LoggerFactory.getLogger(ReapalService.class);

    @Autowired
    private ReapalConfig reapalConfig;

    @Autowired
    private PayService payService;

    @Autowired
    private RechargeService rechargeService;


    public Map<String, String> createReapalOrder(long uid, long amount, String memberIp, String terminalType, String terminalInfo, String defaultBank, String returnUrl) {
        //service_fee//手续费;单位为分，教育商户填此字段
        //pay_cus_no//付款客户号;支付方式为银行直列时：民生、浦发、交通三家银行B2B支付需要提交该字段
        OrderInfo order = new OrderInfo(Constants.DEFAULT_APP_ID, uid, amount, PayPlatform.PPF_BANK_PAY, MobilePlatform.MPF_PC, memberIp);
        RechargeRecord rechargeRecord = rechargeService.createRechargeRecord(order);
        String orderId = rechargeRecord.getOrderId();

        Map<String, String> sPara = new HashMap<>();
        sPara.put("seller_email", reapalConfig.getSellerEmail());
        sPara.put("merchant_id", reapalConfig.getMerchantid());
        sPara.put("notify_url", reapalConfig.getNotifyUrl());
        sPara.put("return_url", returnUrl);
        sPara.put("transtime", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
//      sPara.put("currency", "156");//可选参数,默认人民币156
        sPara.put("sign_type", reapalConfig.getSigntype());
        sPara.put("payment_type", "1");//支付类型
        sPara.put("pay_method", "directPay");//支付方式
        sPara.put("member_id", String.valueOf(uid));
        sPara.put("member_ip", memberIp);//用户IP
        sPara.put("terminal_type", terminalType);//终端类型
        sPara.put("terminal_info", terminalInfo);//终端信息
        sPara.put("default_bank", defaultBank);//网银代码,支付方式为银行直列时有效
        sPara.put("order_no", orderId);
        String detail = payService.getProductName(PayPlatform.PPF_BANK_PAY, amount);
        sPara.put("total_fee", String.valueOf(amount));
        sPara.put("title", detail);
        sPara.put("body", detail);
        String mysign = Md5Utils.buildMySign(sPara, reapalConfig.getKey());//生成签名结果
        sPara.put("sign", mysign);
        String json = JSON.toJSONString(sPara);

        Map<String, String> maps;
        try {
            maps = ReapalDecipher.encryptData(reapalConfig.getPubkey(), json);
            maps.put("merchant_id", reapalConfig.getMerchantid());
            maps.put("orderid", orderId);
            maps.put("rongpay_api", reapalConfig.getRongpayApi());
        } catch (Exception e) {
            throw new ResponseException(ResponseError.E_SERVER, "Internal Server Error, Reapal Encryption error");
        }

        logger.info("[createReapalOrder][success] orderid:[{}],uid:[{}],rmb:[{}]", orderId, uid, amount);
        return maps;
    }

    @Transactional
    public String reapalNotify(String data, String encryptKey) throws Exception {
        String result = null;
        String decryData = ReapalDecipher.decryptData(reapalConfig.getPrivatekey(), reapalConfig.getPrivatekeyPwd(), encryptKey, data);
        JSONObject jsonObject = JSON.parseObject(decryData);
        Map<String, String> sPara = new HashMap<>();
        String merchantId = (String) jsonObject.get("merchant_id");
        String tradeNo = (String) jsonObject.get("trade_no");
        String orderNo = (String) jsonObject.get("order_no");
        String totalFee = (String) jsonObject.get("total_fee");
        String status = (String) jsonObject.get("status");
        String sign = (String) jsonObject.get("sign");
        String notifyId = (String) jsonObject.get("notify_id");
        sPara.put("merchant_id", merchantId);
        sPara.put("trade_no", tradeNo);
        sPara.put("order_no", orderNo);
        sPara.put("total_fee", totalFee);
        sPara.put("status", status);
        sPara.put("notify_id", notifyId);
        String mySign = Md5Utils.buildMySign(sPara, reapalConfig.getKey());
        if (sign.equals(mySign)) {
            if (status.equals("TRADE_FINISHED")) {
                logger.info("[reapalNotify][success] orderid:[{}], reapay response is TRADE_FINISHED", orderNo);
                RechargeRecord r = rechargeService.getRecordByOrderIdWithLock(orderNo);
                if (r != null && r.getStatus() == OrderStatus.ORS_CREATE.getValue()) {
                    RechargeResult rechargeResult = rechargeService.recharge(r, tradeNo);
                    result = "success";
                    logger.info("[reapalNotify][recharge][success] uid [{}],rmb [{}],ecoin [{}],orderid [{}]", r.getUid(), r.getRmb(), rechargeResult.getEcoin(), r.getOrderId());
                } else {
                    if (r == null) {
                        logger.error("[reapalNotify][recharge][failed] Order is not created");
                    } else {
                        logger.error("[reapalNotify][recharge][failed] Order [{}] is dealed,order stauts [{}]", r.getOrderId(), r.getStatus());
                    }
                }
            } else {
                logger.error("[reapalNotify][failed] orderid [{}], reapay response status [{}]", orderNo, status);
            }
        } else {
            logger.error("[reapalNotify][verify][failed] orderid [{}], reapay sign [{}], our sign [{}]", orderNo, sign, mySign);
        }
        return result;
    }
}
