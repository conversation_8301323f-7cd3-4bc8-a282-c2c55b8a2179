package com.easylive.pay.service;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseErrorMessage;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.ThirdBankConfig;
import com.easylive.pay.model.MediaTypeXmlT;
import com.easylive.pay.model.cashout.*;
import com.easylive.pay.utils.EncodeUtils;
import com.easylive.pay.utils.MAC;
import com.easylive.pay.utils.XMLUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/8/13
 */
@Service
public class TestService {

    private static Logger logger = LoggerFactory.getLogger(TestService.class);

    private RestTemplate restTemplate;

    @Autowired
    private ThirdBankConfig thirdBankConfig;

    private static String MacSecretKey;

    @Autowired
    private TaxCollectService taxCollectService;

    @PostConstruct
    private void init() {
        HttpClient httpClient = HttpClients.custom().build();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(
                httpClient);
        requestFactory.setConnectionRequestTimeout(30000);
        requestFactory.setConnectTimeout(30000);
        requestFactory.setReadTimeout(30000);
        this.restTemplate = new RestTemplate(requestFactory);
        restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());
        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter(Charset.forName("GBK"));
        stringHttpMessageConverter.setWriteAcceptCharset(false);
        restTemplate.getMessageConverters().add(0, stringHttpMessageConverter);

        MacSecretKey = thirdBankConfig.getSecretKey();
    }

    public static String getMac(Map<String, String> args) {
        String data = "0200 199559993500000000002 000000 ********0000 ********** 003924 7011 00 ********** ********** F4112345 F42012345678912";
        try {
            String mac = MAC.getOnlineMac(data, MacSecretKey);
            logger.info("generate MAC={}", mac);
            return mac;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("get MAC string failed, data={}", data);
        }
        return "";
    }

    /**
     * 3.1.1 客户工资提取
     */
    public static String buildWithdrawBankXml(Map<String, String> args) {
        /*<?xml version="1.0" encoding="gbk"?>
            <root>
              <head>
                <TransCode>1120</TransCode>
                <TransDate>********</TransDate>
                <TransTime>151902</TransTime>
                <ChnlType>CZ01</ChnlType>
                <TrcNo>********0008</TrcNo>
                <EnterpriseNo>********</EnterpriseNo>
                <BankNo>********</BankNo>
                <Mac>************</Mac>
              </head>
              <body>
                <employeeNum>1000520203920005345</employeeNum>
                <txAmt>100.00</txAmt>
                <acctountNo>6216520203920005345</acctountNo>
                <acctName>李三一</acctName>
                <bankNo>************</bankNo>
                <bankName>淇县中原村镇银行鹤淇支行</bankName>
                <idType>01</idType>
                <idNo>410603199001011056</idNo>
                <brf>abc</brf>
              </body>
            </root>*/
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode("1120");
        requestXMLHead.setTransDate("********");
        requestXMLHead.setTransTime("151902");
        requestXMLHead.setChnlType("CZ01");
        requestXMLHead.setTrcNo("********0008");
        requestXMLHead.setEnterpriseNo("********");
        requestXMLHead.setBankNo("********");

        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);

        WithdrawRequestBody withdrawBody = new WithdrawRequestBody();
        withdrawBody.setEmployeeNum("1000520203920005345");
        withdrawBody.setTxAmt("1.00");
        withdrawBody.setAcctountNo("6216520203920005345");
        withdrawBody.setAcctName("李三一");
        withdrawBody.setBankNo("************");
        withdrawBody.setBankName("淇县中原村镇银行鹤淇支行");
        withdrawBody.setIdType("01");
        withdrawBody.setIdNo("410603199001011056");
        withdrawBody.setBrf("客户工资提取");

        baseRequestXML.setBody(withdrawBody);

        //生成MAC
        String mac = getMacFromBean(requestXMLHead, withdrawBody.getAcctountNo());
        requestXMLHead.setMac(mac);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);

        return EncodeUtils.utf8ToGbk(xml);
    }

    /**
     * 3.1.2 客户工资提取冲正
     */
    public static String buildWithdrawReversalXml() {
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode("1121");
        requestXMLHead.setTransDate("********");
        requestXMLHead.setTransTime("151902");
        requestXMLHead.setChnlType("CZ01");
        requestXMLHead.setTrcNo("********0008");
        requestXMLHead.setEnterpriseNo("********");
        requestXMLHead.setBankNo("********");

        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);

        WithdrawReversalRequestBody withdrawBody = new WithdrawReversalRequestBody();
        withdrawBody.setEmployeeNum("1000520203920005345");
        withdrawBody.setTxAmt("1.00");
        withdrawBody.setAcctountNo("6216520203920005345");
        withdrawBody.setAcctName("李三一");
        withdrawBody.setBankNo("************");
        withdrawBody.setBankName("淇县中原村镇银行鹤淇支行");
        withdrawBody.setOriTransDate("********");
        withdrawBody.setOriTransTime("151902");
        withdrawBody.setOriTransTraceNo("********0008");
        withdrawBody.setBrf("客户工资提取冲正");

        baseRequestXML.setBody(withdrawBody);

        //生成MAC
        String mac = getMacFromBean(requestXMLHead, withdrawBody.getAcctountNo());
        requestXMLHead.setMac(mac);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);

        return EncodeUtils.utf8ToGbk(xml);
    }

    /**
     * 3.1.3 客户工资提取撤销
     */
    public static String buildWithdrawCancelXml() {
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode("1122");
        requestXMLHead.setTransDate("********");
        requestXMLHead.setTransTime("151902");
        requestXMLHead.setChnlType("CZ01");
        requestXMLHead.setTrcNo("********0008");
        requestXMLHead.setEnterpriseNo("********");
        requestXMLHead.setBankNo("********");

        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);

        WithdrawReversalRequestBody withdrawBody = new WithdrawReversalRequestBody();
        withdrawBody.setEmployeeNum("1000520203920005345");
        withdrawBody.setAcctountNo("6216520203920005345");
        withdrawBody.setTxAmt("1.00");
        withdrawBody.setAcctName("李三一");
        withdrawBody.setBankNo("************");
        withdrawBody.setBankName("淇县中原村镇银行鹤淇支行");
        withdrawBody.setOriTransDate("********");
        withdrawBody.setOriTransTime("151902");
        withdrawBody.setOriTransTraceNo("********0008");
        withdrawBody.setBrf("客户工资提取撤销");

        baseRequestXML.setBody(withdrawBody);

        //生成MAC
        String mac = getMacFromBean(requestXMLHead, withdrawBody.getAcctountNo());
        requestXMLHead.setMac(mac);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);

        return EncodeUtils.utf8ToGbk(xml);
    }

    /**
     * 3.1.4 客户工资提取确认
     */
    public static String buildWithdrawConfirmXml() {
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode("1123");
        requestXMLHead.setTransDate("********");
        requestXMLHead.setTransTime("151902");
        requestXMLHead.setChnlType("CZ01");
        requestXMLHead.setTrcNo("********0008");
        requestXMLHead.setEnterpriseNo("********");
        requestXMLHead.setBankNo("********");

        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);

        WithdrawConfirmRequestBody withdrawBody = new WithdrawConfirmRequestBody();
        withdrawBody.setEmployeeNum("1000520203920005345");
        withdrawBody.setAcctountNo("6216520203920005345");
        withdrawBody.setTxAmt("1.00");
        withdrawBody.setAcctName("李三一");
        withdrawBody.setBankNo("************");
        withdrawBody.setBankName("淇县中原村镇银行鹤淇支行");
        withdrawBody.setOriTransDate("********");
        withdrawBody.setOriTransTime("151902");
        withdrawBody.setOriTransTraceNo("********0008");
        withdrawBody.setBrf("客户工资提取确认");

        baseRequestXML.setBody(withdrawBody);

        //生成MAC
        String mac = getMacFromBean(requestXMLHead, withdrawBody.getAcctountNo());
        requestXMLHead.setMac(mac);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);

        return EncodeUtils.utf8ToGbk(xml);
    }

    /**
     * 3.1.5 客户工资提取结果确认
     */
    public static String buildWithdrawResultConfirmXml() {
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode("1124");
        requestXMLHead.setTransDate("********");
        requestXMLHead.setTransTime("151902");
        requestXMLHead.setChnlType("CZ01");
        requestXMLHead.setTrcNo("********0008");
        requestXMLHead.setEnterpriseNo("********");
        requestXMLHead.setBankNo("********");

        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);

        WithdrawResultConfirmRequestBody withdrawBody = new WithdrawResultConfirmRequestBody();
        withdrawBody.setEmployeeNum("1000520203920005345");
        withdrawBody.setAcctountNo("6216520203920005345");
        withdrawBody.setTxAmt("1.00");
        withdrawBody.setAcctName("李三一");
        withdrawBody.setBankNo("************");
        withdrawBody.setBankName("淇县中原村镇银行鹤淇支行");
        withdrawBody.setOriTransDate("********");
        withdrawBody.setOriTransTime("151902");
        withdrawBody.setOriTransTraceNo("********0008");
        withdrawBody.setBrf("客户工资提取确认");

        baseRequestXML.setBody(withdrawBody);

        //生成MAC
        String mac = getMacFromBean(requestXMLHead, withdrawBody.getAcctountNo());
        requestXMLHead.setMac(mac);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);

        return EncodeUtils.utf8ToGbk(xml);
    }

    /**
     * 3.2.1 交易信息查询
     */
    public static String buildTradeInfoXml() {
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode("1101");
        requestXMLHead.setTransDate("********");
        requestXMLHead.setTransTime("151902");
        requestXMLHead.setChnlType("CZ01");
        requestXMLHead.setTrcNo("********0008");
        requestXMLHead.setEnterpriseNo("********");
        requestXMLHead.setBankNo("********");

        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);

        TradeInfoRequestXML withdrawBody = new TradeInfoRequestXML();
        withdrawBody.setSelTransDate("********");
        withdrawBody.setSelTransTime("151902");
        withdrawBody.setSelTransTraceNo("********0008");
        withdrawBody.setBrf("交易信息查询");

        baseRequestXML.setBody(withdrawBody);

        //生成MAC
        String mac = getMacFromBean(requestXMLHead);
        requestXMLHead.setMac(mac);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);

        return EncodeUtils.utf8ToGbk(xml);
    }

    /**
     * 3.2.2 客户信息签约维护
     */
    public static String buildAccountSignXml() {
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode("1102");
        requestXMLHead.setTransDate("********");
        requestXMLHead.setTransTime("151902");
        requestXMLHead.setChnlType("CZ01");
        requestXMLHead.setTrcNo("********0008");
        requestXMLHead.setEnterpriseNo("********");
        requestXMLHead.setBankNo("********");

        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);

        AccountSignRequestBody withdrawBody = new AccountSignRequestBody();
        withdrawBody.setEmployeeNum("1000520203920005345");
        withdrawBody.setAcctountNo("6216520203920005345");
        withdrawBody.setAcctName("李三一");
        withdrawBody.setBankNo("************");
        withdrawBody.setBankName("淇县中原村镇银行鹤淇支行");
        withdrawBody.setIdType("01");
        withdrawBody.setIdNo("410603199001011056");
        withdrawBody.setCellPhone("***********");
        withdrawBody.setBrf("客户信息签约维护");

        baseRequestXML.setBody(withdrawBody);

        //生成MAC
        String mac = getMacFromBean(requestXMLHead, withdrawBody.getAcctountNo());
        requestXMLHead.setMac(mac);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);

        return EncodeUtils.utf8ToGbk(xml);
    }

    public static String getMacFromBean(RequestXMLHead baseXML) {
        return getMacFromBean(baseXML, null);
    }

    public static String getMacFromBean(RequestXMLHead baseXML, String accountNo) {
        /**
         * 交易日期 交易时间 交易流水 交易代码 企业机构代码 交易账号(选填)
         */
        List<String> arrayList = new ArrayList<>();
        arrayList.add(baseXML.getTransDate());
        arrayList.add(baseXML.getTransTime());
        arrayList.add(baseXML.getTrcNo());
        arrayList.add(baseXML.getTransCode());
        arrayList.add(baseXML.getEnterpriseNo());
        if (accountNo != null) {
            arrayList.add(accountNo);
        }
        String macData = StringUtils.join(arrayList, " ");
        return MAC.getOnlineMac(macData, MacSecretKey);
    }

    public static Object bankObjFromXml() {

        /**
         * <?xml version="1.0" encoding="CHARSET_GBK"?>
         * <root>
         * 	<head>
         * 		<TransDate>********</TransDate>
         * 		<TransTime>175246</TransTime>
         * 		<TransCode>1200</TransCode>
         * 		<ChnlType>0001</ChnlType>
         * 		<TrcNo>********175246000027</TrcNo>
         * 		<EnterpriseNo>********</EnterpriseNo>
         * 		<BankNo>806000</BankNo>
         * 		<ResCode>0000</ResCode>
         * 		<ResMsg>交易成功</ResMsg>
         * 		<BankTxDate>********</BankTxDate>
         * 		<BankTxTime>175246</BankTxTime>
         * 		<BankTraceNo>************</BankTraceNo>
         * 	</head>
         *  	<body>
         * 		<txSts>1</txSts>
         * 	</body>
         * </root>
         */

        String xml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<root>\n" +
                "<head>\n" +
                "<TransDate>********</TransDate>\n" +
                "<TransTime>175246</TransTime>\n" +
                "<TransCode>1200</TransCode>\n" +
                "<ChnlType>0001</ChnlType>\n" +
                "<TrcNo>********175246000027</TrcNo>\n" +
                "<EnterpriseNo>********</EnterpriseNo>\n" +
                "<BankNo>806000</BankNo>\n" +
                "<ResCode>0000</ResCode>\n" +
                "<ResMsg>交易成功</ResMsg>\n" +
                "<BankTxDate>********</BankTxDate>\n" +
                "<BankTxTime>175246</BankTxTime>\n" +
                "<BankTraceNo>************</BankTraceNo>\n" +
                "</head>\n" +
                " <body>\n" +
                "<txSts>1</txSts>\n" +
                "</body>\n" +
                "</root>\n";

        String headXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<head>\n" +
                "<TransDate>********</TransDate>\n" +
                "<TransTime>175246</TransTime>\n" +
                "<TransCode>1200</TransCode>\n" +
                "<ChnlType>0001</ChnlType>\n" +
                "<TrcNo>********175246000027</TrcNo>\n" +
                "<EnterpriseNo>********</EnterpriseNo>\n" +
                "<BankNo>806000</BankNo>\n" +
                "<ResCode>0000</ResCode>\n" +
                "<ResMsg>交易成功</ResMsg>\n" +
                "<BankTxDate>********</BankTxDate>\n" +
                "<BankTxTime>175246</BankTxTime>\n" +
                "<BankTraceNo>************</BankTraceNo>\n" +
                "</head>\n";

        String bodyXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<body>\n" +
                "<txSts>1</txSts>\n" +
                "</body>\n";

        ResponseXMLHead headObj = (ResponseXMLHead) XMLUtils.xmlToBean(headXml, ResponseXMLHead.class);
        logger.info("Head obj = {}", headObj);

        ResponseXMLBody bodyObj = (ResponseXMLBody) XMLUtils.xmlToBean(bodyXml, ResponseXMLBody.class);
        logger.info("Body obj = {}", bodyObj);

        BaseResponseXML xmlObj = (BaseResponseXML) XMLUtils.xmlToBean(xml, BaseResponseXML.class);
        logger.info("XML obj = {}", xmlObj);

        return XMLUtils.xmlToBean(xml, BaseResponseXML.class);

    }

    public void requestBankUrlWithXml(String xml) {
        try {
            //logger.info("xml = {}", xml);
            //logger.info("bankUrl = {}", bankUrl);
            HttpHeaders headers = new HttpHeaders();
            //headers.setContentType(MediaType.APPLICATION_XML);
            headers.setContentType(MediaTypeXmlT.APPLICATION_XML_GBK);
            HttpEntity<String> httpEntity = new HttpEntity<>(xml, headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(thirdBankConfig.getBankUrl(), httpEntity, String.class);
            logger.info("xml response = {}", responseEntity);
            String xmlB = responseEntity.getBody();
            //logger.info("xmlB body = {}", xmlB);
            String gbkXml = EncodeUtils.gbkToUtf8(xmlB);
            logger.info("IOS Xml = {}", gbkXml);
        } catch (Exception e) {
            logger.error("[requestBankUrlWithXml] Failed, Message={}", e.getMessage());
            throw new ResponseException(ResponseError.E_CASHOUT_WEIXIN_SERVICE, "提现服务器出错");
        }
    }

}
