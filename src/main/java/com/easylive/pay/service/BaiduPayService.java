package com.easylive.pay.service;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.model.baidu.BaiduOrderInfo;
import com.easylive.pay.model.baidu.BaiduSmallProgramOrderInfo;
import com.easylive.pay.utils.JsonUtils;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

@Service
public class BaiduPayService {

    private static final String DEAL_ID = "252073049";
    private static final String APP_KEY = "MMUn4g";
    private static final String APP_ID = "27658";
    private static Logger logger = LoggerFactory.getLogger(BaiduPayService.class);

    private String privateKey;
    private String baiduPublicKey;
    @Autowired
    private RechargeService rechargeService;

    @PostConstruct
    public void init() {
        load();
    }

    private void load() {
        privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALYXeyEuUnfH36aQ\n" +
                "+H3SouixcJF25DoBzIduLMHmOML3Qg941KXzRZ3TVUgee1oKObBMb3VFBW3eY4Tu\n" +
                "hUbAiMuEC2O0WE3Gf7Vn711nqSac7SNBHyHXryGMP9BYk1CAwGZ8LNKzW7DmHWVs\n" +
                "hOhplb7AUWeslPz+ZYEn7YLCcRBFAgMBAAECgYBItwnBj1j5YkTKpFvHCyVpHOqh\n" +
                "/ENZcrx1XKbc643BNbIYsZzhhEtJokoNGbcSlsDAyVU9FEn9vPAq9oEcZuh5CJY6\n" +
                "YA3cEWrXDOmYVqOXssbnNVlSjTIDwtFPDYhN58HIrzmuBO5vGv7ZuECKAqX4sPmN\n" +
                "/zzbYMLRzNnAli1TAQJBANrzV+J6xg3C81AbSM2oazMhvzYsmIuNqNl+RNcs94L8\n" +
                "Oz6wfx5BtK+gAvpiC2trXVlOfFXcOw5fhDhn00HLZQUCQQDU53XSlj+0hbOWzMfh\n" +
                "ZqpFmwyXQKyp00QpaKioJuD/QZPJxd+kb1nNFNHXF4EFt6Bq2m6/77cw7IA6/kqG\n" +
                "huJBAkEAgp8OXUUVhvw5IB9G7bp+ScDmTDGKyCa5Bxf3hc+D0hccWSf+jlRwsR3q\n" +
                "6Ok5GtqwlCjosGh0qvx86xG1zNjE6QJAdP3/28melieGaU58Y9sTpA4wnAj5GJU0\n" +
                "doDBdS1wJDQ37v+iBmz97tkK+mQ7DRY7u5vukeA7TVIYoNJNx6DlQQJAY8JgML4F\n" +
                "M2q1TQYkEY69n5FIj8d2khXfPOMG74eIp/d3QFU+jZQlY1Ru55eYSv/Qsa+wVZ4J\n" +
                "jl1pvmI+WCp55g==";

        baiduPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHOxQdiOmAuBpPwhD1nCTIy5OjJofodauEV19kx4K6OjceQ7ueooWylFI9LsGqkwd78zMN3YmrkxCr5v+zaaOchUwf6HdTgG5jSZDAa0fZo6vzWhTBdWXpzR1CGccPnxePeLMuDh0AP3l0vTYw0p0fOyjhULXKSXYE9eSMkkw0HQIDAQAB";
    }

    @Transactional
    public BaiduSmallProgramOrderInfo createSmallProgramOrder(long uid, long amount, MobilePlatform mobilePlatform, String clientIp) {
        logger.info("[Baidu] Create small program order: {}, {}, {},{} ", uid, amount, mobilePlatform, clientIp);
        BaiduOrderInfo orderInfo = new BaiduOrderInfo();

        OrderInfo order = new OrderInfo(0, uid, amount, PayPlatform.PPF_BAIDU_PAY, mobilePlatform, clientIp);
        RechargeRecord rr = rechargeService.createRechargeRecord(order);

        BigDecimal totalFeeDecimal = new BigDecimal(amount);
        String strAmount = String.valueOf(totalFeeDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        String title = "充值" + strAmount + "元";

        orderInfo.setTotalAmount(String.valueOf(amount));
        orderInfo.setAppKey(APP_KEY);
        orderInfo.setDealId(DEAL_ID);
        orderInfo.setTpOrderId(rr.getOrderId());
        orderInfo.setDealTitle(title);

        String sign = getSignature(orderInfo);
        orderInfo.setRsaSign(sign);
        orderInfo.setBizInfo(getBizInfoString(orderInfo));

        BaiduSmallProgramOrderInfo ret = new BaiduSmallProgramOrderInfo();
        ret.setOrderInfo(orderInfo);
        return ret;
    }

    @Transactional
    public void notifyValid(Map<String, String> params) {
        String sign = params.remove("rsaSign");
        if (!checkSignWithRsa(params, baiduPublicKey, sign)) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "Check failed");
        }

        String orderId = params.get("orderId");
        String tpOrderId = params.get("tpOrderId");

        RechargeRecord rr = rechargeService.ensureRecordByOrderId(tpOrderId);
        if (rr.getStatus() != 0) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "Invalid status");
        }
        rechargeService.recharge(rr, orderId);
    }

    private String getBizInfoString(BaiduOrderInfo orderInfo) {
        BaiduBizInfo bizInfo = new BaiduBizInfo();
        bizInfo.setAppKey(orderInfo.getAppKey());
        bizInfo.setDealId(orderInfo.getDealId());
        bizInfo.setTotalAmount(orderInfo.getTotalAmount());
        bizInfo.setTpOrderId(orderInfo.getTpOrderId());
        return JsonUtils.printObject(GenericMap.toMap("tpData", bizInfo));
    }

    private String getSignature(BaiduOrderInfo orderInfo) {
        Map<String, String> params = new HashMap<>();
        params.put("appKey", orderInfo.getAppKey());
        params.put("dealId", orderInfo.getDealId());
        params.put("tpOrderId", orderInfo.getTpOrderId());
        params.put("totalAmount", orderInfo.getTotalAmount());
        return genSignWithRsa(params, privateKey);
    }

    private String genSignWithRsa(Map<String, String> sortedParams, String privateKey) {
        String sortedParamsContent = getSignContent(sortedParams);
        return rsaSign(sortedParamsContent, privateKey, StandardCharsets.UTF_8.name());
    }

    private boolean checkSignWithRsa(Map<String, String> sortedParams, String pubKey, String sign) {
        String sortedParamsContent = getSignContent(sortedParams);
        return doCheck(sortedParamsContent, sign, pubKey);
    }

    /**
     * @param sortedParams 已经排序的字符串
     * @return 返回签名后的字符串
     */
    private String getSignContent(Map<String, String> sortedParams) {
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList<>(sortedParams.keySet());
        Collections.sort(keys);
        int index = 0;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = sortedParams.get(key);

            content.append((index == 0 ? "" : "&") + key + "=" + value);
            index++;

        }
        return content.toString();
    }


    private String rsaSign(String content, String privateKey,
                           String charset) {
        try {
            PrivateKey priKey = getPrivateKeyFromPKCS8("RSA",
                    new ByteArrayInputStream(privateKey.getBytes()));

            java.security.Signature signature = java.security.Signature
                    .getInstance("SHA1WithRsa");
            signature.initSign(priKey);
            if (StringUtils.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }

            byte[] signed = signature.sign();

            return new String(Base64.encodeBase64(signed));
        } catch (Exception e) {
            logger.error("Error RSA Sign", e);
            throw new RuntimeException("Error RSA Sign");
        }
    }


    private PrivateKey getPrivateKeyFromPKCS8(String algorithm,
                                              InputStream ins) throws Exception {
        if (ins == null || StringUtils.isEmpty(algorithm)) {
            return null;
        }

        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);

        byte[] encodedKey = IOUtils.toByteArray(ins);

        encodedKey = Base64.decodeBase64(encodedKey);

        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
    }

    private static boolean doCheck(String content, String sign, String publicKey) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] bytes = publicKey.getBytes();
            byte[] encodedKey = Base64.decodeBase64(bytes);
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
            java.security.Signature signature = java.security.Signature.getInstance("SHA1WithRSA");

            signature.initVerify(pubKey);
            signature.update(content.getBytes(StandardCharsets.UTF_8));

            return signature.verify(Base64.decodeBase64(sign.getBytes()));

        } catch (Exception e) {
            throw new RuntimeException("Invalid Private Key", e);
        }
    }


}

@Data
class BaiduNotifyData {
    private String userId;
    private String orderId;
    private long unitPrice;
    private long count;
    private long totalMoney;
    private long payMoney;
    private long promoMoney;
    private long hbMoney;
    private long hbBalanceMoney;
    private long giftCardMoney;
    private String dealId;
    private long payTime;
    private long payType;
    private long partnerId;
    private int status;
    private String tpOrderId;
    private String rsaSign;
    private Object promoDetail;
    private Object returnData;
}

@Data
class BaiduBizInfo {
    private String dealId;
    private String appKey;
    private String totalAmount;
    private String tpOrderId;
}







