package com.easylive.pay.service.paypal;

import com.braintreegateway.*;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.dao.RechargePaypalMapRepository;
import com.easylive.pay.entity.RechargeOption;
import com.easylive.pay.entity.RechargePaypalMerchant;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.model.PaypalOrder;
import com.easylive.pay.model.User;
import com.easylive.pay.service.AppService;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.service.UserService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/11
 */
@Slf4j
@Service
public class PaypalService {


    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private RechargePaypalMapRepository paypalMapRepository;

    @Autowired
    private AppService appService;

    @Autowired
    private UserService userService;


    private volatile Map<Integer, PaypalGatewayInfo> paypalGateways = new HashMap<>();
    private PaypalGatewayInfo defaultGatewayInfo;


    @PostConstruct
    public void init() {
        load();
    }

    public void load() {
        Map<Integer, PaypalGatewayInfo> tmpMap = new HashMap<>();

        Map<String,PaypalGatewayInfo> gatewayMap = new HashMap<>();
        paypalMapRepository.findAll().forEach( rechargePaypalMap -> {
            RechargePaypalMerchant merchant = rechargePaypalMap.getMerchant();

            PaypalGatewayInfo info = gatewayMap.get(merchant.getName());
            if( info == null ) {
                BraintreeGateway gateway = new BraintreeGateway(merchant.getAccessToken());
                log.info("Creating paypal gateway {}, {}, {}", merchant.getName(), merchant.getEmail(), merchant.getAccessToken());
                info = new PaypalGatewayInfo();
                info.setCurrency(merchant.getCurrency());
                info.setGateway(gateway);
                info.setName(merchant.getName());
                info.setMid(String.valueOf(merchant.getId()));
                gatewayMap.put(merchant.getName(), info);
            }
            tmpMap.put( rechargePaypalMap.getMgsAppId(), info);
            if( rechargePaypalMap.isPrime() )
                defaultGatewayInfo =  info;
        });
        paypalGateways = tmpMap;

        if( defaultGatewayInfo == null )
            throw new ResponseException(ResponseError.E_SERVER,"No default paypal gateway configuration found");

    }

    public PaypalOrder createOrder(long uid, String appName, String productId, MobilePlatform mobilePlatform, String clientIp) {
        log.info("Create paypal order: {}, {}, {}, {}, {}", uid, productId, mobilePlatform, appName,clientIp );

        if( !userService.isLiveUUser(uid) ) {
            log.info("Non LiveU user:{} uses paypal, rejected", uid);
            throw new ResponseException(ResponseError.E_AUTH,"请使用LiveU账号");
        }


        int appId = appService.getAppIdByName(appName);
        PaypalGatewayInfo gateway = getGatewayInfo(appId);
        log.info("Use paypal gateway: {}", gateway.getName());
        OrderInfo orderInfo = new OrderInfo(appId, uid, productId, PayPlatform.PPF_PAYPAL,mobilePlatform, clientIp);
        orderInfo.setMerchantCurrency(gateway.getCurrency());
        orderInfo.setMchId(gateway.getMid());
        RechargeRecord rechargeRecord = rechargeService.createRechargeRecord(orderInfo);
        String token = gateway.getGateway().clientToken().generate();
        PaypalOrder order = new PaypalOrder();
        order.setOrderId(rechargeRecord.getOrderId());
        order.setToken(token);
        log.info("Token generated: {}",token);
        return order;
    }

    public String getTransactionInfo(String orderId) {
        RechargeRecord rr = rechargeService.getRecordByOrderId(orderId);
        if (rr == null)
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID);

        if( rr.getPlatform() != PayPlatform.PPF_PAYPAL.getValue())
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID,"Invalid platform");

        if( rr.getPlatformOrderId() == null )
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_STATUS,"Not submit");

        BraintreeGateway paypalGateway = getGatewayInfo(rr.getAppId()).getGateway();

        Transaction transaction = paypalGateway.transaction().find(rr.getPlatformOrderId());
        if( transaction == null ) {
            return "Transaction Not Found";
        }

        return "Transaction Status";

    }

    @Transactional
    public void createTransaction(long uid, String orderId, String nonce) {
        log.info("Create paypal transaction: {}, {}, {}", uid, orderId, nonce );
        RechargeRecord rr = rechargeService.getRecordByOrderId(orderId);
        if (rr == null)
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID);

        if (rr.getUid() != uid) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "Invalid uid");
        }

        User user = userService.ensureExists(uid);

        if( rr.getStatus() != OrderStatus.ORS_CREATE.getValue() ) {
            log.error("Invalid order status:{}", rr.getStatus());
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_STATUS);
        }

        RechargeOption option = rechargeService.getOptionByPlatformAndRmb(PayPlatform.PPF_PAYPAL, rr.getRmb());
        if( option == null ) {
            log.error("Invalid recharge value. No option found: {}", rr.getRmb());
            throw new ResponseException(ResponseError.E_RECHARGE_OPTION,"Invalid recharge rmb");
        }
        BraintreeGateway paypalGateway = getGatewayInfo(rr.getAppId()).getGateway();


        BigDecimal amount = BigDecimal.valueOf(rr.getRmb()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        TransactionRequest request = new TransactionRequest().amount(amount)
                .merchantAccountId("USD")
                .paymentMethodNonce(nonce)
                .orderId(orderId)
                .options()
                    .submitForSettlement(true)
                    .paypal().customField("Product Detail").description("Ecoin " + option.getEcoin() )
                    .done()
                .done();


        log.info("Committing paypal transaction...");
        Result<Transaction> result = paypalGateway.transaction().sale(request);

        if (result.isSuccess()) {
            Transaction transaction = result.getTarget();
            log.info("Transaction commit success:  id: {}, status: {} ", transaction.getId(), transaction.getStatus());

            Transaction.Status status = transaction.getStatus();
            /*
              PayPal状态解释:
              SETTINLING: 已经支付成功，商家未归档，可以退款
              SETTLED:   商家已经归档，180天内可以退款
              AUTHORIZED: 已经授权，但是用户没确认。其实就是没支付成功，信用卡会遇到此问题，这类问题照理来说应该后续继续轮询状态，直到超时或者用户确认
              TODO: 授权的处理
             */
            if( status == Transaction.Status.SETTLING) {
                log.info("[Recharge][PayPal]Transaction Settled:{}, {}. Do recharge", transaction.getAmount(), transaction.getCurrencyIsoCode());
                long ecoin = rechargeService.getRechargeScaleEcoin(PayPlatform.PPF_PAYPAL, option.getEcoin(), user);
                rechargeService.rechargeTransaction(rr, rr.getUid(), transaction.getId(), rr.getRmb(), ecoin);
                return;
            } else {
                log.info("Transaction not success: {}", getTransactionStatus(transaction));
            }
            rechargeService.setTradeOrderId(rr, transaction.getId());
        } else {
            log.info("Error transaction : " + result.getMessage());
            if ( result.getErrors().deepSize() >  0 ) {
                for (ValidationError error : result.getErrors().getAllDeepValidationErrors()) {
                    log.info("Validation error: {} ",error.getMessage());
                }
            } else {
                log.info("Processor settlement error: {}, {}", result.getTransaction().getProcessorSettlementResponseCode(),
                        result.getTransaction().getProcessorSettlementResponseText());
            }
        }
        throw new ResponseException(ResponseError.E_RECHARGE_NOT_PAID,"PayPal支付失败");
    }

    private String getTransactionStatus(Transaction transaction)  {
        Transaction.Status status = transaction.getStatus();
        if( status == Transaction.Status.SETTLED) {
            return "Transaction Settled";
        } else if ( status == Transaction.Status.AUTHORIZED) {
            return String.format("Authorized: instrument type: %s", transaction.getPaymentInstrumentType());
        } else if( status == Transaction.Status.PROCESSOR_DECLINED ) {
            return String.format("Processor Declined: type: %s, code: %s, message:%s", transaction.getProcessorResponseType(),
                    transaction.getProcessorResponseCode(), transaction.getProcessorResponseText());
        } else if( status == Transaction.Status.SETTLEMENT_DECLINED) {
            return String.format("Settlement Declined: code:%s, message:%s", transaction.getProcessorSettlementResponseCode(), transaction.getProcessorSettlementResponseText());
        } else if( status == Transaction.Status.GATEWAY_REJECTED) {
            return String.format("Gateway Rejected: %s", transaction.getGatewayRejectionReason());
        }
        return status.toString();
    }

    private PaypalGatewayInfo getGatewayInfo(int appId) {
        PaypalGatewayInfo gateway = paypalGateways.get(appId);
        return gateway == null ? defaultGatewayInfo : gateway;
    }

    @Data
    private static class PaypalGatewayInfo {
        private BraintreeGateway gateway;
        private String currency;
        private String name;
        private String mid;
    }
}
