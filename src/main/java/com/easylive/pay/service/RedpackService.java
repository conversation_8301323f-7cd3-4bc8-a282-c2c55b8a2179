package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.CacheConfig;
import com.easylive.pay.config.CashConfig;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.dao.RedpackDao;
import com.easylive.pay.entity.Redpack;
import com.easylive.pay.entity.RedpackNumber;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.enums.AssetSystemID;
import com.easylive.pay.enums.RedpackNumberStatus;
import com.easylive.pay.enums.RedpackStatus;
import com.easylive.pay.enums.RedpackType;
import com.easylive.pay.model.AssetUpdateResult;
import com.easylive.pay.model.User;
import com.easylive.pay.output.Asset;
import com.easylive.pay.output.OpenRedpackList;
import com.easylive.pay.output.OpenRedpackUser;
import com.easylive.pay.output.RedpackInfo;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Time;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by c on 2016/1/21.
 */
@Service
public class RedpackService {

    private final static String CACHE_KEY = "redpack_ecoin";
    private final static int LEVEL1 = 5000;
    private static final int SYSTEM_REDPACK_VALUE = 40;
    private static final int SYSTEM_REDPACK_NUMBER = 10;
    private static final int MAX_REDPACK_COUNT = 50;
    private static final int PK_REDPACK_VALUE = 40;
    private static final int PK_REDPACK_NUMBER = 10;
    private static final int DURATION_REDPACK = 20;

    private static final String RED_PACK_SET_CACHE_KEY = "pay:red_pack_set";
    private static final String RED_PACK_HASH_CACHE_KEY = "pay:red_pack_hash:";
    private static final String RED_PACK_NUM = "mgs:bizcore:redpack:num:";

    private static Logger logger = LoggerFactory.getLogger(RedpackService.class);
    @Autowired
    private RedpackDao redpackDao;

    @Autowired
    private StringRedisTemplate template;

    @Autowired
    private CacheConfig cacheConfig;

    @Autowired
    private AssetService assetService;

    @Autowired
    private UserService userService;

    @Autowired
    private URLConfig urlConfig;

    @Autowired
    private ChatService chatService;

    @Autowired
    private CashConfig cashConfig;

    @Autowired
    private AppGateService appGateService;

    private static String randomString(int len) {
        char[] chars = "abcdefghijklmnopqrstuvwxyz".toCharArray();
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < len; i++) {
            char c = chars[random.nextInt(chars.length)];
            sb.append(c);
        }

        return sb.toString();
    }

    @Transactional(readOnly = true)
    public int getRedpackStatusOfUser(String code) {
        return redpackDao.getRedpackStatusByCode(code);
    }

    @Transactional
    public void createGiftRedpack(User user, String vid, int ecoin, long anchorUid) {
        int count = 10;

        if (ecoin > 500) {
            count = 20;
        }

        if (ecoin < 0) {
            logger.error("Invalid ecoin for gift redpack: {}", ecoin);
            return;
        }

        if (ecoin < count) {
            count = ecoin;
        }

        this.createRedpack(null, user, vid, ecoin, count, "恭喜发财", RedpackType.GIFT.getValue(), anchorUid);
    }

    @Transactional
    public void addSystemRedpackScore(User user, String vid, long ecoin) {
        ValueOperations<String, String> ops = this.template.opsForValue();
        String key = cacheConfig.getPrefix() + CACHE_KEY;
        String value = "0";

        if (this.template.hasKey(key)) {
            value = ops.get(key);
        }

        long currEcoin = Long.parseLong(value);
        value = String.valueOf(currEcoin + ecoin);
        ops.set(key, value);

        // 判断是否能够触发红包
        long score = checkRedpack(currEcoin, currEcoin + ecoin);
        if (score == 0) {
            return;
        }

        String code = genRedpackCode(RedpackType.SYSTEM.getValue(), score);
        genRedpack(null, code, user, vid, SYSTEM_REDPACK_VALUE, SYSTEM_REDPACK_NUMBER, RedpackType.SYSTEM.getValue(), "恭喜发财");
    }

    @Transactional
    public Redpack createGroupRedpack(User user, int ecoin, int count, String title) {
        logger.info("Create group Redpack: {}, {}, {}", user.getId(), ecoin, count);
        if ((count > MAX_REDPACK_COUNT) || (ecoin < count) || count < 1) {
            throw new ResponseException(ResponseError.E_PARAM);
        }
        String tid = assetService.unaryOperate(AssetSystemID.REDPACK_GROUP, user, ecoin).getTid();
        return this.createRedpack(tid, user, null, ecoin, count, title, RedpackType.GROUP.getValue());
    }

    @Transactional
    public Asset createRoomRedpack(User user, String vid, int ecoin, int count, String title) {
        logger.info("Create room Redpack: {}, {}, {},{}", user.getId(), vid, ecoin, count);
        if ((count > MAX_REDPACK_COUNT) || (ecoin < count) || count < 1) {
            throw new ResponseException(ResponseError.E_PARAM);
        }

        String tid = assetService.unaryOperate(AssetSystemID.REDPACK_ROOM, user, ecoin).getTid();
        UserCoin uc = assetService.getByUid(user.getId());
        Asset asset = new Asset(uc);

        this.createRedpack(tid, user, vid, ecoin, count, title, RedpackType.ANCHOR.getValue(), user.getId());
        return asset;
    }

    @Transactional
    public void createPKRedpack(User user, String vid, String title) {
        logger.info("Create PK Redpack: {}, {}", user.getId(), vid);
        this.createRedpack(null, user, vid, PK_REDPACK_VALUE, PK_REDPACK_NUMBER, title, RedpackType.PK.getValue());
    }

    @Transactional
    public RedpackNumber openRedpackInRoom(User user, String vid, String code) {
        Redpack redpack = getRedpackByCode(code);
        RedpackNumber number = openRedpack(user, code, redpack);
        if (number != null) {
            chatService.openRedpackToChatSched(user, vid, number, redpack);
        }
        return number;
    }

    @Transactional
    public OpenRedpackList openGroupRedpack(User fromUser, String code, int open) {
        RedpackNumber node = null;
        if (open == 1) {
            Redpack redpack = getRedpackByCode(code);
            node = openRedpack(fromUser, code, redpack);
        } else if (open == 0) {
            node = getRedpackStatusOfUser(fromUser.getId(), code);
        }

        OpenRedpackList list = new OpenRedpackList();
        if (node != null) {
            list.setValue(node.getValue());
            list.setBest(node.getBest());
            list.setTime(node.getTime());
        }

        list.setRedpackInfo(getRedpackInfo(code));
        List<OpenRedpackUser> userList = getListByCode(code);
        list.setOpenList(userList);

        return list;
    }

    /**
     * 红包退款
     */
    @Transactional
    public Redpack refundRedpack(String code) {
        logger.info("Start refund redpack:" + code);
        Redpack redpack = redpackDao.getRedpackByCode(code);

        if (redpack == null) {
            logger.info("redpack is not exists, code " + code);
            throw new ResponseException(ResponseError.E_REDPACK_NOT_EXISTS);
        }

        //只有group红包能够退款
        if (RedpackType.GROUP.getValue() != redpack.getType()) {
            logger.info("refund fail, redpack is not group type, code " + code);
            throw new ResponseException(ResponseError.E_REDPACK_NOT_GROUP_TYPE);
        }

        //有一次领取就不允许退款
        if (redpack.getNumber() >= 1) {
            logger.info("redpack has opened, can not refund, code: " + code);
            throw new ResponseException(ResponseError.E_REDPACK_OPEN_REPEAT);
        }

        //退还发红包用户易币(模拟发送者抢了最后一个红包)
        long uid = redpack.getUid();
        RedpackNumber redpackNumber = redpackDao.getByCodeAndUid(code, uid);
        if (redpackNumber != null) {
            logger.info("redpack is empty, code " + code + ", uid " + uid);
            throw new ResponseException(ResponseError.E_REDPACK_OPEN_REPEAT);
        }

        redpack.setNumber(redpack.getNumber() + 1);
        redpackNumber = redpackDao.getRedpackNumber(code, redpack.getNumber());
        if (redpackNumber == null) {
            logger.error("redpack number not exist.code:" + code);
            return null;
        }

        if (redpackNumber.getStatus() == RedpackNumberStatus.RPS_OPENED.getValue()) {
            logger.info("redpack " + redpack.getCode() + " number " + redpack.getNumber() + " has opened");
            return null;
        }

        redpackNumber.setStatus(RedpackNumberStatus.RPS_OPENED.getValue());
        redpackNumber.setUid(uid);
        redpackNumber.setTime(new Date());

        // 退还易币数
        String tid = assetService.unaryOperate(AssetSystemID.REDPACK_REFUND, uid, redpack.getValue()).getTid();

        redpackNumber.setTid(tid);
        redpackDao.update(redpackNumber);

        //设置为已领完
        redpack.setNumber(redpack.getCount());
        //状态设置为已退款
        redpack.setStatus(RedpackStatus.RPS_REFUND.getValue());
        redpackDao.update(redpack);

        logger.info("Redpack refund success, code : " + code + ", refund ecoin:" + redpack.getValue() + ", refund uid:" + uid);

        return redpack;
    }

    @Transactional
    public List<OpenRedpackUser> getListByCode(String code) {
        List<OpenRedpackUser> redPackUserList = new ArrayList<>();
        List<RedpackNumber> redpackNumberList = redpackDao.getListByCode(code);
        int size = redpackNumberList.size();

        if (size == 0) {
            return redPackUserList;
        }

        List<Long> uids = new ArrayList<>();
        redpackNumberList.forEach(rn -> uids.add(rn.getUid()));

        Map<Long, User> mapUser = userService.listByUids(uids);

        for (RedpackNumber aRedpackNumberList : redpackNumberList) {
            OpenRedpackUser user = new OpenRedpackUser();
            Long uid = aRedpackNumberList.getUid();
            user.setBest(aRedpackNumberList.getBest());
            user.setValue(aRedpackNumberList.getValue());
            user.setTime(aRedpackNumberList.getTime());
            if (mapUser.get(uid) != null) {
                user.setName(mapUser.get(uid).getName());
                user.setNickname(mapUser.get(uid).getNickname());
                user.setLogourl(mapUser.get(uid).getLogoUrl());
            }
            redPackUserList.add(user);
        }
        return redPackUserList;
    }

    private RedpackInfo getRedpackInfo(String code) {

        Redpack redpack = redpackDao.getRedpackByCode(code);

        if (redpack == null) {
            logger.info("redpack does not exists, code " + code);
            throw new ResponseException(ResponseError.E_REDPACK_NOT_EXISTS);
        }

        RedpackInfo redpackInfo = new RedpackInfo();
        redpackInfo.setCount(redpack.getCount());
        redpackInfo.setTitle(redpack.getName());
        redpackInfo.setSendTime(redpack.getTime());
        redpackInfo.setGetNumber(redpack.getNumber());
        User redpackSendUser = userService.getById(redpack.getUid());
        redpackInfo.setLogourl(redpackSendUser.getLogoUrl());
        redpackInfo.setNickname(redpackSendUser.getNickname());
        redpackInfo.setTotalValue(redpack.getValue());
        return redpackInfo;
    }

    /**
     * 操作红包剩余易币
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void operateSurplusRedPack() {
        long now = System.currentTimeMillis();

        // 获取操作的集合
        Set<String> codes = template.opsForZSet().rangeByScore(RedpackService.RED_PACK_SET_CACHE_KEY, 0, now);
        if (codes.size() <= 0){
            return;
        }

        // 移除集合
        template.opsForZSet().removeRangeByScore(RedpackService.RED_PACK_SET_CACHE_KEY, 0, now);

        List<Object> hashList = new ArrayList<>();
        hashList.add("fromUid");
        hashList.add("ecoin");
        hashList.add("vid");
        hashList.add("toUid");

        try {
            //更新状态数据
            redpackDao.updateSurplusRedPack(codes);

            //操作饭团
            codes.forEach(code -> {
                String key = RED_PACK_HASH_CACHE_KEY + code;
                List<Object> hash = this.template.opsForHash().multiGet(key, hashList);

                logger.info("operate surplus red pack info:{}", hash.toString());

                if (hash.size() == 4) {
                    Long toUid = Long.valueOf(String.valueOf(hash.get(0)));
                    Long ecoin = Long.valueOf(String.valueOf(hash.get(1)));
                    String vid = String.valueOf(hash.get(2));
                    Long fromUid = Long.valueOf(String.valueOf(hash.get(3)));

                    if (ecoin > 0) {
                        long settleRiceroll = ecoin * cashConfig.getGiftRateOfEcoinToRiceroll();

                        if (fromUid.equals(toUid)) {
                            assetService.unaryOperate(AssetSystemID.SURPLUS_REDPACK_COIN, fromUid, ecoin);
                        } else {
                            AssetUpdateResult assetResult = assetService.binaryOperate(AssetSystemID.SURPLUS_REDPACK_RICEROLL, fromUid, 0L, toUid, settleRiceroll);

                            //更新直播间饭团
                            long accumriceroll = assetResult.getUserCoin(1).getAccumriceroll();
                            chatService.updateRicerollToChatSched(vid, accumriceroll);

                            //更新榜单
                            appGateService.redPackUpdateRank(fromUid, toUid, ecoin, settleRiceroll, 1);
                        }


                        //记录
                        redpackDao.insertSurplusRedPackRecod(fromUid, toUid, vid, ecoin, settleRiceroll);
                    }
                }
            });
        } catch (Exception e) {
            logger.error("operate surplus redPack error. {}", e.getMessage());
        }
    }

    /**
     * 系统、主播、PK红包单独处理存入缓存
     * @param code
     * @param ecoin
     */
    private void setRedPackCache(String code, int ecoin, long fromUid, String vid, long toUid, int count) {
        try {
            Map<String, String> data = new HashMap<>();
            data.put("fromUid", String.valueOf(fromUid));
            data.put("ecoin", String.valueOf(ecoin));
            data.put("vid", vid);
            data.put("toUid", String.valueOf(toUid));

            //缓存红包个数
            this.template.opsForValue().set(RED_PACK_NUM + code, String.valueOf(count), 7200, TimeUnit.SECONDS);

            this.template.opsForZSet().add(RED_PACK_SET_CACHE_KEY, code, System.currentTimeMillis() + DURATION_REDPACK * 1000L);
            this.template.opsForHash().putAll(RED_PACK_HASH_CACHE_KEY + code, data);
            this.template.expire(RED_PACK_HASH_CACHE_KEY + code, 60, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("set red pack to redis error.{}", e.getMessage());
        }
    }

    private RedpackNumber openRedpack(User user, String code, Redpack redpack) {
        long uid = user.getId();

        //补充 duration 过期失效
        if (redpack.getType() == RedpackType.GIFT.getValue() || redpack.getType() == RedpackType.ANCHOR.getValue()) {
            long expireTime = redpack.getDuration() * 1000L + redpack.getTime().getTime();
            if (expireTime <= System.currentTimeMillis()) {
                return null;
            }
        }

        RedpackNumber redpackNumber = redpackDao.getByCodeAndUid(code, uid);
        if (redpackNumber != null) {
            logger.info("redpack open repeat, code " + code + ", uid " + uid);
            return redpackNumber;
            // throw new ResponseException(ResponseError.E_REDPACK_OPEN_REPEAT);
        }

        if (redpack.getNumber() >= redpack.getCount()) {
            logger.info("redpack is empty " + code + ", uid " + uid);
            return null;
            //throw new ResponseException(ResponseError.E_REDPACK_EMPTY);
        }


        redpack.setNumber(redpack.getNumber() + 1);
        redpackNumber = redpackDao.getRedpackNumber(code, redpack.getNumber());
        if (redpackNumber == null) {
            return null;
        }

        if (redpackNumber.getStatus() > 0) {
            logger.info("redpack " + redpack.getCode() + " number " + redpack.getNumber() + " has opened");
            return null;
        }

        redpackNumber.setStatus(1);
        redpackNumber.setUid(uid);
        redpackNumber.setTime(new Date());

        // 加入易币数
        String tid = assetService.unaryOperate(AssetSystemID.REDPACK_OPEN, user, redpackNumber.getValue()).getTid();

        redpackNumber.setTid(tid);

        redpackDao.update(redpackNumber);
        redpackDao.update(redpack);

        // 开红包缓存扣减币
        if (redpack.getType() == RedpackType.GIFT.getValue() || redpack.getType() == RedpackType.ANCHOR.getValue()) {
            this.template.opsForHash().increment(RED_PACK_HASH_CACHE_KEY + code, "ecoin", 0 - redpackNumber.getValue());
        }

        return redpackNumber;
    }

    private int genNumbers(int[] intArray, int value, int count, int number, int maxValue) {
        int result = value;

        if (count > 1) {
            int avg = value / count;
            int min = avg / 8;
            int max = avg * 2;
            max = (max > value - count + 1) ? value - count + 1 : max;
            min = (min < 1) ? 1 : min;

            Random random = new Random();
            result = random.nextInt((max - min) + 1) + min;
            logger.debug("value " + value + ", result " + result + ", number " + number);
            value -= result;
            count--;

            intArray[number] = result;
            number++;

            maxValue = genNumbers(intArray, value, count, number, maxValue);
        } else {
            intArray[number] = result;
        }

        if (maxValue < result) {
            maxValue = result;
        }

        return maxValue;
    }

    private void genRedpackNumber(String code, int ecoin, int count) {
        int[] intArray = new int[count];
        int maxValue = genNumbers(intArray, ecoin, count, 0, 0);
        boolean flag = false;

        for (int i = 0; i < intArray.length; i++) {
            RedpackNumber redpackNumber = new RedpackNumber();
            if ((!flag) && (maxValue == intArray[i])) {
                flag = true;
                redpackNumber.setBest(1);
            } else {
                redpackNumber.setBest(0);
            }

            logger.debug("set value " + intArray[i]);

            redpackNumber.setValue((long) intArray[i]);
            redpackNumber.setRedpackCode(code);
            redpackNumber.setRedpackCount(count);
            redpackNumber.setRedpackValue(ecoin);
            redpackNumber.setStatus(0);
            redpackNumber.setNumber(i + 1);

            redpackDao.save(redpackNumber);
        }
    }

    private Redpack genRedpack(String tid, String code, User user, String vid, int ecoin, int count, int type, String title) {
        return genRedpack(tid, code, user, vid, ecoin, count, type, title, null);
    }

    private Redpack genRedpack(String tid, String code, User user, String vid, int ecoin, int count, int type, String title, Long anchorUid) {

        try {
            Redpack redpack = new Redpack();
            redpack.setTid(tid);
            redpack.setStatus(1);
            redpack.setCode(code);
            redpack.setUid(user.getId());
            redpack.setVid(vid);
            if (type == RedpackType.SYSTEM.getValue()) {
                //TODO: 配置应该另外处理，比如放到配置中心
                if( user.getAppSourceId() == Constants.MAGIC_LIVE_APP_SOURCE_ID )
                    redpack.setLogo(urlConfig.getRedpackIconMagicLive());
                else
                    redpack.setLogo(urlConfig.getRedpackIcon());

            } else {
                redpack.setLogo(user.getLogoUrl());
            }
            redpack.setDuration(DURATION_REDPACK);
            redpack.setValue(ecoin);
            redpack.setCount(count);
            redpack.setType(type);
            redpack.setName(title);
            redpack.setTime(new Date());
            redpackDao.save(redpack);

            // 生成红包每份的大小
            genRedpackNumber(code, ecoin, count);
            logger.info("[advstat][redpackgen][success] total {} uid {} touid {} count {} ecoin {} vid {} type {}", ecoin * count, user.getId(), null, count, ecoin, vid, type);

            // 红包创建通知
            notifyRedpackMessage(user, vid, redpack);

            // 红包缓存
            if (type == RedpackType.GIFT.getValue() || type == RedpackType.ANCHOR.getValue()) {
                setRedPackCache(code, ecoin, anchorUid, vid, user.getId(), count);
            }

            return redpack;
        } catch (Exception e) {
            logger.error("gen redpack error " + user.getId() + ", code " + code, e);
        }

        return null;
    }

    private String genRedpackCode(int type, long score) {
        String code;
        if (type == RedpackType.SYSTEM.getValue()) {
            code = String.format("sys%08dsys%04d%02d%s", score, SYSTEM_REDPACK_VALUE, SYSTEM_REDPACK_NUMBER, randomString(3));
        } else {
            code = String.format("%s%10d%s%04d%s%02d", randomString(8), System.currentTimeMillis() / 1000,
                    randomString(4), SYSTEM_REDPACK_VALUE, randomString(4), SYSTEM_REDPACK_NUMBER);
        }
        return code;
    }

    private void notifyRedpackMessage(User user, String vid, Redpack redpack) {

        //通知聊天服务器红包消息
        if (redpack.getType() != RedpackType.GROUP.getValue() ) {
            logger.info("send redpack to chat server: redPack code " + redpack.getCode() + ", value " + redpack.getValue() + ", number" + redpack.getCount());
            chatService.createRedpackToChatSched(user, vid, redpack);
        }
    }

    private Redpack createRedpack(String tid, User user, String vid, int ecoin, int count, String title, int type, long anchorUid) {
        String code = genRedpackCode(type, 0);
        return genRedpack(tid, code, user, vid, ecoin, count, type, title, anchorUid);
    }

    private Redpack createRedpack(String tid, User user, String vid, int ecoin, int count, String title, int type) {
        String code = genRedpackCode(type, 0);
        return genRedpack(tid, code, user, vid, ecoin, count, type, title, null);
    }


    private RedpackNumber getRedpackStatusOfUser(long uid, String code) {
        getRedpackByCode(code);
        return redpackDao.getByCodeAndUid(code, uid);
    }

    private Redpack getRedpackByCode(String code) {
        Redpack redpack = redpackDao.getRedpackByCode(code);
        if (redpack == null) {
            logger.info("redpack is not exists, code " + code);
            throw new ResponseException(ResponseError.E_REDPACK_NOT_EXISTS);
        }
        return redpack;
    }


    private long checkRedpack(long lastEcoin, long ecoin) {
        long score = checkRedpackWithLevel(lastEcoin, ecoin, LEVEL1);
        if (score > 0) {
            return score;
        }

        return 0;
    }

    private long checkRedpackWithLevel(long lastEcoin, long ecoin, int level) {
        long last = lastEcoin / level;
        long curr = ecoin / level;

        if (last != curr) {
            return curr * level;
        } else {
            return 0;
        }
    }

}
