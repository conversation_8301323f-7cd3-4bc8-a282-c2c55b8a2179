package com.easylive.pay.service.lianlian;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@Data
@JsonNaming(PropertyNamingStrategy.LowerCaseWithUnderscoresStrategy.class)
public class NotificationData {
    private String oidPartner;
    private String signType;
    private String sign;
    private String dtOrder;
    private String noOrder;
    private String oidPaybill;
    private String moneyOrder;
    private String resultPay;
    private String settleDate;
    private String infoOrder;
    private String payType;
    private String noAgree;
    private String bankCode;
    private String idType;
    private String idNo;
    private String acctName;
    private String cardNo;
}
