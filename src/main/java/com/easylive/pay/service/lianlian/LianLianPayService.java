package com.easylive.pay.service.lianlian;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.apollo.ServerApiConfig;
import com.easylive.pay.enums.AliPayProtocol;
import com.easylive.pay.enums.AliPayType;
import com.easylive.pay.model.RechargeResult;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.service.alipay.AliPayService;
import com.easylive.pay.service.alipay.AlipayOrderExecuteResult;
import com.easylive.pay.service.alipay.AlipayOrderInfo;
import com.easylive.pay.service.alipay.AlipayProvider;
import com.easylive.pay.service.lianlian.client.LLianPayClient;
import com.easylive.pay.service.lianlian.params.mpayapi.AliPrePayResult;
import com.easylive.pay.service.lianlian.params.mpayapi.BankCardPrePayParams;
import com.easylive.pay.service.lianlian.security.LLianPayYtSignature;
import com.easylive.pay.service.lianlian.utils.LLianPayDateUtils;
import com.easylive.pay.utils.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @date 2023/11/29
 */
@Service
@Slf4j
public class LianLianPayService {
    private static final String LIANLIAN_PRE_PAY_URL = "https://mpayapi.lianlianpay.com/v1/bankcardprepay";

    @Autowired
    private ServerApiConfig serverApiConfig;

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private AliPayService aliPayService;

    @Value("${pay.third.lianlian.notify-url}")
    private String notifyUrl;


    private String getNotifyUrl() {
        return serverApiConfig.getPayCallbackDomain() + this.notifyUrl;
    }


    public String validOrder(String data) {
        String ret = "{\"ret_code\":\"%s\", \"ret_msg\":\"%s\"}";
        String retCode = "0000";
        String retMsg = "交易成功";

        log.info("[LianLian][ValidOrder]: {}", data);
        try {
            NotificationData notificationData = new ObjectMapper().readValue(data, NotificationData.class);
            boolean checkOk = LLianPayYtSignature.getInstance().checkSignJson(LLianPayConstant.LLianPayPublicKey, data, notificationData.getSign());
            if (!checkOk) {
                log.error("[LianLian][ValidOrder] signature validation failed: {}", notificationData.getNoOrder());
                throw new ResponseException(ResponseError.E_THIRD_SIGN, "签名校验失败");
            }
            log.info("[LianLian][ValidOrder]: Validation OK");
            if ( !"SUCCESS".equals(notificationData.getResultPay())) {
                log.error("[LianLian][ValidOrder] result pay is not success: {},{}", notificationData.getNoOrder(), notificationData.getResultPay());
                throw new ResponseException(ResponseError.E_THIRD_ERROR,"支付失败");
            }

            String orderId = notificationData.getNoOrder();
            String pfOrderId = notificationData.getOidPaybill();
            RechargeResult r = rechargeService.validAndRecharge(orderId, pfOrderId, notificationData.getMoneyOrder());
            if( !r.isDuplicated() )
                aliPayService.rechargeControlFeedback(r.getMerchantId(), r.getAppId(), (int)r.getRmb());
        } catch (ResponseException e) {
            retCode = "0001";
            retMsg = e.getMessage();
        } catch (Exception e) {
            log.error("验证订单错误",e);
            retCode = "0002";
            retMsg = "系统错误";
        }
        return String.format(ret, retCode, retMsg);
    }

    public AlipayProvider getAliProvider(AlipayOrderInfo info) {
        if( info.getType() == AliPayType.WAP ) {
            AlipayProvider provider = new AlipayLianlianProvider(info);
            provider.setNotifyUrl( this.getNotifyUrl() );
            return provider;
        }
        throw new ResponseException(ResponseError.E_THIRD_ERROR,"不支持的支付方式");
    }

    static class AlipayLianlianProvider implements AlipayProvider {

        private String notifyUrl;
        AlipayOrderInfo info;
        public AlipayLianlianProvider(AlipayOrderInfo info) {
            this.info = info;
        }

        @Override
        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }

        @Override
        public void setReturnUrl(String returnUrl) {
        }

        @Override
        public AlipayOrderExecuteResult execute() {

            BankCardPrePayParams params = new BankCardPrePayParams();
            String timestamp = LLianPayDateUtils.getTimestamp();
            params.setUser_id(info.getName());
            params.setOid_partner(info.getApp().getAppId());
            params.setSign_type("RSA");
            params.setBusi_partner("101001");
            params.setNo_order(info.getOrderId());
            params.setDt_order(timestamp);
            // 商户商品名称。 建议传入真实商品名称
            params.setName_goods(info.getDetail());
            // 交易金额。请求no_order对应的订单总金额，单位为元，精确到小数点后两位，小数点计入字符长度。 取值范围为 0.01 ~ ********。初始额度：50元
            params.setMoney_order(info.getAmount());
            params.setNotify_url(this.notifyUrl);
            // 订单有效期。订单创建后，开始计时， 以分钟为单位，不传默认为10080 (7天)
            params.setValid_order(120);
            RiskItem riskItem = new RiskItem();
            riskItem.setUserInfoMerchtUserno(info.getName());
            String userRegTime = new SimpleDateFormat("yyyyMMddHHmmss").format(info.getUserRegTime());
            riskItem.setUserInfoDtRegister(userRegTime);
            riskItem.setUserInfoBindPhone("");
            riskItem.setFrmsWareCategory("1004");
            riskItem.setGoodsName(info.getDetail());

            log.info("risk items: {}", JsonUtils.toString(riskItem));
            params.setRisk_item(JsonUtils.toString(riskItem));
        /*
        支付方式。 指定使用的支付方式。
        2， 快捷支付 - 借记卡。
        3， 快捷支付 - 信用卡。
        P， 新认证支付。
        27， 运通卡支付（信用卡）。
         */
            params.setPay_type("L");
            // 签约协议号。用户以签约支付API或者签约API成功进行签约后，连连下发的永久令牌
            params.setNo_agree("");
            // 生成签名
            params.setSign(LLianPayYtSignature.getInstance().sign(this.info.getApp().getPrivateKey(), JSON.toJSONString(params)));

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("oid_partner", this.info.getApp().getAppId());
            // 使用连连公钥对请求参数进行加密
            jsonObject.put("pay_load", LLianPayYtSignature.getInstance().encryptGeneratePayload(JSON.toJSONString(params), LLianPayConstant.LLianPayPublicKey));

            // 银行卡签约支付申请URL
            LLianPayClient lLianPayClient = new LLianPayClient();
            String resultJsonStr = lLianPayClient.sendRequest(LIANLIAN_PRE_PAY_URL, jsonObject.toJSONString());
            AliPrePayResult result = JSON.parseObject(resultJsonStr, AliPrePayResult.class);
            log.debug(result.toString());
            if( result.getPayload() != null ) {
                AliPrePayResult.Payload payload = JSON.parseObject(result.getPayload(),AliPrePayResult.Payload.class);
                if( payload.getGateway_url() != null ) {
                    return new AlipayOrderExecuteResult(payload.getGateway_url(), AliPayProtocol.QR_CODE);
                }
            }

            throw new ResponseException(ResponseError.E_THIRD_ERROR,"获取支付地址失败");
        }
    }
}
