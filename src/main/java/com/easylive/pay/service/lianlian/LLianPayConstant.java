package com.easylive.pay.service.lianlian;

/**
 * 连连银通系统常量
 *
 */
public class LLianPayConstant {
    // 连连银通公钥（无需进行替换）
    public final static String LLianPayPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCSS/DiwdCf/aZsxxcacDnooGph3d2JOj5GXWi+q3gznZauZjkNP8SKl3J2liP0O6rU/Y/29+IUe+GTMhMOFJuZm1htAtKiu5ekW0GlBMWxf4FPkYlQkPE0FtaoMP3gYfh+OwI+fIRrpW3ySn3mScnc6Z700nU/VYrRkfcSCbSnRwIDAQAB";
    // 商户系统（服务器）私钥（测试环境无需替换，正式环境需要替换）
    // 目前配置的为测试商户号：201408071000001539 的私钥，需要替换成商户自己的私钥
    // public final static String MerchantPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKZGXpmfgya2gGh6UdFPqPqi6e2z/HX4aIlMH394FOXTVwErnSGY5S0YFw5WskJrQLU2RHwFiA5P9Yt8VPxwgLDpdIm1/a7NlyjvWNvBd8V7wyITH8teJA1Ae5yWmRRrWFcVRSjpBq3xfwv76lVl+Nq/jR08p/ugVYJgtYEIM53JAgMBAAECgYA17SarPj+j45a7y8gTUXmlaAbkb/ZWMG1+8fBZQBHPA/74wzNf/R1+xYxcuyNvRSekXehSLN0WfzpMtdM+WCJ0ODqHRFsbAxmi784hzBZHOAxoJV49P8PVy6HIPthXxiSNUcacSt/HKJrUI6zACpymJLiVxMb9GqQAyx3BJl7rjQJBANG+RDszZYl3J1z1AtD0WggycrH2YOB7v5o3qKOz2AQ6CHWApSN6cuvqFwaUtHK9gMpDhvWR6zbYVRP+f4AxoQ8CQQDK8fTkpHNrHc011E8jjk3Uq5PWTJ0jAvcqk4rqZa4eV9953YSJYtJ2Fk2JnL3Ba7AU+qStnyD6MvSIpwIPSaOnAkEAptbFaZ4Jn55jdmMC2Xn1f925NGx6RTbKg37Qq18sbrhG8Ejjk2QctCIiLL7vBvJM1xd97CslQhw1GNFxVGSl6wJAQzwFtfoFgudMpRjBXzY18s8lG0omhQLmf+SBkUY+eS8Diowo7Jsgvp6E8aJL+1iB7XFcPWkKs9lNyjgKJqZu4QJAM22ULfWKrNIqaBJaYDmQSupUkHR/WL5rQJtAuVo8Zg3+rBrtMTXfIHJpR0MNpMgRSsPK6pZ3n4i+VvC5WxKUzA==";
    // 连连测试环境测试商户号（测试环境无需替换，正式环境需要替换）
    //public final static String OidPartner = "201408071000001539";

    //public final static String MerchantPrivateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDlLbwJVd/bZg2lN2rZkp+VQL7rzlWb6/B7iah+cuQcfls6i8FuegeDtUHlquHWzGKwNz3mjHAKeYPRzK60D4/HDLawWP9my8t56BVJNuxUty5Z+7ltYwM8RkHcv35kypkYLtEZzPGtjV0tz32w7J89GxyrmWLqaKeRXeTI4lxeFdGJ1n/Lfgu41qP61qSgRbOskLYh7fJF/jjwp8k+FV5sCYQYERcA8Wi7etjSY28UPAlNBGNSAFqctkTre7pKAqh6wiK6kICMEUx6ytPtnXTzjtB3Glj/Rsl2Iu9sFiL4u/uWFL78MJLGbN0t4KdLd2qE4TPj1lWsBla6ygiBsEe7AgMBAAECggEAFEQ4miy9aJX8Q+2EJexo71Zz679p46l4MTyJqXD4iiqEX8eHGgFxk6yeQLSdVxTo70NbVn151PtM9Gsg0eyEXupZZ3vfThRckJG8/CX+vTkbwLpZefplCVn/LgnDIB+obTKKrDjq8FmNcWUlbNVn+Yvhu60LScZqdxjyB++4kls5vu94mtHuObJCdIzT1vylKDTWJG871YH7yITDJw1g4Jn6SL74JtAo5NnwcBWEGK+QMj7z2uA3rIlae3r38pXK0jnMhjvK5mI+8agsCbUAhql1DAPhAah7miWp+mlQ1kE7FN/ItLdYz1VGSs/xHXTehsdXktFBtLlsY+I3L2/fjQKBgQDyfKufYoHWsnMRM9IvywZCHrrRQvWgXyT4aNrQYsHwqHmOmfWBS6konHEeVPrRGmVcl02+C8+QIBFgToSTnnpRZEq5V+pg6Kxz40w5UbPWz5pNm908IXmAhMvL9PpZp/yRK0FumpTvywHqRs33eELxXqPcKgAbPH8Y59nJTxuOPQKBgQDx8zTsi2p1fRo3/Wcp/cxvVHTEGvYZdgp4GViXxkEfW/DVaejsfnkNNlihHKbIECka7YphbuoGX4iOQ4p9mTwUvYU5/LUi4Is9Q7eC3y9gn5gHBVaOFABswroZz3ChViI8c5PVwl1lmMyyRl07CcfJzSDFETHicgpW7ByosjbFVwKBgG00nmpCh+z0IwRNTUy+54uhoUsbF9no35KMTndo8qT6ivwg9EMNmxoyEnOxm9/OPnYLojSDxy/GcWzpD6YXykibnVeJc4G/Z+MtK6fo1tBYYMqw9lrdTIYHyGyLYLA+Cm4qh+ZS8d5ok+05hAr+e0+O6sb9l1ysyyShTiDuYp+VAoGBAJyVl9TWzYlH86X0u8VzWdFbSU55XHhR6qibTri7N/5BlHWp23Lizxj76RQr5MxpmRCyVvKDw7VoMopPr9tlRnrtxMEoiVoPFE4L+0IZiyHvZ4Zzr/a3fybkR2jY1b20bufMrQqUhblw0Fk4LLI+vCwgM2sWm8A/qe1jVZDf9Y51AoGBAJl6OKPv9TPf1Sg4yQN/Unnva4rh8ZL6MO0ReWCV+1CXU17k4Udx/UmbSNvIyv0RqTXfZmrRAp/e9LVnKXlG8bCYHkIXNyS4sj//4FLINh9jekcxYbjqvW3k6EN6BQvc9KyLeHb4u/UYEfHhoSw5dgeN03CC7rRKgB0OYZ7h5dcV";
    //public final static String OidPartner = "302311010000026271";


}
