package com.easylive.pay.service.lianlian;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/30
 */
@Data
@JsonNaming(PropertyNamingStrategy.LowerCaseWithUnderscoresStrategy.class)
public class RiskItem {
    private String frmsWareCategory;
    private String userInfoMerchtUserno;
    private String userInfoBindPhone;
    private String userInfoDtRegister;
    private String goodsName;
}
