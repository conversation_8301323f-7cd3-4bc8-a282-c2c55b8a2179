package com.easylive.pay.service;

import com.easylive.pay.component.OssComponent;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.entity.Goods;
import com.easylive.pay.enums.im.ImMessageContentType;
import com.easylive.pay.enums.im.ImMessageType;
import com.easylive.pay.model.RetNode;
import com.easylive.pay.model.User;
import com.easylive.pay.utils.HttpUtils;
import com.easylive.pay.utils.JsonUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class ImService {

    private static Logger logger = LoggerFactory.getLogger(ImService.class);

    @Autowired
    private OssComponent ossComponent;

    @Autowired
    private URLConfig urlConfig;

    private static final String SEND_MESSAGE = "v1/server/message/sendmessage";

    @Async
    public void sendGitMessage(User user, Goods goods, int number, String sender, String receiver) {
        logger.info("[sendGitMessage] uid={}, goodsId={}, number={}, sender={}, receiver={}, goods={}", user.getId(), goods.getId(), number, sender, receiver, JsonUtils.printObject(goods));
        Map<String, Object> args = new HashMap<>(6);
        args.put("sessionid", user.getSessionId());
        args.put("sender", sender);
        args.put("receiver", receiver);
        args.put("messageType", ImMessageType.PERSONAL.getValue());
        args.put("messageContentType", ImMessageContentType.GIFT.getValue());

        Map<String, Object> content = new HashMap<>(4);
        content.put("receiver", receiver);
        content.put("goodsId", goods.getId());
        content.put("number", number);
        content.put("name", goods.getName());
        String icon = ossComponent.getGoodsImageUrl(goods.getPicture());
        content.put("icon", icon);
        String messageContent = JsonUtils.printObject(content);
        args.put("messageContent", messageContent);

        String url = urlConfig.getImServerServiceUrl();
        Map<String, Object> ret = postImServer(url, SEND_MESSAGE, args);
        if (ret == null) {
            logger.error("[sendGitMessage] 1. send gift message failed, url={}, args={}", url, JsonUtils.printObject(args));
        }
    }

    private Map<String, Object> postImServer(String urlPrefix, String cmd, Map<String, Object> args) {
        String url = urlPrefix + "/" + cmd;
        logger.info("calling imserver for command {} ", cmd);

        String response = HttpUtils.doPost(url, args);
        GsonBuilder builder = new GsonBuilder().setDateFormat("yyyy-MM-dd");
        Gson gson = builder.create();
        RetNode retNode = gson.fromJson(response, RetNode.class);

        if (retNode.getRetval().compareTo("ok") != 0) {
            return null;
        }

        return retNode.getRetinfo();
    }

}
