package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.event.EventProducer;
import com.easylive.pay.dao.AssetDao;
import com.easylive.pay.dao.UserCoinDao;
import com.easylive.pay.entity.AssetAuth;
import com.easylive.pay.entity.AssetBiz;
import com.easylive.pay.entity.AssetRecord;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.enums.AssetAuthType;
import com.easylive.pay.enums.AssetBizType;
import com.easylive.pay.enums.AssetSystemID;
import com.easylive.pay.enums.AssetType;
import com.easylive.pay.model.*;
import com.easylive.pay.model.callback.AssetTransactionListener;
import com.easylive.pay.model.callback.AssetUpdateListener;
import com.easylive.pay.model.callback.UpdateUserCoin;
import com.easylive.pay.model.event.AssetTransaction;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class AssetService {

    private static Logger logger = LoggerFactory.getLogger(AssetService.class);
    @Autowired
    private UserCoinDao userCoinDao;
    @Autowired
    private AssetDao assetDao;
    @Autowired
    private UserService userService;
    @Autowired
    private SpecialUserService specialUserService;
    @Autowired
    private EventProducer eventProducer;

    @Autowired
    private AssetCenterService assetCenterService;

    private volatile Map<String, AssetBiz> assetBiz = new HashMap<>();
    private List<AssetTransactionListener> listeners = new ArrayList<>(1);

    @PostConstruct
    public void init() {
        loadAssetBiz();
    }

    public void addTransactionListener(AssetTransactionListener listener) {
        this.listeners.add(listener);
    }

    @Transactional(readOnly = true)
    public synchronized void loadAssetBiz() {
        Map<String, AssetBiz> tmp = new HashMap<>();
        boolean error = false;
        List<AssetBiz> assetBizs = assetDao.getAllAssetBiz();
        for (AssetBiz assetBiz : assetBizs) {
            tmp.put(assetBiz.getAk(), assetBiz);
            AssetBizType bizType = AssetBizType.fromValue(assetBiz.getType());
            if (bizType == null) {
                logger.error("Invalid asset biz type {} for asset biz {}", assetBiz.getType(), assetBiz.getId());
            }

            List<AssetAuth> authList = assetBiz.getAuths();
            if (authList != null) {
                for (AssetAuth auth : authList) {
                    AssetType assetType = AssetType.fromValue(auth.getAsset());
                    AssetAuthType authType = AssetAuthType.fromValue(auth.getAuth());
                    if (assetType == null) {
                        logger.error("Invalid asset type {} for asset biz {}", auth.getAsset(), assetBiz.getId());
                        error = true;
                    }

                    if (authType == null) {
                        logger.error("Invalid asset auth {} for asset biz {}", auth.getAuth(), assetBiz.getId());
                        error = true;
                    }
                }
            }
        }

        if (error) {
            throw new RuntimeException("Invalid asset auth configuration");
        }

        this.assetBiz = tmp;
    }

    public List<AssetBiz> getAll() {
        return new ArrayList<>(assetBiz.values());
    }

    public UserCoin getByUid(long uid) {
        UserCoin uc = userCoinDao.getByUid(uid);
        if( uc == null )
            uc = new UserCoin(uid);
        return uc;
    }

    public Map<Long, UserCoin> getByUids(List<Long> uids) {
        Map<Long, UserCoin> map = new HashMap<>();
        userCoinDao.multiGetByUids(uids).forEach(userCoin -> {
            map.put(userCoin.getUid(), userCoin);
        });
        return map;
    }

    private UserCoin lockUpdate(long uid, UpdateUserCoin runnable) {
        UserCoin uc = userCoinDao.getByUidLock(uid);
        uc = checkAndUpdate(uc, uid);
        runnable.run(uc);
        uc.setUpdateTime(new Date());
        userCoinDao.update(uc);
        return uc;
    }

    private UserCoin checkAndUpdate(UserCoin uc, long uid) {
        if (uc == null) {
            synchronized (this) {
                uc = userCoinDao.getByUidLock(uid);
                if (uc == null) {
                    try {
                        logger.info("create user coin for uid {}",uid);
                        userCoinDao.create(uid);
                    } catch (Exception e) {
                        logger.error("create user coin for uid {} failed. Try get it", uid);
                    }
                    uc = userCoinDao.getByUidLock(uid);
                    if (uc == null) {
                        logger.error("We may encounter real database error when getting user coin for {}", uid);
                        throw new ResponseException(ResponseError.E_SERVER, "Asset Server Error");
                    }
                }
                return uc;
            }
        }

        if (uc.getRiceroll() < 0) {
            logger.error("invalid user coin detected. riceroll, less than zero");
            throw new ResponseException(ResponseError.E_SERVICE, "Invalid user riceroll");
        }

//        if (!DateUtils.isToday(uc.getCashoutdate())) {
//            userCoinDao.resetCashoutDay(uc);
//        }
        return uc;
    }


    public List<UserCoin> getReceiveGiftRank() {
        return userCoinDao.getRecvGiftRank();
    }

    public List<UserCoin> getSendGiftRank() {
        return userCoinDao.getSendGiftRank();
    }

    public List<UserCoin> getRechargeRank(int start, int count, String order) {
        if (order.compareTo("accumecoin") == 0) {
            return userCoinDao.getRechargeRank(start, count);
        } else {
            return userCoinDao.getRechargeRankByEcoin(start, count);
        }
    }

    public List<Object[]> getTotalAsset() {
        return userCoinDao.getTotalAsset();
    }

    public AssetBiz getAssetBizByAK(String ak) {
        return this.assetBiz.get(ak);
    }

    private AssetUpdateResult transferUpdateResult(CenterAssetTransaction transaction, long uid) {
        AssetUpdateResult result = new AssetUpdateResult();
        result.setTid(transaction.getTid());
        result.addAsset(getByUid(uid));
        return result;
    }


    @Transactional
    public AssetUpdateResult unaryOperate(AssetSystemID id, User user, long value) {
/*        AssetBiz assetBiz = AssetSystemID.getAssetBiz(id);
        AssetOperation operation = new AssetOperation();
        operation.addOperand(user, value);
        return operate(assetBiz, operation);*/
        return transferUpdateResult(assetCenterService.unaryOperation(id.getValue(), user.getId(), value, ""), user.getId());
    }

    @Transactional
    public AssetUpdateResult unaryOperate(AssetSystemID id, long uid, long value) {
/*        AssetBiz assetBiz = AssetSystemID.getAssetBiz(id);
        AssetOperation operation = new AssetOperation();
        operation.addOperand(uid, value);
        return operate(assetBiz, operation);*/

        return transferUpdateResult(assetCenterService.unaryOperation(id.getValue(), uid, value, ""), uid);
    }


    @Transactional
    public AssetUpdateResult binaryOperate(AssetSystemID id, long fromUid, long fromValue, long toUid, long toValue) {
/*        AssetBiz assetBiz = AssetSystemID.getAssetBiz(id);
        AssetOperation operation = new AssetOperation();
        operation.addOperand(fromUid, fromValue);
        operation.addOperand(toUid, toValue);
        return operate(assetBiz, operation);*/

        CenterAssetTransaction transaction = assetCenterService.binaryOperation(id.getValue(), fromUid, fromValue, toUid, toValue, "");
        AssetUpdateResult result = new AssetUpdateResult();
        result.setTid(transaction.getTid());
        result.addAsset(getByUid(fromUid));
        result.addAsset(getByUid(toUid));

        return result;
    }

    @Transactional
    public AssetUpdateResult binaryOperate(AssetSystemID id, User fromUser, long fromValue, User toUser, long toValue) {
/*        AssetBiz assetBiz = AssetSystemID.getAssetBiz(id);
        AssetOperation operation = new AssetOperation();
        operation.addOperand(fromUser, fromValue);
        operation.addOperand(toUser, toValue);
        return operate(assetBiz, operation);*/

        CenterAssetTransaction transaction = assetCenterService.binaryOperation(id.getValue(), fromUser.getId(), fromValue, toUser.getId(), toValue, "");
        AssetUpdateResult result = new AssetUpdateResult();
        result.setTid(transaction.getTid());
        result.addAsset(getByUid(fromUser.getId()));
        result.addAsset(getByUid(toUser.getId()));

        return result;
    }

    @Transactional
    public AssetUpdateResult operate(AssetBiz assetBiz, AssetOperation operation) {
        return this.operate(assetBiz, operation, null);
    }

    private AssetUpdateResult operate(AssetBiz assetBiz, AssetOperation operation, AssetUpdateListener listener) {
        List<AssetAuth> authList = assetBiz.getAuths();

        if (authList == null) {
            throw new ResponseException(ResponseError.E_ASSET_AUTH, "Asset Biz have no auth");
        }

        if (authList.size() != operation.operandCount()) {
            throw new ResponseException(ResponseError.E_ASSET_OPERATION, "operands mismatch");
        }

        boolean isSpecialUser = false;
        AssetUpdateResult result = new AssetUpdateResult();
        for (int i = 0; i < authList.size(); i++) {
            AssetAuth auth = authList.get(i);
            AssetOperand operand = operation.getOperand(i);

            long value = Math.abs(operand.getValue());
            /*
            TODO: Maybe should be controlled by a flag
            if (value == 0) {
                throw new ResponseException(ResponseError.E_ASSET_OPERATION, "Asset operation value should not be zero");
            }
             */


            Long lower = auth.getLowerBound();
            Long upper = auth.getUpperBound();
            if (lower != null && lower > value) {
                throw new ResponseException(ResponseError.E_ASSET_OPERATION, "Asset operation value should not be less than lower bound ");
            }

            if (upper != null && value > upper) {
                throw new ResponseException(ResponseError.E_ASSET_OPERATION, "Asset operation value should not be greater than lower bound ");
            }

            User user = operand.getUser();
            if (user == null)
                user = userService.ensureExists(operand.getUid());

            //特殊用户不允许易币消费类型操作
            if (i == 0
                    && (assetBiz.getType() == AssetBizType.BUSINESS.getValue() || assetBiz.getType() == AssetBizType.EXCHANGE.getValue())
                    && auth.getAuth() == AssetAuthType.MINUS.getValue()
                    && auth.getAsset() == AssetType.ECOIN.getValue()) {
                if (specialUserService.isSpecialUser(user)) {
                    if (authList.size() == 1)
                        throw new ResponseException(ResponseError.E_ASSET_OPERATION, "Special user not allowed to consume ecoin");

                    isSpecialUser = true;
                }
            }

            boolean onlyAccum = false;
            //易币到饭团类的特殊用户处理： 饭团不增加，只累积增加
            if (i == 1 && isSpecialUser && auth.getAsset() == AssetType.RICEROLL.getValue()) {
                logger.debug("Special user try to consume ecoin to riceroll. {} Only accumulate", user.getId());
                onlyAccum = true;
            }

            Pair<AssetRecord, UserCoin> singleResult = assetUpdate(result.getTid(), assetBiz, auth, operand.getUid(), value, onlyAccum, listener);
            result.setTid(singleResult.getLeft().getTid());
            result.addRecord(singleResult.getLeft());
            result.addAsset(singleResult.getRight());
            result.addUser(user);

            logger.info("[asset][update] {}: {} update {}({}) for type {} @ {}, accum: {}", result.getTid(), operand.getUid(), value, auth.getAuth(), auth.getAsset(), assetBiz.getId(), onlyAccum);
        }

        logger.info("[asset][update][success]: tid: {}", result.getTid());
        transactionNotify(assetBiz, operation, result);


        return result;
    }

    private void transactionNotify(AssetBiz biz, AssetOperation operation, AssetUpdateResult result) {
        AssetTransaction transaction = new AssetTransaction(biz, operation, result);
        this.listeners.forEach(listener -> {
            try {
                listener.onComplete(transaction);
            } catch (Exception e) {
                logger.error("Error notify transaction event", e);
            }
        });

        eventProducer.emitAsync(Constants.KAFKA_TOPIC_ASSET_TRANSACTION, null, transaction);
    }

    private Pair<AssetRecord, UserCoin> assetUpdate(String tid, AssetBiz biz, AssetAuth auth, long uid, long value, boolean onlyAccum, AssetUpdateListener listener) {

        AssetType assetType = AssetType.fromValue(auth.getAsset());
        AssetAuthType authType = AssetAuthType.fromValue(auth.getAuth());

        if (authType == AssetAuthType.MINUS)
            value = -value;

        final long val = value;
        UserCoin userCoin;
        long balance;
        boolean consume = biz.getType() == AssetBizType.BUSINESS.getValue();
        boolean accumulate = biz.getType() == AssetBizType.BUSINESS.getValue();

        if (assetType == AssetType.ECOIN) {

            userCoin = this.lockUpdate(uid, uc -> {
                if (uc.getEcoin() + val < 0) {
                    logger.info("{} minus ecoin " + " ecoin is not enough, needs {}, only {} ", uid, -val, uc.getEcoin());
                    throw new ResponseException(ResponseError.E_ASSET_ECOIN_NOT_ENOUGH, "金币不足");
                }
                if (listener != null)
                    listener.before(uc);

                uc.setEcoin(uc.getEcoin() + val);
                if (consume && val < 0) {
                    uc.setCostecoin(uc.getCostecoin() - val);
                }
                if (accumulate && val > 0) {
                    uc.setAccumecoin(uc.getAccumecoin() + val);
                }

                if (listener != null)
                    listener.after(uc);
            });
            balance = userCoin.getEcoin();

        } else if (assetType == AssetType.RICEROLL) {

            userCoin = this.lockUpdate(uid, uc -> {
                if (uc.getRiceroll() + val < 0) {
                    logger.info("{} minus riceroll " + " riceroll is not enough, needs {}, only {} ", uid, val, uc.getRiceroll());
                    throw new ResponseException(ResponseError.E_ASSET_RICEROLL_NOT_ENOUGH, "饭团不足");
                }
                if (listener != null)
                    listener.before(uc);

                if (!onlyAccum)
                    uc.setRiceroll(uc.getRiceroll() + val);

                if (val > 0 && accumulate )
                    uc.setAccumriceroll(uc.getAccumriceroll() + val);

                if (listener != null)
                    listener.after(uc);
            });
            balance = userCoin.getRiceroll();

        } else if (assetType == AssetType.BARLEY) {

            userCoin = this.lockUpdate(uid, uc -> {
                if (uc.getBarley() + val < 0) {
                    logger.info("{} cost ecoin " + " ecoin is not enough, needs {}, only {} ", uid, val, uc.getBarley());
                    throw new ResponseException(ResponseError.E_ASSET_BARLEY_NOT_ENOUGH);
                }
                if (listener != null)
                    listener.before(uc);

                uc.setBarley(uc.getBarley() + val);

                if (val > 0)
                    uc.setAccumbarley(uc.getAccumbarley() + val);

                if (listener != null)
                    listener.after(uc);
            });
            balance = userCoin.getBarley();

        } else {
            throw new ResponseException(ResponseError.E_ASSET_OPERATION, "Invalid asset type");
        }

        if (onlyAccum) {
            value = 0;
        }

        AssetRecord record = addAssetRecord(tid, uid, biz.getId(), value, balance, assetType, "");
        record.setOriginValue(val);
        return Pair.of(record, userCoin);
    }

    private AssetRecord addAssetRecord(String tid, long uid, long authid, long value, long balance, AssetType type, String desc) {
        if (tid == null) {
            DecimalFormat df = new DecimalFormat("0000");
            tid = System.currentTimeMillis() +
                    new DecimalFormat("0000000").format(new Random().nextInt(10000000));

            long fmt = authid;
            if (authid < 0) {
                fmt = 10000 + fmt;
            }

            tid = df.format(fmt) + tid;
        }


        AssetRecord assetRecord = new AssetRecord();
        assetRecord.setUid(uid);
        assetRecord.setTid(tid);
        assetRecord.setType(type.getValue());
        assetRecord.setValue(value);
        assetRecord.setBalance(balance);
        assetRecord.setAuthid(authid);
        assetRecord.setOperateTime(new Date());
        assetRecord.setDescription(desc);
        String hash = DigestUtils.sha256Hex(String.format("%s-%d-%d-%d-%d-%s", tid, uid, authid, value, type.getValue(), assetRecord.getOperateTime().toString()));
        assetRecord.setHash(hash.substring(0, 16));

        if (value != 0) {
            assetDao.save(assetRecord);
        }

        return assetRecord;
    }
}
