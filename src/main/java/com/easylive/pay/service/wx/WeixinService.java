package com.easylive.pay.service.wx;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.CashConfig;
import com.easylive.pay.config.WeixinConfig;
import com.easylive.pay.dao.RechargeDao;
import com.easylive.pay.dao.RechargeWeixinAppletRepository;
import com.easylive.pay.dao.jpa.RechargeWeixinDomainMapRepository;
import com.easylive.pay.dao.jpa.RechargeWeixinDomainRepository;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.*;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.model.RechargeResult;
import com.easylive.pay.model.User;
import com.easylive.pay.model.user.UserInfo;
import com.easylive.pay.output.BaseAsset;
import com.easylive.pay.service.*;
import com.easylive.pay.service.huifu.HuifuPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.KeyStore;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class WeixinService {
    private static final String WEIXIN_REDPACK_URL = "https://api.mch.weixin.qq.com/mmpaymkttransfers/sendredpack";
    private static final String REDPACK_APP_ID = "wxf8a573fb0b091fa7";
    private static final String REDPACK_MCH_ID = "1298541101";
    @Autowired
    private WeixinConfig weixinConfig;
    @Autowired
    private StringRedisTemplate template;
    @Autowired
    private CashoutService cashoutService;
    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private RechargeDao rechargeDao;

    @Autowired
    private PayService payService;

    @Autowired
    private UserService userService;

    @Autowired
    private RechargeControlService rechargeControlService;

    @Autowired
    private WeixinAppService weixinAppService;

    @Autowired
    private RechargeWeixinAppletRepository appletRepository;

    @Autowired
    private RechargeWeixinDomainMapRepository domainMapRepository;

    @Autowired
    private RechargeWeixinDomainRepository domainRepository;

    @Autowired
    private HuifuPayService huifuPayService;

    @Autowired
    private CashConfig cashConfig;
    private RestTemplate restTemplate;
    private volatile Map<String, RechargeWeixinMap> appMap = new HashMap<>();
    private volatile Map<Long, RechargeWeixinApp> idMap = new HashMap<>();
    private volatile Map<String, RechargeWeixinAppletEntity> appletMap = new HashMap<>();
    private volatile Map<Long,RechargeWeixinDomain> appDomainMap = new HashMap<>();
    private RechargeWeixinDomain primeDomain;

    @PostConstruct
    private void init() throws Exception {
        load();

        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        File key = ResourceUtils.getFile(weixinConfig.getRedpackCertFile());
        try (InputStream in = Files.newInputStream(key.toPath())) {
            keyStore.load(in, weixinConfig.getRedpackCertPass().toCharArray());
        }

        SSLContext sslContext = SSLContextBuilder.create()
                .loadKeyMaterial(keyStore, weixinConfig.getRedpackCertPass().toCharArray())
                .loadTrustMaterial(null, new TrustSelfSignedStrategy()).build();
        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext);
        HttpClient httpClient = HttpClients.custom().setSSLSocketFactory(socketFactory).build();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(
                httpClient);
        requestFactory.setConnectionRequestTimeout(30000);
        requestFactory.setConnectTimeout(30000);
        requestFactory.setReadTimeout(30000);
        this.restTemplate = new RestTemplate(requestFactory);
        restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());
        restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
    }

    @Transactional(readOnly = true)
    public void load() {
        Map<String, RechargeWeixinMap> map1 = new HashMap<>();
        Map<Long, RechargeWeixinApp> app1 = new HashMap<>();
        Map<String, RechargeWeixinAppletEntity> applet1 = new HashMap<>();
        Map<Long, RechargeWeixinDomain> domainMap1 = new HashMap<>();

        rechargeDao.getAllWeixinMap().forEach(rechargeWeixinMap -> {
            map1.put(getMapKey(rechargeWeixinMap.getMgsAppId(), rechargeWeixinMap.getType()), rechargeWeixinMap);
        });

        rechargeDao.getAllWeixinApp().forEach(rechargeWeixinApp -> {
            app1.put(rechargeWeixinApp.getId(), rechargeWeixinApp);
        });

        appletRepository.findAll().forEach(rechargeWeixinApplet -> {
            applet1.put(rechargeWeixinApplet.getAppId(), rechargeWeixinApplet);
        });

        for( RechargeWeixinDomainMap rechargeWeixinDomainMap : domainMapRepository.findAll()) {
            RechargeWeixinDomain domain = rechargeWeixinDomainMap.getDomain();
            domainMap1.put( rechargeWeixinDomainMap.getMgsAppId(), domain);
        }

        RechargeWeixinDomain primeDomain = null;
        for(RechargeWeixinDomain domain : domainRepository.findAll()) {
            if( domain.isPrime()) {
                primeDomain = domain;
            }
        }

        if( primeDomain == null ) {
            throw new ResponseException(ResponseError.E_SERVER, "没有设置默认域名");
        }

        appMap = map1;
        this.idMap = app1;
        this.appletMap = applet1;
        this.appDomainMap = domainMap1;
        this.primeDomain = primeDomain;
    }

    private RechargeWeixinApp getWeixinAppById(String merchantId) {
        try {
            return this.idMap.get(Long.parseLong(merchantId));
        } catch (Exception ignored) {
        }
        return null;
    }

    private RechargeWeixinApp getByMGSAppIdAndType(int appId, WeixinPayType type) {
        String key = getMapKey(appId, type.ordinal());
        RechargeWeixinMap app = this.appMap.get(key);
        if (app == null)
            return null;
        return app.getWeixinApp();
    }

    private String getAppDomain(int appId) {
        RechargeWeixinDomain domain = this.appDomainMap.get((long)appId);
        if( domain != null )
            return domain.getDomain();
        return primeDomain.getDomain();
    }

    private String getMapKey(int appId, int type) {
        return appId + "_" + type;
    }

    private RechargeWeixinApp getWeixinPay(String appId, String mchId, List<Integer> tradeTypeList) {

        for (RechargeWeixinApp app : idMap.values()) {
            if (app.getAppId().equals(appId) &&
                    app.getMchId().equals(mchId) &&
                    tradeTypeList.contains(app.getType())) {
                return app;
            }
        }
        log.error("Invalid weixin pay app id: {}, {}, {}", appId, mchId, tradeTypeList.toString());
        throw new ResponseException(ResponseError.E_RECHARGE_INVALID_APPID, "Invalid Weixin Pay APP ID");
    }

    private RechargeWeixinApp getRedPackWeixinPay() {
        return getWeixinPay(REDPACK_APP_ID, REDPACK_MCH_ID, List.of(WeixinPayType.MP.ordinal()));
    }

    public MobilePlatform getPlatform(String platform) {
        if (platform.compareTo("ios") == 0) {
            return MobilePlatform.MPF_IOS;
        } else if (platform.compareTo("android") == 0) {
            return MobilePlatform.MPF_ANDROID;
        } else {
            return MobilePlatform.MPF_IOS;
        }
    }

    public Map<String, String> createAppOrder(long uid, long amount, String clientIp, MobilePlatform mobilePlatform, int mgsAppId) {
        return createWeixinUnifiedOrder(mgsAppId, WeixinPayType.APP, amount, uid, clientIp, mobilePlatform, "");
    }

    public Map<String, String> createH5Order(long uid, long amount, String clientIp, int mgsAppId) {
        return createWeixinUnifiedOrder(mgsAppId, WeixinPayType.H5, amount, uid, clientIp, MobilePlatform.MPF_H5, "");
    }

    public Map<String, String> createCodePayOrder(long uid, long amount, String clientIp, int mgsAppId) {

        return createWeixinUnifiedOrder(mgsAppId, WeixinPayType.CODE, amount, uid, clientIp, MobilePlatform.MPF_WEIXIN, "");
    }

    public Map<String, String> createMPOrder(long uid, long amount, String clientIp, String openId, MobilePlatform mobilePlatform, int mgsAppId) {
        return createWeixinUnifiedOrder(mgsAppId, WeixinPayType.MP, amount, uid, clientIp, mobilePlatform, openId);
    }

    public Map<String, String> createMiniAppOrder(long uid, long amount, String clientIp, MobilePlatform mobilePlatform, String openId, int mgsAppId) {
        return createWeixinUnifiedOrder(mgsAppId, WeixinPayType.MINIAPP, amount, uid, clientIp, mobilePlatform, openId);
    }

    private RechargeRecord createWeixinRechargeOrder(int mgsAppId,  WeixinPayType type, long uid, long totalFee, String clientIp, MobilePlatform clientPlatform, boolean prepare) {
        RechargeWeixinApp weixinPay = getByMGSAppIdAndType(mgsAppId, type);

        User user = userService.ensureExists(uid);

        int rechargeId = rechargeControlService.getWeixinRechargeId(user, mgsAppId, type.ordinal(), totalFee);

        if( rechargeId != -1 ) {
            RechargeWeixinApp controlledApp = this.idMap.get((long) rechargeId);
            if (controlledApp != null) {
                // 原来的下单模式不支持H5下单小程序支付，因此需要判断类型是否一致。如果不一致，就不映射
                if (!prepare && controlledApp.getRealType() != null && controlledApp.getRealType() != type.ordinal()) {
                    log.info("Mapped result ignored, as mapped real type is not equal to origin type: {} != {}", controlledApp.getRealType(), type);
                } else {
                    if (weixinPay != null)
                        log.info("Map Weixin App from {} to {}", weixinPay.getAppId(), controlledApp.getAppId());
                    else
                        log.info("Map Weixin App to {}", controlledApp.getAppId());
                    weixinPay = controlledApp;
                }
            } else {
                log.warn("Recharge ID {} not found", rechargeId);
            }
        }

        if (weixinPay == null) {
            log.error("can not find weixin app for mgs app_id:{}", mgsAppId);
            throw new ResponseException(ResponseError.E_RECHARGE_MGS_APPID, "can not find weixin app for mgs app_id");
        }


        String appId = weixinPay.getAppId();
        String mchId = weixinPay.getMchId();

        log.info("[Recharge][CreateOrder][Start] MGS AppId:{}, Weixin AppId: {},Pay AppId: {}, Type: {}, MchId: {},  Client IP: {}, UID: [{}], Amount: [{}] ",
               mgsAppId, appId, weixinPay.getId(), weixinPay.getType(), mchId, clientIp, uid, totalFee);

        if( weixinPay.getType() != type.ordinal() ) {
            log.error("Invalid weixin pay config, {}, {}", weixinPay.getType(), type);
            throw new ResponseException(ResponseError.E_WEIXINPAY_CONFIG,"微信支付配置错误");
        }

        OrderInfo orderInfo = new OrderInfo(mgsAppId, uid, totalFee, type.getPayPlatform(), clientPlatform, clientIp);
        if( weixinPay.getRealType() != null ) {
            WeixinPayType realType = WeixinPayType.fromValue(weixinPay.getRealType());
            if( realType == null ) {
                log.error("{} is an invalid weixin pay type", weixinPay.getRealType());
                throw new ResponseException(ResponseError.E_WEIXINPAY_TYPE, "无效的微信支付类型");
            }
            orderInfo.setRealPayPlatform(realType.getPayPlatform());
        }
        orderInfo.setMchId(String.valueOf(weixinPay.getId()));
        return rechargeService.createRechargeRecord(orderInfo);
    }


    private Map<String,String> createWeixinUnifiedOrder(RechargeRecord rechargeRecord, String openId) {
        String orderId = rechargeRecord.getOrderId();
        int mgsAppId = rechargeRecord.getAppId();
        long uid = rechargeRecord.getUid();
        long totalFee = rechargeRecord.getRmb();

        log.info("Create weixin unified order from recharge record:{}, {}, {},{},{} ", orderId, mgsAppId, uid, totalFee, rechargeRecord);

        PayPlatform platform = PayPlatform.fromValue(rechargeRecord.getPlatform());

        RechargeWeixinApp weixinPay = this.getWeixinAppById(rechargeRecord.getMerchantId());
        if( weixinPay == null )
            throw new ResponseException(ResponseError.E_RECHARGE_MGS_APPID, "can not find weixin app for mgs app_id");

        Integer payTypeInt = weixinPay.getRealType();
        if( payTypeInt == null)
            payTypeInt = weixinPay.getType();

        WeixinPayType payType = WeixinPayType.fromValue(payTypeInt);
        if (payType == null) {
            log.error("Invalid weixin pay type configured:{}", weixinPay.getType());
            throw new ResponseException(ResponseError.E_WEIXINPAY_TYPE);
        }

        long ecoin = rechargeService.calcEcoinByUid(uid, rechargeRecord.getRmb());
        String detail = payService.getProductName(platform, mgsAppId, totalFee, ecoin);
        //String detail = "充值 " + new BigDecimal(totalFee).divide(hundred, 2, RoundingMode.HALF_UP) + " 元";

        UserInfo user = userService.getUserRegInfoByUid(rechargeRecord.getUid());

        WeixinOrderInfo orderInfo = new WeixinOrderInfo();
        orderInfo.setOrderId(orderId);
        orderInfo.setAmount( RechargeService.getAmountDecimal(totalFee) );
        orderInfo.setApp(weixinPay);
        orderInfo.setDetail(detail);
        orderInfo.setName(user.getName());
        orderInfo.setUserRegTime(user.getReg().getRegisterTime());
        orderInfo.setType(payType);
        orderInfo.setOpenId(openId);
        orderInfo.setClientIp(rechargeRecord.getClientIp());



        log.info("Weixin order Info:{}", orderInfo);
        WeixinPayProvider provider;
        if ( weixinPay.getPlatform() == ThirdPayPlatform.OFFICIAL.getValue() ) {
            provider = new WeixinPayOfficialProvider(orderInfo, weixinConfig.getNotifyUrl());
        } else if ( weixinPay.getPlatform() == ThirdPayPlatform.HUIFU.getValue() ) {
            provider = huifuPayService.getWeixinProvider(orderInfo);
        } else {
            throw new ResponseException(ResponseError.E_THIRD_ERROR,"不支持的微信支付平台");
        }
        orderInfo.setProvider(provider);
        Map<String,String>  result = provider.execute();
        log.info("Create Weixin unified order result: {}", result);
        return result;
    }

    private Map<String, String> createWeixinUnifiedOrder(int mgsAppId, WeixinPayType type, Long totalFee, Long uid, String clientIp, MobilePlatform clientPlatform, String openId) {
        RechargeRecord rechargeRecord = this.createWeixinRechargeOrder(mgsAppId, type, uid, totalFee, clientIp, clientPlatform, false);
        return createWeixinUnifiedOrder(rechargeRecord, openId);
    }

    /**
     * 验证微信发回来的订单情况
     *
     * @param xml
     * @return
     */
    @Transactional
    public String valid(String xml) {
        log.info("validate weixin pay order: {}", xml);
        List<String> list = new ArrayList<>();
        Collections.addAll(list, "appid", "attach", "bank_type", "cash_fee", "coupon_count","coupon_fee","fee_type", "is_subscribe", "mch_id",
                "nonce_str", "openid", "out_trade_no", "result_code", "return_code",
                "sign", "time_end",
                "total_fee", "trade_type", "transaction_id");

        for(int i = 0; i <= 9; ++i) {
            list.add("coupon_id_" + i );
            list.add("coupon_fee_" + i );
        }

        //TODO: 检查订单是不是已经完成
        String ret = "<xml>" +
                "  <return_code><![CDATA[FAIL]]></return_code>" +
                "  <return_msg><![CDATA[SIGN ERROR]]></return_msg>" +
                "</xml>";

        TreeMap<String, String> reply = WeixinPayOfficialProvider.parseResultXml(xml, list);
        if (reply.containsKey("result_code") && reply.get("result_code").equals("SUCCESS")) {
            // 微信公众号和APP支付是两套key,因此需要先拿出来Order看看是哪个平台
            RechargeRecord r = rechargeService.getRecordByOrderIdWithLock(reply.get("out_trade_no"));
            if (r == null) {
                log.error("[ReCharge][Valid][Failed] Exit R is Null");
                return ret;
            }

            String appId = reply.get("appid");
            String mchId = reply.get("mch_id");
            String tradeType = reply.get("trade_type");

            log.info("Weixin valid order on: {} {} {}", appId, mchId, tradeType);
            List<Integer> payTypeList = WeixinPayType.fromTradeType(tradeType);
            if (payTypeList.isEmpty()) {
                log.error("Invalid trade type when valid :{}", tradeType);
                throw new ResponseException(ResponseError.E_WEIXINPAY_TRADE_TYPE);
            }

            RechargeWeixinApp weixinPay = this.getWeixinPay(appId, mchId, payTypeList);
            String wxSign = reply.get("sign");
            reply.remove("sign");
            String expectedSign = WeixinPayOfficialProvider.getSignature(reply, weixinPay.getAppSecret());
            if (expectedSign.equals(wxSign)) {
                // 根据订单号找到订单,然后更新其中的状态
                log.info("Param: client platform: {}", r.getClientPlatform());
                // TODO 需要鉴定更多的状态
                if (r.getStatus() == OrderStatus.ORS_CREATE.getValue()) {

                    String tradeOrderId = reply.get("transaction_id");

                    RechargeResult result = rechargeService.recharge(r, tradeOrderId);
                    rechargeControlFeedback(r.getMerchantId(), r.getAppId(), (int) result.getRmb());

                    //String tid = result.getTid();
                    //long ecoin = result.getEcoin();
                    log.info("[ReCharge][Valid][Success]: LocalTradeNo [ {} ] ,outTradeID [ {} ], Uid [{}]",
                            r.getOrderId(), r.getPlatformOrderId(), r.getUid());
                    //rechargeService.completeTradeNo(r, tid, reply.get("transaction_id"), ecoin);
                    log.info("[advstat][recharge][success] total {} rmb {} uid {} platform {} order_id {}",
                            r.getEcoin(), r.getRmb(), r.getUid(), r.getPlatform(), r.getOrderId());

                } else {
                    log.error("[ReCharge][Valid][Failed] Order status is Not CREATE : {}", r.getStatus());
                    return ret;
                }
                return "<xml>" +
                        "  <return_code><![CDATA[SUCCESS]]></return_code>" +
                        "  <return_msg><![CDATA[OK]]></return_msg>" +
                        "</xml>";
            } else {
                log.error("[ReCharge][Valid][Failed] Sign Not Equal: MySign: [ {} ], WxSign [ {} ]"
                        , expectedSign, wxSign);
            }
        } else {
            if (reply.containsKey("result_code")) {
                log.error("[ReCharge][Valid][Failed] Result Code is Not Succcess:  {}", xml);
            } else {
                log.error("[ReCharge][Valid][Failed] TX No Result Code: {} ", xml);
            }
        }
        return ret;
    }


    public BaseAsset cashout(long uid, long rmb, String openId, String clientIp, CashoutPlatform cashoutPlatform) {
        int device = MobilePlatform.MPF_WEIXIN.getValue();
        this.verifyCashout(uid, rmb);


        log.info("{} {} cashout rmb {}", uid, openId, rmb);
        //创建提现记录
        CashoutRecord cashoutRecord = cashoutService.createCashOut(getRedPackWeixinPay().getMchId(), openId, uid, rmb,
                device, rmb * cashConfig.getRateOfRmbToRiceroll(), cashoutPlatform.getValue(), clientIp);

        try {
            UserCoin uc = cashoutService.cashout(cashoutRecord, cr -> {
                this.sendWxRedpack(cashoutRecord);
            });
            return new BaseAsset(uc);

        } catch (Exception e) {
            cashoutService.failCashout(cashoutRecord);
            log.error("weixin cashout error: " + e.getMessage());
            throw e;
        }
    }


    public void sendWxRedpack(CashoutRecord cashoutRecord) {
        String sendName = weixinConfig.getSendName();
        String wishing = weixinConfig.getWishing();
        String actName = weixinConfig.getActName();
        String remark = weixinConfig.getRemark();

        RechargeWeixinApp weixinPay = getRedPackWeixinPay();

        long uid = cashoutRecord.getUid();
        long amount = cashoutRecord.getRmb();
        String openId = cashoutRecord.getOpenid();

        // 显式重新声明当前的请求状态
        log.info("[Cashout][RedPack][Start] UID [{}], Openid: [{}], Amount: [{}]", uid, openId, amount);
        Map<String, String> payload = new TreeMap<>();

        payload.put("nonce_str", cashoutRecord.getOrderid());
        payload.put("mch_billno", cashoutRecord.getOrderid());
        payload.put("mch_id", weixinPay.getMchId());
        payload.put("wxappid", weixinPay.getAppId());
        payload.put("send_name", sendName);
        payload.put("re_openid", cashoutRecord.getOpenid());
        payload.put("total_amount", String.valueOf(cashoutRecord.getRmb()));
        payload.put("total_num", "1");
        payload.put("wishing", wishing);
        payload.put("client_ip", cashoutRecord.getServer_ip());
        payload.put("act_name", actName);
        payload.put("remark", remark);

        try {
            String buf = WeixinPayOfficialProvider.buildXmlMessage(payload, weixinPay.getAppSecret(), true);
            log.info("[Cashout][RedPack] Weixin Request: {}", buf);


            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_XML);
            HttpEntity<String> request = new HttpEntity<>(buf, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(WEIXIN_REDPACK_URL, request, String.class);
            String xml = response.getBody();

            log.info("[Cashout][RedPack] Weixin Response: {}", xml);
            List<String> list = new ArrayList<>();
            Collections.addAll(list, "return_code", "return_msg", "result_code", "err_code", "mch_billno", "mch_id",
                    "wxappid", "re_openid", "total_amount",
                    "send_listid", "send_time");
            Map<String, String> reply = WeixinPayOfficialProvider.parseResultXml(xml, list);

            String returnCode = reply.get("return_code");
            String returnMsg = reply.get("return_msg");
            if (returnCode != null) {
                if (returnCode.equals("SUCCESS")) {
                    String errCode = reply.get("err_code");
                    String errMsg = reply.get("err_code_des");
                    String resultCode = reply.get("result_code");

                    if (resultCode.equals("SUCCESS")) {
                        log.info("[Cashout][Redpack][End] SUCCESS: UID [{}], Amount: [{}]", uid, amount);
                        return;
                    }

                    log.info("[Cashout][Redpack] Weixin return business error: {}, {}", errCode, errMsg);
                    if (errCode.equals("NO_AUTH")) {
                        throw new ResponseException(ResponseError.E_CASHOUT_NOAUTH, "请求被微信拦截");
                    } else if (errCode.equals("SENDNUM_LIMIT")) {
                        throw new ResponseException(ResponseError.E_CASHOUT_NUMLIMIT, "红包发放数量限制");
                    }

                } else {
                    log.info("[Cashout][Redpack] Weixin return error, {}, {}", returnCode, returnMsg);
                }
                throw new ResponseException(ResponseError.E_CASHOUT_WEIXIN_SERVICE, "发放红包失败");

            } else {
                log.info("[Cashout][Redpack] Weixin server response invalid");
                throw new ResponseException(ResponseError.E_CASHOUT_WEIXIN_SERVICE, "微信服务错误");
            }

        } catch (RestClientException e) {
            log.error("[Cashout][RedPack][End] FAILED: Request weixin server error: {}", e.getMessage());
            throw new ResponseException(ResponseError.E_CASHOUT_WEIXIN_SERVICE, "微信服务错误");
        } catch (ResponseException e) {
            log.info("[Cashout][Redpack][End] FAILED: {}, {}, {}", uid, amount, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("[Cashout][RedPack][End] FAILED Unknown exception :UID [{}], Amount: [{}]: {} ", cashoutRecord.getUid(), cashoutRecord.getRmb(), e.getMessage());
            throw new ResponseException(ResponseError.E_CASHOUT_WEIXIN, "微信提现服务器出错");
        }
    }

    private void verifyCashout(long uid, long rmb) {
        log.info("User: " + uid + " rmb: " + rmb);
        // 最大最小提现额度
        int maxCashoutLimit = 20000;
        int minCashoutLimit = 200;

        // 每分钟最大提现次数
        int maxCashoutCount = 1;

        // 额度限制是要做的
        if (rmb > maxCashoutLimit || rmb < minCashoutLimit) {
            throw new ResponseException(ResponseError.E_CASHOUT_AMOUNT_LIMIT, "单次提款额度:2元~200元");
        }

        // 每分钟提现一次
        ValueOperations<String, String> ops = this.template.opsForValue();
        String key = "cashout_limit_" + uid;
        String value = "0";
        if (this.template.hasKey(key)) {
            value = ops.get(key);
        }
        if (!value.equals("0")) {
            // 有值,那就说,不能这么快的请求
            throw new ResponseException(ResponseError.E_CASHOUT_INTERVAL, "每分钟只能领取1次");
        }
        ops.set(key, "1", maxCashoutCount, TimeUnit.MINUTES);

    }


    public CashoutRecord createCashOutRecord(String openid, long uid, long rmb, int device, int riceroll, int value, String clientIp) {
        return cashoutService.createCashOut(getRedPackWeixinPay().getMchId(), openid, uid, rmb, device, riceroll, value, clientIp);
    }


    public WeixinPrepareOrder prepareH5Order(long uid, long amount, String clientIp, MobilePlatform clientPlatform, int appId) {
        return prepareOrder(WeixinPayType.H5, uid, amount, clientIp, clientPlatform, appId);
    }

    public WeixinPrepareOrder prepareAppOrder(long uid, long amount, String clientIp, MobilePlatform clientPlatform, int appId) {
        return prepareOrder(WeixinPayType.APP, uid, amount, clientIp, clientPlatform, appId);
    }

    public WeixinPrepareOrder prepareOrder(WeixinPayType payType, long uid, long amount, String clientIp, MobilePlatform clientPlatform, int appId) {
        WeixinPrepareOrder order = new WeixinPrepareOrder();

        RechargeRecord rechargeRecord = this.createWeixinRechargeOrder(appId, payType, uid, amount, clientIp, clientPlatform,true);
        RechargeWeixinApp weixinApp = this.getWeixinAppById(rechargeRecord.getMerchantId());
        if( weixinApp == null ) {
            log.error("Invalid merchant id:{}", rechargeRecord.getMerchantId());
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_APPID, "微信应用不存在");
        }


        Integer type = weixinApp.getRealType();
        if( type == null )
            type = weixinApp.getType();
        String url;
        if( type == WeixinPayType.H5.ordinal() ) {
            //H5支付链接
            String domain = getAppDomain(rechargeRecord.getAppId());
            url = String.format("https://%s%s?orderId=%s",domain, weixinConfig.getH5PayPath(), rechargeRecord.getOrderId());
            log.info("H5 pay url:{}", url);
        } else if (type == WeixinPayType.APP.ordinal()) {
            // APP 下单不需要url
            url = "";
        } else if( type == WeixinPayType.MINIAPP.ordinal() ) {
            //小程序支付链接
            RechargeWeixinAppletEntity appletEntity = this.appletMap.get(weixinApp.getAppId());
            if( appletEntity == null ) {
                log.error("Applet pay path not configured: {}", weixinApp.getAppId());
                throw new ResponseException(ResponseError.E_WEIXINPAY_APPLET,"小程序没有配置");
            }
            String query = "orderId=" + rechargeRecord.getOrderId();
            log.info("Generate applet pay scheme: {}, {},{},{}", weixinApp.getAppId(), appletEntity.getPath(), query, appletEntity.getEnv());
            url = weixinAppService.generateAppletScheme(weixinApp.getAppId(),  appletEntity.getPath(), query, appletEntity.getEnv());
            log.info("Generate result: {}", url);
        } else {
            log.error("Invalid weixin pay type :{}", weixinApp.getRealType());
            throw new ResponseException(ResponseError.E_WEIXINPAY_TRADE_TYPE,"微信支付类型不存在");
        }

        order.setOrderId( rechargeRecord.getOrderId() );
        order.setUrl(url);
        order.setType(type);
        return order;
    }

    @Transactional
    public  Map<String,String> requestAppletOrder(String orderId, String code) {
        log.info("[Recharge][WX][Request] Request applet order: {},{}",  orderId, code);
        RechargeRecord record = rechargeService.getRecordByOrderIdWithLock(orderId);
        validateRechargeRecordBeforeRequest(record, orderId, WeixinPayType.MINIAPP );

        RechargeWeixinApp weixinApp = this.getWeixinAppById(record.getMerchantId());
        if (weixinApp == null) {
            log.error("Invalid merchant id:{}", record.getMerchantId());
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_APPID, "微信应用不存在");
        }

        //根据code获取openId
        String openId = weixinAppService.getOpenIdFromCode(weixinApp.getAppId(), code);
        log.info("get openId: {}", openId);
        if( openId == null ) {
            log.error("cant not get open id");
            throw new ResponseException(ResponseError.E_WEIXINPAY_APPLET, "无法获取openID");
        }

        return createWeixinUnifiedOrder(record, openId);
    }

    private void validateRechargeRecordBeforeRequest(RechargeRecord record, String orderId, WeixinPayType type) {
        if( record == null ) {
            log.error("[RECHARGE][WX][REQUEST]: order not exists:{}", orderId);
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "订单不存在");
        }
        if( record.getStatus() != OrderStatus.ORS_CREATE.ordinal() )  {
            log.error("[RECHARGE][WX][REQUEST]: order status not create:{}", orderId);
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_STATUS, "订单状态错误");
        }


    }

    @Transactional
    public Map<String, String> requestH5Order(String orderId) {
        log.info("[Recharge][WX][Request] Request H5 order: {}",  orderId );
        RechargeRecord record = rechargeService.getRecordByOrderIdWithLock(orderId);
        validateRechargeRecordBeforeRequest(record, orderId, WeixinPayType.H5);
        return createWeixinUnifiedOrder(record, "");
    }


    @Transactional
    public Map<String, String> requestAppOrder(String orderId) {
        log.info("[Recharge][WX][Request] Request App order: {}",  orderId );
        RechargeRecord record = rechargeService.getRecordByOrderIdWithLock(orderId);
        validateRechargeRecordBeforeRequest(record, orderId, WeixinPayType.APP);
        return createWeixinUnifiedOrder(record, "");
    }

    public void rechargeControlFeedback(String merchantId, int appId, int rmb) {
        RechargeWeixinApp app = getWeixinAppById(merchantId);
        if (app != null)
            rechargeControlService.increaseWeixinAmount(appId, (int) app.getId(), rmb);
        else {
            log.error("Invalid weixin app id: {}", merchantId);
        }
    }
}

