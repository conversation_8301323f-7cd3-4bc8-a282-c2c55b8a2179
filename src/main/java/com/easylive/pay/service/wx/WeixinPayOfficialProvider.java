package com.easylive.pay.service.wx;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.enums.WeixinPayType;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.io.SAXReader;
import org.xml.sax.InputSource;

import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/1/19
 */
@Slf4j
public class WeixinPayOfficialProvider implements WeixinPayProvider {
    private static final String WEIXIN_UNIFIED_ORDER_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";

    private final WeixinOrderInfo orderInfo;
    private String notifyUrl;
    public WeixinPayOfficialProvider(WeixinOrderInfo orderInfo, String notifyUrl) {
        this.notifyUrl = notifyUrl;
        this.orderInfo = orderInfo;
    }

    @Override
    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    @Override
    public void setReturnUrl(String returnUrl) {

    }

    @Override
    public Map<String,String> execute() {
        String appId = orderInfo.getApp().getAppId();
        String mchId = orderInfo.getApp().getMchId();
        String clientIp = orderInfo.getClientIp();
        String detail = orderInfo.getDetail();
        String orderId = orderInfo.getOrderId();
        String openId = orderInfo.getOpenId();
        String amount = RechargeService.getAmountIntegerInCent(orderInfo.getAmount());
        String appSecret = orderInfo.getApp().getAppSecret();
        WeixinPayType weixinPayType = orderInfo.getType();


        log.info("create weixin unified order:  {}, {}, {}", appId, mchId, weixinPayType);
        TreeMap<String, String> args = new TreeMap<>();
        args.put("appid", appId);
        // 商户号
        args.put("mch_id", mchId);
        args.put("device_info", "");
        // 随机无意义的字符串
        args.put("nonce_str", orderId);
        // 商品或者支付简要的表述
        args.put("body", detail);
        args.put("detail", detail);
        args.put("attach", orderId);
        // 系统内的交易号
        args.put("out_trade_no", orderId);
        args.put("fee_type", "CNY");
        // 总额度,单位为分
        args.put("total_fee", amount);
        // 终端IP,限制访问
        args.put("spbill_create_ip", clientIp);
        // 交易起始 time_start
        args.put("time_start", "");
        args.put("time_expire", "");
        args.put("goods_tag", "");
        // 交易结束 time_expire
        // 商品标记 goods_tag
        // 回调地址
        args.put("notify_url",  this.notifyUrl);
        args.put("trade_type", this.orderInfo.getType().getTradeType());
        args.put("product_id", "");
        args.put("limit_pay", "");

        if (StringUtils.isNotEmpty(openId)) {
            args.put("openid", openId);
        }

        String buf;
        buf = buildXmlMessage(args, orderInfo.getApp().getAppSecret());

        try {
            log.info("[Recharge][CreateOrder] Send XML {}", new String(buf.getBytes("ISO8859-1"), StandardCharsets.UTF_8));
        } catch (Exception ignored) {

        }


        TreeMap<String, String> ret;
        try {

            Map<String, String> headers = new HashMap<>();
            //headers.put(HttpHeaders.CONTENT_TYPE,"text/plain;charset=utf-8");
            String xml = HttpUtils.doPost(WEIXIN_UNIFIED_ORDER_URL, buf, headers);
            if (xml.isEmpty()) {
                log.error("[Recharge][CreateOrder][Failed] WX Request Error : XML Empty ");
                throw new ResponseException(ResponseError.E_WEIXINPAY_VALID_FAILD, "支付失败,微信服务器无响应");
            }
            xml = new String(xml.getBytes("ISO8859-1"), StandardCharsets.UTF_8);
            log.info("Response XML:{}", xml);

            List<String> list = new ArrayList<>();
            Collections.addAll(list, "return_code", "return_msg", "appid", "mch_id",
                    "nonce_str", "sign",
                    "result_code", "prepay_id", "trade_type", "code_url", "mweb_url");
            ret = parseResultXml(xml, list);
            if (!ret.containsKey("return_code")) {
                log.info("Invalid Weixin Server response");
                throw new ResponseException(ResponseError.E_WEIXINPAY_VALID_FAILD, "支付失败,微信服务器响应错误");
            }
            String retCode = ret.get("return_code");
            if (!retCode.equals("SUCCESS")) {
                String retMsg = ret.get("return_msg");
                log.info("[Recharge][CreateOrder][Failed]WX Request Error : [{}] - [{}]",
                        retCode, retMsg);
                throw new ResponseException(ResponseError.E_WEIXINPAY_VALID_FAILD, "支付失败");
            }

            String wxSign = ret.get("sign");

            ret.remove("sign");

            String mySign = this.getSignature(ret, appSecret);

            //校验返回请求的合法性
            TreeMap<String, String> signNode = new TreeMap<>();
            ret.remove("mch_id");
            ret.remove("nonce_str");

            if (mySign.equals(wxSign)) {
                // 重新构造签名字典

                long ts = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
                String sign;
                if (WeixinPayType.APP == weixinPayType) {
                    signNode.put("prepayid", ret.get("prepay_id"));
                    signNode.put("appid", appId);
                    signNode.put("noncestr", orderId);
                    signNode.put("package", "Sign=WXPay");
                    signNode.put("partnerid", mchId);
                    signNode.put("timestamp", Long.toString(ts));
                } else {
                    signNode.put("appId", appId);
                    signNode.put("timeStamp", Long.toString(ts));
                    signNode.put("nonceStr", orderId);
                    signNode.put("package", "prepay_id=" + ret.get("prepay_id"));
                    signNode.put("signType", "MD5");
                }
                sign = getSignature(signNode, appSecret);
                log.info("[Recharge][CreateOrder] 2nd Sign: {}", sign);
                ret.put("package", "Sign=WXPay");
                ret.put("prepayid", ret.get("prepay_id"));
                ret.put("partnerid", mchId);
                ret.put("noncestr", orderId);
                ret.put("orderid", orderId);
                ret.put("timestamp", Long.toString(ts));
                ret.put("sign", sign);
                log.info("[Recharge][CreateOrder] Return Sign: {}", sign);
                ret.put("mch_id", mchId);

                log.info("[Recharge][CreateOrder][Success] ");
            } else {
                log.error("[Recharge][CreateOrder][Failed] WXSign Not Equal:  WX[{}] != US[{}]", wxSign, mySign);
                throw new ResponseException(ResponseError.E_WEIXINPAY_VALID_FAILD);
            }

        } catch (Exception e) {
            log.error("[Recharge][CreateOrder][Failed]: {} ", e.getMessage());
            throw new ResponseException(ResponseError.E_WEIXINPAY_VALID_FAILD);
        }
        log.info("[Recharge][CreateOrder][Success] Type: {}, MchId: {}, Amount: [{}] ", weixinPayType, mchId, amount);

        return ret;
    }



    /**
     * 通过给定的参数给算参数
     *
     * @param args 参数数组
     * @return String, 算好的微信的签名
     */
    public static String getSignature(Map<String, String> args, String appSecret) {

        StringBuilder sbQuery = new StringBuilder();
        for (Object o : args.keySet()) {
            String key = o.toString();
            if (!args.get(key).isEmpty()) {
                sbQuery.append(key).append('=').append(args.get(key)).append("&");
            }
        }
        String data = sbQuery + "key=" + appSecret;
        log.info("data for signature: " + data);
        return DigestUtils.md5Hex(data).toUpperCase();
    }

    /**
     * 根据给定的参数算出一个XML出来
     *
     * @param args 参数数组,TreeMap
     * @return XML字符串
     */

    public static String buildXmlMessage(Map<String, String> args, String appSecret) {
        return buildXmlMessage(args, appSecret, false);
    }

    public static String buildXmlMessage(Map<String, String> args, String appSecret, boolean utf8) {
        String sign = getSignature(args, appSecret);
        log.info("signature:" + sign);

        StringBuilder sbFormat = new StringBuilder();

        if (!sign.isEmpty()) {
            sbFormat.append("<xml>");
            for (Object o : args.keySet()) {
                String key = o.toString();
                if (!args.get(key).isEmpty()) {
                    log.debug("Key: " + key + ", Value: " + args.get(key));
                    sbFormat.append("<").append(key).append("><![CDATA[").append(args.get(key)).append("]]></").append(key).append(">");
                }
            }
            sbFormat.append("<sign>").append(sign).append("</sign>");
            sbFormat.append("</xml>");
            log.info("Encoded Xml Message, UTF8 {}", utf8);
            if (!utf8) {
                try {
                    return new String(sbFormat.toString().getBytes(), "ISO8859-1");
                } catch (Exception e) {
                    log.error("Encode ISO8859 Failed", e);
                }
            } else {
                try {
                    return new String(sbFormat.toString().getBytes(), StandardCharsets.UTF_8);
                } catch (Exception e) {
                    log.error("Encode UTF8 Failed", e);
                }

            }
        }
        return "";
    }


    private static String getEncoding(String text) {
        String result = null;

        String xml = text.trim();

        if (xml.startsWith("<?xml")) {
            int end = xml.indexOf("?>");
            String sub = xml.substring(0, end);
            StringTokenizer tokens = new StringTokenizer(sub, " =\"\'");

            while (tokens.hasMoreTokens()) {
                String token = tokens.nextToken();

                if ("encoding".equals(token)) {
                    if (tokens.hasMoreTokens()) {
                        result = tokens.nextToken();
                    }

                    break;
                }
            }
        }

        return result;
    }

    private static Document parseText(String text) throws Exception {
        Document result = null;

        SAXReader reader = new SAXReader();
        reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        reader.setFeature("http://xml.org/sax/features/external-general-entities", false);
        reader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        reader.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        String encoding = getEncoding(text);

        InputSource source = new InputSource(new StringReader(text));
        source.setEncoding(encoding);

        result = reader.read(source);

        // if the XML parser doesn't provide a way to retrieve the encoding,
        // specify it manually
        if (result.getXMLEncoding() == null) {
            result.setXMLEncoding(encoding);
        }

        return result;
    }

    public static TreeMap<String, String> parseResultXml(String xml, List<String> list) {
        TreeMap<String, String> ret = new TreeMap<>();
        try {
            //Document doc = DocumentHelper.parseText(xml);
            Document doc = parseText(xml);
            //String s = this.getElement(doc, "return_code");

            //TODO: 校验腾讯返回的sign
            for (String key : list) {
                String value = "";
                try {
                    value = doc.getRootElement().element(key).getText();
                } catch (Exception ignored) {

                }
                ret.put(key, value);
            }
            return ret;
        } catch (Exception e) {
            log.error("Parse Weixin response xml error:" + e.getMessage());
        }
        return ret;
    }
}
