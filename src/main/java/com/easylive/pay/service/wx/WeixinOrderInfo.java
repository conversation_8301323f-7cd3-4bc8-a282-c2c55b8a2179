package com.easylive.pay.service.wx;

import com.easylive.pay.entity.RechargeWeixinApp;
import com.easylive.pay.enums.WeixinPayType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/5
 */
@Data
public class WeixinOrderInfo {
    private WeixinPayType type;
    private String name;
    private Date userRegTime;
    private String detail;
    private String orderId;
    private String amount;
    private String clientIp;

    private String openId;
    private RechargeWeixinApp app;
    private WeixinPayProvider provider;
}
