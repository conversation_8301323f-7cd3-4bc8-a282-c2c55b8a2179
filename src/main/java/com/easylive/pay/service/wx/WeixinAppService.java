package com.easylive.pay.service.wx;


import com.easylive.pay.common.mgs.BizException;
import com.easylive.pay.common.mgs.MgsServiceBuilder;
import com.easylive.pay.config.URLConfig;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.PostConstruct;

@Data
class WeixinGenerateSchemeDTO {
    private String url;
}

@Data
class WeixinJscode2SessionDTO {
    private String sessionKey;
    private String openId;
    private String unionId;
    private int code;
    private String message;
}

interface WeixinAppCenterService {
    @RequestLine("POST /weixin/applet/scheme")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    WeixinGenerateSchemeDTO generateAppletScheme(
            @Param("appId") @RequestParam("appId") String appId,
            @Param("path") @RequestParam(value = "path") String path,
            @Param("query") @RequestParam(value = "query") String query,
            @Param("env") @RequestParam(value = "env") String env,
            @Param("validTime") @RequestParam(value = "validTime") int validTime
    );

    @RequestLine("POST /weixin/applet/jscode2session")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    WeixinJscode2SessionDTO jscode2Session(@Param("appId") @RequestParam("appId") String appId,
                                           @Param("code") @RequestParam("code") String code);
}

@Slf4j
@Service
public class WeixinAppService {


    private WeixinAppCenterService weixinAppCenterService;
    private static final String DEFAULT_ENV = "release";
    private static final int DEFAULT_VALID_TIME = 1440;


    @Autowired
    private URLConfig urlConfig;

    @PostConstruct
    public void init() {
        weixinAppCenterService = MgsServiceBuilder.build(urlConfig.getProviderServiceUrl(), WeixinAppCenterService.class);
    }

    public String generateAppletScheme(String appId, String path, String query, String env, int validTime) {
        try {
            WeixinGenerateSchemeDTO dto = weixinAppCenterService.generateAppletScheme(appId, path, query, env, validTime);
            return dto.getUrl();
        } catch (BizException e) {
            log.error("Error generate scheme, service error: {}",e.toString());
        }  catch (Exception e) {
            log.error("Error generate scheme", e);
        }
        return null;
    }


    public String generateAppletScheme(String appId, String path, String query, String env) {
        return generateAppletScheme(appId, path, query, env, DEFAULT_VALID_TIME);
    }

    public String generateAppletScheme(String appId, String path, String query) {
        return generateAppletScheme(appId, path, query, DEFAULT_ENV, DEFAULT_VALID_TIME);
    }

    public String getOpenIdFromCode(String appId, String code) {
        try {
            WeixinJscode2SessionDTO dto = weixinAppCenterService.jscode2Session(appId, code);
            if( dto.getCode() == 0)
                return dto.getOpenId();
            log.error("Weixin service return error code:{}, {}", dto.getCode(), dto.getMessage());
        } catch (BizException e) {
            log.error("Error get openId from code, service error: {}",e.toString());
        } catch(Exception e) {
            log.error("Error get openId from code", e);
        }
        return null;
    }
}
