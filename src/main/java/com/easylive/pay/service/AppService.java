package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.dao.AppDao;
import com.easylive.pay.entity.AppEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class AppService {

    private static final int DEFAULT_APP_ID = 1;
    private static Logger logger = LoggerFactory.getLogger(AppService.class);
    @Autowired
    private AppDao appDao;
    private Map<Long, AppEntity> appIdMap = new HashMap<>();
    private Map<String, AppEntity> appNameMap = new HashMap<>();

    @PostConstruct
    public void init() {
        load();
    }

    @Transactional
    public void load() {
        Map<Long, AppEntity> idMap = new HashMap<>();
        Map<String, AppEntity> nameMap = new HashMap<>();

        appDao.getAllApp().forEach(appEntity -> {
            idMap.put(appEntity.getId(), appEntity);
            nameMap.put(appEntity.getName(), appEntity);
        });

        this.appIdMap = idMap;
        this.appNameMap = nameMap;
    }


    public AppEntity getAppById(int appId) {
        return appIdMap.get((long) appId);
    }

    public AppEntity getAppIdWithDefaultFallback(int appId) {
        AppEntity entity = getAppById(appId);
        return entity == null ? getDefaultApp() : entity;
    }

    public AppEntity getAppByName(String name) {
        return appNameMap.get(name);
    }

    public int decideAppIdByIdAndName(Integer appId, String name) {
        Integer mgsAppId = appId;
        if (mgsAppId == null) {
            AppEntity app = getAppByName(name);
            if (app != null)
                mgsAppId = ((Long) app.getId()).intValue();
        }
        if (mgsAppId == null) {
            logger.warn("invalid app name found: {}, use default app id", name);
            mgsAppId = Constants.DEFAULT_APP_ID;
        }
        return mgsAppId;
    }

    public AppEntity getAppByNameWithDefaultFallback(String name) {
        AppEntity appEntity = appNameMap.get(name);
        if (appEntity == null) {
            appEntity = getDefaultApp();
        }
        return appEntity;
    }


    public AppEntity getDefaultApp() {
        return appIdMap.get((long)DEFAULT_APP_ID);
    }

    public List<AppEntity> getAllApps() {
        return new ArrayList<>(appIdMap.values());
    }

    public AppEntity getByBundleId(String bundleId) {
        if(StringUtils.isEmpty(bundleId) )
            return null;

        for (AppEntity entity : appIdMap.values()) {
            if (bundleId.equalsIgnoreCase(entity.getBundleId())) {
                return entity;
            }
        }
        return null;
    }

    public int getAppIdByName(String appName) {
        AppEntity appEntity = getAppByName(appName);
        return appEntity != null ? (int)appEntity.getId(): 0;
    }
}
