package com.easylive.pay.service;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.event.EventProducer;
import com.easylive.pay.dao.GuardianDao;
import com.easylive.pay.dao.GuardianOptionRepository;
import com.easylive.pay.dao.GuardianRelationRepository;
import com.easylive.pay.entity.GuardianOption;
import com.easylive.pay.entity.GuardianPayRecord;
import com.easylive.pay.entity.GuardianRelationEntity;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.enums.AssetSystemID;
import com.easylive.pay.model.AssetUpdateResult;
import com.easylive.pay.model.User;
import com.easylive.pay.output.BaseAsset;
import com.easylive.pay.output.BuyGuardianOut;
import com.easylive.pay.pub.event.GuardianBuyEvent;
import com.easylive.pay.pub.event.GuardianEventType;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.utils.UAgentInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class GuardianService {
    private static Logger logger = LoggerFactory.getLogger(GuardianService.class);

    @Autowired
    private GuardianDao guardianDao;

    @Autowired
    private AppGateService appGateService;

    @Autowired
    private ChatService chatService;

    @Autowired
    private UserService userService;

    @Autowired
    private AssetService assetService;

    @Autowired
    private GuardianRelationRepository guardianRelationRepository;

    @Autowired
    private GuardianOptionRepository guardianOptionRepository;

    @Autowired
    private EventProducer eventProducer;

    @Value("${goods.sizhibo.cost.factor}")
    private long goodsSizhiboCostFactor;

    @Value("${goods.sizhibo.riceroll.factor}")
    private long goodsSizhiboRicerollFactor;

    private volatile Map<Long, GuardianOption> optionMap = new HashMap<>();

    @PostConstruct
    public void init() {
        reload();
    }

    private void reload() {
        Map<Long, GuardianOption> tmpMap = new HashMap<>();
        guardianOptionRepository.findAll().forEach(guardianOption -> {
            tmpMap.put(guardianOption.getId(), guardianOption);
        });

        this.optionMap = tmpMap;
    }


    @Transactional(readOnly = true)
    public GuardianRelationEntity getRelation(long fromUid, long toUid) {
        Date now = DateUtils.now();
        List<GuardianRelationEntity> relations = guardianRelationRepository.getRelation(fromUid, toUid, now);
        GuardianRelationEntity relation = null;
        for (GuardianRelationEntity guardianRelationEntity : relations) {
            if (relation != null) {
                if (isHigherThan(guardianRelationEntity.getGuardianId(), relation.getGuardianId())) {
                    relation = guardianRelationEntity;
                }
            } else {
                relation = guardianRelationEntity;
            }
        }

        return relation;
    }

    @Transactional(readOnly = true)
    public Map<Long, GuardianRelationEntity> getRelationOfAnchorAndUsers(long anchorUid, List<Long> uids) {
        Date now = DateUtils.now();
        Map<Long, GuardianRelationEntity> maps = new HashMap<>();
        guardianRelationRepository.getRelationOfAnchorAndUsers(anchorUid, uids, now).forEach(guardianRelationEntity -> {
            Long uid = guardianRelationEntity.getUid();
            GuardianRelationEntity relation = maps.get(uid);
            if (relation == null) {
                maps.put(uid, guardianRelationEntity);
            } else {
                if (isHigherThan(guardianRelationEntity.getGuardianId(), relation.getGuardianId())) {
                    maps.put(uid, guardianRelationEntity);
                }
            }
        });

        uids.forEach(uid -> {
            if (!maps.containsKey(uid)) {
                maps.put(uid, null);
            }
        });

        return maps;
    }

    public GuardianOption getOptionById(long id) {
        return optionMap.get(id);
    }

    private boolean isHigherThan(int guardianId1, int guardianId2) {
        GuardianOption option1 = this.getOptionById(guardianId1);
        GuardianOption option2 = this.getOptionById(guardianId2);

        if (option1 != null) {
            if (option2 != null && option2.getType() > option1.getType()) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 购买守护
     *
     * @param fromUser   购买用户
     * @param name       被赠送主播易播号
     * @param guardianId 守护ID
     */
    @Transactional
    public BuyGuardianOut buyGuardian(User fromUser, String name, int guardianId, String vid, UAgentInfo ua, long month, int appId) {
        logger.info(fromUser.getName() + " buy guardian " + guardianId + " to anchor " + name);

        //获取守护属性
        GuardianOption guardianDb = getOptionById(guardianId);

        if (guardianDb == null) {
            throw new ResponseException(ResponseError.E_GUARDIAN_OPTION_NOT_EXIST, "guardian option is not exists");
        }

        GuardianOption guardian = processGuardian(ua, guardianDb);

        User toUser = userService.ensureExists(name);
        User fromUserOut = userService.ensureExists(fromUser.getName(), appId);

        //扣去购买用户易币
        long ecoinCost = guardian.getEcoin() * month;
        long riceroll = guardian.getRiceroll() * month;

        AssetUpdateResult result = assetService.binaryOperate(AssetSystemID.GUARDIAN, fromUser, ecoinCost, toUser, riceroll);
        UserCoin fromUserCoin = result.getUserCoin(0);
        UserCoin toUserCoin = result.getUserCoin(1);


        //记录购买流水
        GuardianPayRecord gpr = new GuardianPayRecord();
        gpr.set(result.getTid(), guardian, toUser.getId(), fromUser.getId(), month);
        guardianDao.addPayGuardianRecord(gpr);

        //guardian event
        emitGuardianEvent(result.getTid(), guardian, gpr, fromUser, toUser, vid);

        //回调APPGATE
        appGateService.buyGuardianToAppgw(fromUser.getId(), toUser.getId(), guardian, toUserCoin.getAccumriceroll(), fromUserCoin.getCostecoin(), vid, month);

        //回调消息服务器加饭团
        chatService.updateRicerollToChatSched(vid, toUserCoin.getAccumriceroll());

        BaseAsset asset = new BaseAsset(fromUserCoin);

        return new BuyGuardianOut(fromUserOut, asset);
    }

    private GuardianOption processGuardian(UAgentInfo ua, GuardianOption guardianDb) {
        GuardianOption guardian = new GuardianOption();
        guardian.setExp(guardianDb.getExp());
        guardian.setId(guardianDb.getId());
        guardian.setRiceroll(guardianDb.getRiceroll());
        guardian.setTitle(guardianDb.getTitle());
        guardian.setType(guardianDb.getType());
        guardian.setEcoin(guardianDb.getEcoin());

        if (ua != null) {
            switch (ua.getAppName()) {
                case UAgentInfo.SIZHIBO:
                    guardian.setEcoin( guardianDb.getEcoin() * goodsSizhiboCostFactor);
                    guardian.setRiceroll( guardianDb.getRiceroll() * goodsSizhiboRicerollFactor);
                    break;
                default:
            }
        }

        return guardian;
    }

    private void emitGuardianEvent(String tid, GuardianOption guardian, GuardianPayRecord gpr, User fromUser, User toUser, String vid) {
        GuardianBuyEvent event = new GuardianBuyEvent();
        event.setEcoin( gpr.getEcoin());
        event.setRiceroll( gpr.getRiceroll());
        GuardianBuyEvent.GuardianUser from = new GuardianBuyEvent.GuardianUser();
        from.setName( fromUser.getName() );
        from.setUid( fromUser.getId());
        from.setNickname( fromUser.getNickname());
        GuardianBuyEvent.GuardianUser to = new GuardianBuyEvent.GuardianUser();
        to.setUid(toUser.getId());
        to.setName(toUser.getName());
        to.setNickname(toUser.getNickname());
        event.setFrom(from);
        event.setTo(to);
        event.setType(guardian.getType());
        event.setOrderId(tid);
        if (null != vid) {
            event.setVid(vid);
        }
        event.setTime(gpr.getTime());
        eventProducer.emitAsync(GuardianEventType.TOPIC_NAME, null,
                GuardianEventType.BUY.getValue(), event);
    }
}
