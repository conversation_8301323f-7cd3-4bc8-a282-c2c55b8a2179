package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.entity.Goods;
import com.easylive.pay.entity.GuardianOption;
import com.easylive.pay.entity.LuckPool;
import com.easylive.pay.enums.AssetType;
import com.easylive.pay.model.RetNode;
import com.easylive.pay.model.User;
import com.easylive.pay.utils.HttpUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2016/2/19
 */
@Service
public class AppGateService {

    private static Logger logger = LoggerFactory.getLogger(AppGateService.class);
    @Autowired
    private URLConfig urlConfig;

    public void pushGoodsToAppGate(long fromuid, long toUid, Goods goods, long number, long riceroll, long costecoin, String vid) {
        if (goods.getCostType() != AssetType.ECOIN.getValue() && goods.getCostType() != AssetType.BARLEY.getValue()) {
            return;
        }
        Map<String, Object> args = new HashMap<>();
        args.put("from_uid", fromuid);
        args.put("to_uid", toUid);
        args.put("goodsid", goods.getId());
        args.put("number", number);
        args.put("riceroll", goods.getRiceroll());
        args.put("costvalue", goods.getCost());
        args.put("costtype", goods.getCostType());
        args.put("exp", goods.getExp());
        args.put("accumriceroll", riceroll);
        args.put("costecoin", costecoin);
        args.put("vid", vid);
        args.put("goodsname", goods.getName());

        pushMessageToAppGateAsync("buygift", args);
    }

    public void pushLuckMsgToAppGate(User fromUser, User toUser, long multiple, String vid, long ecoin, LuckPool luckPool) {
        Map<String, Object> args = new HashMap<>();

        args.put("name", fromUser.getName());
        args.put("multiple", multiple);
        args.put("from", fromUser.getName());
        args.put("to", toUser.getName());
        args.put("vid", vid);
        args.put("ecoin", ecoin);
        args.put("explosion_status", luckPool.getExplosionStatus());
        args.put("total_ecoin", luckPool.getTotalEcoin());
        args.put("next_explosion_pool", luckPool.getNextExplosionPool());
        pushMessageToAppGateAsync("publishluckmsg", args);
    }


    /*
    public long getLuckyGiftRate(long goodsValue, long need, long totalEcoin, long totalEcoinOld) {
        Map<String, Object> args = new HashMap<>();
        args.put("current_ecoin", need);
        args.put("total_ecoin_new", totalEcoin);
        args.put("total_ecoin_old", totalEcoinOld);
        args.put("good_value", goodsValue);
        Map<String, Object> retInfo = pushMessageToAppGate("luckytriggers", args);
        Double ret = (Double) retInfo.get("rate");

        return (long) ret.doubleValue();
    }
     */

    public void buyGuardianToAppgw(long fromUid, long toUid, GuardianOption guardian, long riceroll, long totalCostEcoin, String vid, long month) {
        Map<String, Object> args = new HashMap<>();
        args.put("from_uid", fromUid);
        args.put("to_uid", toUid);
        args.put("exp", guardian.getExp());
        args.put("accumriceroll", riceroll);
        args.put("costecoin", guardian.getEcoin());
        args.put("guardian_id", guardian.getId());
        args.put("riceroll", guardian.getRiceroll());
        args.put("totalcostecoin", totalCostEcoin);
        args.put("vid", vid);
        args.put("month", month);

        pushMessageToAppGate("buyguardian", args);
    }

    @Async
    public void redPackUpdateRank(long fromUid, long toUid, long ecoin, long riceroll, int number) {
        Map<String, Object> args = new HashMap<>();
        args.put("from_uid", fromUid);
        args.put("to_uid", toUid);
        args.put("ecoin", ecoin);
        args.put("riceroll", riceroll);
        args.put("number", number);

        pushMessageToAppGate("updaterank", args);
    }

    public User getUserInfoBySession(String sessionid) {
        Map<String, Object> args = new HashMap<>();
        args.put("sessionid", sessionid);

        User user = new User();
        Map<String, Object> retInfo = pushMessageToAppGate("userinfobysession", args);
        user.setId(((Double) retInfo.get("id")).longValue());
        user.setName((String) retInfo.get("name"));
        user.setNickname((String) retInfo.get("nickname"));
        user.setSessionId(sessionid);
        user.setLogoUrl((String) retInfo.get("logourl"));

        Object cid = retInfo.get("cid");
        if (cid != null) {
            user.setCid((String) cid);
        }

        Object appSourceId = retInfo.get("app_source_id");
        if (appSourceId != null) {
            user.setAppSourceId(((Double) appSourceId).longValue());
        }

        return user;
    }


    public User getByName(String name) {
        Map<String, Object> args = new HashMap<>();
        args.put("name", name);

        Map<String, Object> retInfo = pushMessageToAppGate("userinfobyname", args);

        User user = new User();
        user.setId(((Double) retInfo.get("id")).longValue());
        user.setName(name);
        user.setNickname((String) retInfo.get("nickname"));
        user.setLogoUrl((String) retInfo.get("logourl"));
        user.setVip(((Double) retInfo.get("vip")).longValue());
        user.setLevel(((Double) retInfo.get("level")).longValue());
        user.setAnchorLevel(((Double) retInfo.get("anchor_level")).longValue());
        user.setVipLevel(((Double) retInfo.get("vip_level")).longValue());
        Object cid = retInfo.get("cid");
        if (cid != null) {
            user.setCid((String) cid);
        }

        Object appSourceId = retInfo.get("app_source_id");
        if (appSourceId != null) {
            user.setAppSourceId(((Double) appSourceId).longValue());
        }
        Integer nobleLevel = null;
        try {
            nobleLevel = ((Double) retInfo.get("noble_level")).intValue();
        } catch (Exception e) {
            nobleLevel = 0;
        }
        user.setNobleLevel(nobleLevel);
        return user;
    }

    public User getById(long uid) {
        Map<String, Object> args = new HashMap<>();
        args.put("uid", uid);

        Map<String, Object> retInfo = pushMessageToAppGate("userinfobyid", args);

        User user = new User();
        user.setId(((Double) retInfo.get("id")).longValue());
        user.setName((String) retInfo.get("name"));
        user.setNickname((String) retInfo.get("nickname"));
        user.setLogoUrl((String) retInfo.get("logourl"));

        Object cid = retInfo.get("cid");
        if (cid != null) {
            user.setCid((String) cid);
        }

        Object appSourceId = retInfo.get("app_source_id");
        if (appSourceId != null) {
            user.setAppSourceId(((Double) appSourceId).longValue());
        }

        return user;
    }

    @SuppressWarnings("unchecked")
    public Map<Long, User> getListByUids(List<Long> uids) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < uids.size(); i++) {
            sb.append(uids.get(i));
            if (i != uids.size() - 1) {
                sb.append(",");
            }
        }
        Map<String, Object> args = new HashMap<>();
        args.put("uidlist", sb.toString());

        Map<String, Object> retInfo = pushMessageToAppGate("userinfobyids", args);
        List<Object> userList = (List<Object>) retInfo.get("users");
        //List<User> userList = (List<User>)retInfo.get("users");
        Map<Long, User> mapUser = new HashMap<>();

        for (int i = 0; i < userList.size(); i++) {
            Map<String, Object> mapInfo = (Map<String, Object>) userList.get(i);
            User user = new User();
            user.setId(((Double) mapInfo.get("id")).longValue());
            user.setName((String) mapInfo.get("name"));
            user.setNickname((String) mapInfo.get("nickname"));
            user.setLogoUrl((String) mapInfo.get("logourl"));
            mapUser.put(user.getId(), user);
        }
        return mapUser;
    }

    public void updateUserBean(int type, String name, long bean, long gameId) {

        Map<String, Object> args = new HashMap<>();
        args.put("type", type);
        args.put("unames", name);
        args.put("cost", bean);
        args.put("gameid", gameId);
        logger.info("notify appgw params:" + args);
        pushMessageToAppGate("recordbeancost", args);

    }


    /**
     * 支付成功的appgate回调 2018-08-01, 异步执行
     *
     * @param uid
     * @param ecoin
     */
    @Async
    public void rechargeCallBack(long uid, long ecoin) {
        Map<String, Object> args = new HashMap<>();
        args.put("uid", uid);
        args.put("ecoin", ecoin);
        logger.info("notify appgw params:" + args);
        pushMessageToAppGate("paycallback", args);
    }

    /**
     * 检查用户的背包里面是否有此道具礼物、数量是否足够
     *
     * @param uid
     * @param goodsId
     * @param number
     * @return
     */
    public boolean packageToolExist(long uid, long goodsId, long number, long toUid) {
        Map<String, Object> args = new HashMap<>();
        args.put("uid", uid);
        args.put("goodsid", goodsId);
        args.put("number", number);
        args.put("touid", toUid);
        logger.info("notify appgw params:" + args);
        Map<String, Object> retInfo = pushMessageToAppGate("packagetoolexist", args);

        boolean exist = (boolean) retInfo.get("exist");

        return exist;
    }

    private Map<String, Object> pushMessageToAppGate(User user, String cmd, Map<String, Object> args) {
        return pushMessageToAppGate(getPrefixByUser(user), cmd, args);
    }

    private Map<String, Object> pushMessageToAppGate(String cmd, Map<String, Object> args) {
        return pushMessageToAppGate(urlConfig.getAppGateService(), cmd, args);
    }

    private Map<String, Object> pushMessageToAppGate(String urlPrefix, String cmd, Map<String, Object> args) {
        String url = urlPrefix + "/" + cmd;
        logger.info("calling appgate for command {} ", cmd);

        String response = HttpUtils.doGet(url, args);
        GsonBuilder builder = new GsonBuilder().setDateFormat("yyyy-MM-dd");
        Gson gson = builder.create();
        RetNode retNode = gson.fromJson(response, RetNode.class);

        if (retNode.getRetval().compareTo("ok") != 0) {
            throw new ResponseException(ResponseError.E_PARAM);
        }

        return retNode.getRetinfo();

    }


    private void pushMessageToAppGateAsync(User user, String cmd, Map<String, Object> args) {
        pushMessageToAppGateAsync(getPrefixByUser(user), cmd, args);
    }

    private void pushMessageToAppGateAsync(String cmd, Map<String, Object> args) {
        pushMessageToAppGateAsync(urlConfig.getAppGateService(), cmd, args);
    }

    private void pushMessageToAppGateAsync(String prefix, String cmd, Map<String, Object> args) {
        String url = prefix + "/" + cmd;
        logger.info("calling appgate for command {} ", cmd);
        HttpUtils.doGet(url, args);
    }


    private String getPrefixByUser(User user) {
        String prefix = urlConfig.getAppGateService();
        if (user.getAppSourceId() == Constants.MAGIC_LIVE_APP_SOURCE_ID) {
            prefix = urlConfig.getNewAppGateService();
        }
        return prefix;
    }
}
