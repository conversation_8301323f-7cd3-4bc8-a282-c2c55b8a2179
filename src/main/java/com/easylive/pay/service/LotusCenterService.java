package com.easylive.pay.service;

import com.easylive.pay.common.mgs.MgsServiceBuilder;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.model.LotusLuckMsgInput;
import feign.Headers;
import feign.Param;
import feign.QueryMap;
import feign.RequestLine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;


interface MgsLotusService {

    @RequestLine("POST /service/pay/luck/publish/msg")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    void publishLuckMsg(@QueryMap LotusLuckMsgInput luckMsgInput);

    @RequestLine("POST /service/video/record/paid/detail")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    void recordPaidDetail(@Param("uid") long uid, @Param("vid") String vid, @Param("ecoin") long ecoin);

}

@Service
public class LotusCenterService {

    private static Logger logger = LoggerFactory.getLogger(UserService.class);

    private MgsLotusService mgsLotusService;

    @Autowired
    private URLConfig urlConfig;


    @PostConstruct
    public void init() {
        mgsLotusService = MgsServiceBuilder.build(urlConfig.getLoutsServiceUrl(), MgsLotusService.class);
    }

    public void publishLuckGiftMsg(LotusLuckMsgInput input) {
        try {
            mgsLotusService.publishLuckMsg(input);
        } catch (Exception e) {
            logger.error("Publish luck message error, message:{}", e.getMessage());
        }
    }

    public void recordPaidDetail(long uid, String vid, long ecoin) {
        try {
            mgsLotusService.recordPaidDetail(uid, vid, ecoin);
        } catch (Exception e) {
            logger.error("Lotus record paid detail error, vid {}. uid {}, message: {}", uid, vid, e.getMessage());
        }
    }
}
