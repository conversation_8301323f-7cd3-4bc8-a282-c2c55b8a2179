package com.easylive.pay.service.bytedance;

import com.easylive.pay.common.Constants;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.model.ByteDanceOrderInfo;
import com.easylive.pay.service.alipay.AliPayService;
import com.easylive.pay.service.wx.WeixinService;
import com.easylive.pay.utils.UAgentInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/10
 */
@Service
public class ByteDancePayService {

    @Autowired
    private AliPayService aliPayService;

    @Autowired
    private WeixinService wxPayService;

    private static final String BYTE_DANCE_APP_ID = "************";
    private static final String BYTE_DANCE_MER_ID = "1900071410";
    private static final String BYTE_DANCE_SECRET = "yygxsj9apnwf7c03m4wdkuxh4zzwjexcbq3gq2dz";

    public ByteDanceOrderInfo createRechargeOrder(long uid, long amount, String clientIp) {
        Map<String, String> wxOrderInfo = wxPayService.createH5Order(uid, amount, clientIp, Constants.DEFAULT_APP_ID);
        String aliPayUrl = aliPayService.createAppOrderNew(UAgentInfo.YIZHIBO, amount, uid, clientIp, MobilePlatform.MPF_ANDROID);
        ByteDanceOrderInfo orderInfo = new ByteDanceOrderInfo();
        orderInfo.setAppId(BYTE_DANCE_APP_ID);
        orderInfo.setMerchantId(BYTE_DANCE_MER_ID);
        orderInfo.setUid(BYTE_DANCE_APP_ID);

        String cur = "" + System.currentTimeMillis() / 1000;
        orderInfo.setTimestamp(cur);
        orderInfo.setOutOrderNo(wxOrderInfo.get("orderid"));
        orderInfo.setTotalAmount("" + amount);

        orderInfo.setAlipayUrl(aliPayUrl);
        orderInfo.setWxUrl(wxOrderInfo.get("mweb_url"));
        orderInfo.setRiskInfo("{\"ip\":\"" + clientIp + "\"}");


        BigDecimal totalFeeDecimal = new BigDecimal(amount);
        String price = String.valueOf(totalFeeDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        String subject = "充值：" + price + "元";
        orderInfo.setSubject(subject);
        orderInfo.setBody(subject);
        orderInfo.setTradeTime(cur);

        String sign = createOrderSign(orderInfo);
        orderInfo.setSign(sign);
        return orderInfo;
    }

    /**
     * 订单签名
     * @param info
     * @return
     */
    private String createOrderSign(ByteDanceOrderInfo info) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("alipay_url=").append(info.getAlipayUrl());
        buffer.append("&app_id=").append(info.getAppId());
        buffer.append("&body=").append(info.getBody());
        buffer.append("&currency=").append(info.getCurrency());
        buffer.append("&merchant_id=").append(info.getMerchantId());
        buffer.append("&notify_url=").append(info.getNotifyUrl());
        buffer.append("&out_order_no=").append(info.getOutOrderNo());
        buffer.append("&payment_type=").append(info.getPaymentType());
        buffer.append("&product_code=").append(info.getProductCode());
        buffer.append("&sign_type=").append(info.getSignType());
        buffer.append("&subject=").append(info.getSubject());
        buffer.append("&timestamp=").append(info.getTimestamp());
        buffer.append("&total_amount=").append(info.getTotalAmount());
        buffer.append("&trade_time=").append(info.getTradeTime());
        buffer.append("&trade_type=").append(info.getTradeType());
        buffer.append("&uid=").append(info.getUid());
        buffer.append("&valid_time=").append(info.getValidTime());
        buffer.append("&version=").append(info.getVersion());
        buffer.append("&wx_type=").append(info.getWxType());
        buffer.append("&wx_url=").append(info.getWxUrl());

        return DigestUtils.md5DigestAsHex((buffer.toString() + BYTE_DANCE_SECRET).getBytes());
    }

}
