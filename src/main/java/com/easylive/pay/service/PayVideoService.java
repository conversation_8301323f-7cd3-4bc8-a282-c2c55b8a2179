package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.event.EventProducer;
import com.easylive.pay.config.CashConfig;
import com.easylive.pay.dao.WatchPayDao;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.entity.WatchPayRecord;
import com.easylive.pay.enums.AssetSystemID;
import com.easylive.pay.enums.VideoLivingStatus;
import com.easylive.pay.model.AssetUpdateResult;
import com.easylive.pay.model.User;
import com.easylive.pay.output.Asset;
import com.easylive.pay.output.BaseAsset;
import com.easylive.pay.pub.event.BuyEventType;
import com.easylive.pay.pub.event.GiftEvent;
import com.easylive.pay.pub.event.GiftEventType;
import com.easylive.pay.pub.event.PayLiveEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class PayVideoService {
    private static Logger logger = LoggerFactory.getLogger(PayVideoService.class);

    @Autowired
    private AssetService assetService;

    @Autowired
    private ChatService chatService;

    @Autowired
    private CashConfig cashConfig;

    @Autowired
    private WatchPayDao watchPayDao;

    @Autowired
    private UserService userService;

    @Autowired
    private LotusCenterService lotusCenterService;

    @Autowired
    private EventProducer eventProducer;


    /**
     * 正常的视频付费，不需要价格变动
     */
    @Transactional
    public BaseAsset pay(long uid, long ecoin, long toUid, String vid, long living) {
        return pay(uid, ecoin, toUid, vid, living, 0);
    }

    @Transactional
    public BaseAsset pay(long uid, long ecoin, long toUid, String vid, long living, long ecoinFactor) {
        logger.info("{} pay {} ecoin to {} for video {} is living? {}", uid, ecoin, toUid, vid, living);

        //ecoin validity will be check when minusEcoin
        long riceroll = 0L;
        if (living == VideoLivingStatus.VIDEO_STATUS_LIVING.getValue()) {
            riceroll = ecoin * cashConfig.getPayLivingOfEcoinToRiceroll();
        } else if (living == VideoLivingStatus.VIDEO_STATUS_RECORD.getValue()) {
            //如果是丝直播、丝足直播用户 回放不加饭团
            User fromUser = userService.getById(uid);
            if (fromUser != null && (fromUser.getAppSourceId() == Constants.SIZHIBO_LIVE_APP_SOURCE_ID || fromUser.getCid().indexOf("sizhibo") > 0)) {
                logger.debug("Sizhibo user : {} pay record video, no riceroll add to anchor uid: {}", fromUser.getName(), toUid);
                riceroll = 0;
            } else if (fromUser != null && (fromUser.getAppSourceId() == Constants.SIZU_LIVE_APP_SOURCE_ID || fromUser.getCid().startsWith("sizulive"))) {
                logger.debug("Sizulive user : {} pay record video, no riceroll add to anchor uid: {}", fromUser.getName(), toUid);
                riceroll = 0;
            } else if (fromUser != null && (fromUser.getAppSourceId() == Constants.ZUJI_LIVE_APP_SOURCE_ID || fromUser.getCid().startsWith("zujilive"))) {
                logger.debug("Sizulive user : {} pay record video, no riceroll add to anchor uid: {}", fromUser.getName(), toUid);
                riceroll = 0;
            } else if (fromUser != null && (fromUser.getAppSourceId() == Constants.DRAGON_LIVE_APP_SOURCE_ID || fromUser.getCid().startsWith("dragonlive"))) {
                logger.debug("Dragon live user : {} pay record video, no riceroll add to anchor uid: {}", fromUser.getName(), toUid);
                riceroll = 0;
            } else {
                riceroll = ecoin * cashConfig.getPayRecordOfEcoinToRiceroll();
            }
        } else {
            throw new ResponseException(ResponseError.E_SERVER, "Invalid video living status.");
        }

        //如果需要不同的扣费比例（比如丝直播乘以2） 在这里操作
        if (ecoinFactor != 0) {
            ecoin *= ecoinFactor;
        }

        AssetUpdateResult result = assetService.binaryOperate(AssetSystemID.PAY_VIDEO, uid, ecoin, toUid, riceroll);
        UserCoin uc = result.getUserCoin(0);
        UserCoin toUc = result.getUserCoin(1);

        WatchPayRecord payRecord = watchPayDao.getByUid(uid, vid);

        if (payRecord == null) {
            payRecord = new WatchPayRecord();
            payRecord.setEcoin(ecoin);
            payRecord.setVid(vid);
            payRecord.setTid(result.getTid());
            payRecord.setUid(uid);
            payRecord.setLiving(living);
            payRecord.setCreateTime(new Date());
        } else {
            payRecord.setEcoin(ecoin);
        }

        watchPayDao.save(payRecord);
        chatService.updateRicerollToChatSched(vid, toUc.getAccumriceroll());

        //记录支付详情
        lotusCenterService.recordPaidDetail(uid, vid, ecoin);

        logger.info("[advstat][paylive][success] total {} uid {} to_uid {} count {} ecoin {}", ecoin, uid, toUid, 1, ecoin);

        //生成付费直播事件
        PayLiveEvent payLiveEvent = new PayLiveEvent();
        payLiveEvent.setUid(uid);
        payLiveEvent.setToUid(toUid);
        payLiveEvent.setEcoin(ecoin);
        payLiveEvent.setRiceroll(riceroll);
        payLiveEvent.setLiving(living == VideoLivingStatus.VIDEO_STATUS_LIVING.getValue());
        payLiveEvent.setTime(payRecord.getCreateTime());
        eventProducer.emitAsync(BuyEventType.TOPIC_NAME, String.valueOf(payLiveEvent.getToUid()), BuyEventType.PAY_LIVE.getValue(), payLiveEvent);

        return new BaseAsset(uc);
    }


}
