package com.easylive.pay.service;

import com.easylive.pay.common.mgs.MgsServiceBuilder;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.model.bizcore.UserWalletVO;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.PostConstruct;

interface MgsBizcoreCenterService {
    /**
     * 获取用户钱包
     */
    @RequestLine("GET /front/wallet/get?uid={uid}")
    UserWalletVO getUserWallet(@Param("uid") @RequestParam("uid") Long uid);

    /**
     * 修改用户钱包rmb余额，加钱传正数，扣钱传负数
     */
    @RequestLine("POST /front/wallet/modify/rmb")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    void modifyWalletRmb(@Param("uid") @RequestParam("uid") long uid,
                                                 @Param("rmb") @RequestParam("rmb") long rmb);


    /**
     * 订阅购买vip
     */
    @RequestLine("POST /service/vip/v2/buy")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    void buyVip(@Param("uid") @RequestParam("uid") long uid,
                         @Param("productid") @RequestParam("productid") String productid,
                         @Param("autoRenew") @RequestParam("autoRenew") boolean autoRenew);

    /**
     * 取消订阅通知
     */
    @RequestLine("POST /service/vip/v2/user/unsubscribe")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    void cancelSubscribe(@Param("uid") @RequestParam("uid") long uid,
                @Param("productid") @RequestParam("productid") String productid);


}


@Service
public class BizcoreCenterService {
    private static Logger logger = LoggerFactory.getLogger(BizcoreCenterService.class);

    private MgsBizcoreCenterService bizcoreCenterService;

    @Autowired
    private URLConfig urlConfig;


    @PostConstruct
    public void init() {
        bizcoreCenterService = MgsServiceBuilder.build(urlConfig.getBizcoreServiceUrl(), MgsBizcoreCenterService.class);
    }

    public UserWalletVO getUserWallet(long uid) {
        try {
            return bizcoreCenterService.getUserWallet(uid);
        } catch (Exception e) {
            logger.error("Get user {} wallet from biz core center error, message {}", uid, e.getMessage());

            return new UserWalletVO();
        }
    }

    public void modifyWalletRmb(long uid, long rmb) {
        try {
            bizcoreCenterService.modifyWalletRmb(uid, rmb);
            logger.info("Modify user {} wallet rmb {} success", uid, rmb);
        } catch (Exception e) {
            logger.error("Modify user {} wallet rmb {} error {}", uid, rmb, e.getMessage());

            //throw new ResponseException(ResponseError.E_USER_WALLET_MODIFY_ERROR);
        }
    }

    public void appleBuyVIP(long uid, String productId, boolean autoRenew) {
        try {
            bizcoreCenterService.buyVip(uid, productId, autoRenew);
        } catch (Exception e) {
            logger.error("User {} buy vip productId {} autoRenew:{} error {}", uid, productId, autoRenew, e.getMessage());
        }
    }


    public void cancelSubscribe(long uid, String productId) {
        try {
            bizcoreCenterService.cancelSubscribe(uid, productId);
        } catch (Exception e) {
            logger.error("User cancel {} buy vip productId {} error {}", uid, productId, e.getMessage());
        }
    }
}
