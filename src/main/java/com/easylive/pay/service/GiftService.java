package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.DataSource;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.event.EventProducer;
import com.easylive.pay.component.OssComponent;
import com.easylive.pay.dao.*;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.*;
import com.easylive.pay.model.AssetUpdateResult;
import com.easylive.pay.model.NobleInfo;
import com.easylive.pay.model.User;
import com.easylive.pay.output.Asset;
import com.easylive.pay.output.PersonalGiftRecordVO;
import com.easylive.pay.pub.event.GiftEvent;
import com.easylive.pay.pub.event.GiftEventType;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.utils.JsonUtils;
import com.easylive.pay.utils.UAgentInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class GiftService {

    private static Logger logger = LoggerFactory.getLogger(GiftService.class);

    private static final Random random = new Random();

    @Autowired
    private GiftDao giftDao;

    @Autowired
    private GuardianDao guardianDao;

    @Autowired
    private AppGateService appGateService;

    @Autowired
    private RedpackService redpackService;

    @Autowired
    private ChatService chatService;

    @Autowired
    private UserService userService;

    @Autowired
    private AssetService assetService;

    @Autowired
    private Executor taskExecutor;

    @Autowired
    private EventProducer eventProducer;

    @Autowired
    private GoodsGuardianRepository goodsGuardianRepository;

    @Autowired
    private GuardianLevelRepository guardianLevelRepository;

    @Autowired
    private PersonGiftRecordRepository personGiftRecordRepository;

    @Autowired
    private PKGiftRepository pkGiftRepository;

    @Autowired
    private OssComponent ossComponent;

    @Autowired
    private GoodsRepository goodsRepository;

    @Autowired
    private PackageToolService packageToolService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ImService imService;

    @Autowired
    private LuckGiftService luckGiftService;

    @Autowired
    private IeasyService ieasyService;

    @Value("${goods.sizhibo.cost.factor}")
    private long goodsSizhiboCostFactor;

    @Value("${goods.sizhibo.riceroll.factor}")
    private long goodsSizhiboRicerollFactor;

    @Value("${cache.prefix}")
    private String cachePrefix;

    /**
     * 直播间倒计时免费礼物信息cacheKey
     */
    private static final String COUNTDOWN_GIFT_CACHE_KEY = "mgs:lotus:countdown_gift_info";
    /**
     * 直播中房间列表
     */
    public static final String KEY_LIVING_HASH = "mgs:video:living:hash";

    @Autowired
    private PersonGiftRecordMockRepository personGiftRecordMockRepository;

    @Transactional(readOnly = true)
    public Goods getGoodsById(long id) {
        Goods goods = giftDao.getGoodsById(id);
        if (goods == null) {
            throw new ResponseException(ResponseError.E_GOODS_NOT_EXIST, "goods is not exists");
        }

        return goods;
    }

    @Transactional
    @DataSource(name = DataSource.slave)
    public List<GoodsRecord> getGoodsRecordByDate(String strDate, int start, int limit) {
        DateTime dt = DateUtils.getDefaultFormatter().parseDateTime(strDate);
        Date dtStart = dt.toDate();
        Date dtEnd = dt.plusDays(1).toDate();
        return giftDao.getGoodsRecordByDate(start, limit, dtStart, dtEnd);
    }

    @Transactional
    public List<GoodsRecord> getGoodsRecordByUser(long uid, String startDate, String endDate, int type) {
        Date dtStart = DateUtils.parse(startDate);
        Date dtEnd = DateUtils.parse(endDate);
        return giftDao.getGoodsRecordByUser(uid, dtStart, dtEnd, type);
    }

    @Transactional
    public List<Goods> getGoodsList() {
        return giftDao.getGoodsList();
    }

    @Transactional(readOnly = true)
    public UserGiftStat getUserGiftStat(long uid) {
        UserGiftStat stat = giftDao.getUserGiftStat(uid);
        if (stat == null) {
            stat = new UserGiftStat();
            stat.setUid(uid);
        }
        return stat;
    }

    @Transactional
    public Asset buy(User fromUser, long goodsId, String vid, String name, int number, Long pkId, UAgentInfo ua) {
        return buy(fromUser, goodsId, vid, name, number, pkId, null, ua, null, null);
    }

    @Transactional
    public Asset buy(User fromUser, long goodsId, String vid, String name, int number, Long pkId, Integer type, UAgentInfo ua, String sender, String receiver) {
        /**
         * 1. 对输入参数进行验证
         * 2. 获得礼物，并处理丝直播逻辑
         * 3. 红包类型不允许连发
         * 4. 计算礼物价格
         * 5. 资产操作记录
         * 6. 对礼物和背包礼物进行计数
         * 7. 购买礼物成功之后处理相关逻辑
         * 8. 删除个人中心礼物缓存
         */
        logger.info(" {} buy goods {} x {} in vid {}, pkId={}, type={}", fromUser.getName(), goodsId, number, vid, pkId, type);
        //1. 对输入参数进行验证
        if (number <= 0) {
            logger.error("gift number less than zero. {}", number);
            throw new ResponseException(ResponseError.E_PARAM);
        }

        //2. 获得礼物，并处理丝直播逻辑
        User toUser = userService.ensureExists(name);
        Goods goodsDb = getGoodsById(goodsId);
        Goods goods = processAppGoods(ua, goodsDb);
        if (type != null && type == GiftPositionType.PRIVATE_LETTER.getValue()) {
            goods.setPicture(goodsDb.getPicture());
        }

        //检查能不能送出免费礼物
        if (!checkFreeGift(goods, fromUser.getId(), vid, number)) {
            throw new ResponseException(ResponseError.E_FREE_GOODS_NOT_ALLOW, "赠送失败");
        }

        //3. 红包类型不允许连发
        if (goods.getAnimationType() == 4) {
            number = 1;
        }

        //4. 计算礼物价格
        final long needs = goods.getCost() * number;
        long riceroll = goods.getRiceroll() * number;

        //5. 资产操作记录
        AssetUpdateResult updateResult;

        if (goods.getCostType() == AssetType.ECOIN.getValue()) {
            if (goods.getType() == GoodsType.GT_PACKAGE_GIFT.getValue()) {
                //道具礼物特殊处理，先判断用户是否拥有 只给主播加饭团 不扣用户易币
                //判断的时候 同时也会去减去用户道具数量
                boolean exist = appGateService.packageToolExist(fromUser.getId(), goodsId, number, toUser.getId());
                if (!exist) {
                    throw new ResponseException(ResponseError.E_INVALID_USER_PACKAGE_GIFT, "User not own this package gift.");
                }

                updateResult = assetService.binaryOperate(AssetSystemID.PACKAGE_GIFT, fromUser, needs, toUser, riceroll);

            } else {
                //红包类型不给主播加饭团
                if (goods.getAnimationType() == 4) {
                    riceroll = 0;
                }

                updateResult = assetService.binaryOperate(AssetSystemID.GIFT, fromUser, needs, toUser, riceroll);
            }

        } else if (goods.getCostType() == AssetType.BARLEY.getValue()) {
            updateResult = assetService.binaryOperate(AssetSystemID.BARLEY_GIFT, fromUser, needs, toUser, riceroll);
        } else {
            logger.error("invalid gift type {}", goods.getCostType());
            throw new ResponseException(ResponseError.E_INVALID_GIFT_TYPE);
        }

        UserCoin toUserCoin;
        UserCoin fromUserCoin;

        fromUserCoin = updateResult.getUserCoin(0);
        toUserCoin = updateResult.getUserCoin(1);

        //6. 对礼物和背包礼物进行计数
        if (goods.getType() == GoodsType.GT_GIFT.getValue()
                || goods.getType() == GoodsType.GT_PACKAGE_GIFT.getValue()) {
            updateGiftCount(fromUserCoin.getUid(), toUserCoin.getUid(), number);
        }

        //7. 购买礼物成功之后处理相关逻辑
        if (type != null) {
            this.afterBuy(updateResult.getTid(), goods, fromUser, toUser, number, vid, fromUserCoin, toUserCoin, pkId, type, sender, receiver);
        } else {
            this.afterBuy(updateResult.getTid(), goods, fromUser, toUser, number, vid, fromUserCoin, toUserCoin, pkId);
        }

        //8. 删除个人中心礼物缓存
        if (type != null) {
            this.delGoodsPositionRecordCache(toUser.getId());
        }

        return new Asset(fromUserCoin);
    }
    
    /**
     * 检查用户是否有权限送出
     */
    private boolean checkFreeGift(Goods goods, long uid, String vid, int number) {
        if (goods.getType() != GoodsType.GT_FREE_GIFT.getValue()) {
            return true;
        }

        if (number > 1) {
            return false;
        }

        HashOperations<String, String, String> valueOperations = this.redisTemplate.opsForHash();
        String countdownGiftInfo = valueOperations.get(COUNTDOWN_GIFT_CACHE_KEY, Long.valueOf(uid).toString());

        logger.debug("Send free goods cache value:{}", countdownGiftInfo);
        if (countdownGiftInfo != null) {
            CountdownGift countdownGift = JsonUtils.fromString(countdownGiftInfo, CountdownGift.class);
            return countdownGift.getVid().equals(vid) && System.currentTimeMillis() - countdownGift.getJoinTime().getTime() > 300 * 1000;
        }

        return false;
    }

    private void updateGiftCount(long fromUid, long toUid, long number) {
        UserGiftStat from = giftDao.getUserGiftStat(fromUid);
        if (from == null) {
            from = new UserGiftStat();
            from.setUid(fromUid);
        }

        UserGiftStat to = giftDao.getUserGiftStat(toUid);
        if (to == null) {
            to = new UserGiftStat();
            to.setUid(toUid);
        }

        to.setRecvGiftCount(to.getRecvGiftCount() + number);
        from.setSendGiftCount(from.getSendGiftCount() + number);
        giftDao.saveOrUpdate(from);
        giftDao.saveOrUpdate(to);
    }


    private void notifyGiftMessage(Goods goods, User fromUser, User toUser, long number, String vid, UserCoin fromUserCoin, UserCoin toUserCoin) {
        long start = System.currentTimeMillis();
        try {
            long uid = fromUser.getId();

            // 礼物发送成功，给appgate发送请求
            //appGateService.pushGoodsToAppGate(uid, toUser.getId(), goods, number, toUserCoin.getAccumriceroll(), fromUserCoin.getCostecoin(), vid);

            // 滤镜不需要通知聊天服务器
//            if (goods.getAssetType() == GoodsType.GT_FILTER.getValue())
//                return;

            if (goods.getAnimationType() == 4) {
                // 动画类型为4表示红包，生成红包
                redpackService.createGiftRedpack(fromUser, vid, (int) goods.getCost(), toUser.getId());
            } else {

//                if (goods.getCostType() == (long) AssetType.ECOIN.getValue()) {
//                    redpackService.addSystemRedpackScore(fromUser, vid, goods.getCost() * number);
//                }
            }
        } catch (Exception e) {
            logger.error("Buy gift error", e);
        }

        long end = System.currentTimeMillis();
        logger.info("notifyLuckGiftMessage cost: {} ms", end - start);
    }

    @Transactional
    public void afterBuy(String tid, Goods goods, User from, User to, int number, String vid, UserCoin fromUc, UserCoin toUc) {
        this.afterBuy(tid, goods, from, to, number, vid, fromUc, toUc, null);
    }

    @Transactional
    public void afterBuy(String tid, Goods goods, User from, User to, int number, String vid, UserCoin fromUc, UserCoin toUc, Long pkId) {
        this.afterBuy(tid, goods, from, to, number, vid, fromUc, toUc, pkId, null, null, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void afterBuy(String tid, Goods goods, User from, User to, int number, String vid, UserCoin fromUc, UserCoin toUc, Long pkId, Integer recordType, String sender, String receiver) {
        /**
         * 1. 增加礼物记录
         * 2. 如果是PK增加PK礼物记录
         * 3. 如果是幸运礼物，则处理幸运礼物逻辑
         * 4. 异步处理礼物其他逻辑
         * 5. kafka礼物消息
         */
        //1. 增加礼物记录、个人中心礼物记录
        GoodsRecord gr = new GoodsRecord();
        gr.set(goods, from.getId(), vid, to.getId(), number);
        gr.setTid(tid);
        giftDao.addGoodsRecord(gr);

        if (recordType != null) {
            if (recordType == GiftPositionType.PERSONAL.getValue()) {
                PersonalGiftRecord gpr = new PersonalGiftRecord(from.getId(), to.getId(), goods.getId(), goods.getType(), number, gr.getId());
                personGiftRecordRepository.save(gpr);
            } else if (recordType == GiftPositionType.PRIVATE_LETTER.getValue()) {
                //发送私信礼物消息
                imService.sendGitMessage(from, goods, number, sender, receiver);
            }
        }

        //2. 如果是PK增加PK礼物记录
        if (pkId != null && pkId != 0) {
            PKGiftEntity pkGiftEntity = new PKGiftEntity();
            pkGiftEntity.setOrderId(gr.getId());
            pkGiftEntity.setCreateTime(DateUtils.now());
            pkGiftEntity.setPkId(pkId);
            pkGiftRepository.save(pkGiftEntity);
        }

        //3. 如果是幸运礼物，则处理幸运礼物逻辑
        if (goods.getType() == (long) GoodsType.GT_LUCK_GIFT.getValue()) {
            luckGiftService.afterBuyLuckGift(goods, number, to, from, tid, vid);
        }

        //4. 如果是豌豆荚礼物，则查询豌豆道具ID，给用户背包增加豌豆
        if (goods.getType() == GoodsType.GT_PEASECOD_GIFT.getValue()) {
            // PK中赠送豌豆荚自动下注
            if (pkId != null) {
                boolean guessRet = ieasyService.pkGuessInvest(from.getId(), to.getId(), pkId, number);
                if (!guessRet) {
                    packageToolService.addPeaToUserPackage(from.getId(), number);
                }
            } else {
                packageToolService.addPeaToUserPackage(from.getId(), number);
            }
        }

        //4. 异步处理礼物其他逻辑
        logger.info("[advstat][buygoods][success] total {} costtype {} uid {} touid {} count {} cost {} vid {} goodsid {} real 1",
                goods.getCost() * number, goods.getCostType(), from.getId(), to.getId(), number, goods.getCost(), vid, goods.getId());

        //取消累计红包发送
        notifyGiftMessageThroughNewThread(goods, from, to, number, vid, fromUc, toUc);

        //5. kafka礼物消息
        GiftEvent event = createGiftEvent(gr, pkId, from.getName(), toUc.getAccumriceroll(), goods, from, to);
        // emit gift message
        eventProducer.emitAsync(GiftEventType.TOPIC_NAME, String.valueOf(gr.getToUid()), event);
    }

    /**
     * 自定义礼物消息
     * @param goods
     * @param from
     * @param to
     * @return
     */
    private Pair<String, String> buildGiftMsg(Goods goods, User from, User to, Integer number) {
        String giftMsg = goods.getGiftMsg();

        if (giftMsg == null || giftMsg.length() <= 0) {
            return Pair.of("","");
        }

        if (JsonUtils.isJSON(giftMsg)) {
            List<String> strings = JsonUtils.fromString(giftMsg, new TypeReference<List<String>>() {
            });
            Random random = new Random();
            int index = random.nextInt(strings.size());
            giftMsg = strings.get(index);
        }

        String formNick = from.getNickname();
        NobleInfo userNoble = userService.getNobleInfo(from.getId());
        if (userNoble.getLevel() >= Constants.STEALTH_START_LEVEL && userNoble.isLiveStealth()) {
            formNick = Constants.STEALTH_NICK;
        }

        String msg = giftMsg.replace("[give]", formNick)
                .replace("[received]", to.getNickname())
                .replace("[num]", String.valueOf(number))
                .replace("[gift]", goods.getName())
                .replace("[gifticon]", goods.getName());
        return Pair.of(msg, giftMsg);
    }

    private GiftEvent createGiftEvent(GoodsRecord r, Long pkId, String fromName, long accumriceroll, Goods goods, User from, User to) {
        //自定义礼物消息
        Pair<String, String> msgs = buildGiftMsg(goods, from, to, r.getNumber());

        GiftEvent event = new GiftEvent();
        event.setOrderId(String.valueOf(r.getId()));
        event.setGiftId(r.getGiftId());
        event.setGiftName(r.getGiftName());
        event.setGiftType(r.getGiftType());
        event.setGiftExp(goods.getExp());
        event.setGiftMsg(msgs.getLeft());
        event.setGiftMsgTemplate(msgs.getRight());
        if (goods.getShowRunway() != null)
            event.setShowRunway(goods.getShowRunway());

        event.setFromUid(r.getFromUid());
        event.setFromName(fromName);
        event.setFromAppSourceId(from.getAppSourceId());

        event.setToUid(r.getToUid());
        event.setToName(to.getName());
        event.setToTopicId(to.getTid());
        event.setToAppSourceId(to.getAppSourceId());

        event.setCost(r.getCost());
        event.setCostType(r.getCostType());
        event.setRiceroll(r.getRiceroll());
        event.setAccumRiceroll(accumriceroll);

        event.setNumber(r.getNumber());
        event.setVid(r.getVid());
        event.setLiving(isLiving(r.getVid()));

        if (pkId != null)
            event.setPkId(pkId);

        event.setTime(r.getTime());
        return event;
    }

    public Boolean isLiving(String vid) {
        if (vid == null) {
            return false;
        }
        return redisTemplate.opsForHash().hasKey(KEY_LIVING_HASH, vid);
    }

    private void notifyGiftMessageThroughNewThread(Goods goods, User fromUser, User toUser, long number, String vid, UserCoin fromUserCoin, UserCoin toUserCoin) {
        try {
            taskExecutor.execute(() -> notifyGiftMessage(goods, fromUser, toUser, number, vid, fromUserCoin, toUserCoin));
        } catch (Exception e) {
            logger.error("can not execute notification thread.", e);
        }
    }



    /**
     * 根据UA处理不同平台礼物的价值
     */
    private Goods processAppGoods(UAgentInfo ua, Goods goodsDb) {
        Goods goods = new Goods();
        goods.setCostType(goodsDb.getCostType());
        goods.setExp(goodsDb.getExp());
        goods.setId(goodsDb.getId());
        goods.setName(goodsDb.getName());
        goods.setNobleLevel(goodsDb.getNobleLevel());
        goods.setType(goodsDb.getType());
        goods.setCost(goodsDb.getCost());
        goods.setRiceroll(goodsDb.getRiceroll());
        goods.setAnimationType(goodsDb.getAnimationType());
        goods.setGiftMsg(goodsDb.getGiftMsg());
        goods.setShowRunway(goodsDb.getShowRunway());

        if (ua != null) {
            switch (ua.getAppName()) {
                case UAgentInfo.SIZHIBO:
                    goods.setCost(goodsDb.getCost() * goodsSizhiboCostFactor);
                    goods.setRiceroll(goodsDb.getRiceroll() * goodsSizhiboRicerollFactor);
                    break;
                default:
            }
        }
        return goods;
    }


    private void verifyUserAuth(User fUser, Long anchorUid, Goods goods) {
        Long uid = fUser.getId();
        if (goods == null) {
            throw new ResponseException(ResponseError.E_GOODS_NOT_EXIST, "无此礼物");
        }
        long goodType = goods.getType();

        if (goodType == GoodsType.GT_GUARD_GIFT.getValue()) {
            verifyGuardGiftLimit(uid, anchorUid, goods);
        } else if (goodType == GoodsType.GT_NOBLE_GIFT.getValue()) {
            verifyNobleGiftLimit(fUser, goods);
        }
    }

    /***
     * 验证是否满足发送贵族礼物的等级要求
     */
    private void verifyNobleGiftLimit(User user, Goods goods) {
        NobleInfo nobleInfo = userService.getNobleInfo(user.getId());

        if (nobleInfo.getLevel() < goods.getNobleLevel()) {
            throw new ResponseException(ResponseError.E_USER_NOBLE_LEVEL_LIMIT, "您当前的贵族等级不能发送该礼物，请升级后再尝试。");
        }
    }

    /**
     * 验证是否满足发送守护礼物的等级要求
     *
     * @param uid
     * @param goods
     * @param anchorUid
     * @return
     */
    private void verifyGuardGiftLimit(Long uid, Long anchorUid, Goods goods) {
        GoodsGuardian goodsGuardian = goodsGuardianRepository.findByGoodsIdAndIsDeletedIsFalse(goods.getId());

        GuardianLevel guardianLevel = guardianLevelRepository.findByAnchorUidAndUidAndIsDeletedIsFalseAndEndTimeAfter(
                anchorUid, uid, new Date()
        );
        if (guardianLevel == null) {
            throw new ResponseException(ResponseError.E_USER_GUARDIAN_LEVEL_LIMIT, "你不是当前主播的守护用户，无法赠送。 ");
        }
        if (guardianLevel.getLevel() < goodsGuardian.getGuardianLevel()) {
            GuardianOption guardianOption = guardianDao.getGuardianById(goodsGuardian.getGuardianId());
            throw new ResponseException(ResponseError.E_USER_GUARDIAN_LEVEL_LIMIT, "升级到 " + guardianOption.getTitle() + goodsGuardian.getGuardianLevel() + "以上才能赠送");
        }
    }

    /**
     * 礼物记录
     */
    public List<PersonalGiftRecordVO> giftRecord(String name, int appId) {
        User to = userService.ensureExists(name);
        List<PersonalGiftRecordVO> result = getGoodsPositionRecordCache(to.getId());
        if (result == null) {
            final List<PersonalGiftRecordMock> personalGiftRecordMockList = personGiftRecordMockRepository.listPersonGiftRecord(to.getId(), 0, 20);
            List<PersonalGiftRecord> list = personalGiftRecordMockList.isEmpty() ? personGiftRecordRepository.listPersonGiftRecord(to.getId(), 0, 20)
                    : prepare(personalGiftRecordMockList);
            List<PersonalGiftRecordVO> resultCache = new ArrayList<>();
            if (list.size() > 0) {
                Set<Long> uids = list.stream().map(PersonalGiftRecord::getFromUid).collect(Collectors.toSet());
                Set<Long> goods = list.stream().map(PersonalGiftRecord::getGoodsId).collect(Collectors.toSet());

                Map<Long, Goods> goodsMap = listByGoodsIds(new ArrayList<>(goods));
                Map<Long, User> users = userService.listByUids(new ArrayList<>(uids));

                List<String> files = goodsMap.values().stream().map(Goods::getPicture).collect(Collectors.toList());
                Map<String, String> filesMap = ossComponent.getGoodsImageUrl(files, appId);
                for (PersonalGiftRecord s : list) {
                    if (null != users.get(s.getFromUid())) {
                        if (null != goodsMap.get(s.getGoodsId())) {
                            String icon = filesMap.getOrDefault(goodsMap.get(s.getGoodsId()).getPicture(), "");
                            PersonalGiftRecordVO g = new PersonalGiftRecordVO(s, goodsMap.get(s.getGoodsId()).getName(), icon, users.get(s.getFromUid()));
                            resultCache.add(g);
                        }
                    }
                }
            }
            setGoodsPositionRecordCache(to.getId(), resultCache);
            result = resultCache;
        }
        return result;
    }

    private Map<Long, Goods> listByGoodsIds(List<Long> list) {
        List<Goods> goodsList = goodsRepository.findByIdIn(list);
        Map<Long, Goods> goodsMap = new HashMap<>(goodsList.size());
        for (Goods g : goodsList) {
            goodsMap.put(g.getId(), g);
        }
        return goodsMap;
    }

    private String keyGoodsPositionRecord(long uid) {
        return cachePrefix + "goods_record_u_" + uid;
    }

    private void setGoodsPositionRecordCache(long uid, List<PersonalGiftRecordVO> list) {
        String key = keyGoodsPositionRecord(uid);
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(key, JsonUtils.printObject(list), 1L, TimeUnit.HOURS);
    }

    private List<PersonalGiftRecordVO> getGoodsPositionRecordCache(long uid) {
        String key = keyGoodsPositionRecord(uid);
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        String list = valueOperations.get(key);
        if (list == null) {
            return null;
        }
        redisTemplate.expire(key, 1L, TimeUnit.HOURS);
        return JsonUtils.fromString(list, new TypeReference<List<PersonalGiftRecordVO>>() {});
    }

    private void delGoodsPositionRecordCache(long uid) {
        String key = keyGoodsPositionRecord(uid);
        if (redisTemplate.hasKey(key)) {
            redisTemplate.delete(key);
        }
    }

    private List<PersonalGiftRecord> prepare(List<PersonalGiftRecordMock> src){
        List<PersonalGiftRecord> result = new ArrayList<>(src.size());
        src.stream().forEach(item -> {
            PersonalGiftRecord personalGiftRecord = new PersonalGiftRecord();
            BeanUtils.copyProperties(item, personalGiftRecord);
            personalGiftRecord.setNumber((int)item.getNumber());
            result.add(personalGiftRecord);
        });
        return result;
    }
}
