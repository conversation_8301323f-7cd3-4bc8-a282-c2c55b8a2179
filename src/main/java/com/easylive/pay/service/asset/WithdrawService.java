package com.easylive.pay.service.asset;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.dao.UserBankCardRepository;
import com.easylive.pay.dao.jpa.SmsRepository;
import com.easylive.pay.entity.Sms;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.BankSignOperateType;
import com.easylive.pay.enums.IdentityType;
import com.easylive.pay.enums.sms.SmsStatus;
import com.easylive.pay.model.user.IdentityInfo;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.UserService;
import com.easylive.pay.utils.ValidatorUtils;
import com.easylive.pay.vo.BindBankCard;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

/**
 * 提现服务
 *
 * <AUTHOR>
 * @date 2019/8/16
 */
@Service
@Slf4j
public class WithdrawService {

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private UserService userCenterService;

    @Autowired
    private CashoutService cashoutService;

    @Autowired
    private SmsRepository smsRepository;

    /**
     * 新增/编辑银行卡
     */
    public UserBankCard bindBankCard(BindBankCard card) {

        /**
         * 1.验证接口参数
         * 2.获得用户实名认证的真实姓名
         * 3.手机短信验证码验证
         * 4.判断是否新增，如果新增，则查询用户银行卡是否绑定，没有绑定新增
         * 5.如果是修改银行卡，判断该银行卡是否跟当前用户uid一致，不一致提示错误
         * 6.银行卡号验证重复
         * 7.更新短信
         * 8.银行签约
         */
        //1.验证接口参数
        log.info("1.. Bind Bank Card param={}", card);
        try {
            validateBindBankCardParam(card);
        } catch (ResponseException e) {
            throw e;
        } catch (Exception e) {
            throw new ResponseException(ResponseError.E_PARAM);
        }

        //2.获得用户实名认证的真实姓名
        IdentityInfo certificationInfo = userCenterService.getIdentityByUid(card.getUid());

        if (StringUtils.isEmpty(certificationInfo.getRealName())) {
            throw new ResponseException(ResponseError.E_USER_BANK_CARD_NOT_AUTHENTICATION, "你还没有实名认证");
        }
        if (certificationInfo.getType() == IdentityType.ALIYUN.getType()) {
            throw new ResponseException(ResponseError.E_USER_BANK_CARD_NEED_AUTH, "你还没有绑定银行卡");
        }

        String realName = certificationInfo.getRealName();
        card.setAccountName(realName);

        //3.手机短信验证码验证
        String phone = validateSmsCode(card.getSmsId(), card.getSmsCode(), card.getUid());

        //4.判断是否新增，如果新增，则查询用户银行卡是否绑定，没有绑定新增
        if (card.getCardId() != null && card.getCardId().longValue() > 0) {
            //编辑
            //5.如果是修改银行卡，判断该银行卡是否跟当前用户uid一致，不一致提示错误
            UserBankCard userBankCard = validateUpdateBankCard(card.getUid(), card.getCardId());
            //6.银行卡号验证重复
            validateUpdateBankAccountNo(card.getAccountNo(), card.getCardId());

            //7.更新短信
            Date now = new Date();
            smsRepository.updateStatusAndVerifyTimeAndCloseTime(card.getSmsId(), SmsStatus.COLOSED.getCode(), now, now);
            UserBankCard bankCard = updateUserBankCard(userBankCard, card);

            //8.客户信息签约签约
            cashoutService.customerSign(card.getUid(),
                    BankSignOperateType.ADD.getCode(),
                    bankCard.getAccountName(),
                    bankCard.getAccountNo(),
                    bankCard.getOpenBankNo(),
                    bankCard.getOpenBankName(),
                    certificationInfo.getIdCard(),
                    phone,
                    "", "", "", ""
            );
            return bankCard;
        } else {
            //新增
            UserBankCard bankCard = userBankCardRepository.findTopByUidAndIsDeletedIsFalse(card.getUid());
            if (bankCard != null) {
                throw new ResponseException(ResponseError.E_USER_BANK_CARD_EXISTS, "你已经绑定银行卡");
            }
            //6.银行卡号验证重复
            validateUpdateBankAccountNo(card.getAccountNo());

            //7.更新短信
            Date now = new Date();
            smsRepository.updateStatusAndVerifyTimeAndCloseTime(card.getSmsId(), SmsStatus.COLOSED.getCode(), now, now);
            UserBankCard userBankCard = insertUserBankCard(card);

            //8.银行签约
            cashoutService.customerSign(card.getUid(),
                    BankSignOperateType.ADD.getCode(),
                    userBankCard.getAccountName(),
                    userBankCard.getAccountNo(),
                    userBankCard.getOpenBankNo(),
                    userBankCard.getOpenBankName(),
                    certificationInfo.getIdCard(),
                    phone,
                    "", "", "", ""
            );

            return userBankCard;
        }
    }

    private void validateBindBankCardParam(BindBankCard card) {
        if (!ValidatorUtils.checkLength(card.getAccountNo(), 10, 20)) {
            throw new ResponseException(ResponseError.E_PARAM, "请输入正确的卡号");
        }

        if (!ValidatorUtils.isNumeric(card.getAccountNo())) {
            throw new ResponseException(ResponseError.E_PARAM);
        }

        if (!ValidatorUtils.checkLength(card.getOpenBankName(), 0, 60)) {
            throw new ResponseException(ResponseError.E_PARAM);
        }

        if (!ValidatorUtils.checkLength(card.getOpenBankNo(), 5, 20)) {
            throw new ResponseException(ResponseError.E_PARAM, "请输入正确的开户行号");
        }

        if (!ValidatorUtils.isNumeric(card.getOpenBankNo())) {
            throw new ResponseException(ResponseError.E_PARAM);
        }

        if (!ValidatorUtils.checkLength(card.getSmsCode(), 4, 4)) {
            throw new ResponseException(ResponseError.E_PARAM, "验证码位数不对");
        }

        if (!ValidatorUtils.isNumeric(card.getSmsCode())) {
            throw new ResponseException(ResponseError.E_PARAM);
        }
    }

    private UserBankCard insertUserBankCard(BindBankCard card) {
        UserBankCard userBankCard = new UserBankCard();
        userBankCard.setUid(card.getUid());
        userBankCard.setAccountName(card.getAccountName());
        userBankCard.setAccountNo(card.getAccountNo());
        userBankCard.setOpenBankName(card.getOpenBankName());
        userBankCard.setOpenBankNo(card.getOpenBankNo());
        return userBankCardRepository.saveAndFlush(userBankCard);
    }

    private UserBankCard updateUserBankCard(UserBankCard bankCard, BindBankCard card) {
        bankCard.setUid(card.getUid());
        bankCard.setAccountName(card.getAccountName());
        bankCard.setAccountNo(card.getAccountNo());
        bankCard.setOpenBankName(card.getOpenBankName());
        bankCard.setOpenBankNo(card.getOpenBankNo());
        return userBankCardRepository.saveAndFlush(bankCard);
    }

    /**
     * 验证短信
     * @param smsId 短信ID
     * @param smsCode 短信码
     * @param uid 用户UID
     * @return
     */
    public String validateSmsCode(Long smsId, String smsCode, long uid) {
        /**
         * 1.缓存限制验证次数5次，5次之后需要重新发送，缓存20分钟过期
         * 2.查询短信验证有效期是否存在，20分钟超时
         * 3.判断传入的验证码和数据库中是否一致，一致在更新验证时间、关闭时间、短信状态
         */
        //1.缓存限制验证次数5次，5次之后需要重新发送，缓存20分钟过期

        //2.查询短信验证有效期是否存在，20分钟超时
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -20);
        Sms sms = smsRepository.findTopByIdAndAndCreateTimeGreaterThanEqual(smsId, calendar.getTime());
        if (sms == null) {
            throw new ResponseException(ResponseError.E_SMS_VALIDATE_HAS_EXPIRED, "验证码已过有效期");
        }
        if (sms.getStatus() != SmsStatus.SENT.getCode()) {
            throw new ResponseException(ResponseError.E_SMS_VALIDATE_EXPIRED, "验证码不在有效期");
        }

        String phone = userCenterService.getUserAuthPhoneNumber(uid);
        if (sms.getPhone().compareTo(phone) != 0) {
            throw new ResponseException(ResponseError.E_SMS_PHONE_NOT_RIGHT, "短信验证码错误，请确认手机号");
        }

        //3.判断传入的验证码和数据库中是否一致，一致在更新验证时间、关闭时间、短信状态
        smsRepository.updateVerifyTime(smsId, new Date());
        if (sms.getCode().compareTo(smsCode) != 0) {
            throw new ResponseException(ResponseError.E_SMS_CODE_NOT_RIGHT, "短信验证码错误");
        }

        return phone;
    }

    /**
     * 验证银行卡操作
     */
    private UserBankCard validateUpdateBankCard(Long uid, int cardId) {
        UserBankCard bankCard = userBankCardRepository.findTopById(cardId);
        if (bankCard == null) {
            throw new ResponseException(ResponseError.E_USER_BANK_CARD_NOT_EXISTS, "你还没有绑定银行卡");
        }
        if (bankCard.getUid().longValue() != uid.longValue()) {
            throw new ResponseException(ResponseError.E_USER_BANK_CARD_NOT_YOURS, "该银行卡不属于你");
        }
        return bankCard;
    }

    private void validateUpdateBankAccountNo(String accountNo) {
        validateUpdateBankAccountNo(accountNo, null);
    }

    private void validateUpdateBankAccountNo(String accountNo, Integer id) {
        UserBankCard userBankCard;
        if (id == null) {
            userBankCard = userBankCardRepository.findTopByAccountNoAndIsDeletedIsFalse(accountNo);
        } else {
            userBankCard = userBankCardRepository.findTopByAccountNoAndIdNotAndIsDeletedIsFalse(accountNo, id);
        }
        if (userBankCard != null) {
            throw new ResponseException(ResponseError.E_USER_BANK_CARD_ACCOUNT_NO_EXISTS, "卡号已被绑定");
        }
    }

    /**
     * 查询绑定的银行卡
     */
    public UserBankCard findUserBankCard(long uid) {
        return userBankCardRepository.findTopByUidAndIsDeletedIsFalse(uid);
    }

    public boolean unbindBankCard(long uid, int unBindType) {
        UserBankCard card = userBankCardRepository.findTopByUidAndIsDeletedIsFalse(uid);
        if (card == null) {
            return false;
        }

        log.info("User unbind type: {} cashout info:{}", unBindType, card);

        // 银行卡解绑
        if (unBindType == 1) {
            card.setOpenBankName("");
            card.setOpenBankNo("");
            card.setAccountNo("");
        }

        // 支付宝解绑
        if (unBindType == 2) {
            card.setZfbAccount("");
        }

        //两者都解绑
        if ((card.getAccountNo() == null || card.getAccountNo().isEmpty()) && (card.getZfbAccount() == null || card.getZfbAccount().isEmpty())) {
            card.setDeleted(true);
        }

        userBankCardRepository.save(card);
        return true;
    }

}
