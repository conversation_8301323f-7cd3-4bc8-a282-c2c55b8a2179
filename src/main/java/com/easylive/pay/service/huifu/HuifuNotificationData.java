package com.easylive.pay.service.huifu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Data
@JsonNaming(PropertyNamingStrategy.LowerCaseWithUnderscoresStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HuifuNotificationData {
    private String appId;
    private String businessMode;
    private String createdTime;
    private String endTime;
    private String id;
    private String orderNo;
    private String outTransId;
    private String partyOrderId;
    private String payAmt;
    private String payChannel;
    private String status;
    private String realAmt;
    // private String expend;
}
