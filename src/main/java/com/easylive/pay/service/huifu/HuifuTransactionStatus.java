package com.easylive.pay.service.huifu;

/**
 * <AUTHOR>
 * @date 2024/4/25
 */
public enum HuifuTransactionStatus {
    succeeded,
    pending,
    failed;

    public static boolean isSucceeded(String status) {
        return succeeded.name().equals(status);
    }

    public static boolean isPending(String status) {
        return pending.name().equals(status);
    }

    public static boolean isFailed(String status) {
        return failed.name().equals(status);
    }
}
