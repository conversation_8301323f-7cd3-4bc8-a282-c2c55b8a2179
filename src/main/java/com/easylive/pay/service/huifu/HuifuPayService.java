package com.easylive.pay.service.huifu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.component.RedisDistributedLock;
import com.easylive.pay.component.RedisDistributedLockFactory;
import com.easylive.pay.component.SpringContext;
import com.easylive.pay.config.apollo.ServerApiConfig;
import com.easylive.pay.dao.*;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.AliPayProtocol;
import com.easylive.pay.enums.AliPayType;
import com.easylive.pay.enums.BusinessOrderId;
import com.easylive.pay.enums.WeixinPayType;
import com.easylive.pay.model.RechargeResult;
import com.easylive.pay.service.ConfigService;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.service.alipay.AliPayService;
import com.easylive.pay.service.alipay.AlipayOrderExecuteResult;
import com.easylive.pay.service.alipay.AlipayOrderInfo;
import com.easylive.pay.service.alipay.AlipayProvider;
import com.easylive.pay.service.wx.WeixinOrderInfo;
import com.easylive.pay.service.wx.WeixinPayProvider;
import com.easylive.pay.service.wx.WeixinService;
import com.easylive.pay.utils.AppStringUtils;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huifu.adapay.Adapay;
import com.huifu.adapay.core.AdapayCore;
import com.huifu.adapay.core.exception.BaseAdaPayException;
import com.huifu.adapay.core.util.AdapaySign;
import com.huifu.adapay.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/12/22
 * 三方支付-汇付天下支付服务
 */
@Slf4j
@Service
public class HuifuPayService {


    @Autowired
    private RechargeThirdHuifuAppRepository appRepository;

    @Autowired
    private AliPayService aliPayService;

    @Autowired
    private WeixinService weixinService;

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private RechargeThirdHuifuRecordRepository recordRepository;

    @Autowired
    private CashoutHuifuRecordRepository cashoutRecordRepository;

    @Autowired
    private CashoutHuifuUserRecordRepository userRecordRepository;

    @Autowired
    private CashoutHuifuUserRepository userRepository;

    @Autowired
    private CashoutHuifuDao cashoutHuifuDao;

    @Autowired
    private RedisDistributedLockFactory redisDistributedLockFactory;

    private static final String SETTLE_OPERATION_LOCK = "huifu_settle_lock";
    private static final String GET_USER_LOCK = "huifu_get_user_lock";
    private static final long SETTLE_LOCK_INTERVAL = 1000 * 5; //结算账号操作延迟5秒锁定
    private static final long GET_USER_LOCK_INTERVAL = 1000 * 10; //获取用户信息最多等待10秒


    private static final int MAX_AMOUNT_PER_TRANS = 5000000;


    @Value("${pay.third.huifu.cash.member.prefix}")
    private String memberPrefix;


    private final Map<String, RechargeThirdHuifuApp> appMap = new HashMap<>();
    @Autowired
    private ServerApiConfig serverApiConfig;

    @Autowired
    private ConfigService configService;

    @Value("${pay.third.huifu.notify-url}")
    private String notifyUrl;


    private String getNotifyUrl() {
        return serverApiConfig.getPayCallbackDomain() + this.notifyUrl;
    }

    @PostConstruct
    public void init() {
        String appId = configService.getHuifuCashAppId();
        log.info("Current Huifu app id: {}", appId);
    }


    public void load(Map<String, RechargeAliApp> aliApps) {
        Map<String, MerConfig> configPathMap = new HashMap<>();

        for (RechargeThirdHuifuApp app : appRepository.findAll()) {
            String appId = app.getAppId();
            appMap.put(appId, app);


            RechargeAliApp aliApp = aliApps.get(appId);
            if (aliApp != null) {
                MerConfig merConfig = new MerConfig();
                merConfig.setApiKey(app.getAppKey());
                merConfig.setApiMockKey(app.getMockKey());
                merConfig.setRSAPrivateKey(aliApp.getPrivateKey());
                configPathMap.put(app.getHuifuAppId(), merConfig);
                log.info("Add huifu app config:{},{}", appId, app.getHuifuAppId());
            } else {
                log.error("汇付APPID没有配置: {}", appId);
            }

        }
        try {
            /**
             * debug 模式，开启后有详细的日志
             */
            Adapay.debug = true;

            /**
             * prodMode 模式，默认为生产模式，false可以使用mock模式
             */
            Adapay.prodMode = true;
            Adapay.initWithMerConfigs(configPathMap);
        } catch (Exception e) {
            log.error("初始化汇付天下失败", e);
            throw new ResponseException(ResponseError.E_THIRD_ERROR, "初始化汇付天下失败");
        }
    }

    public AlipayProvider getAliProvider(AlipayOrderInfo info) {
        if (info.getType() == AliPayType.APP || info.getType() == AliPayType.WAP) {
            RechargeThirdHuifuApp app = appMap.get(info.getApp().getAppId());
            if (app == null)
                throw new ResponseException(ResponseError.E_THIRD_ERROR, "未配置APPID");
            AliPayHuifuProvider provider = new AliPayHuifuProvider(app, this);
            provider.setNotifyUrl(this.getNotifyUrl());
            Map<String, Object> paymentParams = preparePaymentParams(app.getHuifuAppId(), "alipay_qr",
                    info.getOrderId(), info.getAmount(), info.getDetail());
            provider.setPaymentParams(paymentParams);
            return provider;
        }
        throw new ResponseException(ResponseError.E_THIRD_ERROR, "不支持的支付类型");
    }

    private Map<String, Object> preparePaymentParams(String appId, String payChannel, String orderId, String amount, String detail) {
        Map<String, Object> paymentParams = new HashMap<>(10);

        //paymentParams.put("adapay_connection_request_timeout", 500);
        //paymentParams.put("adapay_connect_timeout", 500);
        //paymentParams.put("adapay_socket_timeout", 500);
        //paymentParams.put("adapay_region", "beijing");

        paymentParams.put("app_id", appId);
        paymentParams.put("order_no", orderId);
        paymentParams.put("pay_channel", payChannel);
        paymentParams.put("pay_amt", amount);

        paymentParams.put("goods_title", detail);
        paymentParams.put("goods_desc", detail);

        paymentParams.put("div_members", "");
        paymentParams.put("notify_url", this.getNotifyUrl());
        return paymentParams;
    }

    public WeixinPayProvider getWeixinProvider(WeixinOrderInfo info) {
        if (info.getType() == WeixinPayType.MINIAPP || info.getType() == WeixinPayType.MP) {
            RechargeThirdHuifuApp app = appMap.get(info.getApp().getAppSecret());
            if (app == null)
                throw new ResponseException(ResponseError.E_THIRD_ERROR, "未配置APPID");
            WeixinPayHuifuProvider provider = new WeixinPayHuifuProvider(app, this);
            String payChannel = info.getType() == WeixinPayType.MINIAPP ? "wx_lite" : "wx_pub";
            Map<String, Object> paymentParams = preparePaymentParams(app.getHuifuAppId(),  payChannel,
                    info.getOrderId(), info.getAmount(), info.getDetail());
            provider.setPaymentParams(paymentParams);
            Map<String, String> expend = new HashMap<>();
            expend.put("open_id", info.getOpenId());
            paymentParams.put("expend", expend);
            return provider;
        }
        throw new ResponseException(ResponseError.E_THIRD_ERROR, "不支持的支付类型");
    }

    @Transactional
    public void validOrder(String data, String sign) {
        log.info("[Huifu][ValidOrder]: {}  sign: {}", data, sign);
        try {
            boolean checkSign;
            checkSign = AdapaySign.verifySign(data, sign, AdapayCore.PUBLIC_KEY);
            if (checkSign) {
                log.info("[Huifu][ValidOrder] Validation OK");
                HuifuNotificationData notificationData = new ObjectMapper().readValue(data, HuifuNotificationData.class);
                if ("succeeded".equals(notificationData.getStatus())) {
                    String orderId = notificationData.getOrderNo();
                    String pfOrderId = notificationData.getOutTransId();
                    RechargeResult r = rechargeService.validAndRecharge(orderId, pfOrderId, notificationData.getPayAmt());
                    if (!r.isDuplicated()) {
                        try {
                            if(notificationData.getPayChannel().startsWith("alipay"))
                                aliPayService.rechargeControlFeedback(r.getMerchantId(), r.getAppId(), (int) r.getRmb());
                            else if(notificationData.getPayChannel().startsWith("wx"))
                                weixinService.rechargeControlFeedback(r.getMerchantId(), r.getAppId(), (int) r.getRmb());
                            else {
                                log.error("[Huifu][ValidOrder] unknown pay channel: {}", notificationData.getPayChannel());
                            }
                        } catch (Exception e) {
                            log.error("[Huifu][ValidOrder] error when recharge control feedback", e);
                        }
                    }
                    finalOrderInfo(notificationData);
                    return;
                } else {
                    log.info("[Huifu][ValidOrder] status not succeeded: {}", notificationData.getStatus());
                    finalOrderInfo(notificationData);
                }
            } else {
                //验签失败逻辑
                log.info("[Huifu][ValidOrder] Valid Fail");
                throw new ResponseException(ResponseError.E_THIRD_SIGN, "验签失败");
            }
        } catch (ResponseException e) {
            throw e;
        } catch (Exception e) {
            log.error("[Huifu][ValidOrder] error when valid order", e);
        }
        throw new ResponseException(ResponseError.E_THIRD_ERROR, "订单验证失败");
    }


    void saveOrderInfo(Map<String, Object> payment) {
        String orderId = (String) payment.get("order_no");
        String huifuId = (String) payment.get("id");
        String partyOrderId = (String) payment.get("party_order_id");
        String payChannel = (String) payment.get("pay_channel");
        String payAmt = (String) payment.get("pay_amt");
        String createdTime = (String) payment.get("created_time");
        String status = (String) payment.get("status");

        RechargeThirdHuifuRecord record = new RechargeThirdHuifuRecord();
        record.setOrderId(orderId);
        record.setHuifuId(huifuId);
        record.setPartyOrderId(partyOrderId);
        record.setChannel(payChannel);
        record.setAmount(new BigDecimal(payAmt).multiply(new BigDecimal("100")).longValue());
        record.setCreateTime(new Date(Long.parseLong(createdTime) * 1000L));
        record.setStatus(status);
        recordRepository.save(record);
    }

    void finalOrderInfo(HuifuNotificationData notificationData) {
        RechargeThirdHuifuRecord record = recordRepository.findByOrderId(notificationData.getOrderNo());
        if (record == null) {
            return;
        }

        record.setOutTransId(notificationData.getOutTransId());
        record.setStatus(notificationData.getStatus());
        String endTime = notificationData.getEndTime();
        if (endTime != null) {
            // parse end time to date
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            try {
                record.setEndTime(sdf.parse(endTime));
            } catch (Exception ignore) {
            }
        }
        recordRepository.save(record);
    }

    public boolean isUserExist(long uid) {
        CashoutHuifuUserEntity user = userRepository.findByUid(uid);
        return user != null;
    }

    private String getCurrentHuifuAppId() {
        return getHuifuAppId(configService.getHuifuCashAppId());
    }

    private int getCurrentHuifuAppIdInt() {
        return getHuifuAppIdInt(configService.getHuifuCashAppId());
    }

    private String getHuifuAppId(String appId) {
        return appMap.get(appId).getHuifuAppId();
    }

    private int getHuifuAppIdInt(String appId) {
        return Math.toIntExact(appMap.get(appId).getId());
    }

    private Integer getAppIdFromString(String appId) {
        RechargeThirdHuifuApp app = appMap.get(appId);
        if (app != null)
            return Math.toIntExact(app.getId());
        return null;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public HuifuUser getUser(long uid, String phone, String realName, String idCard, String bankCardId) {
        RedisDistributedLock lock = redisDistributedLockFactory.getLock(GET_USER_LOCK, GET_USER_LOCK_INTERVAL);
        if( !lock.lock() ) {
            log.error("Unable to get lock for get user");
            return null;
        }
        try {
            phone = phone.replaceAll("\\s+", "");
            idCard = idCard.replaceAll("\\s+", "");
            bankCardId = bankCardId.replaceAll("\\s+", "");
            realName = realName.replaceAll("\\s+", "");
            if (StringUtils.isEmpty(realName) || StringUtils.isEmpty(idCard) || StringUtils.isEmpty(bankCardId)) {
                log.error("[Huifu][GetUser] invalid params: phone={}, realName={}, idCard={}, bankCardId={}", phone, realName, idCard, bankCardId);
                return null;
            }

            CashoutHuifuUserEntity user = userRepository.findByAppIdAndUidAndNameAndCertIdAndCardId(getCurrentHuifuAppIdInt(), uid, realName, idCard, bankCardId);
            if (user == null) {
                user = createUser(getCurrentHuifuAppId(), uid, phone, realName, idCard, bankCardId);
            }
            if (user != null) {
                HuifuUser huifuUser = new HuifuUser();
                huifuUser.setMemberId(user.getMemberId());
                huifuUser.setSettleId(user.getSettleId());
                huifuUser.setRealName(user.getName());
                huifuUser.setPhone(user.getPhone());
                huifuUser.setBankCardId(user.getCardId());
                huifuUser.setCertId(user.getCertId());
                return huifuUser;
            }
        } catch (Exception e ) {
            log.error("Error get huifu user:",e);
        } finally {
            lock.releaseLock();
        }
        return null;
    }

    private String decideMemberId(long uid) {
        String memberId = getMemberId(uid);

        int maxRetries = 5;
        int retryTimes = 0;
        while (retryTimes < maxRetries) {
            Member member = queryMember(getCurrentHuifuAppId(), memberId);
            if (member != null) {
                log.info("{} already exists, generate new one", memberId);
                memberId = getMemberId2(uid);
            } else {
                break;
            }
            retryTimes++;
        }
        return memberId;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public CashoutHuifuUserEntity createUser(String appId, long uid, String phone, String realName, String idCard, String bankCardId) {
        log.info("create huifu user:{},{},{},{},{}", uid, phone, realName, idCard, bankCardId);
        phone = AppStringUtils.getMobile(phone);
        CashoutHuifuUserEntity user = null;

        //TODO: 检查参数, 实际上汇付接口会检查参数

        String code = "succeeded";
        String message = "成功";
        String settleId = null;
        Date now = DateUtils.now();
        String memberId = decideMemberId(uid);
        boolean ok;
        log.info("Member ID:{}", memberId);

        try {
            //2. 创建实名和用户
            ok = createMember(appId, memberId, phone, realName, idCard);
            //3. 创建对应的结算账户
            settleId = createSettleAccount(appId, memberId, bankCardId, realName, phone, idCard);
        } catch (BaseAdaPayException e) {
            log.error("Create member failed with adapay error: {},{}", e.getCode(), e.getMessage());
            code = e.getCode();
            message = e.getMessage();
            ok = false;
        } catch (Exception e) {
            log.error("Create member failed", e);
            code = "error";
            message = "unknown, check for system log";
            ok = false;
        }


        try {
            if (ok) {
                CashoutHuifuUserEntity huifuUser = new CashoutHuifuUserEntity();
                huifuUser.setCreateTime(now);
                huifuUser.setUid(uid);
                huifuUser.setAppId(getCurrentHuifuAppIdInt());
                huifuUser.setName(realName);
                huifuUser.setPhone(phone);
                huifuUser.setCertId(idCard);
                huifuUser.setCardId(bankCardId);
                huifuUser.setMemberId(memberId);
                huifuUser.setSettleId(settleId);
                cashoutHuifuDao.save(huifuUser);
                user = huifuUser;
                log.info("Huifu user created :{}", memberId);
            }
        } catch (Exception e) {
            log.error("create user failed:", e);
            code = "DB Error";
            message = e.getMessage();
            if( message.length() > 128 )
                message = message.substring(0, 128);
        }

        try {
            CashoutHuifuUserRecord record = new CashoutHuifuUserRecord();
            record.setName(realName);
            record.setUid(uid);
            record.setCertId(idCard);
            record.setPhone(phone);
            record.setCardId(bankCardId);
            record.setMemberId(memberId);
            record.setSettleId(settleId);
            record.setErrorMsg(message);
            record.setErrorCode(code);
            record.setType(HuifuUserOperateType.CREATE.getValue());
            record.setOperateTime(now);
            cashoutHuifuDao.save(record);
        } catch (Exception e) {
            log.error("create user record failed:", e);
        }

        return user;
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean updateUser(long uid, String phone, String realName, String idCard, String bankCardId) {
        log.info("update huifu user:{},{},{},{},{}", uid, phone, realName, idCard, bankCardId);
        phone = AppStringUtils.getMobile(phone);

        //1. 检查用户是否存在
        int appId = getCurrentHuifuAppIdInt();
        CashoutHuifuUserEntity user = cashoutHuifuDao.findUserByAppIdAndUidAndName(appId, uid, realName);
        if (user == null) {
            log.info("user not exists for app id :{},{},{}", uid, realName, appId);
            return  getUser(uid, phone, realName, idCard, bankCardId ) != null;
        }

        //暂时先只支持更新银行卡，其他的如果存在不一致，则返回错误
        //2.对比手机、身份证、姓名，如果存在不一致，则需要新创建一个用户，重新绑定到uid，目前暂时不做，只返回错误
        if (!user.getCertId().equals(idCard)) {
            log.info("Modification of identity information is not supported now");
            return false;
        }

        //3. 如果银行卡号不一致，则需要更新结算账户
        if (user.getCardId().equals(bankCardId)) {
            log.info("Same bank card information , no need to update");
            return true;
        }

        String settleId = null;
        String code = "succeeded";
        String message = "成功";

        //TODO: 先创建新的再删除老的
        try {
            log.info("Create new settle account {}, {}", user.getMemberId(), bankCardId);
            settleId = createSettleAccount(getCurrentHuifuAppId(), user.getMemberId(), bankCardId, realName, phone, idCard);
            if (settleId != null) {
                user.setCardId(bankCardId);
                user.setSettleId(settleId);
                cashoutHuifuDao.saveOrUpdate(user);
                log.info("update settle account success:{},{}", user.getMemberId(), settleId);
            } else {
                log.error("Impossible");
            }
        } catch (BaseAdaPayException e) {
            log.error("update settle account failed with adapay error: {},{}", e.getCode(), e.getMessage());
            code = e.getCode();
            message = e.getMessage();
        } catch (Exception e) {
            log.error("create settle account failed:{}", user.getMemberId());
            message = e.getMessage();
            code = "error";
        }

        boolean deleteOK = false;
        try {
            //结算账户只能先删除再新创建
            log.info("Delete old settle account");
            deleteSettleAccount(getCurrentHuifuAppId(), user.getMemberId(), user.getSettleId());
            deleteOK = true;
        } catch (BaseAdaPayException e) {
            log.error("delete settle account failed with adapay error: {},{}", e.getCode(), e.getMessage());
            code = e.getCode();
            message = e.getMessage();
        } catch (Exception e) {
            log.error("delete settle account failed:{}", user.getMemberId());
            message = e.getMessage();
            code = "error";
        }

/*
        if (deleteOK) {


            //创建新账号失败，需要恢复之前删除的账号，也就是用老账号创建新的
            if (settleId == null) {
                log.info("create new settle account failed, needs to restore the old account");
                try {
                    String oldAccountSettleId = createSettleAccount(getCurrentHuifuAppId(), user.getMemberId(), user.getCardId(), user.getName(), user.getPhone(), user.getCertId());
                    user.setSettleId(oldAccountSettleId);
                    cashoutHuifuDao.saveOrUpdate(user);
                    log.info("restore settle account success:{},{}", user.getMemberId(), oldAccountSettleId);
                } catch (Exception e) {
                    log.error("restore settle account failed with adapay error", e);
                }
            }

        }
        */



        try {
            //保存更新用户信息的记录
            CashoutHuifuUserRecord record = new CashoutHuifuUserRecord();
            record.setName(realName);
            record.setUid(uid);
            record.setCertId(idCard);
            record.setPhone(phone);
            record.setCardId(bankCardId);
            record.setMemberId(user.getMemberId());
            record.setSettleId(settleId);
            record.setErrorMsg(message);
            record.setErrorCode(code);
            record.setType(HuifuUserOperateType.UPDATE.getValue());
            record.setOperateTime(DateUtils.now());
            cashoutHuifuDao.save(record);
        } catch (Exception e) {
            log.error("create user record failed:", e);
        }

        if (settleId != null) {
            log.info("update user success:{},{}", user.getMemberId(), settleId);
            return true;
        } else {
            log.info("update user failed:{}", user.getMemberId());
            return false;
        }

    }

    private static final Random random = new Random();

    private String getMemberId(long uid) {
        return this.memberPrefix + "_" + uid;//+ "_" + String.format("%04d", random.nextInt(10000));
    }

    private String getMemberId2(long uid) {
        return this.memberPrefix + "_" + uid + "_" + String.format("%04d", random.nextInt(1000));
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public HuifuCashRecord withdraw(String orderId, long uid, long rmb, String phone, String realName, String idCard, String bankCardId) {
        //1. 查找用户是否存在，如果没有则创建

        log.info("huifu withdraw:{},{},{},{},{}", uid, phone, realName, idCard, bankCardId);
        HuifuUser huifuUser = SpringContext.getBean(HuifuPayService.class).getUser(uid, phone, realName, idCard, bankCardId);
        if (huifuUser == null) {
            throw new ResponseException(ResponseError.E_CASHOUT, "can't create user");
        }

        String memberId = huifuUser.getMemberId();
        Date now = DateUtils.now();
        CashoutHuifuRecord record = new CashoutHuifuRecord();
        record.setAmount(rmb);
        record.setOrderId(orderId);
        record.setMemberId(memberId);
        record.setSettleId(huifuUser.getSettleId());
        record.setName(huifuUser.getRealName());
        record.setCertId(huifuUser.getCertId());
        record.setBankCard(huifuUser.getBankCardId());
        record.setCreateTime(now);
        record.setStatus(HuifuCashStatus.CREATED.getValue());

        //2. 余额支付
        if (createBalancePay(record)) {
            log.info("Balance pay succeeded. {}, {}, {}", record.getOrderId(), record.getPayId(), record.getAmount());
        }

        return createModelFromCashoutRecord(record);
    }

    public List<CashoutHuifuRecord> listCashoutRecordForProcessing() {
        //需要处理创建好的，以及余额支付、提现中的提现记录
        List<Integer> status = List.of(HuifuCashStatus.CREATED.getValue(),
                HuifuCashStatus.PAY_SUCCESS.getValue(),
                HuifuCashStatus.CASH_PENDING.getValue());
        //return new ArrayList<>(cashoutRecordRepository.findByStatusIn(status));
        return new ArrayList<>(cashoutHuifuDao.findByStatusIn(status));
    }

    public HuifuCashRecord getByOrderId(String orderId) {
        CashoutHuifuRecord record = cashoutRecordRepository.findByOrderId(orderId);
        if (record == null) {
            return null;
        }
        return createModelFromCashoutRecord(record);
    }

    @Transactional
    public HuifuCashRecord processOrder(CashoutHuifuRecord record) {
        log.info("Processing huifu record:{},", record.getOrderId());
        HuifuCashStatus status = HuifuCashStatus.fromValue(record.getStatus());
        if (status == null) {
            log.error("Invalid status:{}", record.getStatus());
            return null;
        }


        switch (status) {
            case PAY_SUCCESS:
                //支付成功状态，进行提现
                log.info("{} status is pay success, performs cashout", record.getOrderId());
                createCashout(record);
                break;
            case CREATED:
                //新创建的订单，进行余额支付
                createBalancePay(record);
                break;
            case CASH_PENDING:
                //查询提现状态
                updateCashoutStatusByQuery(record);
                break;
            case CASH_FAILED:
            case ERROR:
            case CASH_SUCCESS:
            case PAY_FAILED:
                //提现成功、系统错误、支付失败、提现失败的都不处理
                break;
            default:
                //impossible
                log.error("Invalid status:{}", status);
        }
        log.info("Process huifu cashout {} completed: {} --> {}", record.getOrderId(), status, HuifuCashStatus.fromValue(record.getStatus()));
        return createModelFromCashoutRecord(record);
    }

    private void updateCashoutStatusByQuery(CashoutHuifuRecord record) {
        int originStatus = record.getStatus();
        HuifuTransactionStatus transactionStatus = null;
        String cashId = null;
        HuifuQueryCashout cashout = queryCashout(getCurrentHuifuAppId(), record.getOrderId());
        if (cashout == null) {
            log.info("query Cashout order id failed  :{}", record.getOrderId());
            return;
        }

        if (HuifuTransactionStatus.isSucceeded(cashout.getStatus())) {
            String stat = cashout.getCashList().get(0).getTransStat();
            cashId = cashout.getCashList().get(0).getCashId();
            if ("S".equals(stat)) {
                transactionStatus = HuifuTransactionStatus.succeeded;
            } else if ("F".equals(stat)) {
                transactionStatus = HuifuTransactionStatus.failed;
            } else if ("P".equals(stat)) {
                transactionStatus = HuifuTransactionStatus.pending;
            }
        }

        //无法找到该提现订单号，认为是失败的提现
        if (HuifuTransactionStatus.isFailed(cashout.getStatus())) {
            if ("cash_error".equals(cashout.getErrorCode())) {
                transactionStatus = HuifuTransactionStatus.failed;
                record.setErrorCode(cashout.getErrorCode());
                record.setErrorMsg(cashout.getErrorMsg());
            }
        }


        record.setCashoutId(cashId);
        if (transactionStatus == HuifuTransactionStatus.succeeded) {
            record.setErrorMsg("成功");
            record.setErrorCode("succeeded");
            record.setStatus(HuifuCashStatus.CASH_SUCCESS.getValue());
            record.setCompleteTime(DateUtils.now());
        } else if (transactionStatus == HuifuTransactionStatus.failed) {
            record.setStatus(HuifuCashStatus.CASH_FAILED.getValue());
        } else {
            //pending
            record.setStatus(HuifuCashStatus.CASH_PENDING.getValue());
        }

        if (originStatus != record.getStatus()) {
            log.info("update record status:{},{}, {} --> {}", record.getOrderId(), transactionStatus, originStatus, record.getStatus());
            cashoutHuifuDao.saveOrUpdate(record);
        }
    }

    private boolean createBalancePay(CashoutHuifuRecord record) {
        String appId = getCurrentHuifuAppId();
        boolean committed = false;
        try {
            String balancePayId = createBalancePay(appId, record.getOrderId(), record.getMemberId(), record.getAmount());
            record.setPayId(balancePayId);
            record.setStatus(HuifuCashStatus.PAY_SUCCESS.getValue());
            record.setErrorCode("succeeded");
            record.setErrorMsg("提现请求成功");
            committed = true;
        } catch (BaseAdaPayException e) {
            log.error("Create balance pay failed with adapay error: {},{}", e.getCode(), e.getMessage());
            record.setErrorCode(e.getCode());
            record.setErrorMsg(e.getMessage());
            record.setStatus(HuifuCashStatus.PAY_FAILED.getValue());
        } catch (Exception e) {
            log.error("Create balance pay failed", e);
            record.setErrorCode("error");
            record.setErrorMsg("未知错误，请检查系统日志");
            record.setStatus(HuifuCashStatus.ERROR.getValue());
        }

        try {
            cashoutHuifuDao.save(record);
        } catch (Exception e) {
            log.error("save record error");
            if (committed) {
                //数据库发生错误， 确认并且回退余额
                log.info("refund balance pay:{}, {}", record.getPayId(), record.getAmount());
                refundBalancePay(appId, record.getPayId(), "系统数据库错误，回滚余额支付");
            }
        }

        return committed;
    }


    private boolean createCashout(CashoutHuifuRecord record) {
        // 每天0点到6点禁止提现
        int hour = new DateTime().getHourOfDay();
        if (hour >= 0 && hour < 6) {
            return false;
        }

        // 检查结算账号是否OK,如果不存在，则需要创建
        SpringContext.getBean(HuifuPayService.class).ensureSettleAccount(record.getMemberId());

        log.info("create cashout:{},{},{}", record.getOrderId(), record.getMemberId(), record.getAmount());
        try {
            String cashoutId = this.createCashout(getCurrentHuifuAppId(), record.getOrderId(), record.getMemberId(), record.getAmount());
            record.setCashoutId(cashoutId);
            record.setStatus(HuifuCashStatus.CASH_PENDING.getValue());
            cashoutHuifuDao.saveOrUpdate(record);
            log.info("commit cashout success");
            return true;
        } catch (BaseAdaPayException e) {
            log.error("Create cashout failed with adapay error: {},{}", e.getCode(), e.getMessage());
            record.setErrorCode(e.getCode());
            record.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            log.error("Create cashout failed", e);
            record.setErrorCode("error");
            record.setErrorMsg("unknown, check for system log");
        }
        updateCashoutStatusByQuery(record);
        return false;
    }

    private HuifuCashRecord createModelFromCashoutRecord(CashoutHuifuRecord record) {
        HuifuCashRecord model = new HuifuCashRecord();
        model.setStatus(record.getStatus());
        model.setOrderId(record.getOrderId());
        model.setErrCode(record.getErrorCode());
        model.setErrMsg(record.getErrorMsg());
        return model;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void ensureSettleAccount(String memberId) {
        String appId = getCurrentHuifuAppId();
        int appIdInt = getCurrentHuifuAppIdInt();

        CashoutHuifuUserEntity user = cashoutHuifuDao.findUserByMemberIdAndAppId(memberId, appIdInt);
        if (user == null) {
            return;
        }

        String settleId = user.getSettleId();
        try {
            if (settleId == null || !querySettleAccount(appId, memberId, settleId)) {
                log.info("settle account for member {} not exists, create one", memberId);
                settleId = createSettleAccount(appId, memberId, user.getCardId(), user.getName(), user.getPhone(), user.getCertId());
                user.setSettleId(settleId);
                cashoutHuifuDao.saveOrUpdate(user);
                log.info("settle account created, {}, {}", memberId, settleId);
            }
        } catch (Exception e) {
            log.error("ensure settle account failed", e);
        }
    }

    private String generateOrderId() {
        return BusinessOrderId.generateOrderId(BusinessOrderId.HUIFU);
    }


    private void settleLock() {
        log.info("Try to get settle lock");
        if (!redisDistributedLockFactory.getLock(SETTLE_OPERATION_LOCK, SETTLE_LOCK_INTERVAL).lock()) {
            throw new ResponseException(ResponseError.E_SERVER, "Unable to get lock");
        }
        log.info("Settle lock got");
    }


    //绑定结算账户
    public String createSettleAccount(String appId, String memberId, String cardId, String cardName, String phone, String idCard) throws BaseAdaPayException {
        log.info("create settle account:{},{},{},{},{}", appId, memberId, cardId, cardName, phone);
        settleLock();


        Map<String, Object> settleCountParams = new HashMap<>(4);
        Map<String, Object> accountInfo = new HashMap<>(9);
        settleCountParams.put("member_id", memberId);
        accountInfo.put("card_id", cardId);
        accountInfo.put("card_name", cardName);
        accountInfo.put("cert_id", idCard);
        accountInfo.put("cert_type", "00");
        accountInfo.put("tel_no", phone);
        accountInfo.put("bank_acct_type", "2");  //对私
        // accountInfo.put("bank_code","********");
        // accountInfo.put("prov_code","0031");
        // accountInfo.put("area_code","3100");
        settleCountParams.put("app_id", appId);
        settleCountParams.put("channel", "bank_account");
        settleCountParams.put("account_info", accountInfo);

        Map<String, Object> settleAccount = SettleAccount.create(settleCountParams, appId);
        //{"member_id":"test_1","create_time":"**********","prod_mode":"true","channel":"bank_account","account_info":{"bank_code":"********","cert_type":"00","tel_no":"1581023****","area_code":"3100","card_name":"肖谋","bank_name":"","cert_id":"****************55","bank_acct_type":"2","prov_code":"0031","card_id":"******************5"},"id":"****************","app_id":"app_571259a4-281e-4fbc-a818-a85005e4ed57","object":"settle_account","status":"succeeded"}
        // {"error_msg":"结算账户已存在","error_type":"invalid_request_error","prod_mode":"true","error_code":"account_exists","status":"failed"}
        // {"error_msg":"银行卡实名认证失败，请确认银行卡与身份证是否匹配","error_type":"invalid_request_error","prod_mode":"true","error_code":"card_realm_certify_error","status":"failed"}
        log.info("create settlement account result: {}", settleAccount);
        String status = (String) settleAccount.get("status");
        if (!HuifuTransactionStatus.isSucceeded(status)) {
            String errorMsg = (String) settleAccount.get("error_msg");
            String errorCode = (String) settleAccount.get("error_code");
            log.error("Create settlement account failed,{},{}", errorCode, errorMsg);
            throw new BaseAdaPayException(errorCode, errorMsg);
        }
        return (String) settleAccount.get("id");

    }

    public void deleteSettleAccount(String appId, String memberId, String settleId) throws BaseAdaPayException {
        log.info("Delete settle account:{},{},{}", appId, memberId, settleId);
        settleLock();
        Map<String, Object> settleCountParams = new HashMap<>(2);
        settleCountParams.put("settle_account_id", settleId);
        settleCountParams.put("member_id", memberId);
        settleCountParams.put("app_id", appId);
        Map<String, Object> settleCount = SettleAccount.delete(settleCountParams, appId);
        if (!HuifuTransactionStatus.isSucceeded((String) settleCount.get("status"))) {
            String errorMsg = (String) settleCount.get("error_msg");
            String errorCode = (String) settleCount.get("error_code");
            if ("account_not_exists".equals(errorCode))
                return;

            log.error("Delete settlement account failed,{},{}", errorCode, errorMsg);
            throw new BaseAdaPayException(errorCode, errorMsg);
        }
    }

    public boolean querySettleAccount(String appId, String memberId, String settleId) throws BaseAdaPayException {
        Map<String, Object> settleCountParams = new HashMap<>(3);
        settleCountParams.put("settle_account_id", settleId);
        settleCountParams.put("member_id", memberId);
        settleCountParams.put("app_id", appId);
        try {
            Map<String, Object> settleCount = SettleAccount.query(settleCountParams, appId);
            if (HuifuTransactionStatus.isSucceeded((String) settleCount.get("status"))) {
                return settleCount.get("id") != null;
            }
        } catch (BaseAdaPayException e) {
            if ("account_not_exists".equals(e.getCode())) {
                return false;
            }
            log.error("Query settlement account failed:{},{}", e.getCode(), e.getMessage());
            throw e;
        }
        return false;
    }

    //创建会员账号
    public boolean createMember(String appId, String memberId, String phone, String realName, String idCard) throws BaseAdaPayException {
        log.info("Create huifu member:{}, {}, {}, {}", memberId, phone, realName, idCard);

        Map<String, Object> memberParams = new HashMap<>(10);
        memberParams.put("adapay_func_code", "members.realname");

        memberParams.put("member_id", memberId);
        memberParams.put("app_id", appId);
        memberParams.put("tel_no", phone);
        memberParams.put("user_name", realName);
        memberParams.put("cert_type", "00");
        memberParams.put("cert_id", idCard);
        Map<String, Object> member = AdapayCommon.requestAdapay(memberParams, appId);
        log.info("Create member result: {}", member);
        String status = (String) member.get("status");
        if (!HuifuTransactionStatus.isSucceeded(status)) {
            String errorMsg = (String) member.get("error_msg");
            String errorCode = (String) member.get("error_code");
            if ("member_id_exists".equals(errorCode)) {

            }
            log.error("Create member failed,{},{}", errorCode, errorMsg);
            throw new BaseAdaPayException(errorCode, errorMsg);
        }
        return true;

        // {"member_id":"test_2","created_time":"1712826022","cert_type":"00","identified":"Y","tel_no":"15810236820","user_name":"肖谋","prod_mode":"true","cert_id":"**********08098455","disabled":"N","app_id":"app_571259a4-281e-4fbc-a818-a85005e4ed57","object":"member","status":"succeeded"}
        // {"error_msg":"member_id已存在","error_type":"invalid_request_error","prod_mode":"true","error_code":"member_id_exists","status":"failed"}
    }

    public Member queryMember(String appId, String memberId) {
        Map<String, Object> memberParams = new HashMap<>(2);
        memberParams.put("member_id", memberId);
        memberParams.put("app_id", appId);
        try {
            Map<String, Object> member = Member.query(memberParams, appId);
            log.info("Query member result: {}", member);
            String retMemberId = (String) member.get("member_id");
            if (retMemberId == null)
                return null;

            Member ret = new Member();
            ret.setMemberId(retMemberId);
            return ret;
            //Member ret =  JsonUtils.fromString(s, Member.class);
            //if( ret == null || ret.getMemberId() == null )
            //    return null;
            //return ret;
        } catch (BaseAdaPayException e) {
            log.error("Query member failed,{},{}", e.getCode(), e.getMessage());
        }

        return null;
    }

    public void updateMember(String appId, String memberId, String phone, String realName, String idCard) throws BaseAdaPayException {
        log.info("Update huifu member:{}, {}, {}, {}", memberId, phone, realName, idCard);
        Map<String, Object> memberParams = new HashMap<>(10);
        memberParams.put("member_id", memberId);
        memberParams.put("app_id", appId);
        memberParams.put("tel_no", phone);
        memberParams.put("user_name", realName);
        memberParams.put("cert_type", "00");
        memberParams.put("cert_id", idCard);

        Map<String, Object> member = Member.update(memberParams, appId);
        log.info("Update member result: {}", member);
        if (!HuifuTransactionStatus.isSucceeded(member.get("status").toString())) {
            String errorMsg = (String) member.get("error_msg");
            String errorCode = (String) member.get("error_code");
            log.error("Update member failed,{},{}", errorCode, errorMsg);
            throw new BaseAdaPayException(errorCode, errorMsg);
        }
    }


    //余额支付
    public String createBalancePay(String appId, String orderId, String memberId, long amount) throws BaseAdaPayException {

        //TODO: 校验amount
        if (amount <= 0 || amount > MAX_AMOUNT_PER_TRANS) {
            throw new RuntimeException("提现金额太大");
        }
        String amountStr = new BigDecimal(amount).divide(new BigDecimal("100"), 2, RoundingMode.DOWN).toString();

        log.info("create balance pay: {}, {}, {}", appId, orderId, amountStr);


        Map<String, Object> balanceParam = new HashMap<>(4);
        balanceParam.put("app_id", appId);
        balanceParam.put("adapay_func_code", "settle_accounts.balancePay");
        balanceParam.put("order_no", orderId);
        balanceParam.put("out_member_id", "0");
        balanceParam.put("in_member_id", memberId);
        balanceParam.put("trans_amt", amountStr);
        balanceParam.put("goods_title", "提现");
        balanceParam.put("goods_desc", "提现");

        Map<String, Object> paymentResult = AdapayCommon.requestAdapay(balanceParam, appId);
        //{"order_no":"**************","prod_mode":"true","trans_amt":"0.99","app_id":"app_571259a4-281e-4fbc-a818-a85005e4ed57","balance_seq_id":"0022120240411190840970624454040254431232","fee_amt":"0.00","status":"succeeded"}
        log.info("{}", paymentResult);
        String status = (String) paymentResult.get("status");
        if (HuifuTransactionStatus.isSucceeded(status)) {
            return (String) paymentResult.get("balance_seq_id");
        }

        //这个状态不可能出现
        //if (HuifuTransactionStatus.isPending(status)) {
        //    return null;
        //}

        String errorMsg = (String) paymentResult.get("error_msg");
        String errorCode = (String) paymentResult.get("error_code");
        log.error("Create balance pay failed,{},{}", errorCode, errorMsg);
        throw new BaseAdaPayException(errorCode, errorMsg);
    }

    public void refundBalancePay(String appId, String payId, String reason) {
        String orderId = BusinessOrderId.generateOrderId(BusinessOrderId.HUIFU_REFUND);
        Map<String, Object> refundParam = new HashMap<>(4);
        refundParam.put("adapay_func_code", "settle_accounts.balanceRefund");
        refundParam.put("balanec_refund_no", orderId);
        refundParam.put("balance_seq_id", payId);
        refundParam.put("reason", reason);

        try {
            Map<String, Object> refundResult = AdapayCommon.requestAdapay(refundParam, appId);
            log.info("refund balance pay result:{}", refundResult);
        } catch (BaseAdaPayException e) {
            log.error("refund balance pay failed with adapay error: {},{}", e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("refund balance pay failed", e);
        }

    }

    //提现操作
    public String createCashout(String appId, String orderId, String memberId, long amount) throws BaseAdaPayException {
        String amountStr = new BigDecimal(amount).divide(new BigDecimal("100"), 2, RoundingMode.DOWN).toString();
        Map<String, Object> cashParam = new HashMap<>(5);
        cashParam.put("order_no", orderId);
        cashParam.put("app_id", appId);
        cashParam.put("cash_type", "DM");
        cashParam.put("cash_amt", amountStr);
        cashParam.put("member_id", memberId);

        Map<String, Object> cash = Drawcash.create(cashParam, appId);
        //{"member_id":"test_1","order_no":"20240411192000","created_time":"1713195275","real_amt":"0.99","prod_mode":"true","cash_amt":"0.99","cash_type":"DM","id":"0021110625970511252275200","app_id":"app_571259a4-281e-4fbc-a818-a85005e4ed57","fee_amt":"0.00","object":"cash","status":"pending"}
        log.info("cash result:{}", cash);
        String id = (String) cash.get("id");
        if (id != null) {
            return (String) cash.get("id");
        }

        String errorMsg = (String) cash.get("error_msg");
        String errorCode = (String) cash.get("error_code");
        log.error("Create cashout failed,{},{}", errorCode, errorMsg);
        throw new BaseAdaPayException(errorCode, errorMsg);
    }

    /**
     * 查询提现状态，
     */
    public HuifuQueryCashout queryCashout(String appId, String orderId) {
        Map<String, Object> queryCashParam = new HashMap<>(2);
        queryCashParam.put("order_no", orderId);
        try {
            Map<String, Object> cash = Drawcash.query(queryCashParam, appId);
            //{"cash_list":[{"cash_id":"0021110625973506043891712","trans_stat":"S","cash_amt":"0.99"}],"prod_mode":"true","status":"succeeded"}
            log.info("query cashout returns: {}", cash);
            return JsonUtils.fromString(JsonUtils.toString(cash), HuifuQueryCashout.class);

        } catch (Exception e) {
            log.error("Error query cashout", e);
        }
        return null;
    }

    public void queryBalance(String appId, String memberId) {
        Map<String, Object> queryParams = new HashMap<String, Object>(5);
        //queryParams.put("settle_account_id", settle_account_id);
        queryParams.put("member_id", memberId);
        queryParams.put("app_id", appId);
        try {
            Map<String, Object> settleCount = SettleAccount.balance(queryParams, appId);
            //{"acct_balance":"0.00","frz_balance":"0.00","prod_mode":"true","last_avl_balance":"0.00","avl_balance":"0.00","app_id":"app_571259a4-281e-4fbc-a818-a85005e4ed57","object":"account_balance","status":"succeeded"}
            log.info("balance:{}", settleCount);
        } catch (Exception e) {
            log.error("Error query balanace", e);
        }
    }


    static class AliPayHuifuProvider implements AlipayProvider {
        private final RechargeThirdHuifuApp app;
        private final HuifuPayService payService;
        private Map<String, Object> paymentParams;

        public AliPayHuifuProvider(RechargeThirdHuifuApp app, HuifuPayService payService) {
            this.app = app;
            this.payService = payService;
        }

        public void setPaymentParams(Map<String, Object> paymentParams) {
            this.paymentParams = paymentParams;
        }

        @Override
        public void setNotifyUrl(String notifyUrl) {
        }

        @Override
        public void setReturnUrl(String returnUrl) {

        }

        @Override
        public AlipayOrderExecuteResult execute() {
            try {
                log.info("支付交易，请求参数：" + JSON.toJSONString(paymentParams));
                Map<String, Object> payment = Payment.create(paymentParams, this.app.getHuifuAppId());
                log.info("执行结果:{}", JSON.toJSONString(payment));
                // {"order_no":"jsdk_payment_1703209768982","created_time":"**********","party_order_id":"02212312223536998619749","pay_amt":"0.01","expend":{"pay_info":"https://qr.alipay.com/bax02739ysfbatmaezn62541"},"prod_mode":"true","pay_channel":"alipay","query_url":"https://api.adapay.tech/v1/expire/payments/1/14c6d12e47042352948e864f09b07a35","id":"002212023122209492910584088263242919936","app_id":"app_571259a4-281e-4fbc-a818-a85005e4ed57","object":"payment","status":"succeeded"}
                String status = (String) payment.get("status");
                if ("succeeded".equals(status)) {
                    JSONObject jsonObject = (JSONObject) payment.get("expend");
                    String payInfo = jsonObject.getString("qrcode_url");
                    if (payInfo == null) {
                        log.error("支付失败,无法获取支付宝支付地址");
                        throw new ResponseException(ResponseError.E_THIRD_ERROR, "支付失败");
                    }

                    payService.saveOrderInfo(payment);
                    return new AlipayOrderExecuteResult(payInfo, AliPayProtocol.QR_CODE);
                } else {
                    log.error("支付失败，返回结果:{}", status);
                    throw new ResponseException(ResponseError.E_THIRD_ERROR, "支付失败");
                }
            } catch (BaseAdaPayException e) {
                log.error("请求支付失败", e);
                throw new ResponseException(ResponseError.E_THIRD_ERROR, "支付失败");
            }
        }


    }


    static class WeixinPayHuifuProvider implements WeixinPayProvider {
        private final RechargeThirdHuifuApp app;
        private final HuifuPayService payService;
        private Map<String, Object> paymentParams;

        public WeixinPayHuifuProvider(RechargeThirdHuifuApp app, HuifuPayService payService) {
            this.app = app;
            this.payService = payService;
        }

        public void setPaymentParams(Map<String, Object> paymentParams) {
            this.paymentParams = paymentParams;
        }


        @Override
        public void setNotifyUrl(String notifyUrl) {

        }

        @Override
        public void setReturnUrl(String returnUrl) {

        }

        @Override
        public Map<String, String> execute() {
            try {
                log.info("支付交易，请求参数：" + JSON.toJSONString(paymentParams));
                Map<String, Object> payment = Payment.create(paymentParams, this.app.getHuifuAppId());
                log.info("执行结果:{}", JSON.toJSONString(payment));
                String status = (String) payment.get("status");
                if ("succeeded".equals(status)) {
                    JSONObject jsonObject = (JSONObject) payment.get("expend");
                    String ret = jsonObject.getString("pay_info");
                    if (ret == null) {
                        log.error("支付失败,无法获取微信支付信息");
                        throw new ResponseException(ResponseError.E_THIRD_ERROR, "支付失败");
                    }
                    payService.saveOrderInfo(payment);
                    log.info("Pay Info:{}", ret);
                    return JsonUtils.fromString(ret, new TypeReference<>() {
                    });
                } else {
                    log.error("支付失败，返回结果:{}", status);
                    throw new ResponseException(ResponseError.E_THIRD_ERROR, "支付失败");
                }
            } catch (BaseAdaPayException e) {
                log.error("请求支付失败", e);
                throw new ResponseException(ResponseError.E_THIRD_ERROR, "支付失败");
            }
        }
    }


    public static void main(String[] args) throws IOException, BaseAdaPayException {

        //String data = "{\"app_id\":\"app_571259a4-281e-4fbc-a818-a85005e4ed57\",\"created_time\":\"20231222172729\",\"end_time\":\"20231222172837\",\"id\":\"002212023122217272810584203519093334016\",\"order_no\":\"20231222172728131209592224714505\",\"out_trans_id\":\"2023122222001434501416005836\",\"party_order_id\":\"02212312226284911417709\",\"pay_amt\":\"0.20\",\"pay_channel\":\"alipay\",\"status\":\"succeeded\"}";
        //HuifuNotificationData notificationData = new ObjectMapper().readValue(data, HuifuNotificationData.class);
        //log.info("{}", notificationData);
        /*
        Date dt = new Date(Long.parseLong("1703576517")*1000);
        log.info("{}", dt);

        String payInfo = "{\"appId\":\"wxc5060ccd0c57d8e1\",\"timeStamp\":\"1706084406\",\"nonceStr\":\"2aaeeef73cef45fda0e0c4c52aa6894f\",\"package\":\"prepay_id=wx24162006011628f53f36b26881273e0000\",\"signType\":\"RSA\",\"paySign\":\"lWZqTeAJ3kqSfVA6LJ1uoQRapB901bzrlp4NIoyBRIVbsIhAi98OtkNnyMno1A77OLgWJN0SnL2UrqQjQLGoqEadk58kiFDaAVO9DzMlN80Ka78vZWvWvV9QVJwmIIMmDgibobMic6KCU94Nfz0vFxTiQpINwSRDZ6G7hI7xDPCSwRoSxZGx+1FR4HaYWfe/pbEaaiWLunlJskp+tFC9UqUO6rYJ4szJ1VHneLT87fomgZK7EShD9o3yPCmiqpIBmMR6hdUBb8MzXBsBPRmu3zw4b7H5l24QhVpGgviGC681qdc4a/fNq+ZrDqvJhL2o+IHoCyhEDBV9BS70J0/wdQ==\"}";
        log.info(payInfo);
        Map<String,String> ret = JsonUtils.fromString(payInfo, new TypeReference<>() {});
        log.info("{}",ret);
         */

        Map<String, MerConfig> configPathMap = new HashMap<>();
        String appId = "app_571259a4-281e-4fbc-a818-a85005e4ed57";

        MerConfig config = new MerConfig();
        config.setApiKey("api_live_c655e573-96a0-422c-a7de-529e43328276");
        config.setRSAPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDG7pU9Z3KEJcnrwGebwuMyXXpGdmNNnzikL1goJiogOzMGDMr24bngwZ0wmVRUFgQ7eaTK1xdV4E7L/h1y3ejzPLmEfSLGB/NdI8S/kg4AcGMFyzrYxeRY1TJ0JokQMy1Eoeug9p17Tyb2gVZOjpSARFoz0G4shMoec1zYHGnE4alczRAtFk/MvCwCvRgUPREfKaecNcfC3CuZbh1PXPBRS0w3D2PTP8bCuBOmQIHiJ22BTf7UAE/I4zl8QYWMtg65R8PU3fGjZ7ZggfVJWoLGCw1pRpYDwm8gKStgcn2cD1ztj3BvBKzFUha2SiITpc5RLdcPiOgmyclG99DJcdANAgMBAAECggEAD6aNjzsoQ0VF0YpdnF+EyF2aVRjTTzQLnL8szFvUTJzbssom3a18ljYlS8GEZOkmhBdZ46twZo0zJdFkN7lFrn1EiyvdJH9ToixCOmAfQeGVqMsIYi2PfX2oboLpnaCpGxEyHIPKd/0OL66VP7Dv9fdqsVDgmhgIXsTstQPnPlnvDs35LczqqlXmwAu89nloSdsQpqQ+BFBSdBZxS3/i1KywuzgBJxLk7G1aBh8p8FFwlSpfNsaDxOHo2y4D/I4E3RZeUhmBlsQYGol/yve7p5KIFVLICnjdICV/tplBKoUFYY9gjnTZwhoxcbVnfVqWs8Q8ai2QkMqV1YeynyTXAQKBgQDtnN2Hxo8HfmlzUD87d69BicI9/0eODf9PlHZwN5qmF78WLlcIGXkfFp3pMSM+57blOgB2vxYKhvBJ4gsG2fB9XRw7I/2a872gZfM7UMLvg9q5vWA9cqQ5F88HHeyIo37IwHAtNPKV1nUgOTdMvqQS/XLaPBHYK3bwC5y9R5ThPQKBgQDWU3Jp06gHZlN2RFd7L5n8tAVgZHuZNQtk1kBL4RWWdfJsDwn3Ry3t0CKVh+w0G9X5WmuzUf/Kv8fArYiPQN9Q27YwSgId3z7lY4oK+mcvMmP7GfuHYAScdj+JJUfOQSUjo3PDPC945Yf7qDpLwu4RNJo1gb2hC0EK4ISTqgH3EQKBgQCW/UA0YDNRMr31fb/AnSwrvEtHNCmXFkPvoMqmQl8uPha6FJZPWkiCPc/4d7YK6hlpjjLvQjvSUq7sdAxHvUO5leozloo35bzQ6Rfn0EkfBC2AK9u1EF1UvlFbF2PP6/zLuqVNxMCGAHILGPCP/vCp9I4AKX25XN1GakqeaJctjQKBgQDF0hvGhe+PdOkQPiAouVT+JALaZ6SVUpqDpiWOY+CTEaaCCpd+P5qq5/Mj5WMk7ZRgJQ9HYiYgs0biFsn2vz/f2AufIpA1ljrtaySc4Waf73jM6bx5xSw8ZgBay6gtPX/UFyfgSgrfIayToUC9cNxf0ReMz4WBwWt1aKm1ybMqoQKBgGTetNfpuRctiaYSgizluVqx0iDid8F5Z6H7LiCOjVAnyCafl+XO9qAC/g3pyATP4fZAylHqaVhA0un7slQTad/Z7r3s+tAimiuUNdByv3i3eOucEAGlBc+zV6Pnzals/GNzgZFQ2AY2JfT2OEFtEScKl7AmrKvX1Rg3Rx6iIwC2");
        config.setApiMockKey("api_test_f38eb03d-2039-4220-a1da-1d96f25eb61c");
        configPathMap.put(appId, config);

        try {
            /**
             * debug 模式，开启后有详细的日志
             */
            Adapay.debug = true;

            /**
             * prodMode 模式，默认为生产模式，false可以使用mock模式
             */
            Adapay.prodMode = true;
            Adapay.initWithMerConfigs(configPathMap);
        } catch (Exception e) {
            log.error("初始化汇付天下失败", e);
            throw new ResponseException(ResponseError.E_THIRD_ERROR, "初始化汇付天下失败");
        }

        HuifuPayService payService = new HuifuPayService();

        log.info("memberId: {}", payService.getMemberId(1));

        String orderId = payService.generateOrderId();

        payService.createMember(appId,"on_4010002_1","13389229050", "周逸文", "610102198810220020");

        //payService.createBalancePay(appId, orderId,"test_2", 1);
        //payService.createCashout(appId, orderId,"test_2", 1);
        //payService.queryCashout(appId, orderId);
        // log.info("Query result:{}", payService.queryCashout(appId, "00220240426130300380132961"));
        //Member member = payService.queryMember(appId, "test_3");
        //log.info("Member:{}", member);

        //payService.queryBalance(appId, "test_2");

        //payService.deleteSettleAccount(appId, "test_9508107_5794","****************");
        //payService.querySettleAccount(appId, "test_9508107_0367", "****************");
        //log.info("exists: {}", payService.querySettleAccount(appId, "test_9508107_8224", "****************"));
        //log.info("exists: {}", payService.querySettleAccount(appId, "test_9508107_0367", "****************"));
        Member member = payService.queryMember(appId, "on_11326441");
        log.info("{}", member);

        HuifuQueryCashout cashout = payService.queryCashout(appId, "001202405111121551208");
        log.info("{}", cashout);
        log.info("OK");
    }
}
