package com.easylive.pay.service.huifu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */


@Data
public class HuifuQueryCashout {
    private String status;

    @JsonProperty("error_code")
    private String errorCode;

    @JsonProperty("error_msg")
    private String errorMsg;

    @JsonProperty("cash_list")
    private List<HuifuCashoutObject> cashList;

    @Data
    public static class HuifuCashoutObject {
        @JsonProperty("cash_id")
        private String cashId;

        @JsonProperty("cash_amt")
        private String cashAmt;

        @JsonProperty("trans_stat")
        private String transStat;
    }
}
