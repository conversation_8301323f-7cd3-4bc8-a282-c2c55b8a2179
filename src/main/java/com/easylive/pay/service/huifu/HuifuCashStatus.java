package com.easylive.pay.service.huifu;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
@Getter
public enum HuifuCashStatus {
    CREATED(0),
    PAY_SUCCESS(1),
    PAY_FAILED(2),
    CASH_PENDING(3),
    CASH_SUCCESS(4),
    CASH_FAILED(5),
    ERROR(6);

    private final int value;
    HuifuCashStatus(int value) {
        this.value = value;
    }

    public static HuifuCashStatus fromValue(int value) {
        for (HuifuCashStatus cashStatus : HuifuCashStatus.values()) {
            if (cashStatus.value == value) {
                return cashStatus;
            }
        }
        return null;
    }
}
