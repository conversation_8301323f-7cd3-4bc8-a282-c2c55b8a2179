package com.easylive.pay.service.huifu;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
public enum HuifuUserOperateType {
    CREATE(0),
    UPDATE(1 ),
    DELETE(2);

    private final int value;
    HuifuUserOperateType(int value) {
        this.value = value;
    }

    public static HuifuUserOperateType fromValue(int value) {
        for (HuifuUserOperateType cashStatus : HuifuUserOperateType.values()) {
            if (cashStatus.value == value) {
                return cashStatus;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}
