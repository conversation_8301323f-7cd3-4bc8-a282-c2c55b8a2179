package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.dao.ConfigDao;
import com.easylive.pay.entity.PayConfig;
import com.easylive.pay.enums.ConfigDataType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@Service
public class ConfigService {

    private Map<String, PayConfig> config = new HashMap<>();

    @Autowired
    private ConfigDao configDao;


    @PostConstruct
    private void init() {
        reload();
    }

    public synchronized void reload() {
        Map<String, PayConfig> newConfig = new HashMap<>();
        for (PayConfig configEntity : configDao.findAll()) {
            newConfig.put(configEntity.getItem(), configEntity);
        }
        this.config = newConfig;
    }

    public Map getAll() {
        return this.config;
    }

    public String getAssetSpecialChannel() {
        return get(Constants.CONFIG_ASSET_SPECIAL_CHANNEL, String.class);
    }

    public String getAppleSandboxUids() {
        return get(Constants.CONFIG_APPLE_SANDBOX_UIDS, String.class);
    }

    public String getHuifuCashAppId() {
        return get(Constants.CONFIG_HUIFU_CASHOUT_APPID, String.class);
    }

    public String getYunZhangHuCashoutAppId() {
        return get(Constants.CONFIG_YZH_CASHOUT_APPID, String.class);
    }

    public boolean isCashoutEnabled() {
        try {
            BigInteger val = get(Constants.CONFIG_ENABLE_CASHOUT, BigInteger.class);
            return val != null &&  val.equals(BigInteger.ONE);
        } catch (Exception e) {
            log.error("Check cashout enabled error", e);
            return true;
        }
    }

    public String getAppleSandboxWhitelistVersion() {
        return get(Constants.CONFIG_APPLE_SANDBOX_WHITELIST, String.class);
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        PayConfig entity = config.get(key);
        if (entity != null) {
            Class c = ConfigDataType.getDataTypeClass(entity.getType());
            if (c.equals(type)) {
                try {
                    return (T) ConfigDataType.convert(entity.getValue(), entity.getType());
                } catch (Exception e) {
                    throw new ResponseException(ResponseError.E_PARAM, "Unable to convert type");
                }
            } else {
                throw new ResponseException(ResponseError.E_PARAM, "Type mismatch");
            }
        }

        return null;
    }

    @Transactional
    public void set(String key, String value) {
        PayConfig entity = config.get(key);
        if (entity == null)
            return;

        try {
            ConfigDataType.convert(value, entity.getType());
            entity.setValue(value);
            configDao.saveOrUpdate(entity);
        } catch (Exception e) {
            throw new ResponseException(ResponseError.E_PARAM, "unable to convert type");
        }
    }

    @Transactional
    public <T> void set(String key, T value) {
        PayConfig entity = config.get(key);
        if (entity == null)
            return;

        Class c = ConfigDataType.getDataTypeClass(entity.getType());
        if (c.equals(value.getClass())) {
            entity.setValue(value.toString());
            configDao.saveOrUpdate(entity);
        } else {
            throw new ResponseException(ResponseError.E_PARAM, "Type mismatch");
        }

    }

}

