package com.easylive.pay.service;


import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.dao.GameDao;
import com.easylive.pay.dao.UserCoinDao;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.AssetSystemID;
import com.easylive.pay.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.Date;

@Service
public class GameService {
    private static Logger logger = LoggerFactory.getLogger(GameService.class);

    @Autowired
    GameDao gameDao;

    @Autowired
    private AssetService assetService;

    @Autowired
    private GiftService giftService;

    @Autowired
    private UserCoinDao userCoinDao;

    public DdzBeanPool getTotalBean() {
        return gameDao.getTotal(1);
    }

    private void addBeanPool(DdzBeanPool totalBean, long bean) {
        totalBean.setTotalBean(totalBean.getTotalBean().add(new BigInteger(String.valueOf(bean))));
        gameDao.saveOrUpdate(totalBean);
    }


    private void decBeanPool(DdzBeanPool totalBean, long bean) {
        totalBean.setTotalBean(totalBean.getTotalBean().subtract(new BigInteger(String.valueOf(bean))));
        gameDao.saveOrUpdate(totalBean);
    }

    @Transactional
    public void exchangeBeanToEcoin(long bean, long coin, User user, int type) {
        long uid = user.getId();
        DdzBeanPool totalBean = this.getTotalBean();
        BigInteger totalBeanNum = totalBean.getTotalBean();

        if (totalBeanNum.compareTo(BigInteger.valueOf(bean)) < 0) {
            logger.error("bean number error,bean: {}  total bean:", bean, totalBeanNum);
            throw new ResponseException(ResponseError.E_GAME_TOTAL_BEAN_NOT_ENOUGH);
        }

        String tid;
        try {
            tid = assetService.unaryOperate(AssetSystemID.GAME_BEAN_TO_ECOIN, user, coin).getTid();
        } catch (Exception e) {
            logger.error("add ecoin error. uid:" + uid + " coin:" + coin);
            throw new ResponseException(ResponseError.E_GAME_EXCHANGE_FAILD);
        }

        this.decBeanPool(totalBean, bean);

        this.addDdzEcoinRecord(tid, user, coin, type, BigInteger.valueOf(bean));
    }

    @Transactional
    public void exchangeEcoinToBean(long bean, long coin, User user, int type) {
        DdzBeanPool totalBean = this.getTotalBean();
        String tid = assetService.unaryOperate(AssetSystemID.GAME_ECOIN_TO_BEAN, user, coin).getTid();
        this.addBeanPool(totalBean, bean);
        this.addDdzEcoinRecord(tid, user, coin, type, BigInteger.valueOf(bean));
    }

    @Transactional
    public void addBeanCostRecord(String tid, String name, long uid, int gameId, long cost) {
        DdzBeanCostRecord beanCostRecord = new DdzBeanCostRecord();
        beanCostRecord.setTid(tid);
        beanCostRecord.setCost(cost);
        beanCostRecord.setCreateTime(new Date());
        beanCostRecord.setGameId(gameId);
        beanCostRecord.setType(2); // 固定值， 是什么?
        beanCostRecord.setUid(uid);
        beanCostRecord.setUname(name);
        this.gameDao.save(beanCostRecord);
    }

    @Transactional
    public void addDdzEcoinRecord(String tid, User user, long coin, Integer type, BigInteger bean) {
        long uid = user.getId();

        UserCoin currentUserCoin = assetService.getByUid(uid);
        long ecoin = currentUserCoin.getEcoin();
        DdzCoinRecord dcr = new DdzCoinRecord();
        dcr.set(user.getName(), uid, coin, type, BigInteger.valueOf(ecoin), bean);
        dcr.setTid(tid);
        gameDao.save(dcr);
        logger.info("add ddz ecoin record username:" + user.getName() + " type:" + type + " coin:" + String.valueOf(coin) + " currentcoin:" + String.valueOf(ecoin) + " bean:" + String.valueOf(bean));
    }

    @Transactional
    public void buyGameGift(User from, User to, Long gid, String vid, long cost, long riceroll, int gameId) {

        DdzBeanPool totalBean = this.getTotalBean();
        BigInteger totalBeanNum = totalBean.getTotalBean();

        if (totalBeanNum.compareTo(BigInteger.valueOf(cost)) < 0) {
            logger.error("buy gift error, not enough bean:  " + cost + " total bean:" + totalBeanNum.toString());
            throw new ResponseException(ResponseError.E_GAME_TOTAL_BEAN_NOT_ENOUGH);
        }


        int number = 1;
        String tid = assetService.binaryOperate(AssetSystemID.GAME_GIFT, from, 0, to, riceroll).getTid();

        Goods goods = giftService.getGoodsById(gid);
        goods.setRiceroll(riceroll);
        goods.setCost(cost);

        UserCoin fromUserCoin = assetService.getByUid(from.getId());
        UserCoin toUserCoin = assetService.getByUid(to.getId());

        giftService.afterBuy(tid, goods, from, to, number, vid, fromUserCoin, toUserCoin);

        // 修改Pool
        this.decBeanPool(totalBean, cost);
        // 添加消耗记录
        this.addBeanCostRecord(tid, from.getName(), from.getId(), gameId, cost);
    }

    @Transactional(rollbackFor = Exception.class)
    public String buyGameGift(User from, User to, long platformId, long ecoin, long cost, long gid, int number, String vid) {
        //先判断用户是否有足够的游戏币
        UserGameBean ub = userCoinDao.getUserGameBean(from.getId(), platformId);
        if (ub != null && ub.getBeanRemain() < cost) {
            throw new ResponseException(ResponseError.E_GAME_COIN_NOT_ENOUGH);
        }

        Goods goods = giftService.getGoodsById(gid);

        long riceroll = goods.getRiceroll() * number;
        String tid = assetService.binaryOperate(AssetSystemID.GAME_GIFT, from, 0, to, riceroll).getTid();

        UserCoin fromUserCoin = assetService.getByUid(from.getId());
        UserCoin toUserCoin = assetService.getByUid(to.getId());

        goods.setCost(ecoin);
        giftService.afterBuy(tid, goods, from, to, number, vid, fromUserCoin, toUserCoin);

        return tid;
    }
}
