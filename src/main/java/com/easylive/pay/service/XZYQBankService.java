package com.easylive.pay.service;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.ThirdBankConfig;
import com.easylive.pay.dao.SysSettingRepository;
import com.easylive.pay.entity.CashoutRecordQueue;
import com.easylive.pay.entity.SysSettingEntity;
import com.easylive.pay.enums.BankTxSts;
import com.easylive.pay.enums.BusinessOrderId;
import com.easylive.pay.enums.SysSettingMetaKey;
import com.easylive.pay.enums.bank.BankAccountType;
import com.easylive.pay.model.MediaTypeXmlT;
import com.easylive.pay.model.cashout.*;
import com.easylive.pay.utils.EncodeUtils;
import com.easylive.pay.utils.MAC;
import com.easylive.pay.utils.XMLUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 新政银企服务
 *
 * <AUTHOR>
 * @date 2019/8/20
 */
@Service
public class XZYQBankService {

    private static final Logger logger = LoggerFactory.getLogger(XZYQBankService.class);

    private RestTemplate restTemplate;


    @Autowired
    private ThirdBankConfig thirdBankConfig;


    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SysSettingRepository sysSettingRepository;


    private static final String KEY_WITHDRAW_BANK_ACCOUNT = "withdraw:bank_account";

    /**
     * 1120 - 提现交易代码
     * 1124 - 提现结果确认
     * 1102 - 客户信息签约
     */
    private static final String CODE_WITHDRAW = "1120";
    private static final String CODE_WITHDRAW_CONFIRM_RESULT = "1124";
    private static final String CODE_COSTOMER_INFO_SIGN = "1102";

    @PostConstruct
    private void init() {
        HttpClient httpClient = HttpClients.custom().build();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(
                httpClient);
        requestFactory.setConnectionRequestTimeout(30000);
        requestFactory.setConnectTimeout(30000);
        requestFactory.setReadTimeout(120000);
        this.restTemplate = new RestTemplate(requestFactory);
        restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());
        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter(Charset.forName("GBK"));
        stringHttpMessageConverter.setWriteAcceptCharset(false);
        restTemplate.getMessageConverters().add(0, stringHttpMessageConverter);
    }

    /**
     * 3.1.1 客户工资提取
     *
     * @param withdrawBody
     * @return
     */
    public BaseResponseXML bankWithdraw(WithdrawRequestBody withdrawBody, Date commitTime, String trcNo) {
        RequestXMLHead requestXMLHead = getCommonRequestXMLHead(CODE_WITHDRAW, commitTime, trcNo, withdrawBody.getAcctountNo());
        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);
        baseRequestXML.setBody(withdrawBody);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);
        String gbkXml = EncodeUtils.utf8ToGbk(xml);
        return requestBankUrlWithXml(gbkXml);
    }

    private RequestXMLHead getCommonRequestXMLHead(String transCode, Date transTime, String trcNo, String accoutNo) {
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode(transCode);
        requestXMLHead.setTransDate(new SimpleDateFormat("yyyyMMdd").format(transTime));
        requestXMLHead.setTransTime(new SimpleDateFormat("HHmmss").format(transTime));
        requestXMLHead.setChnlType(thirdBankConfig.getChannelType());
        requestXMLHead.setTrcNo(trcNo);
        //企业代码定制化
        BankAccountType accountType = getKeyWithdrawBankAccount();
        logger.debug("[getCommonRequestXMLHead] accountType={}", accountType.name());
        if (BankAccountType.SH == accountType) {
            requestXMLHead.setEnterpriseNo(thirdBankConfig.getEnterpriseCodeNew());
        } else {
            requestXMLHead.setEnterpriseNo(thirdBankConfig.getEnterpriseCode());
        }
        requestXMLHead.setBankNo(thirdBankConfig.getBankNo());
        //生成MAC
        if (accoutNo != null) {
            String mac = getMacFromBean(requestXMLHead, accoutNo);
            requestXMLHead.setMac(mac);
        }
        return requestXMLHead;
    }

    private RequestXMLHead getCommonRequestXMLHead(String enterpriseCode, String transCode, Date transTime, String trcNo, String accoutNo) {
        RequestXMLHead requestXMLHead = new RequestXMLHead();
        requestXMLHead.setTransCode(transCode);
        requestXMLHead.setTransDate(new SimpleDateFormat("yyyyMMdd").format(transTime));
        requestXMLHead.setTransTime(new SimpleDateFormat("HHmmss").format(transTime));
        requestXMLHead.setChnlType(thirdBankConfig.getChannelType());
        requestXMLHead.setTrcNo(trcNo);
        requestXMLHead.setEnterpriseNo(enterpriseCode);
        requestXMLHead.setBankNo(thirdBankConfig.getBankNo());
        //生成MAC
        if (accoutNo != null) {
            String mac = getMacFromBean(requestXMLHead, accoutNo);
            requestXMLHead.setMac(mac);
        }
        return requestXMLHead;
    }

    /**
     * 3.1.5 客户工资提取结果确认
     *
     * @param record
     * @return
     */
    public BaseResponseXML getBankWithdrawConfirmResult(CashoutRecordQueue record) {
        RequestXMLHead requestXMLHead = this.getCommonRequestXMLHead(CODE_WITHDRAW_CONFIRM_RESULT, new Date(), generateTradeNo(), record.getAccount_no());
        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(requestXMLHead);
        //构造body
        WithdrawResultConfirmRequestBody confirmRequestBody = new WithdrawResultConfirmRequestBody();
        confirmRequestBody.setEmployeeNum(String.valueOf(record.getUid()));
        confirmRequestBody.setAcctountNo(record.getAccount_no());
        String txAmt = new DecimalFormat("#.00").format(record.getRmb() / 100d);
        confirmRequestBody.setTxAmt(txAmt);
        confirmRequestBody.setAcctName(record.getAccount_name());
        confirmRequestBody.setBankName(record.getOpen_bank_name());
        confirmRequestBody.setBankNo(record.getOpen_bank_no());
        confirmRequestBody.setOriTransDate(new SimpleDateFormat("yyyyMMdd").format(record.getCommit_time()));
        confirmRequestBody.setOriTransTime(new SimpleDateFormat("HHmmss").format(record.getCommit_time()));
        confirmRequestBody.setOriTransTraceNo(record.getOrderid());
        baseRequestXML.setBody(confirmRequestBody);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);
        String gbkXml = EncodeUtils.utf8ToGbk(xml);

        return requestBankUrlWithXml(gbkXml);
    }

    /**
     * 3.2.2 客户信息签约维护
     */
    public boolean signContract(long uid,
                                int operType,
                                String accountNo,
                                String accountName,
                                String bankNo,
                                String bankName,
                                String idNo,
                                String cellPhone) {
        //新政银村签约
        BaseResponseXML responseXML = clientInfoSignMaintain(uid, operType, accountNo, accountName, bankNo, bankName, idNo, cellPhone);
        if (responseXML.getBody().getTxSts() != null && responseXML.getBody().getTxSts().compareTo(String.valueOf(BankTxSts.SUCCESS.getCode())) == 0) {
            return true;
        } else {
            return false;
        }
    }

    public boolean signContractNew(long uid,
                                   int operType,
                                   String accountNo,
                                   String accountName,
                                   String bankNo,
                                   String bankName,
                                   String idNo,
                                   String cellPhone) {
        //新政银村签约
        BaseResponseXML responseXML = clientInfoSignMaintainNew(uid, operType, accountNo, accountName, bankNo, bankName, idNo, cellPhone);
        logger.debug("[signContractNew] txSts={}", responseXML.getBody().getTxSts());
        if (responseXML.getBody().getTxSts() != null && responseXML.getBody().getTxSts().compareTo(String.valueOf(BankTxSts.SUCCESS.getCode())) == 0) {
            return true;
        } else {
            return false;
        }
    }

    public BaseResponseXML clientInfoSignMaintain(long uid,
                                                  int operType,
                                                  String accountNo,
                                                  String accountName,
                                                  String bankNo,
                                                  String bankName,
                                                  String idNo,
                                                  String cellPhone) {
        RequestXMLHead head = this.getCommonRequestXMLHead(CODE_COSTOMER_INFO_SIGN, new Date(), generateTradeNo(), accountNo);
        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(head);
        //构造body
        AccountSignRequestBody accountSign = new AccountSignRequestBody();
        accountSign.setOperType(String.valueOf(operType));
        accountSign.setEmployeeNum(String.valueOf(uid));
        accountSign.setEmployeeName(accountName);
        accountSign.setAcctName(accountName);
        accountSign.setAcctountNo(accountNo);
        accountSign.setBankName(bankName);
        accountSign.setBankNo(bankNo);
        accountSign.setIdType("01");
        accountSign.setIdNo(idNo);
        accountSign.setCellPhone(cellPhone);

        baseRequestXML.setBody(accountSign);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);
        String gbkXml = EncodeUtils.utf8ToGbk(xml);
        return requestBankUrlWithXml(gbkXml);
    }

    public BaseResponseXML clientInfoSignMaintainNew(long uid,
                                                     int operType,
                                                     String accountNo,
                                                     String accountName,
                                                     String bankNo,
                                                     String bankName,
                                                     String idNo,
                                                     String cellPhone) {
        RequestXMLHead head = this.getCommonRequestXMLHead(thirdBankConfig.getEnterpriseCodeNew(), CODE_COSTOMER_INFO_SIGN, new Date(), generateTradeNo(), accountNo);
        BaseRequestXML baseRequestXML = new BaseRequestXML();
        baseRequestXML.setHead(head);
        //构造body
        AccountSignRequestBody accountSign = new AccountSignRequestBody();
        accountSign.setOperType(String.valueOf(operType));
        accountSign.setEmployeeNum(String.valueOf(uid));
        accountSign.setEmployeeName(accountName);
        accountSign.setAcctName(accountName);
        accountSign.setAcctountNo(accountNo);
        accountSign.setBankName(bankName);
        accountSign.setBankNo(bankNo);
        accountSign.setIdType("01");
        accountSign.setIdNo(idNo);
        accountSign.setCellPhone(cellPhone);

        baseRequestXML.setBody(accountSign);

        String xml = XMLUtils.toXML(BaseRequestXML.class, baseRequestXML);
        String gbkXml = EncodeUtils.utf8ToGbk(xml);
        return requestBankUrlWithXml(gbkXml);
    }

    public String getMacFromBean(RequestXMLHead baseXML) {
        return getMacFromBean(baseXML, null);
    }

    private String getMacFromBean(RequestXMLHead xmlHead, String accountNo) {
        /**
         * 交易日期 交易时间 交易流水 交易代码 企业机构代码 交易账号(选填)
         */
        List<String> list = new ArrayList<>();
        list.add(xmlHead.getTransDate());
        list.add(xmlHead.getTransTime());
        list.add(xmlHead.getTrcNo());
        list.add(xmlHead.getTransCode());
        list.add(xmlHead.getEnterpriseNo());
        if (accountNo != null) {
            list.add(accountNo);
        }
        String mac = StringUtils.join(list, " ");
        return MAC.getOnlineMac(mac, thirdBankConfig.getSecretKey());
    }

    public BaseResponseXML requestBankUrlWithXml(String xml) {
        try {
            logger.info("[1] Request bank start xml={}", xml);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaTypeXmlT.APPLICATION_XML_GBK);
            HttpEntity<String> httpEntity = new HttpEntity<>(xml, headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(thirdBankConfig.getBankUrl(), httpEntity, String.class);
            logger.info("[2] Response Entity = {}", responseEntity);
            String xmlBody = responseEntity.getBody();
            String utf8Xml = EncodeUtils.gbkToUtf8(xmlBody);
            return XMLUtils.xmlToBean(utf8Xml, BaseResponseXML.class);
        } catch (Exception e) {
            logger.error("[requestBankUrlWithXml] Failed, Message={}", e.getMessage());
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现服务器出错");
        }
    }

    private void setKeyWithdrawBankAccount(String account) {
        redisTemplate.opsForValue().set(KEY_WITHDRAW_BANK_ACCOUNT, String.valueOf(account), 30, TimeUnit.DAYS);
    }

    private BankAccountType getKeyWithdrawBankAccount() {
        String value = redisTemplate.opsForValue().get(KEY_WITHDRAW_BANK_ACCOUNT);
        if (value == null) {
            Optional<SysSettingEntity> sysSettingEntity = sysSettingRepository.findTopByMetaKeyAndIsDeletedIsFalse(SysSettingMetaKey.WITHDRAW_BANK_ACCOUNT.getValue());
            if (sysSettingEntity.isPresent()) {
                setKeyWithdrawBankAccount(sysSettingEntity.get().getMetaValue());
                value = sysSettingEntity.get().getMetaValue();
            } else {
                return BankAccountType.PN;
            }
        }
        int val = Integer.valueOf(value);
        BankAccountType bankType = BankAccountType.fromValue(val);
        if (bankType == null) {
            return BankAccountType.PN;
        }
        return bankType;
    }

    private String generateTradeNo() {
        return BusinessOrderId.generateOrderId(BusinessOrderId.XZYQ);
    }
}
