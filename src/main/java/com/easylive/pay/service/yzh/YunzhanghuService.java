package com.easylive.pay.service.yzh;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.apollo.ServerApiConfig;
import com.easylive.pay.dao.CashoutDao;
import com.easylive.pay.dao.jpa.YunzhanghuAppConfigRepository;
import com.easylive.pay.dao.jpa.YunzhanghuCashoutRecordRepository;
import com.easylive.pay.dao.jpa.YzhMemberRepository;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.CashoutRecordAppStatus;
import com.easylive.pay.enums.bank.BankType;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.ConfigService;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.vo.YzhUserContractVo;
import com.yunzhanghu.sdk.apiusersign.domain.ApiUserSignResponse;
import com.yunzhanghu.sdk.apiusersign.domain.GetApiUserSignStatusResponse;
import com.yunzhanghu.sdk.base.YzhConfig;
import com.yunzhanghu.sdk.payment.domain.CreateBankpayOrderResponse;
import com.yunzhanghu.sdk.payment.domain.GetOrderResponse;
import com.yunzhanghu.sdk.payment.domain.NotifyOrderData;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Service
@Slf4j
public class YunzhanghuService {

    @Autowired
    private YunzhanghuAppConfigRepository yunzhanghuAppConfigRepository;

    @Autowired
    private YunzhanghuCashoutRecordRepository yunzhanghuCashoutRecordRepository;

    @Autowired
    private YzhMemberRepository yzhMemberRepository;

    @Autowired
    private CashoutService cashoutService;

    @Autowired
    private ConfigService configService;

    @Value("${cashout.yzh.key.root-path}")
    private String yzhKeyRootPath;

    private volatile YzhUserContractVo contractInfo;

    private volatile Map<String, YzhAppEntity> appMap = new HashMap<>();
    private volatile Map<Long, YzhAppEntity> appIdMap = new HashMap<>();

    @Value("${cashout.yzh.notify-url}")
    private String notifyUrl;

    @Autowired
    private ServerApiConfig serverApiConfig;

    @Autowired
    private CashoutDao cashoutDao;


    private String getNotifyUrl() {
        return serverApiConfig.getPayCallbackDomain() + this.notifyUrl;
    }

    @PostConstruct
    public void init() {
        reload();
        updateContractInfo();
    }

    public void reload() {
        Map<String, YzhAppEntity> tmpAppConfigs = new HashMap<>();
        Map<Long, YzhAppEntity> tmpAppIdMap = new HashMap<>();
        for (YzhAppEntity appConfig : yunzhanghuAppConfigRepository.findAll()) {
            try {
                tmpAppConfigs.put(appConfig.getName(), appConfig);
                tmpAppIdMap.put(appConfig.getId(), appConfig);
                appConfig.setApi(new YunzhanghuApi(buildYzhConfig(appConfig), getNotifyUrl()));
                log.info("Add yunzhanghu app : {}", appConfig.getName());
            } catch (Exception e) {
                log.error("初始化云账户配置失败: {}", appConfig.getName(), e);
            }
        }
        appMap = tmpAppConfigs;
        appIdMap = tmpAppIdMap;
    }

    private YzhAppEntity getCurrentAppConfig() {
        String appName = configService.getYunZhangHuCashoutAppId();
        return appMap.get(appName);
    }


    /**
     * 签约用户
     * @param uid
     * @param realName
     * @param idCard
     * @return
     */
    @Transactional
    public String signUser(long uid, String realName, String idCard) {
        YzhAppEntity appConfig = getCurrentAppConfig();

        //idCard 去掉头尾空格，如果有X转为大写
        idCard = idCard.trim().toUpperCase();
        realName = realName.trim();

        //uid和idCard字符串
        String platformId = uid + "_" + idCard;

        YzhMemberEntity member = syncUserSignStatus( uid, realName, idCard);
        if( "1".equals(member.getSignStatus()) ) {
            return platformId;
        }

        YunzhanghuApi.YzhUserSignRequest request = new YunzhanghuApi.YzhUserSignRequest();
        request.setRealName(realName);
        request.setIdCard(idCard);
        ApiUserSignResponse signResponse = appConfig.getApi().signContract(request);
        if (signResponse == null || !"ok".equals(signResponse.getStatus()) ) {
            throw new ResponseException(ResponseError.E_CASHOUT, "签约失败");
        }

        saveUserSignStatus(uid, appConfig.getId(), realName, idCard, signResponse.getStatus(), new Date());
        return platformId;
    }

    /**
     * 同步签约状态
     * @param uid
     * @param realName
     * @param idCard
     * @return
     */
    @Transactional
    public YzhMemberEntity syncUserSignStatus(long uid, String realName, String idCard) {
        YzhAppEntity appConfig = getCurrentAppConfig();
        long appId = appConfig.getId();
        GetApiUserSignStatusResponse statusResponse = appConfig.getApi().queryUserSignState(realName, idCard);
        if (statusResponse == null) {
            throw new ResponseException(ResponseError.E_CASHOUT, "查询用户签约状态失败");
        }

        Date signedDate = new Date();
        String signedAt = statusResponse.getSignedAt();

        try {
            if (signedAt != null && !signedAt.isEmpty()) {
                signedDate = DateUtils.parseDateTime(signedAt);
            } else {
                signedDate = null;
            }
        } catch (Exception e) {
            log.error("解析签约时间失败: {}", signedAt, e);
        }

        return saveUserSignStatus(uid, appId, realName, idCard, statusResponse.getStatus(), signedDate);
    }

    /**
     * 保存签约记录
     * @param uid
     * @param appId
     * @param realName
     * @param idCard
     * @param signStatus
     * @param signedDate
     * @return
     */
    public YzhMemberEntity saveUserSignStatus(long uid, long appId, String realName, String idCard, String signStatus, Date signedDate) {
        YzhMemberEntity member = yzhMemberRepository.findByUidAndAppId(uid, appId).orElse(new YzhMemberEntity());
        member.setUid(uid);
        member.setAppId(appId);
        member.setRealName(realName);
        member.setIdCard(idCard);
        member.setSignStatus(signStatus);
        member.setSignedAt(signedDate);

        return yzhMemberRepository.save(member);
    }

    public YzhUserContractVo getUserContract() {
        return contractInfo;
    }

    public void updateContractInfo() {
        YzhUserContractVo vo = getCurrentAppConfig().getApi().fetchUserContract();
        if (vo != null) {
            contractInfo = vo;
        }
    }


    private YzhConfig buildYzhConfig(YzhAppEntity appConfig) {
        YzhConfig yzhConfig = new YzhConfig();
        yzhConfig.setDealerId(appConfig.getDealerId());
        yzhConfig.setBrokerId(appConfig.getBrokerId());
        yzhConfig.setYzhAppKey(appConfig.getAppKey());
        yzhConfig.setYzh3DesKey(appConfig.getDesKey());
        yzhConfig.setSignType(YzhConfig.SignType.RSA);
        yzhConfig.setYzhUrl("https://api-service.yunzhanghu.com");

        String keyPath = yzhKeyRootPath + File.separator;
        String appName = appConfig.getName();
        File privateKeyFile = new File(keyPath + "private" + File.separator + appName + ".key");
        File publicKeyFile = new File(keyPath + "public" + File.separator + appName + ".key");

        try (InputStream privateKeyStream = Files.newInputStream(privateKeyFile.toPath());
             InputStream publicKeyStream = Files.newInputStream(publicKeyFile.toPath())) {
            byte[] privateKeyBytes = new byte[(int) privateKeyFile.length()];
            byte[] publicKeyBytes = new byte[(int) publicKeyFile.length()];
            privateKeyStream.read(privateKeyBytes);
            publicKeyStream.read(publicKeyBytes);
            yzhConfig.setYzhRsaPrivateKey(new String(privateKeyBytes));
            yzhConfig.setYzhRsaPublicKey(new String(publicKeyBytes));
        } catch (Exception e) {
            log.error("加载云账户密钥文件失败,路径:{}", keyPath, e);
            throw new RuntimeException("加载云账户密钥文件失败", e);
        }
        return yzhConfig;
    }

    @Transactional
    public YunzhanghuCashoutRecord withdraw(CashoutRecord cashoutRecord, UserBankCard bankCard, String idCard, String phone, long rmb) {
        YzhAppEntity appConfig = getCurrentAppConfig();
        String orderId = cashoutRecord.getOrderid();

        YzhMemberEntity member = syncUserSignStatus(cashoutRecord.getUid(), bankCard.getAccountName(), idCard);
        if (!"1".equals(member.getSignStatus())) {
            throw new ResponseException(ResponseError.E_CASHOUT_SIGN, "用户未签约");
        }

        YunzhanghuApi.YzhWithdrawRequest request = new YunzhanghuApi.YzhWithdrawRequest();
        request.setOrderId(orderId);
        request.setRealName(bankCard.getAccountName());
        request.setBankCardNo(bankCard.getAccountNo());
        request.setIdCard(idCard.trim().toUpperCase());
        request.setPhone(phone);
        request.setAmount(rmb);

        CreateBankpayOrderResponse response = appConfig.getApi().withdraw(request);
        if (response == null) {
            throw new ResponseException(ResponseError.E_CASHOUT, "提交提现申请失败");
        }

        YunzhanghuCashoutRecord yzhRecord = new YunzhanghuCashoutRecord();
        yzhRecord.setOrderId(orderId);
        yzhRecord.setAppId(appConfig.getId());
        yzhRecord.setAmount(cashoutRecord.getRmb());
        yzhRecord.setRealName(bankCard.getAccountName());
        yzhRecord.setCardNo(bankCard.getAccountNo());
        yzhRecord.setPhone(phone);
        yzhRecord.setUid(cashoutRecord.getUid());
        yzhRecord.setYzhOrderId(response.getRef());
        yzhRecord.setStatus(YzhOrderStatus.CREATED.getValue());
        yunzhanghuCashoutRecordRepository.save(yzhRecord);
        return yzhRecord;
    }

    @Transactional
    public String validOrder(Map<String, String> params) {
        YzhAppEntity appConfig = getCurrentAppConfig();
        NotifyOrderData data = appConfig.getApi().validOrder(params);
        if (data == null) {
            //这里是签名失败或者解析失败
            log.error("[Yunzhanghu][notify] sign fail or parse fail");
            return "fail";
        }

        CashoutRecordQueue record = cashoutDao.getRecordQueueByOrderId(data.getOrderId(), BankType.YUNZHANGHU);
        if (record == null) {
            log.error("[Yunzhanghu][notify] order not found: {}", data.getOrderId());
            return "fail";
        }
        int appStatus = record.getApp_status();

        // 处理已经完成状态
        if (appStatus == CashoutRecordAppStatus.ACCOUNT_SUCCESS.getValue() || appStatus == CashoutRecordAppStatus.REFUND_SUCCESS.getValue()){
            log.error("[Yunzhanghu][notify] order already finished: {}", data.getOrderId());
            return "success";
        }

        // 处理非到账中状态
        if (appStatus != CashoutRecordAppStatus.INTO_ACCOUNTING.getValue()) {
            log.error("[Yunzhanghu][notify] order already finished: {}, status:{} ", data.getOrderId(), appStatus);
            return "success";
        }

        OrderData orderData = buildSyncOrderData(data.getOrderId(), data);
        return syncOrderStatus(data.getOrderId(), record, orderData);
    }

    @Data
    @Builder
    public static class OrderData {
        private Integer status;
        private Integer statusDetail;
        private String statusMessage;
        private String statusDetailMessage;
        private String bankOrderId;
        private Date completeTime;
        private String yzhOrderId;
        private YunzhanghuCashoutRecord record;
    }


    /**
     * 同步订单数据
     * @param orderId
     * @param data
     * @return
     */
    public OrderData buildSyncOrderData(String orderId, NotifyOrderData data) {
        YunzhanghuCashoutRecord yzhRecord = yunzhanghuCashoutRecordRepository.findByOrderId(orderId).orElse(null);
        if (yzhRecord == null) {
            return null;
        }

        return OrderData.builder()
                .yzhOrderId(data.getRef())
                .status(Integer.parseInt(data.getStatus()))
                .statusMessage(data.getStatusMessage())
                .statusDetail(Integer.parseInt(data.getStatusDetail()))
                .statusDetailMessage(data.getStatusDetailMessage())
                .bankOrderId(data.getBrokerBankBill())
                .completeTime(DateUtils.parseDateTime(data.getFinishedTime()))
                .record(yzhRecord)
                .build();
    }

    /**
     * 查询订单数据
     * @param orderId
     * @return
     */
    public OrderData buildQueryOrderData(String orderId) {

        YunzhanghuCashoutRecord yzhRecord = yunzhanghuCashoutRecordRepository.findByOrderId(orderId).orElse(null);
        if (yzhRecord == null) {
            return null;
        }

        YzhAppEntity appConfig = appIdMap.get(yzhRecord.getAppId());
        if (appConfig == null) {
            return null;
        }
        YunzhanghuApi api = appConfig.getApi();
        GetOrderResponse response = api.queryOrder(orderId);
        log.info("[Yunzhanghu][Query Order] orderId: {}, response: {}", orderId, response);
        if (response == null) {
            return null;
        }

        return OrderData.builder()
        .status(Integer.parseInt(response.getStatus()))
        .statusMessage(response.getStatusMessage())
        .statusDetail(Integer.parseInt(response.getStatusDetail()))
        .statusDetailMessage(response.getStatusDetailMessage())
        .bankOrderId(response.getBrokerBankBill())
        .completeTime(DateUtils.parseDateTime(response.getFinishedTime()))
        .record(yzhRecord)
        .build();
    }


    /**
     * 同步订单状态
     * @param orderId
     * @param recordQueue
     * @param orderData
     * @return
     */
    @Transactional
    public String syncOrderStatus(String orderId, CashoutRecordQueue recordQueue, OrderData orderData) {
        if (orderData == null) {
            log.error("[Yunzhanghu][Sync Order] order not found: {}", orderId);
            return "fail";
        }

        YunzhanghuCashoutRecord yzhRecord  = orderData.getRecord();
        int recordStatus = yzhRecord.getStatus();
        int orderStatus = orderData.getStatus();

        try {
            if (orderStatus != recordStatus) {
                log.info("Update yunzhanghu order:{} status:{}, {} --> {}, {}", orderId, recordStatus, yzhRecord.getStatusDetail(), orderStatus, orderData.getStatusDetail());
                yzhRecord.setStatus(orderStatus);
                yzhRecord.setStatusDetail(orderData.getStatusDetail());
                yzhRecord.setStatusMsg(orderData.getStatusMessage());
                yzhRecord.setStatusMsgDetail(orderData.getStatusDetailMessage());
                yzhRecord.setBankOrderId(orderData.getBankOrderId());
                yzhRecord.setCompleteTime(orderData.getCompleteTime());
                yzhRecord.setYzhOrderId( orderData.getYzhOrderId() );
                cashoutDao.update(yzhRecord);
            }
        } catch (Exception e) {
            log.error("Parse order status failed: {}", orderData, e);
        }

        //成功，但是不是终态，可能会退汇，两次回调
        if (orderStatus == YzhOrderStatus.SUCCESS.getStatus()) {
            cashoutService.updateCashoutSuccess(recordQueue, "success", "成功", "1");
            return "success";
        }

        //挂单
        if (orderStatus == YzhOrderStatus.PENDING.getStatus()) {
            recordQueue.setRes_msg(orderData.getStatusDetailMessage());
            recordQueue.setRes_code("pending");
            cashoutDao.update(recordQueue);
            return "success";
        }

        //退汇
        if (orderStatus == YzhOrderStatus.REFUND.getStatus()) {
            cashoutService.updateCashoutFail(recordQueue, "refund", yzhRecord.getStatusMsgDetail(), "退汇");
            return "success";
        }

        //取消 和 失败
        if (orderStatus == YzhOrderStatus.FAILED.getStatus() || orderStatus == YzhOrderStatus.CANCEL.getStatus()) {
            cashoutService.updateCashoutFail(recordQueue, "fail", yzhRecord.getStatusMsgDetail(), "取消/失败");
            return "success";
        }

        return "success";
    }


}
