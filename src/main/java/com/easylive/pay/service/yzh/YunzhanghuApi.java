package com.easylive.pay.service.yzh;

import com.easylive.pay.vo.YzhUserContractVo;
import com.yunzhanghu.sdk.YzhException;
import com.yunzhanghu.sdk.apiusersign.ApiUserSignServiceClient;
import com.yunzhanghu.sdk.apiusersign.domain.*;
import com.yunzhanghu.sdk.base.YzhConfig;
import com.yunzhanghu.sdk.base.YzhRequest;
import com.yunzhanghu.sdk.base.YzhResponse;
import com.yunzhanghu.sdk.notify.NotifyClient;
import com.yunzhanghu.sdk.notify.domain.NotifyRequest;
import com.yunzhanghu.sdk.notify.domain.NotifyResponse;
import com.yunzhanghu.sdk.payment.PaymentClient;
import com.yunzhanghu.sdk.payment.domain.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.Map;

import static com.easylive.pay.utils.SystemUtil.sleep;

/**
 * <AUTHOR>
 * @date 2025/7/16
 */
@Slf4j
public class YunzhanghuApi {

    private YzhConfig config;
    private String notifyUrl;

    private PaymentClient paymentClient;
    private NotifyClient notifyClient;

    private ApiUserSignServiceClient apiUserSignServiceClient;

    public YunzhanghuApi(YzhConfig config, String notifyUrl) {
        this.config = config;
        this.notifyUrl = notifyUrl;
        this.paymentClient = new PaymentClient(config);
        this.apiUserSignServiceClient = new ApiUserSignServiceClient(config);
        this.notifyClient = new NotifyClient(config);
    }


    /**
     * 查询用户签约状态
     *
     * @param realName 真实姓名
     * @param idCard   身份证号
     * @return
     */
    public GetApiUserSignStatusResponse queryUserSignState(String realName, String idCard) {
        GetApiUserSignStatusRequest request = new GetApiUserSignStatusRequest();
        request.setDealerId(config.getDealerId());
        request.setBrokerId(config.getBrokerId());
        request.setRealName(realName);
        request.setIdCard(idCard);
        YzhResponse<GetApiUserSignStatusResponse> response;
        try {
            response = apiUserSignServiceClient.getApiUserSignStatus(YzhRequest.build(request));
            if (!response.isSuccess()) {
                throw new RuntimeException("查询用户签约状态失败: " + response.getMessage());
            }
            return response.getData();
        } catch (YzhException e) {
            log.error("查询用户签约状态失败", e);
        }
        return null;
    }

    public GetOrderResponse queryOrder(String orderId) {
        GetOrderRequest request = new GetOrderRequest();
        request.setOrderId(orderId);
        request.setChannel("银行卡");
        try {
            YzhResponse<GetOrderResponse> response = this.paymentClient.getOrder(YzhRequest.build(request));
            if (!response.isSuccess()) {
                log.error("查询订单失败: {}", response.getMessage());
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("Error when query order: {}", e.getMessage());
        }
        return null;
    }

    @Data
    public static class YzhWithdrawRequest {
        private String orderId;
        private String realName;
        private String bankCardNo;
        private String idCard;
        private String phone;
        private long amount;
    }

    /**
     * 提现
     *
     * @param wr 提现请求
     * @return 提现响应
     */
    public CreateBankpayOrderResponse withdraw(YzhWithdrawRequest wr) {
        CreateBankpayOrderRequest request = new CreateBankpayOrderRequest();
        request.setOrderId(wr.getOrderId());
        request.setDealerId(this.config.getDealerId());
        request.setBrokerId(this.config.getBrokerId());
        request.setRealName(wr.getRealName());
        request.setCardNo(wr.getBankCardNo());
        request.setIdCard(wr.getIdCard());

        String[] phoneArr = wr.phone.split("_");
        if (phoneArr.length > 1) {
            wr.setPhone(phoneArr[1]);
        }

        request.setPhoneNo(wr.getPhone());
        request.setPay(String.valueOf(wr.getAmount() / 100.0));
        request.setNotifyUrl(this.notifyUrl);
        try {
            YzhResponse<CreateBankpayOrderResponse> response = this.paymentClient.createBankpayOrder(YzhRequest.build(request));

            // 这里有个问题所以需要重试，如果在请求第三方超时，然后response 返回为空，实际订单已经在第三方平台成功下单，
            // 在咋们平台订单被取消了，这样就会造成重复提现，现在处理方式是重试请求, 这里按照道理应该暂停几秒再试，确保第三方处理完成。
            // 重试这种情况比较少，sleep 5秒 很可能造成接口超时，前端业务提示E_SERVER 但是实际业务成功了。
            if (response == null) {
                sleep(5000);
                response = this.paymentClient.createBankpayOrder(YzhRequest.build(request));
                log.info("提现请求重试：{}, {}", wr, response);
                if (!response.isSuccess() || !"2002".equals(response.getCode())) {
                    //重试依旧失败
                    log.error("提现请求retry失败: {}, {}", wr, response.getMessage());
                    return null;
                }
                return response.getData();
            }

            if (!response.isSuccess()) {
                log.error("云账户提现请求失败: {}, {}",  wr, response);
                return null;
            }

            log.info("云账户提现请求成功: {}, {}", wr, response.getData());
            return response.getData();
        } catch (Exception e) {
            log.error("[YunzhanghuCashoutService][doWithdraw] Exception: {}", e.getMessage(), e);
        }
        return null;
    }


    @Data
    public static class YzhUserSignRequest {
        private String realName;
        private String idCard;
    }

    /**
     * 用户签约
     */
    public ApiUserSignResponse signContract(YzhUserSignRequest signRequest) {
        ApiUserSignRequest request = new ApiUserSignRequest();
        request.setDealerId(config.getDealerId());
        request.setBrokerId(config.getBrokerId());
        request.setRealName(signRequest.getRealName());
        request.setIdCard(signRequest.getIdCard());
        request.setCardType("idcard");

        try {
            YzhResponse<ApiUserSignResponse> response = this.apiUserSignServiceClient.apiUserSign(YzhRequest.build(request));
            if (!response.isSuccess()) {
                log.error("签约失败: {}", response.getMessage());
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("Error when sign contract: {}", e.getMessage());
        }
        return null;
    }


    /**
     * 获取用户签约合同
     *
     * @return YzhUserContractVo
     */
    public YzhUserContractVo fetchUserContract() {
        ApiUserSignContractRequest request = new ApiUserSignContractRequest();
        request.setDealerId(config.getDealerId());
        request.setBrokerId(config.getBrokerId());
        try {
            YzhResponse<ApiUserSignContractResponse> response = this.apiUserSignServiceClient.apiUserSignContract(YzhRequest.build(request));
            if (!response.isSuccess()) {
                return null;
            }
            ApiUserSignContractResponse data = response.getData();
            YzhUserContractVo vo = new YzhUserContractVo();
            vo.setUrl(data.getUrl());
            vo.setTitle(data.getTitle());
            return vo;
        } catch (YzhException e) {
            log.error("Error fetch user contract: ", e);
        }
        return null;
    }

    public NotifyOrderData validOrder(Map<String,String> params) {
        log.info("Yunzhanghu cashout notify params: {}", params);

        NotifyRequest request = new NotifyRequest();
        request.setData(params.get("data"));
        request.setMess(params.get("mess"));
        request.setTimestamp(params.get("timestamp"));
        request.setSign(params.get("sign"));

        try {
            NotifyResponse<NotifyOrderRequest> response = this.notifyClient.notifyDecoder(request, NotifyOrderRequest.class);
            if (response.getSignRes() && response.getDescryptRes()) {
                NotifyOrderRequest notifyRequest = response.getData();
                NotifyOrderData data = notifyRequest.getData();
                log.info("Notify received:{}, data:{}", notifyRequest.getNotifyId(), data);
                return data;
                //String statusMessage = data.getStatusMessage();
            } else {
                log.error("Yunzhanghu cashout notify sign check failed, response sign={}, decrypt={}", response.getSignRes(), response.getDescryptRes());
            }
        } catch (Exception e) {
            log.error("Yunzhanghu cashout notify process error", e);
        }
        return null;
    }


    public static void main(String[] args) {
// 配置基础信息
        YzhConfig config = new YzhConfig();
        config.setDealerId("27643989");
        config.setBrokerId("szjjv1test");
        config.setYzhAppKey("A4StQNPx78hz97f97FvTni8ZUl1T13f2");
        config.setYzh3DesKey("Jydxo0cfw747dhvaxmpkgI6N");
        config.setYzhRsaPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDMbn1n9rQIq8jo\n" +
                "uECA6/+a/T0+MTND2giacR6mqHWTR6+bTHldq6cJkGA3hsxuPfs/dY0pRJlTwif4\n" +
                "sj9YFPISaIIeRKrykbcaZedN4TaXuQAU27HD3QHeI42wCoA6fsziLujl4MabSuaq\n" +
                "VrxLUhE3z3EFDKWsGtKyvb6rSZN/NYNCymojAzurDqyD38nMN4zttu3NM/ETRXBj\n" +
                "avT3vHgSRkVvOCG5CMRp62XGSMNBdTXhVP19U5x7hocDrq8FbMCw4KOpAonmLU0W\n" +
                "QoIsF9VNzvIqfXZvSeUrICe9oqTiAyGSObYtZdmL0e/MdSOV7/eDC3VHaw/XC7j9\n" +
                "LxsQKOpxAgMBAAECggEAC6b6l+vsbBgajgU+i/qnqZZooLxeLy5k0iTDef1dIkVy\n" +
                "QXhU7J1lmnpzuYaB7YCz6vSQ1LrXwHnvHFJQoP0kpR7217HTSps6pBMN4ZcGLRS6\n" +
                "5/VzxvW4bwh4wYF/uJXUqMnCtaOfvmdBCZGDDwoA+DznNMH5SXuH6e89yHabpSL2\n" +
                "ECAB5E5cSVJbyzfPdaJ+DAa/uIN4u0BRf6Q/qDvlYuhYaOlqrKdVuIyduh3fBZMn\n" +
                "bOj5r4y9/jqze9VpZgzMmAQrt0UuNcGyLqzkexL70RzR/EhDt818X4Wy9zVLB+cO\n" +
                "sG1MDpV14CPiE8tRG3GFqSZqdxb7vXE/0KhzlyT4DwKBgQD18DjplTrCSY73KhV/\n" +
                "YyR5X1mbUiXO0DxJPVQtUyDhW3LavMT7Wf6qLcMmmfRCcbiSx0yP6PyPrdoNq8BZ\n" +
                "g0pPAEzQg1O2AV7U2xcW8pwyDayna1QemMmyHcs4rUi7C+HjOwEBkDK/1Rf3Mma6\n" +
                "xTNlxQRJnAh5WzYkOe9j0Ck4pwKBgQDUy45iAGKPrOhzfJAYWhHDB7a9M2Rl/XkD\n" +
                "g9uMS7fz3LU9uQGn0p4SJ/xeGWibk6glaQW/rbxnx/joDdI4WcpErVacMFjA2KYK\n" +
                "R+lPMoeJEJkM9VpX3hgE7gIC6P8O7tRYPyr3TbOiZK0kCeg+eq+tn3Un7yHWSrKl\n" +
                "hmilfmSPJwKBgQCAF+wsjNMb2mAiD8M+pA6dQLJVGYYjn4Wmx9COAtv/RDggAW2/\n" +
                "VHI1hLw94LW93ak6mZ8g3UKeOvh1Yl0+wgjz+L4JZD03cKNJzgrKovYDg+fHEzmu\n" +
                "7fkvUiFElcxpz5iJV3QAsnAcRgrDb3beFFPez6yaAjDPtFgQqjQrEoJFxQKBgQC+\n" +
                "bHY6/us0dWAC0MpF8HwKkmGWSGIk3sInQhz7HFs+UBikZmWz38dyqfOt5YD2EBmd\n" +
                "eLEbSYbbPFlil9Oyd6+I67EgzibuVDGp65Y+OBKPtpmvvfQdw3KtHqwQHJ76pwDB\n" +
                "A64YZqFExmEmafM5ziJd9d8qYFVgZbIea/xDdKraawKBgCXAbaNvX1tz0vBKLqhO\n" +
                "+HQQA+JtyAoHlCWkKHRK+9HNBM0NdOjFhlCQFfXFEjp+eo+q0BiyiPiT4acD7ICi\n" +
                "CFuLtz3ArojKFhPfokYbDyM1wwgi3J1/EzTPL9mCMQRNb9P8ko0yewj39BH7UBFF\n" +
                "Emekk/cdPxPzts+T4MqW/CWQ");
        config.setYzhRsaPublicKey("-----BEGIN PUBLIC KEY-----\n" +
                "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDpdAqhTaobOBr6NRRsphHDaJoB\n" +
                "jibItXvHnCoF2MXnf3Nc8paoNN6yYGTLedYxZX1aEMpDvUwegt91kuoxaitSpacy\n" +
                "VexD1TkwRhZFvLIOUfnJiv6ruAvc02bN+gfCw88t7LbY0xS4D8KTz+OT/MldlyNw\n" +
                "8vx461Y+4QMXVqMLZQIDAQAB\n" +
                "-----END PUBLIC KEY-----");
        config.setSignType(YzhConfig.SignType.RSA);
        config.setYzhUrl("https://api-service.yunzhanghu.com");

        YunzhanghuApi api = new YunzhanghuApi(config, "https://frp.scmagic.net/notify/yzh");
        log.info("Contract: {}", api.fetchUserContract());

        String realName = "肖谋";
        String idCard = "511121198108098455";
        String bankCardNo = "6228450018052607775";
        String phone = "***********";

        GetApiUserSignStatusResponse response = api.queryUserSignState(realName, idCard);
        log.info("Sign status: {}", response);

        if( !"1".equals(response.getStatus())) {
            log.info("签约");
            YzhUserSignRequest signRequest = new YzhUserSignRequest();
            signRequest.setRealName(realName);
            signRequest.setIdCard(idCard);
            ApiUserSignResponse signResponse = api.signContract(signRequest);
            log.info( "Sign response: {}", signResponse);
        } else {
            YzhWithdrawRequest wr = new YzhWithdrawRequest();
            wr.setOrderId(RandomStringUtils.randomAlphanumeric(10));
            wr.setRealName(realName);
            wr.setBankCardNo(bankCardNo);
            wr.setIdCard(idCard);
            wr.setPhone(phone);
            wr.setAmount(1);
            CreateBankpayOrderResponse withdrawResponse = api.withdraw(wr);

        }


        String notifyData = "{data=znuhdtDxqQqozow4xj3LglJPPO3qBeDZzyLwpcGOqmvG2rv6QILcEa8r6mLQD4QdOYoP2i2zyxkmZ1nIEQd2AqLAe8gUGWL6ZtuRdfpdrHk3tE8xQ9gV/vV6HVSs868yrI7dvNrzl5fT+Q82BqDXp3P+xG7JAe0V5W3wzxAFgbXx3KQiMvLj7ylrddX262od+yATT+yEyTflSHrar25LLdffjJMiTv1RoJ5mJCeU9wntc8cosaDwFkqbMdJrAgV2FpJkb1tDYllwME11QPRyfqEXwQQaHPTXj+cnXk+7S0EdrzqkFZGwAhsqBtkBNHBAdKXU5Dbj5huWFZrRZUPbWXIHpz7rULmKTyiKdWNkZgpNenwPp2TufrMMOfRHafLQ7iNc2y9h7uuUVVEBv7DZOZq+b5Hh2f1ffLAhAqiBz/ELXyNjV9UcYS/LmhH3QhgotgspvE2O/+ByV952g8H/ho0u9eaZt4YyWI6JDr9IaSaP2VFyJ08Jb7zc8a42UBAjf87UmEjsM1OhJhcHrYbTwuIH47FpATDgDg8xYelAnmIyrqgkgQ4L9tPQOT2sr0UuZXRRnBd4rH4+pYzDwIqRBUiW4UEjitxwEhkY/SDAAcQsy/7cDSvmJxj6TNlxIrJrNhz2wD0SBbFz1k8JiVS4bcfnhqgApaS4Po3Cwi4TnH3/x1joCw4G5BgB/DhRnamTTZUIlV9Z7pcrgt4z/Xg21qBsL/CC02cI0WSUFXNAJ8mvPwxw6giJ6gZOeSxyigMg2g0jrLIYmPk8Qo8fFywVz6kHDZcZjZCG7vQSItx10diWE1mvmP308FrRrb7aTsTAGWvMYh3nzSlCv47DOnYEFXBqaAg6VwR7e6HErCHkWk3xcdfG1KIXMJyQXHStsnWXHfkFnXYHDCQytCMFaNkkKYXdbtQFxR+VhdCi9y6soSOE/KKME/Gi3BpvpGPsDxEY+z8JwKEa7ufHhmOiaMTWWpNL3OQPrPVaUyohs6o2w6c9jHIEg2bkHpHVhhugQn8AzpCpfSlW9MwikalGoNM++mx96T6NEucmy0w49xzaLAZUJ2/SWG8un2TuhJM4pX9SGbEn/vdZQ1Bx2TYhF5pzxs2FWC8oeLhXwWDSaXj8/pgi9podLA7876RWvXIiDsxjchewkUQ7IiXeHg31AJdmqr680yksUXSHX/NhGXTtHMPqcAvHoWPY5RZLsJLKUSlcAAysgnViHsGhzpsntX65703iOgmwpDAfTmsFpG5/fV2zGar9dg8HWZTSLAiv8hbPqeIZ/lY3AHuoxOlvnrpVAr+YqyCNxkwP6gb677PdyEthkl1ytcwou+LuVtKBV5Q6pbu0I8y4IUScewHIc43yQ4LlbczCoanDzEisL45mR3rPFw0z2JecEzSJWx2JGIXy1Rq5VGXeg4w=, mess=575782413, sign=fW8nVeUyG0169gJZIoo2FttfGuxQhtPa4Xh0e5kngqAyUPE8ZTNTSuqtri0mbOQWVtVzC7239/KrEEGzjrkEr5lsTzK5wJdzXDd0QTIsigO03DrRV47Ov8+50GYi6Y4IrgOC+XfIeq7agLwfifaKR2yxqPRNKx9Sc/bMLU+ez3E=, sign_type=rsa, timestamp=1752659854}";

    }
}