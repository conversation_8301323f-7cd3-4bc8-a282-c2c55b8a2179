package com.easylive.pay.service;

import com.alibaba.fastjson.JSON;
import com.easylive.pay.common.Constants;
import com.easylive.pay.common.DataSource;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.event.EventProducer;
import com.easylive.pay.config.CacheConfig;
import com.easylive.pay.config.CashConfig;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.dao.*;
import com.easylive.pay.dao.jpa.CashoutGuildUserRepository;
import com.easylive.pay.dao.jpa.GuildRepository;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.*;
import com.easylive.pay.enums.bank.BankType;
import com.easylive.pay.model.AffirmCashoutWhite;
import com.easylive.pay.model.AssetUpdateResult;
import com.easylive.pay.model.CashoutOfflineDTO;
import com.easylive.pay.model.User;
import com.easylive.pay.model.callback.CashoutCallback;
import com.easylive.pay.model.cashout.SignContractModel;
import com.easylive.pay.model.user.IdentityInfo;
import com.easylive.pay.output.Asset;
import com.easylive.pay.output.CashoutIdentityLimitBO;
import com.easylive.pay.output.CashoutInfo;
import com.easylive.pay.pub.event.CashoutEvent;
import com.easylive.pay.pub.event.CashoutEventType;
import com.easylive.pay.service.factory.BankCashout;
import com.easylive.pay.service.factory.BankCashoutFactory;
import com.easylive.pay.service.huifu.HuifuPayService;
import com.easylive.pay.utils.AppStringUtils;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.utils.JsonUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class CashoutService {
    private static final Logger logger = LoggerFactory.getLogger(CashoutService.class);

    @Autowired
    private CashoutDao cashoutDao;

    @Autowired
    private CashConfig cashConfig;

    @Autowired
    private URLConfig urlConfig;

    @Autowired
    private AssetService assetService;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private StringRedisTemplate template;

    @Autowired
    private CashoutLimitRepository cashoutLimitRepository;

    @Autowired
    private CashoutIdentityLimitRepository cashoutIdentityLimitRepository;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private CacheConfig cacheConfig;

    @Autowired
    private SysSettingRepository sysSettingRepository;

    @Autowired
    private BankCashoutFactory bankCashoutFactory;

    @Autowired
    private EventProducer eventProducer;

    @Autowired
    private UserService userService;

    @Autowired
    private UserCoinDao userCoinDao;

    @Autowired
    private BizcoreCenterService bizcoreCenterService;

    @Autowired
    private CashoutOfflineRepository cashoutOfflineRepository;

    @Autowired
    private CashoutGuildUserRepository cashoutGuildUserRepository;

    @Autowired
    private GuildRepository guildRepository;

    @Autowired
    private SpecialUserService specialUserService;

    @Autowired
    private HuifuPayService huifuPayService;

    private static final String CASHOUT_LIMIT_CACHE_KEY = "cashout_limit_";
    private static final long CASHOUT_LIMIT_CACHE_TTL = 3600;

    private static final String KEY_WITHDRAW_BANK_TYPE = "withdraw:bank_type";
    private static final long KEY_WITHDRAW_BANK_TYPE_TTL = 24 * 3600;

    private static final int DEFAULT_DAY_CASHOUT_LIMIT = 20000;
    private static final long MAX_CASHOUT_OFFLINE = 500000;

    @Autowired
    private ConfigService configService;

    public List<CashoutRecord> listByUid(Long uid, int start, int limit) {
        return cashoutDao.getByUid(uid, start, limit);
    }

    public long getTotalByUid(Long uid) {
        return getTotalByUid(uid, true);
    }

    public long getTotalByUid(Long uid, boolean app) {
        if (app) {
            return cashoutDao.getAppTotalByUid(uid);
        } else {
            return cashoutDao.getTotalByUid(uid);
        }
    }

    public CashoutInfo getCashoutInfo(long uid) {
        UserCoin uc = assetService.getByUid(uid);
        CashoutLimitEntity limit = getCashoutLimit(uid);
        return new CashoutInfo(uc.getRiceroll(), limit);
    }

    @Transactional
    public void enableCashout(long uid, int enable) {

        CashoutLimitEntity limit = getOrCreateCashoutLimit(uid);
        if (enable == 0) {
            limit.setDayCashoutLimit(0);
            limit.setIsForbid(CashoutLimitForbid.YES);
            CashoutForbid cashoutForbid = cashoutDao.getByUid(uid);
            if (cashoutForbid == null) {
                cashoutForbid = new CashoutForbid(uid);
                cashoutDao.save(cashoutForbid);
            }
        } else {
            limit.setDayCashoutLimit(DEFAULT_DAY_CASHOUT_LIMIT);
            limit.setIsForbid(CashoutLimitForbid.NO);
            cashoutDao.delCashoutForbid(uid);
        }
        cashoutLimitRepository.save(limit);
        removeFromCache(uid);
    }


    /**
     * 创建提款记录
     *
     * @param openid   提款的openid
     * @param uid      提款的用户id
     * @param rmb      人民币,单位为分
     * @param mp       移动平台,iOS/Android
     * @param riceroll 饭团
     * @param clientIp 提款客户的IP
     * @return CashoutRecord, 本函数可能会处罚异常,因此需要外层捕获,创建如果失败的话,建议直接失败.
     */
    @Transactional
    public CashoutRecord createCashOut(String bizId, String openid, Long uid, Long rmb, int mp, long riceroll, int pfPlatform, String clientIp) {
        String serverIp = urlConfig.getServerIp();
        SecureRandom random = new SecureRandom();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String tradeNo = bizId + sdf.format(new Date()) + new BigInteger(130, random).toString().substring(0, 10);
        logger.info("[CashOut][Start] UID:[ {} ], OpenID: [{}], RMB:[ {} ], RiceRoll: [ {} ], Client IP: [ {} ], ServerIP [ {} ], LocalTradeNo [ {} ]",
                uid, openid, rmb, riceroll, clientIp, serverIp, tradeNo);

        CashoutRecord crc = new CashoutRecord(uid, rmb, tradeNo, mp, clientIp, serverIp,
                riceroll, pfPlatform, openid);
        cashoutDao.save(crc);
        return crc;
    }

    /**
     * APP提现记录
     */
    @Transactional
    public CashoutRecord addAppCashoutRecord(long uid, int cardId, long rmb, long riceroll, String clientIp, int appId, int fkType) {
        int pfPlatform = CashoutPlatform.App.getValue();
        int platform = MobilePlatform.MPF_APP.getValue();
        int appStatus = CashoutRecordAppStatus.INIT.getValue();
        String serverIp = urlConfig.getServerIp();
        String app = "001";
        String tradeNo = getTradeNo(app);
        logger.info("[AppCashout][start] uid:[{}], rmb:[{}], riceroll[{}], clientIp[{}], serverIp[{}], tradeNo[{}]", uid, rmb, riceroll, clientIp, serverIp, tradeNo);
        String remark = WithdrawChannelTypeEnum.CARD.getCode() == fkType ? "提现到银行卡" : "提现到支付宝";
        CashoutRecord record = new CashoutRecord(uid, rmb, tradeNo, platform, clientIp, serverIp, riceroll, pfPlatform, app, remark, appId);
        record.setApp_status(appStatus);
        record.setCard_id(cardId);
        record.setCommit_time(DateUtils.nowDb());
        cashoutDao.save(record);
        return record;
    }

    public String getTradeNo(String app) {
        SecureRandom random = new SecureRandom();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return app + sdf.format(new Date()) + new BigInteger(130, random).toString().substring(0, 10);
    }

    @Transactional
    public UserCoin cashout(CashoutRecord cashoutRecord, CashoutCallback callback) {

        long uid = cashoutRecord.getUid();
        long rmb = cashoutRecord.getRmb();
        long riceroll = cashoutRecord.getRiceroll();
        CashoutLimitEntity cashoutLimit = getOrCreateCashoutLimit(uid);

        checkCashoutLimit(uid, cashoutLimit, rmb, riceroll);

        AssetUpdateResult result = assetService.unaryOperate(AssetSystemID.CASHOUT, uid, riceroll);

        updateCashoutLimit(cashoutLimit, rmb);


        callback.run(cashoutRecord);

        cashoutRecord.setComplete_time(new Date());
        cashoutRecord.setStatus(OrderStatus.ORS_SUCCESS.getValue());
        cashoutDao.update(cashoutRecord);

        logger.info("[Cashout][RedPack][Success] UID [{}], Openid: [{}], Amount: [{}]"
                , cashoutRecord.getUid(), cashoutRecord.getOpenid(), cashoutRecord.getRmb());
        logger.info("[advstat][cashout][success] total {} uid {} rmb {}", riceroll, uid, rmb);


        cashoutRecord.setTid(result.getTid());
        cashoutDao.update(cashoutRecord);

        return result.getUserCoin(0);
    }

    @Transactional
    public void failCashout(CashoutRecord cashoutRecord) {
        logger.info("set cashout {} to failed", cashoutRecord.getOrderid());
        cashoutRecord.setComplete_time(new Date());
        cashoutRecord.setStatus(OrderStatus.ORS_FAILED.getValue());
        cashoutDao.update(cashoutRecord);
    }


    @Transactional
    @DataSource(name = DataSource.slave)
    public List<CashoutRecord> getRecordByDate(String strDate, int start, int limit) {
        DateTimeFormatter formatter = DateUtils.getDefaultFormatter();
        DateTime dt = formatter.parseDateTime(strDate);
        return cashoutDao.getRecordByDate(start, limit, dt.toDate(), dt.plusDays(1).toDate());
    }

    @Transactional
    public List<CashoutRecord> getRecordByUser(long uid, String startDate, String endDate) {
        DateTimeFormatter formatter = DateUtils.getDefaultFormatter();
        DateTime start = formatter.parseDateTime(startDate);
        DateTime end = formatter.parseDateTime(endDate);
        return cashoutDao.getRecordByUser(uid, start.toDate(), end.toDate());
    }

    @Transactional
    public void setWhiteList(long uid, int limit) {
        if (limit < 0) {
            limit = 0;
        } else if (limit > 500000) {
            limit = 500000;
        }

        CashoutWhiteEntity white = cashoutDao.getCashoutWhiteByUid(uid);
        if (white != null) {
            white.setActive(1);
            white.setLimit(limit);
            cashoutDao.saveOrUpdate(white);
        } else {
            white = new CashoutWhiteEntity();
            white.setActive(1);
            white.setLimit(limit);
            white.setUid(uid);
            cashoutDao.save(white);
        }
    }

    @Transactional
    public void deleteWhiteList(long uid) {
        cashoutDao.deleteWhiteListByUid(uid);
    }

    @Transactional(readOnly = true)
    public List<CashoutWhiteEntity> listWhiteList() {
        return cashoutDao.getAllCashoutWhite();
    }

    @Transactional
    public List<Richer> listRicher() {
        return cashoutDao.getAllRicher();
    }


    @Transactional
    public void enableOfflineCashout(long uid, boolean enable) {
        logger.info("Enable offline cashout for {}: {}", uid, enable);
        CashoutOffline offline = cashoutDao.getCashoutOfflineByUid(uid);
        if (offline != null) {
            if (!enable)
                cashoutDao.removeOfflineCashout(uid);
        } else {
            if (enable)
                cashoutDao.addOfflineCashout(uid);
        }
    }

    @Transactional(readOnly = true)
    public List<CashoutOffline> listOfflineCashout() {
        return cashoutDao.findAll();
    }

    public List<CashoutOfflineDTO> listOffline() {
        return getCashoutOfflineDTOS(cashoutOfflineRepository.listCashoutOffline());
    }

    private List<CashoutOfflineDTO> getCashoutOfflineDTOS(List<Object> cashoutOfflineDTOS) {
        return cashoutOfflineDTOS.stream().map(v -> {
            String s = JsonUtils.toString(v);
            s = s.replace("[", "");
            s = s.replace("]", "");
            String[] split = StringUtils.split(s, ",");
            return CashoutOfflineDTO.builder()
                    .uid(Long.valueOf(split[0]))
                    .createTime(StringUtils.isNotBlank(split[1]) && !split[1].equals("null") ? new Date(Long.valueOf(split[1])) : null)
                    .lastTime(StringUtils.isNotBlank(split[2]) && !split[2].equals("null") ? new Date(Long.valueOf(split[2])) : null)
                    .idCard(StringUtils.isNotBlank(split[3]) ? split[3] : null)
                    .build();
        }).collect(Collectors.toList());
    }

    @Transactional
    public List<CashoutOffline> listByUids(List<Long> uids) {
        return cashoutDao.findByUids(uids);
    }

    public List<CashoutOfflineDTO> listOfflineByUids(List<Long> uids) {
        return getCashoutOfflineDTOS(cashoutOfflineRepository.listCashoutOfflineByUids(uids));
    }

    @Transactional(readOnly = true)
    public CashoutOfflineInfoEntity getCashoutOfflineInfoByUid(long uid) {
        return cashoutDao.getCashoutOfflineInfoByUid(uid);
    }

    @Transactional
    public String offlineDirectCashout(String name, long amount,  String realName, String phone, String idCard, String bankCard) {
        logger.info("{} do offline direct cashout  {}, {}, {},{},{}", name, amount, realName,phone, idCard, bankCard);

        User user = userService.ensureExists(name);
        long uid = user.getId();


        String orderId = generateOrderId();
        validateOfflineCashout(uid, amount);

        IdentityInfo info = userService.getUserIdentityInfoByUid(uid);
        logger.info("identity info: {}", info);
        if (info == null) {
            throw new ResponseException(ResponseError.E_USER_NOT_EXISTS, "user identity info exists");
        }
        //必须在Cashout Offline列表中
        CashoutOfflineInfoEntity cashInfo = cashoutDao.getCashoutOfflineInfoByUid(uid);
        if (cashInfo == null) {
            throw new ResponseException(ResponseError.E_USER_NOT_EXISTS, "User cashout info not exists");
        }

        UserCoin userCoin = assetService.getByUid(uid);

        long riceroll = amount * cashConfig.getRateOfRmbToRiceroll();

        if(userCoin.getRiceroll() < riceroll) {
            throw new ResponseException(ResponseError.E_ASSET_RICEROLL_NOT_ENOUGH, "User riceroll not enough");
        }

        if( StringUtils.isEmpty(phone) )
            phone = info.getPhone();
        phone = adjustPhoneNumber(phone);

        huifuPayService.withdraw(orderId, uid, amount, phone, realName, idCard, bankCard);
        return orderId;
    }

    @Transactional
    public long addCashoutGuildUser(String name, String certId, String guildName, String description) {
        CashoutGuildUser user = cashoutGuildUserRepository.findByCertId(certId);
        if (user != null) {
            throw new ResponseException(ResponseError.E_CASHOUT, "Guild User already exists");
        }

        GuildEntity guild = guildRepository.findByName(guildName);
        if (guild == null) {
            throw new ResponseException(ResponseError.E_CASHOUT, "Guild not exists");
        }

        name = StringUtils.trim(name);
        certId = StringUtils.trim(certId);

        //公会提现用户的uid采用统一前缀1000 + 公会id(4位) + 随机数(4位)
        long uid = Long.parseLong( String.format("1000%04d%04d", guild.getId(), RandomUtils.nextInt(0,10000)) );
        user = new CashoutGuildUser();
        user.setName(name);
        user.setCertId(certId);
        user.setGuildName(guildName);
        user.setDescription(description);
        user.setUid(uid);
        cashoutGuildUserRepository.save( user);
        return uid;
    }
    @Transactional
    public String offlineGuildDirectCashout(long amount, String realName, String phone, String idCard, String bankCard) {
        logger.info("{} do offline direct cashout  {}, {},{},{}", amount, realName,phone, idCard, bankCard);

        if (amount <= 0 || amount >= 5000000) {
            throw new ResponseException(ResponseError.E_CASHOUT, "Invalid cashout amount");
        }

        //必须是在提现名单中
        CashoutGuildUser guildUser = cashoutGuildUserRepository.findByCertId(idCard);
        if (guildUser == null) {
            throw new ResponseException(ResponseError.E_CASHOUT, "Guild User not exists");
        }

        //对比姓名
        if (!realName.equals(guildUser.getName())) {
            throw new ResponseException(ResponseError.E_CASHOUT, "Guild User name not match");
        }

        long uid = guildUser.getUid();

        User user = userService.getById(uid);
        if( user != null ) {
            throw new ResponseException(ResponseError.E_CASHOUT, "配置错误，公会提现用户UID不正确");
        }
        String orderId = generateOrderId();
        huifuPayService.withdraw(orderId, uid, amount, phone, realName, idCard, bankCard);
        return orderId;
    }


    private String generateOrderId() {
        return BusinessOrderId.generateOrderId(BusinessOrderId.CASHOUT);
    }

    private User validateOfflineCashout(long uid, long amount) {
        if (amount <= 0) {
            logger.error("Invalid cashout amount");
            throw new ResponseException(ResponseError.E_PARAM, "Invalid amount");
        }
        CashoutOffline cashoutOffline = cashoutDao.getCashoutOfflineByUid(uid);
        if (cashoutOffline == null) {
            throw new ResponseException(ResponseError.E_CASHOUT_NOT_OFFLINE, "Cashout offline not enabled");
        }
        // 查询用户app_id
        User user = userService.getById(uid);
        if (null == user) {
            throw new ResponseException(ResponseError.E_USER_NOT_EXISTS, "user not exists");
        }
        return user;
    }

    @Transactional
    public Pair<String,Long> doOfflineCashout(long uid, long amount) {
        logger.info("{} do offline cashout {}", uid, amount);
        User user = validateOfflineCashout(uid, amount);
        UserCoin coin = assetService.getByUid(uid);
        long totalRiceroll = coin.getRiceroll();
        long cashoutRiceroll = getCashRiceroll(amount, user.getAppId());

        if (cashoutRiceroll > totalRiceroll) {
            throw new ResponseException(ResponseError.E_CASHOUT_AMOUNT_LIMIT, "Not enough amount");
        }

        AssetUpdateResult result = assetService.unaryOperate(AssetSystemID.OFFLINE_CASHOUT, uid, cashoutRiceroll);
        CashoutRecord record = createCashOut("offline", "offline", uid, amount, 0, cashoutRiceroll, CashoutPlatform.Offline.getValue(), "");
        record.setTid(result.getTid());
        record.setComplete_time(new Date());
        record.setStatus(OrderStatus.ORS_SUCCESS.getValue());
        record.setApp_id(user.getAppId());
        cashoutDao.update(record);
        cashoutDao.updateLastCashoutTime(uid, new Date());
        emitCashout(record);

        return Pair.of(record.getOrderid(), result.getUserCoin(0).getRiceroll());
    }


    private void checkCashoutLimit(long uid, CashoutLimitEntity cashoutLimit, long rmb, long riceroll) {

        if (riceroll <= 0 || rmb <= 0) {
            throw new ResponseException(ResponseError.E_CASHOUT_INVALID);
        }
        //判断提现状态
        if (cashoutLimit.getIsForbid() == CashoutLimitForbid.YES) {
            throw new ResponseException(ResponseError.E_CASHOUT_STATUS);
        }

        CashoutOffline cashoutOffline = cashoutDao.getCashoutOfflineByUid(uid);
        if (cashoutOffline != null) {
            throw new ResponseException(ResponseError.E_CASHOUT_OFFLINE, "仅线上用户可以提现");
        }

        UserCoin uc = assetService.getByUid(uid);

        //判断是否超出提现上限
        CashoutInfo info = new CashoutInfo(uc.getRiceroll(), cashoutLimit);
        if (rmb > info.getDayAvailable()) {
            logger.error("Cashout amount is over limit, uid = {}, day = {}, ayAvailable = {}", uid, cashoutLimit.getCashoutDate(), info.getDayAvailable());
            throw new ResponseException(ResponseError.E_CASHOUT_DAY_LIMIT, String.format("cashout day limit: %d > %d", rmb, info.getDayAvailable()));
        }

    }

    //==================================APP提现开始===============================================

    /**
     * 判断APP提现限制
     */
    private void checkCashoutIdentityLimit(CashoutIdentityLimitEntity identityLimit, long rmb) {

        CashoutIdentityLimitBO limitBO = new CashoutIdentityLimitBO(identityLimit);
        if (rmb > limitBO.getDayLimit()) {
            logger.error("Cashout amount is over limit, uid = {}, day = {}, ayAvailable = {}", identityLimit.getIdCard(), identityLimit.getCashoutDate(), limitBO.getDayLimit());
            throw new ResponseException(ResponseError.E_CASHOUT_DAY_LIMIT, String.format("cashout day limit: %d > %d", rmb, limitBO.getDayLimit()));
        }
        if (rmb > limitBO.getMonthLimit()) {
            logger.error("Cashout amount is over limit, uid = {}, month = {}, ayAvailable = {}", identityLimit.getIdCard(), identityLimit.getCashoutDate(), limitBO.getMonthLimit());
            throw new ResponseException(ResponseError.E_CASHOUT_DAY_LIMIT, String.format("cashout month limit: %d > %d", rmb, limitBO.getMonthLimit()));
        }
    }

    @Autowired
    private TestService testService;

    /*
    public boolean withdrawTest(long uid, long amount) {

        // * 1.构造MAC参数
        // * 2.构造XML，构造公共参数
        // * 3.解析XML
        //
        Map<String, String> map = new TreeMap<>();
        Date now = new Date();
        map.put("TransDate", new SimpleDateFormat("yyyyMMdd").format(now));
        map.put("TransTime", new SimpleDateFormat("HHmmss").format(now));
        map.put("TransCode", String.valueOf(1120));
        map.put("ChnlType", "0001");
        map.put("TrcNo", "12345678901234567890");
        map.put("EnterpriseNo", "123");
        map.put("BankNo", "123");
        logger.info("[1] Convert Before Map={}", map);

        String xmlString = TestService.buildWithdrawBankXml(map);
        logger.info("3.1.1 客户工资提取 ={}", xmlString);
        //请求接口
        testService.requestBankUrlWithXml(xmlString);

        String reversalXml = TestService.buildWithdrawReversalXml();
        logger.info(" 3.1.2 客户工资提取冲正 ={}", reversalXml);
        //请求接口
        testService.requestBankUrlWithXml(reversalXml);

        String cancelXml = TestService.buildWithdrawCancelXml();
        logger.info(" 3.1.3 客户工资提取撤销 ={}", cancelXml);
        //请求接口
        testService.requestBankUrlWithXml(cancelXml);

        String confirmXml = TestService.buildWithdrawConfirmXml();
        logger.info(" 3.1.4 客户工资提取确认 ={}", confirmXml);
        //请求接口
        testService.requestBankUrlWithXml(confirmXml);

        String resultConfirmXml = TestService.buildWithdrawResultConfirmXml();
        logger.info(" 3.1.5 客户工资提取结果确认 ={}", resultConfirmXml);
        //请求接口
        testService.requestBankUrlWithXml(resultConfirmXml);

        String infoXml = TestService.buildTradeInfoXml();
        logger.info(" 3.2.1 交易信息查询 ={}", infoXml);
        //请求接口
        testService.requestBankUrlWithXml(infoXml);

        String signXml = TestService.buildAccountSignXml();
        logger.info(" 3.2.2 客户信息签约维护 ={}", signXml);
        //请求接口
        testService.requestBankUrlWithXml(signXml);
        return true;

    }
    */

    @Transactional
    public Asset withdraw(long uid, int fkType, int cardId, long amount, String clientIp, String idNo, String mobile, int appId) {
        /**
         * 1.获得银行卡信息
         * 2.提现申请持久化
         * 3.请求第三方服务提现
         */

        if( !configService.isCashoutEnabled() ) {
            throw new ResponseException(ResponseError.E_CASHOUT, "系统维护中，暂停服务，请联系客服");
        }

        AffirmCashoutWhite white = specialUserService.getCashoutWhite(uid);
        if (white != null) {
            long limitRmb = white.getLimitRmb();
            if( limitRmb < amount ) {
                logger.info("{} is in the white list, use limit amount {}", uid, limitRmb);
                amount = limitRmb;
            }

        }

        this.verifyCashout(uid, amount);
        if (null == WithdrawChannelTypeEnum.fromValue(fkType)) {
            throw new ResponseException(ResponseError.E_CASHOUT_CHANNEL_NOT_EXISTS, "提现渠道错误");
        }

        //1.获得银行卡信息
        UserBankCard userBankCard = userBankCardRepository.findTopById(cardId);
        if (userBankCard == null) {
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_NOT_EXISTS, "银行卡不存在");
        }
        if (fkType == WithdrawChannelTypeEnum.ALIPAY.getCode() && StringUtils.isEmpty(userBankCard.getZfbAccount())) {
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_NOT_EXISTS, "支付宝账号不存在");
        }
        if (userBankCard.getUid() != uid) {
            throw new ResponseException(ResponseError.E_PARAM, "该银行卡/支付宝账号不属于你");
        }



        //2.提现申请持久化
        long cashRiceroll = getCashRiceroll(amount, appId);
        logger.debug("[withdraw] amount={}, cashRiceroll={}", amount, cashRiceroll);

        CashoutRecord cashoutRecord = addAppCashoutRecord(uid, cardId, amount, cashRiceroll, clientIp, appId, fkType);
        if (cashoutRecord == null) {
            logger.error("[withdraw] add cashout record error, uid = {}, cardId = {}", uid, cardId);
            throw new ResponseException(ResponseError.E_SERVER, "服务器错误，请稍后再试");
        }

        //3.请求第三方服务提现
        try {
            return appCashout(cashoutRecord, userBankCard, idNo, mobile, fkType);
        } catch (ResponseException e) {
            //如果是三方提现错误信息，则不回滚业务，通过定时任务查询状态
            if (e.getError() == ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE) {
                //获得提现平台
                BankType bankType = getKeyWithdrawBankType();
                if (bankType.getValue() == BankType.XZYQ.getValue()) {
                    logger.error("[withdraw] third bank cashout response, bank error: " + e.getMessage());
                    return null;
                }
            } else if (e.getError() == ResponseError.E_CASHOUT_PAUSE_TRANSATION) {
                logger.error("[withdraw] tax collection platform error: " + e.getMessage());
                return null;
            }
            logger.error("[withdraw] third bank cashout response error: " + e.getMessage());
            this.failCashout(cashoutRecord);
            throw e;
        } catch (Exception e) {
            this.failCashout(cashoutRecord);
            logger.error("third bank cashout error: " + e.getMessage());
            throw e;
        }

    }

    public static long getCashRiceroll(long amount, int appId) {
        long riceroll;
        if (appId == Constants.APP_ID_HIBI) {
            riceroll = (long) (amount * (Constants.HIBI_RATE_RMB_TO_RICEROLL + 0.0f) / 100);
        } else {
            riceroll = amount * Constants.RATE_RMB_TO_RICEROLL;
        }
        return riceroll;
    }

    @Transactional
    public Asset appCashout(CashoutRecord cashoutRecord, UserBankCard bankCard, String idNo, String mobile, int fkType) {

        long uid = cashoutRecord.getUid();
        long rmb = cashoutRecord.getRmb();
        long riceroll = cashoutRecord.getRiceroll();

        //检查每天限额
        CashoutLimitEntity cashoutLimit = getOrCreateCashoutLimit(uid, true);
        checkCashoutLimit(uid, cashoutLimit, rmb, riceroll);

        //检查账户限额
        CashoutIdentityLimitEntity identityLimit = getOrCreateCashoutIdentityLimit(idNo);
        checkCashoutIdentityLimit(identityLimit, rmb);

        AssetUpdateResult result = assetService.unaryOperate(AssetSystemID.CASHOUT, uid, riceroll);

        //更新每天/账户限额
        updateCashoutLimit(cashoutLimit, rmb);
        updateCashoutIdentityLimit(identityLimit, rmb);

        //修改状态为转账发起中
        cashoutRecord.setApp_status(CashoutRecordAppStatus.ACCOUNT_REQUESTING.getValue());
        cashoutDao.update(cashoutRecord);

        //工厂模式提现,获得提现平台
        BankType bankType = getKeyWithdrawBankType();

        AffirmCashoutWhite white = specialUserService.getCashoutWhite(uid);
        if (white != null) {
            BankType type = white.getBankType();
            if (type != null) {
                logger.info("{} is in the cashout white list, use bank type: {}", uid, white.getBankType());
                bankType = type;
            } else {
                logger.info("{} is in the cashout white list, but bank type is null, default bank type {} will be used", uid, bankType);
            }
        }

        //新增转账排队记录
        CashoutRecordQueue queue = saveCashoutRecordQueue(cashoutRecord, bankCard, idNo, mobile, bankType.getValue(), fkType);

        logger.debug("[appCashout] 1. CashoutRecordQueue=" + JsonUtils.printObject(queue));

        BankCashout bankCashout = bankCashoutFactory.getBankCashout(bankType);

        //方式1 捕获异常，退款
        //方式2 相关记录放新的事物执行，再捕获异常时，更新记录表
        try {
            bankCashout.doWithdraw(cashoutRecord, bankCard, idNo, mobile, fkType);
        } catch (ResponseException e) {
            if (result != null && result.getTid() != null) {
                assetService.unaryOperate(AssetSystemID.APP_CASHOUT_REFUND, uid, riceroll);
                logger.error("User {} refund withdraw tid {} error {}", uid, result.getTid(), e);
            }
            throw e;
        }


        if (bankCashout.getRecordStatus() == CashoutRecordAppStatus.INTO_ACCOUNTING.getValue()) {
            //修改状态为转账中
            cashoutRecord.setApp_status(CashoutRecordAppStatus.INTO_ACCOUNTING.getValue());
            cashoutDao.update(cashoutRecord);
            //修改提现队列为转账中
            queue.setApp_status(CashoutRecordAppStatus.INTO_ACCOUNTING.getValue());
            queue.setOrderid(cashoutRecord.getOrderid());
            cashoutDao.update(cashoutRecord);

            logger.info("[App][Cashout][RedPack][Success] UID [{}], Amount: [{}]"
                    , cashoutRecord.getUid(), cashoutRecord.getRmb());
            logger.info("[App][advstat][cashout][success] total {} uid {} rmb {}", riceroll, uid, rmb);
        }
        cashoutRecord.setTid(result.getTid());
        cashoutDao.update(cashoutRecord);

        return new Asset(result.getUserCoin(0), cashoutLimit);
    }

    public void requestThirdBankWithdraw(CashoutRecord cashoutRecord, UserBankCard bankCard, String idNo) {

    }

    public void updateRecordResultByRecordId(long id, String resCode, String resMsg, String resStatus) {
        logger.info("update cashout queue result :{}, {}, {}, {}", id, resCode, resMsg, resStatus);
        try {
            cashoutDao.updateQueueByRecordId(id, resCode, resMsg, resStatus);
        } catch (Exception e) {
            logger.error("update queue failed, recordId={}", id);
        }
    }

    @Transactional
    public CashoutRecordQueue saveCashoutRecordQueue(CashoutRecord record, UserBankCard bankCard, String idNo, String mobile, int bankType, int fkType) {
        CashoutRecordQueue recordQueue = new CashoutRecordQueue();
        recordQueue.setUid(record.getUid());
        recordQueue.setStatus(record.getStatus());
        recordQueue.setApp_status(record.getApp_status());
        recordQueue.setRmb(record.getRmb());
        recordQueue.setCommit_time(record.getCommit_time());
        recordQueue.setOrderid(record.getOrderid());
        recordQueue.setPlatform(record.getPlatform());
        recordQueue.setPfplatform(record.getPfplatform());
        recordQueue.setRiceroll(record.getRiceroll());
        recordQueue.setCard_id(bankCard.getId());
        recordQueue.setAccount_name(bankCard.getAccountName());
        recordQueue.setAccount_no(bankCard.getAccountNo());
        recordQueue.setOpen_bank_name(bankCard.getOpenBankName());
        recordQueue.setOpen_bank_no(bankCard.getOpenBankNo());
        recordQueue.setZfb_account(bankCard.getZfbAccount());
        recordQueue.setMobile(AppStringUtils.getMobile(mobile));
        recordQueue.setWithdraw_type(fkType);
        recordQueue.setRecord_id(record.getId());
        recordQueue.setId_no(idNo);
        recordQueue.setBank_type(bankType);
        recordQueue.setId((long) cashoutDao.save(recordQueue));
        recordQueue.setApp_id(record.getApp_id());
        return recordQueue;
    }

    private void verifyCashout(long uid, long rmb) {
        logger.info("User: " + uid + " rmb: " + rmb);
        if( rmb <= 0 ) {
            throw new ResponseException(ResponseError.E_CASHOUT_AMOUNT_LIMIT, "提现金额不能小于0");
        }

        //每2分钟只能领取1次
        int maxCashoutCount = 2;

        // 每分钟提现一次
        ValueOperations<String, String> ops = this.template.opsForValue();
        String key = "cashout_limit_" + uid;
        String value = "0";
        if (this.template.hasKey(key)) {
            value = ops.get(key);
        }
        if (!value.equals("0")) {
            // 有值,那就说,不能这么快的请求
            throw new ResponseException(ResponseError.E_CASHOUT_INTERVAL, "每2分钟只能领取1次");
        }
        ops.set(key, "1", maxCashoutCount, TimeUnit.MINUTES);
    }

    /**
     * APP提现记录列表
     */
    @DataSource(name = DataSource.slave)
    @Transactional
    public List<CashoutRecord> listAppRecordByUid(Long uid, int start, int limit) {
        return cashoutDao.listAppRecordByUidAndPlatform(uid, CashoutPlatform.App, start, limit);
    }

    @DataSource(name = DataSource.slave)
    @Transactional
    public List<CashoutRecord> listAllRecordByUid(Long uid, int start, int limit) {
        return cashoutDao.listAllRecordByUid(uid, start, limit);
    }

    /**
     * APP提现记录详情
     */
    @DataSource(name = DataSource.slave)
    @Transactional
    public CashoutRecord getAppRecordById(long uid, long id) {
        CashoutRecord record = cashoutDao.getRecordById(id);
        if (record != null && record.getUid() == uid) {
            return record;
        }
        return null;
    }

    /**
     * 如果是APP提现并且在1天内查询提现记录、异步从银行校准提现结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkSuccessCashout(CashoutRecord record) {
        if (record != null
                && record.getCommit_time().compareTo(DateUtils.day3BeforeNow()) > 0
                && record.getPfplatform() == CashoutPlatform.App.getValue()
                && record.getApp_status() == CashoutRecordAppStatus.ACCOUNT_SUCCESS.getValue()) {
            //加锁是为了避免重复退款
            CashoutRecordQueue recordQueue = cashoutDao.getRecordQueueByRecordIdLock(record.getId());
            if (recordQueue.getApp_status() == CashoutRecordAppStatus.ACCOUNT_SUCCESS.getValue()) {
                BankCashout bankCashout = bankCashoutFactory.getBankCashout(recordQueue.getBank_type());
                bankCashout.processSuccessOrder(recordQueue, false);
            }
        }
    }

    /**
     * 定时查询提现到账情况
     */
    public void checkProcessingOrder(BankType type) {
        List<CashoutRecordQueue> recordList = listUnconfirmedAppCashoutRecord(type, CashoutRecordAppStatus.INTO_ACCOUNTING.getValue());
        if (!recordList.isEmpty()) {
            BankCashout bankCashout = bankCashoutFactory.getBankCashout(type);
            for (CashoutRecordQueue record : recordList) {
                try {
                    bankCashout.processOrder(record);
                } catch (Exception e) {
                    //处理提现异常错误
                    logger.error("Process Unconfirm App Cash Record Error ", e);
                }
            }
        }
    }

    /**
     * 定时查询提现到账情况
     */
    public void checkSuccessOrder(BankType type, Date completeTime) {
        List<CashoutRecordQueue> recordList = listSuccessAppCashoutRecord(type.getValue(), completeTime);
        if (!recordList.isEmpty()) {
            for (CashoutRecordQueue record : recordList) {
                try {
                    BankCashout bankCashout = bankCashoutFactory.getBankCashout(type);
                    bankCashout.processSuccessOrder(record, true);
                } catch (Exception e) {
                    //处理提现异常错误
                    logger.error("Process Success App Cash Record Error", e);
                }
            }
        }
    }


    public void batchCheckRequestingOrder(BankType type, Date commitTime) {
        List<CashoutRecordQueue> recordList = queryTimeRangeRequestingAppCashoutRecord(BankType.JIUHE.getValue(), commitTime, CashoutRecordAppStatus.ACCOUNT_REQUESTING.getValue());
        if (CollectionUtils.isEmpty(recordList)) {
            logger.info("[batchCheckRequestingOrder] order batch requesting, no data was queried");
            return;
        }
        logger.info("[batchCheckRequestingOrder] order batch requesting, data => {}", JSON.toJSONString(recordList));
        BankCashout bankCashout = bankCashoutFactory.getBankCashout(type);
        bankCashout.batchIntegrateOrder(recordList);
    }

    public void checkProcessingOrder(BankType type, Date commitTime) {
        List<CashoutRecordQueue> recordList = listRequestingAppCashoutRecord(type.getValue(), commitTime, CashoutRecordAppStatus.INTO_ACCOUNTING.getValue());
        if (CollectionUtils.isEmpty(recordList)) {
            logger.info("[checkProcessingOrder] order query requesting, no data was queried");
            return;
        }
        logger.info("[checkProcessingOrder] order batch requesting, data => {}", JSON.toJSONString(recordList));
        BankCashout bankCashout = bankCashoutFactory.getBankCashout(type);
        for (CashoutRecordQueue record : recordList) {
            try {
                bankCashout.processOrder(record);
            } catch (Exception e) {
                logger.error("[checkProcessingOrder] Process Unconfirm App Cash Record Error, order_id => {} ", record.getOrderid(), e);
            }
        }
    }

    /**
     * 校准提现请求是否真是成功
     * 获得10分钟前提现确认中的提现订单
     */
    public void checkRequestingOrder(BankType type) {
        checkRequestingOrder(type, DateUtils.getBeforeDateForMinute(10));
    }

    public void checkRequestingOrder(BankType type, Date commitTime) {
        List<CashoutRecordQueue> recordList = listRequestingAppCashoutRecord(type.getValue(), commitTime, CashoutRecordAppStatus.INTO_ACCOUNTING.getValue());

        if (CollectionUtils.isEmpty(recordList)) {
            logger.info("[checkRequestingOrder] order query requesting, no data was queried");
            return;
        }
        logger.info("[checkRequestingOrder] order batch requesting, data => {}", JSON.toJSONString(recordList));
        BankCashout bankCashout = bankCashoutFactory.getBankCashout(type);
        for (CashoutRecordQueue record : recordList) {
            try {
                bankCashout.processDealingOrder(record);
            } catch (Exception e) {
                logger.error("[checkRequestingOrder] Process not Success request Cash Record Error, order_id => {} ", record.getOrderid(), e);
            }
        }
    }


    public void emitCashout(CashoutRecord record) {
        emitCashout(record.getId(),
                record.getUid(),
                record.getRmb(),
                record.getRiceroll(),
                record.getPfplatform(),
                record.getApp_id(),
                record.getCommit_time(),
                record.getComplete_time());
    }

    public void emitCashout(CashoutRecordQueue record) {
        emitCashout(record.getRecord_id(),
                record.getUid(),
                record.getRmb(),
                record.getRiceroll(),
                record.getPfplatform(),
                record.getApp_id(),
                record.getCommit_time(),
                record.getComplete_time());
    }

    public void emitCashout(long id, long uid, long rmb, long riceroll, int platform, int appId, Date commitTime, Date completeTime) {
        try {
            CashoutEvent event = CashoutEvent.builder()
                    .id(id)
                    .uid(uid)
                    .rmb(rmb)
                    .riceroll(riceroll)
                    .platform(platform)
                    .appId(appId)
                    .commitTime(commitTime)
                    .completeTime(completeTime)
                    .messageTime(new Date())
                    .build();
            eventProducer.emitAsync(CashoutEventType.TOPIC_NAME, String.valueOf(uid), CashoutEventType.SUCCESS.getValue(), event);
            logger.info("[emitCashout] 1. cashout success, uid={}, event={}", uid, JsonUtils.toString(event));
        } catch (Exception e) {
            logger.error("[emitCashout] 1. uid={}", uid, e);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCashoutSuccess(CashoutRecordQueue record, String resCode, String resMsg, String resStatus) {
        cashoutDao.updateCashRecordSuccess(record,
                CashoutRecordAppStatus.ACCOUNT_SUCCESS,
                OrderStatus.ORS_SUCCESS,
                resCode, resMsg, resStatus);

        //提现成功事件
        emitCashout(record);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCashoutFail(CashoutRecordQueue record, String resCode, String resMsg, String resStatus) {
        refundUserRiceRoll(record, resCode, resMsg, resStatus);
        //清除缓存
        removeFromCache(record.getUid());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCashoutFailWithCheck(CashoutRecordQueue record, String resCode, String resMsg, String resStatus) {
        refundUserRiceRoll(record, resCode, resMsg, resStatus, true);
        //清除缓存
        removeFromCache(record.getUid());
    }

    /**
     * 退款逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public void refundUserRiceRoll(CashoutRecordQueue record, String resCode, String resMsg, String resStatus) {
        refundUserRiceRoll(record, resCode, resMsg, resStatus, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void refundUserRiceRoll(CashoutRecordQueue record, String resCode, String resMsg, String resStatus, Boolean isCheck) {
        /**
         * 1. 更新数据库状态和完成时间、删除队列表
         * 2. 退还饭团
         * 3. 恢复额度和月额度
         */
        //1. 更新数据库状态和完成时间、删除队列表
        cashoutDao.updateCashRecordSuccess(record,
                CashoutRecordAppStatus.REFUND_SUCCESS,
                OrderStatus.ORS_FAILED,
                resCode,
                resMsg,
                resStatus);
        refundUserRiceRoll(record, isCheck);
    }

    @Transactional(rollbackFor = Exception.class)
    public void refundUserRiceRoll(CashoutRecordQueue record, Boolean isCheck) {
        if (isCheck != null) {
            cashoutDao.updateCashRecordCheckStatusById(record.getId());
        }


        User user = userService.getById(record.getUid());
        logger.info("[App Cashout] Refund User RiceRoll uid = {}, record={}", user.getId(), record);
        //2. 恢复额度
        logger.info("recovery cashout start.. {}", user.getId());
        CashoutLimitEntity limit = cashoutLimitRepository.findByUid(user.getId());

        CashoutIdentityLimitEntity identityLimit = cashoutIdentityLimitRepository.findByIdCard(record.getId_no());

        userCoinDao.resetAppDayCashout(limit, identityLimit, record.getRmb(), record.getCommit_time());
        logger.info("recovery cashout end.. {}", user.getId());

        //3. 退还饭团
        try {
            assetService.unaryOperate(AssetSystemID.APP_CASHOUT_REFUND, user, record.getRiceroll());
        } catch (Exception e) {
            logger.error("[App Cashout] Refund User RiceRoll Error =", e);
            throw e;
        }

        //4. 恢复钱包额度
        bizcoreCenterService.modifyWalletRmb(user.getId(), record.getRmb());
    }

    public void updateCashRecordCheckStatusById(long id) {
        cashoutDao.updateCashRecordCheckStatusById(id);
    }

    @DataSource(name = DataSource.slave)
    @Transactional
    public List<CashoutRecordQueue> listUnconfirmedAppCashoutRecord(BankType bankType, int appStatus) {
        return cashoutDao.listUnconfirmedAppCashoutRecord(bankType, appStatus);
    }

    @DataSource(name = DataSource.slave)
    @Transactional
    public List<CashoutRecordQueue> listSuccessAppCashoutRecord(int bankType, Date completeTime) {
        return cashoutDao.listSuccessAppCashoutRecord(completeTime, bankType);
    }

    /**
     * 获得指定分钟数前提现确认中的提现订单
     */
    @Transactional
    public List<CashoutRecordQueue> listRequestingAppCashoutRecord(int bankType, Date commitTime, int appStatus) {
        return cashoutDao.listRequestingAppCashoutRecord(commitTime, bankType, appStatus);
    }

    /**
     * 获得指定时间内的提现确认中的提现订单
     */
    @Transactional
    public List<CashoutRecordQueue> queryTimeRangeRequestingAppCashoutRecord(int bankType, Date commitTime, int appStatus) {
        return cashoutDao.queryTimeRangeRequestingAppCashoutRecord(commitTime, bankType, appStatus);
    }

    /**
     * 新增或者更新会员信息
     *
     * @param uid
     * @param operType
     * @param accountNo
     * @param accountName
     * @param bankNo
     * @param bankName
     * @param idNo
     * @param cellPhone
     * @param zfbAccount
     * @param front
     * @param back
     * @return
     */
    public Object customerSign(long uid, int operType, String accountNo, String accountName, String bankNo, String bankName, String idNo, String cellPhone, String zfbAccount, String front, String back, String platformId) {

        SignContractModel dto = new SignContractModel();
        dto.setUid(uid);
        dto.setOperType(operType);
        dto.setAccountName(accountName);
        dto.setAccountNo(accountNo);
        dto.setBankName(bankName);
        dto.setBankNo(bankNo);
        dto.setIdCard(idNo);
        dto.setPhone(cellPhone);
        dto.setZfbAccount(zfbAccount);
        dto.setFront(front);
        dto.setBack(back);
        dto.setPlatformId(platformId);

        logger.info("customerSign data:{}", dto);

        //TODO: 银行卡、身份证、姓名真实性验证


        // 修正手机号
        String phoneNumber = adjustPhoneNumber(cellPhone);
        if( !StringUtils.equals(cellPhone, phoneNumber)) {
            logger.info("Adjust phone number {} -> {}", cellPhone, phoneNumber);
            dto.setPhone(phoneNumber);
        }



        BankType bankType = getKeyWithdrawBankType();

        BankCashout bankCashout = bankCashoutFactory.getBankCashout(bankType);

        if (BankSignOperateType.ADD.getCode() == operType) {
            String ret = bankCashout.signContract(dto);
            if (ret == null) {
                logger.error("Add cashout fail data:{}", dto);
                return null;
            }

            return ret;
        }

        if (BankSignOperateType.UPDATE.getCode() == operType) {
            Boolean ret = bankCashout.updateMember(dto);
            if (ret == null) {
                logger.error("Update cashout fail data:{}", dto);
                return false;
            }

            return ret;
        }

        logger.error("Cashout operType not found, data:{}", dto);
        return null;
    }

    private String adjustPhoneNumber(String cellPhone) {
        if(cellPhone != null) {
            String[] items = cellPhone.split("_");
            if( items.length == 2 ) {
                return items[1];
            }
        }
        return cellPhone;
    }

    /**
     * 是否线下提现用户
     */
    @Transactional
    public boolean isOfflineCashoutUser(long uid) {
        CashoutOffline cashoutOffline = cashoutDao.getCashoutOfflineByUid(uid);
        return cashoutOffline != null;
    }

    /**
     * 查询APP限额
     */
    @Transactional
    public CashoutLimitEntity getOrCreateCashoutLimit(long uid) {
        return getOrCreateCashoutLimit(uid, false);
    }

    /**
     * 查询APP限额
     */
    @Transactional
    public CashoutLimitEntity getOrCreateCashoutLimit(long uid, boolean lock) {
        /**
         * 1.查询是否有APP提现限额记录，如果没有则插入
         * 2.有查询当日限额
         * 3. FIX : 提现额度需要加悲观锁
         */
        CashoutLimitEntity cashoutLimit;
        if (lock) {
            cashoutLimit = cashoutLimitRepository.findByUidWithLock(uid);
        } else {
            cashoutLimit = cashoutLimitRepository.findByUid(uid);
        }
        if (cashoutLimit == null) {
            CashoutLimitEntity limit = new CashoutLimitEntity(uid);
            cashoutLimit = cashoutLimitRepository.saveAndFlush(limit);
        }
        return cashoutLimit;
    }

    /**
     * 查询每月限额
     */
    @Transactional
    public CashoutIdentityLimitEntity getOrCreateCashoutIdentityLimit(String idCard) {
        /**
         * 1.查询是否有APP提现限额记录，如果没有则插入
         * 2.有查询当日限额
         * 3. FIX : 提现额度需要加悲观锁
         */
        CashoutIdentityLimitEntity identityLimit = cashoutIdentityLimitRepository.findTopByIdCard(idCard);
        if (identityLimit == null) {
            CashoutIdentityLimitEntity limit = new CashoutIdentityLimitEntity(idCard);
            identityLimit = cashoutIdentityLimitRepository.saveAndFlush(limit);
        }
        return identityLimit;
    }


    public CashoutLimitEntity getCashoutLimit(long uid) {
        CashoutLimitEntity limit = getFromCache(uid);
        if (limit == null) {
            limit = cashoutLimitRepository.findByUid(uid);
            //cashoutDao.getCashoutLimitByUid(uid);
            if (limit == null) {
                limit = getDefaultCashoutLimit(uid);
            }
            putToCache(limit);
        }
        return limit;
    }


    public Map<Long, CashoutLimitEntity> getCashoutLimitByUids(List<Long> uidList) {
        Map<Long, CashoutLimitEntity> limits = new HashMap<>();
        cashoutLimitRepository.findByUidIn(uidList).forEach(cashoutLimit -> {
            limits.put(cashoutLimit.getUid(), cashoutLimit);
        });

        uidList.forEach(uid -> {
            if (!limits.containsKey(uid)) {
                limits.put(uid, getDefaultCashoutLimit(uid));
            }
        });
        return limits;
    }


    private CashoutLimitEntity getDefaultCashoutLimit(Long uid) {
        CashoutLimitEntity limit = new CashoutLimitEntity();
        limit.setUid(uid);
        limit.setDayCashout(0);
        limit.setCashoutDate(DateUtils.getFirstDate());
        limit.setDayCashoutLimit(DEFAULT_DAY_CASHOUT_LIMIT);
        limit.setIsForbid(CashoutLimitForbid.NO);
        return limit;
    }

    @Transactional
    public void setLimitCashout(long uid, long limit) {
        if (limit > MAX_CASHOUT_OFFLINE)
            throw new ResponseException(ResponseError.E_CASHOUT_OFFLINE, "Max cashout limit");
        CashoutLimitEntity cashoutLimit = getOrCreateCashoutLimit(uid);
        cashoutLimit.setDayCashoutLimit(limit);
        cashoutLimitRepository.save(cashoutLimit);
        removeFromCache(uid);
    }


    private void updateCashoutLimit(CashoutLimitEntity cashoutLimit, long rmb) {
        if (!DateUtils.isToday(cashoutLimit.getCashoutDate())) {
            cashoutLimit.setCashoutDate(DateUtils.getTodayDate());
            cashoutLimit.setDayCashout(rmb);
        } else {
            cashoutLimit.setDayCashout(cashoutLimit.getDayCashout() + rmb);
        }
        cashoutLimitRepository.save(cashoutLimit);
        removeFromCache(cashoutLimit.getUid());
    }

    private void updateCashoutIdentityLimit(CashoutIdentityLimitEntity identityLimit, long rmb) {
        if (DateUtils.isCurrentMonth(identityLimit.getCashoutDate())) {
            identityLimit.setMonthCashout(identityLimit.getMonthCashout() + rmb);
        } else {
            identityLimit.setMonthCashout(rmb);
        }

        if (!DateUtils.isToday(identityLimit.getCashoutDate())) {
            identityLimit.setCashoutDate(DateUtils.getTodayDate());
            identityLimit.setDayCashout(rmb);
        } else {
            identityLimit.setDayCashout(identityLimit.getDayCashout() + rmb);
        }

        cashoutIdentityLimitRepository.save(identityLimit);
    }

    private CashoutLimitEntity getFromCache(long uid) {
        ValueOperations<String, String> ops = redisTemplate.opsForValue();
        String value = ops.get(getKey(uid));
        if (value != null) {
            return JsonUtils.fromString(value, CashoutLimitEntity.class);
        }
        return null;
    }

    private void putToCache(CashoutLimitEntity limit) {
        String key = getKey(limit.getUid());
        String value = JsonUtils.printObject(limit);
        redisTemplate.opsForValue().set(key, value, CASHOUT_LIMIT_CACHE_TTL, TimeUnit.SECONDS);
    }

    public void removeFromCache(long uid) {
        redisTemplate.delete(getKey(uid));
    }

    private String getKey(long uid) {
        return cacheConfig.getPrefix() + CASHOUT_LIMIT_CACHE_KEY + uid;
    }

    private void setKeyWithdrawBankType(String bankType) {
        redisTemplate.opsForValue().set(KEY_WITHDRAW_BANK_TYPE, String.valueOf(bankType), KEY_WITHDRAW_BANK_TYPE_TTL, TimeUnit.SECONDS);
    }

    private BankType getKeyWithdrawBankType() {
        //TODO 恢复从缓存获取
        String value = redisTemplate.opsForValue().get(KEY_WITHDRAW_BANK_TYPE);
        if (value == null) {
            Optional<SysSettingEntity> sysSettingEntity = sysSettingRepository.findTopByMetaKeyAndIsDeletedIsFalse(SysSettingMetaKey.WITHDRAW_BANK.getValue());
            if (sysSettingEntity.isPresent()) {
                setKeyWithdrawBankType(sysSettingEntity.get().getMetaValue());
                value = sysSettingEntity.get().getMetaValue();
            } else {
                return BankType.JIUHE;
            }
        }
        int val = Integer.parseInt(value);
        BankType bankType = BankType.fromValue(val);
        if (bankType == null) {
            return BankType.JIUHE;
        }
        return bankType;
    }


    public void updateCashoutQueueById(String ids) {
        Set<Long> idSet = AppStringUtils.idToSet(ids);
        if (idSet.isEmpty()) {
            return;
        }

        idSet.forEach(this::updateCashoutQueueById);
    }

    /**
     * 修改is_check
     */
    public void updateCashoutQueueById(long id) {
        // 如果状态为4则不修改
        CashoutRecordQueue entity = cashoutDao.getRecordQueueById(id);
        if (CashoutRecordAppStatus.REFUND_SUCCESS.getValue() == entity.getApp_status()) {
            logger.error("the order has been refunded, id={}", id);
            return;
        }

        cashoutDao.updateCashRecordCheckStatusById(id, 0);
    }

    //==================================APP提现结束===============================================


    /**
     * 交易成功，更新订单状态，发送提现成功事件
     *
     * <AUTHOR>
     * @date 2023/11/22
     **/
    @Transactional(rollbackFor = Exception.class)
    public void transactionSuccess(CashoutRecordQueue record, String resCode, String resMsg, String resStatus, boolean isCheck) {
        //原交易确认成功
        boolean success = cashoutDao.updateCashRecordSuccess(record, CashoutRecordAppStatus.ACCOUNT_SUCCESS, OrderStatus.ORS_SUCCESS, resCode, resMsg, resStatus);
        if (success) {
            if (isCheck) {
                cashoutDao.updateCashRecordCheckStatusById(record.getId());
            }
            //提现成功事件
            emitCashout(record);
        } else {
            logger.error("[JiuHe][transactionSuccess] order_id = {}, update is fail, db record status something may have changed", record.getOrderid());
        }
    }

    /**
     * 交易失败，更新订单状态， 退款饭团，清除缓存
     *
     * <AUTHOR>
     * @date 2023/11/22
     **/
    @Transactional(rollbackFor = Exception.class)
    public void transactionFail(CashoutRecordQueue record, String resCode, String resMsg, String resStatus) {
        if (resMsg == null) {
            resMsg = "";
        }

        //1. 更新数据库状态和完成时间、删除队列表
        boolean update = cashoutDao.updateCashRecordSuccess(record, CashoutRecordAppStatus.REFUND_SUCCESS, OrderStatus.ORS_FAILED, resCode, resMsg, resStatus);
        if (update) {
            //交易失败，退款逻辑
            refundUserRiceRoll(record, true);
            //清除缓存
            removeFromCache(record.getUid());
        } else {
            logger.error("[transactionFail] order_id = {}, update is fail, db record status something may have changed", record.getOrderid());
        }
    }

    @Transactional(readOnly = true)
    public List<UserBankCard> getAppCashoutUser(Date start, Date end) {
        List<Long> uids = cashoutDao.getCashoutUidsByDate(start, end);
        return userBankCardRepository.findByUidIn(uids);
    }


    /**
     * 处理新云平台10分钟前提现确认中的订单
     * <AUTHOR>
     * @date 2023/11/16
     **/
    /*public void xinYunBankCashoutConfirmingOrder() {
        List<CashoutRecordQueue> recordList = listRequestingAppCashoutRecord(BankType.XINYUN.getValue(),DateUtils.getBeforeDateForMinute(10) , CashoutRecordAppStatus.ACCOUNT_REQUESTING.getValue());
        if (null != recordList && recordList.size() > 0){
            BankCashout bankCashout = bankCashoutFactory.getBankCashout(BankType.XINYUN);
            for (CashoutRecordQueue record : recordList) {
                try {
                    bankCashout.processDealingOrder(record);
                } catch (Exception e) {
                    logger.error("[xinYunBankCashoutConfirmingOrder] Process Unconfirm App Cash Record Error, order id => {}", record.getOrderid(), e);
                }
            }
        }
    }*/

    /**
     * 处理新云平台提现未成功的订单
     * <AUTHOR>
     * @date 2023/11/16
     **/
    /*public void xinYunBankCashoutNotSuccessOrder() {
        List<CashoutRecordQueue> recordList = listUnconfirmedAppCashoutRecord(BankType.XINYUN, CashoutRecordAppStatus.INTO_ACCOUNTING.getValue());
        if (null != recordList && recordList.size() > 0) {
            BankCashout bankCashout = bankCashoutFactory.getBankCashout(BankType.XINYUN);
            for (CashoutRecordQueue record : recordList) {
                try {
                    bankCashout.processOrder(record);
                } catch (Exception e) {
                    logger.error("[xinYunBankCashoutConfirmingOrder] Process Unconfirm App Cash Record Error, order id => {}", record.getOrderid(), e);
                }
            }
        }
    }*/
}
