package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.entity.AppEntity;
import com.easylive.pay.enums.PayPlatform;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
@Service
public class PayService {

    @Autowired
    private AppService appService;


    private volatile Map<String, RechargeDetailConfig> detailConfig = new HashMap<>();

    private RechargeDetailConfig defaultConfig;
    private static final BigDecimal HUNDRED = new BigDecimal(100);

    @PostConstruct
    protected void init() {

        defaultConfig = new RechargeDetailConfig();
        defaultConfig.setEcoinName("金币");
        defaultConfig.setGlobal(false);
        defaultConfig.setShowAmount(true);

        RechargeDetailConfig overseaConfig = new RechargeDetailConfig();
        overseaConfig.setEcoinName("Ecoins");
        overseaConfig.setGlobal(true);
        overseaConfig.setShowAmount(false);

        detailConfig.put("yizhibo", defaultConfig);
        detailConfig.put("oversea", overseaConfig);
    }

    public String getProductName(PayPlatform platform, long amount) {
        return getProductName(platform, Constants.DEFAULT_APP_ID, amount,null);
    }

    public String getProductName(PayPlatform platform, int appId, long amount, Long ecoin) {
        AppEntity appEntity = appService.getAppById(appId);
        String appName = "";
        if (appEntity != null)
            appName = appEntity.getName();

        return getProductName( appName, amount,ecoin);
    }

    public String getProductName(PayPlatform platform, String appName, long amount) {
        RechargeDetailConfig config = getDetailConfig(appName);
        return getProductName(config, amount,null);
    }

    public String getProductName(String appName, long amount, Long ecoins) {
        RechargeDetailConfig config = getDetailConfig(appName);
        return getProductName(config, amount,ecoins);
    }

    private String getProductName(RechargeDetailConfig config, long amount,Long ecoins) {
        if (config.isGlobal()) {
            long ecoin = ecoins != null ? ecoins: (amount / 10);
            return ecoin + " " + config.getEcoinName();
        }

//        if( config.isShowAmount() ) {
        BigDecimal totalFeeDecimal = new BigDecimal(amount);
        String s = String.valueOf(totalFeeDecimal.divide(HUNDRED, 2, RoundingMode.HALF_UP));
        return "充值" + s + "元";
        //       }

    }

    private RechargeDetailConfig getDetailConfig(String appName) {
        RechargeDetailConfig ret = detailConfig.get(appName);
        return ret == null ? defaultConfig : ret;
    }


    @Data
    private static class RechargeDetailConfig {
        private boolean global;
        private boolean showAmount;
        private String ecoinName;
    }
}
