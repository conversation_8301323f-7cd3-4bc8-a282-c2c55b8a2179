package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.DataSource;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.event.EventProducer;
import com.easylive.pay.config.ApplicationConfig;
import com.easylive.pay.config.CashConfig;
import com.easylive.pay.dao.RechargeDao;
import com.easylive.pay.dao.RechargeWrongTransferRepository;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.*;
import com.easylive.pay.model.*;
import com.easylive.pay.model.user.UserInfo;
import com.easylive.pay.model.user.UserInfoOption;
import com.easylive.pay.output.RechargeCount;
import com.easylive.pay.output.RechargeRecordStatus;
import com.easylive.pay.pub.event.RechargeEvent;
import com.easylive.pay.pub.event.RechargeEventType;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class RechargeService {

    private static final Logger logger = LoggerFactory.getLogger(RechargeService.class);


    private static final int RECHARGE_PLATFORM_ALL = 0;
    private static final double BASE_RMB_TO_ECOIN_SCALE = 5;
    private static final double DEFAULT_RMB_TO_ECOIN_SCALE = 10;
    //private static final double RMB_TO_ECOIN_SCALE = 9.18559;
    private static final double RMB_TO_ECOIN_SCALE = 10;

    private static final long RECHARGE_MIN_AMOUNT = 100;

    private static final Map<Integer,Double> scaleMap = new HashMap<>();
    static {
        scaleMap.put(Constants.FURO_LIVE_APPID,9.0);
    }

    private static final long RECHARGE_OFFLINE_INTERVAL = 10;
    @Autowired
    private RechargeDao rechargeDao;
    @Autowired
    private AssetService assetService;
    @Autowired
    private CashoutService cashoutService;
    @Autowired
    private AppGateService appgateService;
    @Autowired
    private UserService userService;
    @Autowired
    private CashConfig cashConfig;
    @Autowired
    private ApplicationConfig applicationConfig;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private EventProducer eventProducer;

    @Autowired
    private BizcoreCenterService bizcoreCenterService;

    @Autowired
    private RechargeWrongTransferRepository rechargeWrongTransferRepository;

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public List<RechargeRecord> listRecordByUid(Long uid, int start, int limit) {
        return rechargeDao.getByUid(uid, start, limit);
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public RechargeCount getTotalRechargeCountByUid(Long uid) {
        return rechargeDao.getTotalByUid(uid);
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public RechargeCount getTotalRechargeCountByUidAndTime(Long uid, Date start, Date end) {
        return rechargeDao.getTotalByUidAndTime(uid, start, end);
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public List<RechargeStatByPlatform> statSuccessRechargeRecordWithPlatformGroup(RechargeSearchCriteria criteria) {
        validateSearchCriteria(criteria);
        Map<String,RechargeStatByPlatform> map = new HashMap<>();
        rechargeDao.statSuccessSearchRecordWithPlatformAndCurrency(criteria).forEach( stat -> {
            PrimaryPayPlatform pp = PrimaryPayPlatform.fromSubPlatform(stat.getPlatform());
            if( pp != null ) {
                String key = pp.getValue() + "_" + stat.getCurrency();
                RechargeStatByPlatform platformStat = map.get( key);
                if( platformStat == null ) {
                    platformStat = new RechargeStatByPlatform(pp.getValue());
                    platformStat.setCurrency(stat.getCurrency());
                    map.put(key, platformStat);
                }
                platformStat.accumulate(stat);
            }
        });

        return new ArrayList<>(map.values());
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public RechargeSearchResult.RechargeSearchStat statSuccessRechargeRecord(RechargeSearchCriteria criteria) {
        validateSearchCriteria(criteria);
        return rechargeDao.statSuccessSearchRecord(criteria);
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public RechargeSearchResult.RechargeSearchStat statAllRechargeRecord(RechargeSearchCriteria criteria) {
        validateSearchCriteria(criteria);
        return rechargeDao.statAllSearchRecord(criteria);
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public RechargeSearchResult searchRechargeRecord(RechargeSearchCriteria criteria) {
        logger.info("Search recharge record: {}", JsonUtils.toString(criteria));

        //检查查询条件合法性，转换参数
        validateSearchCriteria(criteria);

        logger.info("unified search criteria: {}", JsonUtils.toString(criteria));
        List<RechargeRecord> records = rechargeDao.searchRecord(criteria);

        if (records.isEmpty()){
            return new RechargeSearchResult();
        }

        int totalRecords = rechargeDao.countSearchRecord(criteria);


        String uids = records.stream().map(e -> String.valueOf(e.getUid())).distinct().collect(Collectors.joining(","));
        Map<Long, UserInfo> userInfoMap = userService.multiGetByUids(uids, EnumSet.of(UserInfoOption.AUTH, UserInfoOption.ATTR, UserInfoOption.EDIT, UserInfoOption.REG)).stream().collect(Collectors.toMap(UserInfo::getUid, u -> u));


        List<RechargeSearchResultItem> ret = new ArrayList<>(records.size());
        for (RechargeRecord r : records) {
            RechargeSearchResultItem sr = new RechargeSearchResultItem();
            sr.setAppId(r.getAppId());
            sr.setEcoin(r.getEcoin());
            sr.setAmount(r.getAmount() == null ? r.getRmb() : r.getAmount());
            sr.setCurrency(r.getCurrency());
            sr.setRmb(r.getRmb());
            sr.setMcurrency(r.getMerchantCurrency());
            sr.setCommitTime(r.getCommitTime());
            sr.setCompleteTime(r.getCompleteTime());

            sr.setStatus(r.getStatus());
            processStatus(sr,r.getStatus(),criteria);

            sr.setOrderId(r.getOrderId());
            sr.setPlatformOrderId(r.getPlatformOrderId());
            sr.setTradeNo(r.getPlatformOrderId());
            sr.setSubPlatform(r.getPlatform());
            PrimaryPayPlatform pp = PrimaryPayPlatform.fromSubPlatform(r.getPlatform());
            if (pp != null){
                sr.setPrimaryPlatform(pp.getValue());
            }
            sr.setClientPlatform(r.getClientPlatform());

            UserInfo userInfo = userInfoMap.get(r.getUid());
            if (null != userInfo ){
                sr.setName(userInfo.getName());
                sr.setCid(userInfo.getAttr().getCid());
                sr.setNickname(userInfo.getEdit().getNickname());
                sr.setRegisterTime(userInfo.getReg().getRegisterTime());
                String phone = "";
                try {
                    phone = userInfo.getAuth().stream().collect(Collectors.toMap(UserInfo.AuthInfo::getAuthType, UserInfo.AuthInfo::getToken)).get("phone");
                } catch (Exception ignore) {
                }
                sr.setPhone(phone);
            }
            ret.add(sr);
        }


        RechargeSearchResult.RechargeSearchStat stat = new RechargeSearchResult.RechargeSearchStat();
        if( (criteria.getOrderId() != null || criteria.getPlatformOrderId() != null ) && !ret.isEmpty() ) {
            RechargeSearchResultItem item = ret.get(0);
            stat.setCount(1);
            stat.setRmb(item.getRmb());
            stat.setEcoin(item.getEcoin());
            stat.setCountByUser(1);
        } else {
            stat = rechargeDao.statSuccessSearchRecord(criteria);
        }

        RechargeSearchResult result = new RechargeSearchResult(criteria.getStart(), totalRecords, ret);
        result.setSuccessStat(stat);
        return result;
    }

    private void processStatus(RechargeSearchResultItem sr, int status, RechargeSearchCriteria criteria) {
        if ( status == OrderStatus.ORS_VALID.getValue()){
            sr.setStatus(OrderStatus.ORS_SUCCESS.getValue());
        }
    }
    private void validateSearchCriteria(RechargeSearchCriteria criteria) {

        // 平台参数检测和转换
        if (criteria.getPlatform() != null) {
            Set<Integer> hs = new HashSet<>();
            for (Integer p : criteria.getPlatform()) {

                PrimaryPayPlatform primaryPayPlatform = PrimaryPayPlatform.fromValue(p);
                if (primaryPayPlatform == null)
                    throw new ResponseException(ResponseError.E_PARAM, "Invalid platform");
                primaryPayPlatform.getPlatforms().forEach(pp -> {
                    hs.add(pp.getValue());
                });
            }
            criteria.setPlatform(new ArrayList<>(hs));
        }

        // name参数，有uid就优先用uid，
        if (criteria.getUid() == null && criteria.getName() != null && !criteria.getName().isEmpty()) {
            Set<Long> hs = new HashSet<>();
            List<UserInfo> users = userService.multiGetByName(criteria.getName());
            users.forEach(u -> {
                hs.add(u.getUid());
            });

            if (hs.isEmpty())
                throw new ResponseException(ResponseError.E_PARAM, "Invalid name");
            criteria.setUid(new ArrayList<>(hs));
        }

        // check date time: 不超过一年
        Date startDate = criteria.getStartDate();
        Date endDate = criteria.getEndDate();
        Date oneDayAgo = new DateTime().minusDays(1).toDate();
        Date now = new DateTime().toDate();

        if (startDate == null)
            startDate = oneDayAgo;


        if (endDate == null)
            endDate = now;

        if( criteria.getCount() == null )
            criteria.setCount(10);

        if( criteria.getStart() == null )
            criteria.setStart(0);


        boolean singleQuery = criteria.getOrderId() != null || criteria.getPlatformOrderId() != null;

        if( !applicationConfig.isDevMode() && !singleQuery ) {
            if( new DateTime(endDate).minusYears(1).toDate().after(startDate) ) {
                throw new ResponseException(ResponseError.E_PARAM, "间隔不能超过一年");
            }

            Date toD = new DateTime(startDate).minusYears(1).toDate();

            if( new DateTime(startDate).toDate().before(toD) ) {
                throw new ResponseException(ResponseError.E_PARAM, "一年以上的记录不能查询");
            }
        }

        if (startDate.after(now))
            throw new ResponseException(ResponseError.E_PARAM, "Invalid start date");

        if( !singleQuery ) {
            criteria.setStartDate(startDate);
            criteria.setEndDate(endDate);
        }
    }


    @Transactional
    public void markRecordDeleted(long uid, String orderId) {
        logger.info("Mark recharge {} as deleted", orderId);
        RechargeRecord rechargeRecord = rechargeDao.getByOrderId(orderId);

        if (rechargeRecord == null) {
            logger.info("Recharge order id does not exists {}", orderId);
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID);
        }

        if (rechargeRecord.getUid() != uid) {
            logger.info("{} does not own order id {}", uid, orderId);
        } else {
            rechargeRecord.setDeleted(true);
            rechargeDao.saveOrUpdate(rechargeRecord);
        }

    }

    @Transactional(readOnly = true)
    public RechargeRecord getRecordByOrderId(String orderId) {
        RechargeRecord r = rechargeDao.getByOrderId(orderId);
        if (r.getStatus() == OrderStatus.ORS_VALID.getValue())
            r.setStatus(OrderStatus.ORS_SUCCESS.getValue());
        return r;
    }


    @Transactional(readOnly = true)
    public RechargeRecordStatus getRecordStatus(String orderId) {
        RechargeRecord rechargeRecord = this.getRecordByOrderId(orderId);
        if (rechargeRecord == null) {
            logger.info("recharge order id " + orderId + " doest not exists");
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "order id not exists");
        }
        RechargeRecordStatus status = new RechargeRecordStatus();
        status.setOrderId(orderId);
        status.setEcoin( rechargeRecord.getEcoin());
        status.setAmount( rechargeRecord.getRmb());
        int statusInt = rechargeRecord.getStatus();
        if( statusInt != OrderStatus.ORS_SUCCESS.getValue() )
            statusInt = OrderStatus.ORS_CREATE.getValue();;
        status.setStatus( statusInt );
        return status;
    }

    @Transactional
    public RechargeRecord getRecordByOrderIdWithLock(String orderId) {
        RechargeRecord r = rechargeDao.getByOrderIdWithLock(orderId);
        if( r == null )
            return null;
        if (r.getStatus() == OrderStatus.ORS_VALID.getValue())
            r.setStatus(OrderStatus.ORS_SUCCESS.getValue());
        return r;
    }

    @Transactional(readOnly = true)
    public RechargeRecord ensureRecordByOrderId(String orderId) {
        RechargeRecord rr = rechargeDao.getByOrderId(orderId);
        if (rr == null)
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID);
        return rr;
    }

    @Transactional
    public void completeTradeNo(RechargeRecord r, String tid, String platformOrderId, long ecoin) {
        rechargeDao.completeTradeNo(r, tid, platformOrderId, ecoin);
    }

    public BigDecimal getBaseRmbToEcoinScale(BigDecimal scale) {
        BigDecimal base = new BigDecimal(BASE_RMB_TO_ECOIN_SCALE);
        return base.compareTo(scale) < 0 ? base : scale;
    }

    private void emitRechargeEvent(RechargeRecord r) {
        try {
            RechargeEvent event = new RechargeEvent();
            event.setAmount(r.getRmb());
            event.setCommitTime(r.getCommitTime());
            event.setCompleteTime(r.getCompleteTime());
            event.setCurrency(r.getCurrency());
            event.setEcoin(r.getEcoin());
            event.setUid(r.getUid());
            event.setMerchantCurrency(r.getMerchantCurrency());
            event.setMerchantId(r.getMerchantId());
            event.setPlatform(r.getPlatform());
            event.setPlatformOrderId(r.getPlatformOrderId());
            event.setOrderId(r.getOrderId());
            event.setTid(r.getTid());
            event.setAppId(r.getAppId());
            event.setClientPlatform(r.getClientPlatform());

            UserInfo user = userService.getInfoByUid(r.getUid(), UserInfoOption.ATTR.name());
            if (user != null) {
                UserInfo.AttributeInfo attr = user.getAttr();
                if (attr != null) {
                    event.setUserAppId(attr.getAppId());
                    event.setUserAppSourceId(attr.getAppSourceId());
                }
            }
            eventProducer.emitAsync(RechargeEventType.TOPIC_NAME, String.valueOf(event.getUid()), RechargeEventType.COMPLETE.getValue(), event);
            logger.info("Emit recharge event: {}", event.toString());
        } catch (Exception e) {
            logger.error("Error emit recharge event", e);
        }
    }

    @Transactional(readOnly = true)
    public RechargeOption getOptionByProductId(long platform, String productId) {
        return rechargeDao.getOptionByProductId(platform, productId);
    }

    @Transactional(readOnly = true)
    public RechargeRecord getByPlatformOrderId(String orderId) {
        return rechargeDao.getByPlatformOrderId(orderId);
    }

    /**
     * @param device 设备类型，1表示android，2表示ios
     * @return 充值选项列表
     */
    @Transactional
    public List<RechargeOption> getOptionListByDevice(int device) {
        List<RechargeOption> list = rechargeDao.getOptionListByDevice(device);
        if (list == null) {
            throw new ResponseException(ResponseError.E_RECHARGE_PLATFORM);
        }
        return list;
    }

    @Transactional(readOnly = true)
    public List<RechargeOption> getOptionListByPlatform(long platform) {
        List<RechargeOption> list = rechargeDao.getOptionListByPlatform(platform);
        if (list == null) {
            throw new ResponseException(ResponseError.E_RECHARGE_PLATFORM);
        }
        return list;
    }

    @Transactional(readOnly = true)
    public List<RechargeOption> getOptionListByPlatformAndAppId(long platform, int appId) {
        List<RechargeOption> list = rechargeDao.getOptionListByPlatformAndAppId(platform, appId);
        if (list.size() == 0) {
            list = rechargeDao.getOptionListByPlatform(platform);
        }
        return list;
    }


    @Transactional(readOnly = true)
    public List<RechargeOption> getOptionListByPlatformAndUser(PayPlatform platform, User user) {
        List<RechargeOption> list = getOptionListByPlatform(platform.getValue());
        RechargeScale rechargeScale = getRechargeScale(user.getCid(), platform);
        if (rechargeScale != null) {
            BigDecimal scale = rechargeScale.getScale();
            list.forEach(option -> {
                option.setEcoin(calcScaleEcoin(option.getEcoin(), scale));
            });
        }
        return list;
    }


    @Transactional
    public RechargeRecord createRechargeRecord(OrderInfo order) {

        if (order.getProductId() != null) {
            RechargeOption option = this.getOptionByProductId(order.getPayPlatform().getValue(), order.getProductId());
            if (option == null) {
                throw new ResponseException(ResponseError.E_RECHARGE_OPTION, "Invalid pay product id");
            }
            if (order.getMerchantCurrency() == null)
                order.setMerchantCurrency(option.getCurrency());
            order.setRmb(option.getRmb());
        }

        long rmb = order.getRmb();

        if (rmb < RECHARGE_MIN_AMOUNT) {
            if (applicationConfig.isDevMode()) {
                if (rmb <= 0) {
                    throw new ResponseException(ResponseError.E_RECHARGE_MIN_AMOUNT_LIMIT, "充值金额必须大于0");
                }
            } else {
                throw new ResponseException(ResponseError.E_RECHARGE_MIN_AMOUNT_LIMIT, "充值金额最少1元");
            }
        }


        SecureRandom random = new SecureRandom();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String tradeNo = sdf.format(new Date()) + new BigInteger(130, random).toString().substring(0, 18);

        RechargeRecord rechargeRecord = new RechargeRecord();
        rechargeRecord.setUid(order.getUid());
        rechargeRecord.setAppId(order.getAppId());
        rechargeRecord.setRmb(rmb);
        rechargeRecord.setAmount(order.getAmount());
        rechargeRecord.setCurrency(order.getCurrency());
        rechargeRecord.setMerchantCurrency(order.getMerchantCurrency());
        rechargeRecord.setEcoin(0L);
        rechargeRecord.setOrderId(tradeNo);
        rechargeRecord.setPlatformOrderId("");
        rechargeRecord.setPlatform(order.getPayPlatform().getValue());
        if( order.getRealPayPlatform() != null )
            rechargeRecord.setRealPlatform(order.getRealPayPlatform().getValue());
        rechargeRecord.setMerchantId(order.getMchId());
        rechargeRecord.setClientPlatform(order.getMobilePlatform().getValue());
        rechargeRecord.setClientIp(order.getClientIp());
        Date today = new Date();
        rechargeRecord.setCommitTime(today);
        rechargeRecord.setCompleteTime(today);
        rechargeRecord.setStatus(OrderStatus.ORS_CREATE.getValue());
        this.rechargeDao.save(rechargeRecord);

        logger.info("Create recharge record: {}", order);

        return rechargeRecord;
    }


    @Transactional
    public RechargeResult rechargeByProductId(long uid, String productId,
                                              PayPlatform payPlatform, MobilePlatform clientPlatform,
                                              int appId,
                                              String clientIp,
                                              String tradeOrderId,
                                              Date commitTime,
                                              String mid) {

        int platform = payPlatform.getValue();
        if (payPlatform == PayPlatform.PPF_SANDBOX) {
            platform = PayPlatform.PPF_APPLE.getValue();
        }

        RechargeOption option = rechargeDao.getOptionByProductId(platform, productId);
        if (option == null) {
            logger.error("can not find recharge option: {}, {}", payPlatform.getValue(), productId);
            //throw new ResponseException(ResponseError.E_RECHARGE_OPTION, "Invalid recharge option");
            return null;
        }

        long rmb = option.getRmb();
        long ecoin = option.getEcoin() + option.getFree();


        OrderInfo order = new OrderInfo(appId, uid, rmb, payPlatform, clientPlatform, clientIp);
        RechargeRecord rechargeRecord = createRechargeRecord(order);
        rechargeRecord.setCommitTime(commitTime);
        rechargeRecord.setMerchantId(mid);
        return rechargeTransaction(rechargeRecord, uid, tradeOrderId, rmb, ecoin);
    }

    private boolean buyBackLock(long uid) {
        String lockKey = "mgs:pay:bank_cashout_anchor_list";
        String isLock = (String) redisTemplate.opsForHash().get(lockKey, String.valueOf(uid));
        if (isLock != null && isLock.equals("1")) {
            return true;
        }

        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    public long buyback(User user, long rmb) {
        long uid = user.getId();
        logger.info("{} buyback rmb {} for ecoin", uid, rmb);
        RechargeOption option;
/*        option = rechargeDao.getOptionByRmbAndPayPlatform(PayPlatform.PPF_BUY_BACK.getValue(), rmb);

        if (option == null) {
            logger.error("Recharge option does not exists! <platform:{}, rmb:{}>",
                    PayPlatform.PPF_BUY_BACK.getValue(), rmb);
            throw new ResponseException(ResponseError.E_RECHARGE_OPTION, "错误的回购数量");
        }*/

        //周结锁定的时候不能提现
        if (buyBackLock(uid)) {
            throw new ResponseException(ResponseError.E_CASHOUT_STATUS, "账号无法回购，请联系客服");
        }

        //冻结提现不能回购
        CashoutLimitEntity limit = cashoutService.getCashoutLimit(uid);
        if (limit.getIsForbid() == CashoutLimitForbid.YES) {
            throw new ResponseException(ResponseError.E_CASHOUT_STATUS, "账号无法回购，请联系客服");
        }

        long riceroll = rmb * cashConfig.getRateOfRmbToRiceroll() * 100;
        long addEcoin = rmb * cashConfig.getRateOfEcoinToRmb();; // + option.getFree();
        logger.info("Totally buyback {} ecoin ({} extra)", addEcoin, 0L);

        String tid = assetService.binaryOperate(AssetSystemID.BUYBACK, user, riceroll, user, addEcoin).getTid();

        BuybackRecord buybackRecord = new BuybackRecord();
        buybackRecord.setEcoin(addEcoin);
        buybackRecord.setRiceroll(riceroll);
        buybackRecord.setCreateTime(new Date());
        buybackRecord.setUid(uid);
        buybackRecord.setTid(tid);

        rechargeDao.save(buybackRecord);

        //扣除用户钱包余额 6.9.0功能
        bizcoreCenterService.modifyWalletRmb(uid, -rmb * 100);
        logger.info("User : {} buy back rmb {} reduce wallet", uid, rmb * 100);

        return addEcoin;
    }

    @Transactional
    public Map<String, String> offlinePay(String clientIp, long uid, long rmb, long ecoin, int appId, String desc) {
        logger.info("OfflineOrder {} pay rmb {}, add  ecoin {}, desc: {}", uid, rmb, ecoin, desc);

        //check rmb and ecoin validity
        if (rmb <= 0) {
            throw new ResponseException(ResponseError.E_PARAM);
        }
        if (ecoin <= 0) {
            throw new ResponseException(ResponseError.E_PARAM);
        }


        ValueOperations<String, String> ops = redisTemplate.opsForValue();
        String key = "offline_recharge_limit_" + uid;
        String value = ops.get(key);

        if (!StringUtils.isEmpty(value)) {
            throw new ResponseException(ResponseError.E_RECHARGE_OFFLINE_INTERVAL, "人工充值频率太快");
        }
        ops.set(key, "1", RECHARGE_OFFLINE_INTERVAL, TimeUnit.SECONDS);


        BigDecimal ratio = new BigDecimal((ecoin * 100) / RMB_TO_ECOIN_SCALE - rmb ).divide(new BigDecimal(rmb), 10, RoundingMode.HALF_UP);
        if (ratio.compareTo(new BigDecimal("0.1")) > 0) {
            logger.error(" ecoin too large for rmb, ratio {}", ratio);
            throw new ResponseException(ResponseError.E_RECHARGE_OPTION, "Invalid ecoin for rmb");
        }


        String tid = assetService.unaryOperate(AssetSystemID.RECHARGE, uid, ecoin).getTid();

        OrderInfo order = new OrderInfo(appId, uid, rmb, PayPlatform.PPF_OFFLINE_PAY, MobilePlatform.MPF_UNKNOWN, clientIp);
        RechargeRecord rechargeRecord = createRechargeRecord(order);
        rechargeDao.completeTradeNo(rechargeRecord, tid, rechargeRecord.getOrderId(), ecoin);
        emitRechargeEvent(rechargeRecord);

        Map<String, String> ret = new HashMap<>();
        ret.put("tid", tid);
        return ret;
    }


    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public List<RechargeRecord> listRecordByDate(String strDate, int start, int limit) {
        DateTimeFormatter formatter = DateUtils.getDefaultFormatter();
        DateTime dt = formatter.parseDateTime(strDate);
        return rechargeDao.getRecordByDate(start, limit, dt.toDate(), dt.plusDays(1).toDate());
    }

    @Transactional(readOnly = true)
    public List<RechargeRecord> listRecordByUserAndDate(long uid, String startDate, String endDate) {
        DateTimeFormatter formatter = DateUtils.getDefaultFormatter();
        DateTime start = formatter.parseDateTime(startDate);
        DateTime end = formatter.parseDateTime(endDate);
        List<RechargeRecord> ret = rechargeDao.getRecordByUser(uid, start.toDate(), end.toDate());
        ret.forEach(rechargeRecord -> {
            if (rechargeRecord.getStatus() == OrderStatus.ORS_VALID.getValue())
                rechargeRecord.setStatus(OrderStatus.ORS_SUCCESS.getValue());
        });
        return ret;
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public long getTotalRechargeRmb(Date start, Date end) {
        return rechargeDao.getTotalRechargeRmb(start, end);
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public long getTotalRechargePersons(Date start, Date end) {
        return rechargeDao.getTotalRechargePersons(start, end);
    }

    @Transactional(readOnly = true)
    @DataSource(name = DataSource.slave)
    public long getTotalRechargeCount(Date start, Date end) {
        return rechargeDao.getTotalRechargeCount(start, end);
    }

    /*
    private RechargeOption createOptionByRmb(long platform, long rmb, long free) {
        RechargeOption option = new RechargeOption();
        option.setRmb(rmb);
        option.setEcoin(rmbToEcoin(rmb));
        option.setFree(free);
        option.setPlatform(platform);
        return option;
    }*/

    private long rmbToEcoin(long rmb) {
        return rmbToEcoin(rmb,DEFAULT_RMB_TO_ECOIN_SCALE );
    }

    private long rmbToEcoin(long rmb, double scale) {
        return  (long)( (rmb+0.0) * scale / 100.0 );
    }


    @Transactional(readOnly = true)
    public Pair<BigDecimal, List<RechargeOption>> getOptionByUser(User user, int appId) {

        String cid = user.getCid();
        RechargeScale rechargeScale = getRechargeScale(cid);

        BigDecimal scale = BigDecimal.ONE;
        double scaleInt = RMB_TO_ECOIN_SCALE;
        List<RechargeOption> options;
        if (rechargeScale != null) {
            scale = rechargeScale.getScale();
            options = getOptionWithScale(scale, appId);
            scaleInt =  DEFAULT_RMB_TO_ECOIN_SCALE;
        } else {
            options = getOptionWithAppId(appId);
        }
//        if( appId == Constants.FURO_LIVE_APPID )
//            scaleInt =  DEFAULT_RMB_TO_ECOIN_SCALE;

        return Pair.of(scale.multiply(new BigDecimal(String.valueOf(scaleInt))), options);
    }

    @Transactional(readOnly = true)
    public List<RechargeOption> getOptionWithScale(BigDecimal scale) {
        return getOptionWithScale(scale, Constants.DEFAULT_APP_ID);
    }

    @Transactional(readOnly = true)
    public List<RechargeOption> getOptionWithScale(BigDecimal scale, int appId) {
        List<RechargeOption> options = getOptionWithAppId(appId);
        options.forEach(option -> {
            long ecoin = rmbToEcoin(option.getRmb());
            option.setEcoin(calcEcoinWithScale(ecoin, scale));
        });
        return options;
    }

    @Transactional(readOnly = true)
    public List<RechargeOption> getOptionWithAppId(int appId) {
        List<RechargeOption> options = rechargeDao.getOptionListByPlatformAndAppId(RECHARGE_PLATFORM_ALL, appId);
        if (options == null || options.size() == 0)
            options = rechargeDao.getOptionListByPlatform(RECHARGE_PLATFORM_ALL);
        return options;
    }


    @Transactional
    public RechargeResult recharge(RechargeRecord rechargeRecord, String tradeOrderId) {
        long uid = rechargeRecord.getUid();
        long rmb = rechargeRecord.getRmb();
        User user = userService.getById(uid);
        String cid = user.getCid();
        long ecoin = calcEcoinByCid(rmb, cid, rechargeRecord.getAppId());
        return rechargeTransaction(rechargeRecord, uid, tradeOrderId, rmb, ecoin);
    }

    @Transactional
    public RechargeResult validAndRecharge(String orderId, String pfOrderId, String rmb) {
        RechargeRecord r = this.getRecordByOrderIdWithLock(orderId);
        if( r == null ) {
            logger.error("[Recharge][Valid]: order id not exists: {}",orderId);
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "订单不存在");
        }

        if (rmb != null) {
            BigDecimal fee = new BigDecimal(rmb).multiply(new BigDecimal(100));
            if (r.getRmb() != fee.longValue()) {
                logger.error("[Alipay][Notify] total fee not match {}! = {}", r.getRmb(), fee.longValue());
                throw new ResponseException(ResponseError.E_RECHARGE_INVALID_FEE,"订单金额不正确");
            }
        } else {
            logger.error("total fee is null");
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_FEE,"订单金额为空");
        }

        int status = r.getStatus();
        RechargeResult result;
        if (status == OrderStatus.ORS_CREATE.getValue()) {
            result = this.recharge(r, pfOrderId);
            logger.info("[Recharge][Valid][Success]: order ID [ {} ] ,platform order ID [ {} ], Uid [{}]", r.getOrderId(), r.getPlatformOrderId(), r.getUid());
            logger.info("[advstat][recharge][success] total {} rmb {} uid {} platform {} order_id {}", r.getEcoin(), r.getRmb(), r.getUid(), r.getPlatform(), r.getOrderId());
        } else {
            if (status == OrderStatus.ORS_SUCCESS.getValue() || status == OrderStatus.ORS_VALID.getValue() ) {
                logger.info("[Recharge][Valid][Success] Order status has already been success : {}", orderId);
                result = new RechargeResult(r);
                result.setDuplicated(true);
            } else {
                logger.error("[Recharge][Valid][Failed] Order status is Not CREATE : {}", r.getStatus());
                throw new ResponseException(ResponseError.E_RECHARGE_INVALID_STATUS,"订单状态不对");
            }
        }

        return result;
    }

    /**
     * 充值完成方法
     * 1. 增加用户金币
     * 2. 修改充值记录状态，外层需要注意消息的幂等性
     * 3. 通知appgw处理其他业务逻辑
     */
    @Transactional
    public RechargeResult rechargeTransaction(RechargeRecord rechargeRecord, long uid, String tradeOrderId, long rmb, long ecoin) {
        String tid = assetService.unaryOperate(AssetSystemID.RECHARGE, uid, ecoin).getTid();
        completeTradeNo(rechargeRecord, tid, tradeOrderId, ecoin);
        emitRechargeEvent(rechargeRecord);
        appgateService.rechargeCallBack(uid, ecoin);
        return new RechargeResult(rechargeRecord);
    }

    @Transactional
    public void setTradeOrderId(RechargeRecord rechargeRecord, String tradeOrderId) {
        rechargeRecord.setPlatformOrderId(tradeOrderId);
        rechargeDao.update(rechargeRecord);
    }

    public long getRechargeScaleEcoin(PayPlatform platform, long ecoin, User user) {
        RechargeScale rechargeScale = getRechargeScale(user.getCid(), platform);
        if (rechargeScale != null) {
            BigDecimal scale = rechargeScale.getScale();
            return calcScaleEcoin(ecoin, scale);
        }
        return ecoin;
    }

    private long calcScaleEcoin(long ecoin, BigDecimal scale) {
        return new BigDecimal(ecoin).multiply(scale).longValue();
    }

    private RechargeScale getRechargeScale(String cid) {
        return getRechargeScale(cid, PayPlatform.PPF_ALL);
    }

    private RechargeScale getRechargeScale(String cid, PayPlatform platform) {
        RechargeScale rechargeScale = rechargeDao.getScaleByCidAndPlatform(cid, platform.getValue());

        //子渠道处理: prefix_sub
        if (rechargeScale == null) {
            String[] ss = cid.split("_");
            if (ss.length > 1) {
                String[] sub = Arrays.copyOfRange(ss, 0, ss.length - 1);
                String parentCid = StringUtils.join(sub, "_");
                rechargeScale = rechargeDao.getScaleByCidAndPlatform(parentCid, platform.getValue());
                if (rechargeScale == null) {
                    String rootCid = ss[0];
                    rechargeScale = rechargeDao.getScaleByCidAndPlatform(rootCid, platform.getValue());
                }
            }
        }

        if (rechargeScale != null) {
            BigDecimal scale = rechargeScale.getScale();
            if (scale.compareTo(BigDecimal.ZERO) <= 0 || scale.compareTo(BigDecimal.ONE) > 0) {
                logger.error("Invalid scale for cid {}", cid);
                throw new ResponseException(ResponseError.E_RECHARGE_OPTION, "Invalid recharge option for cid");
            }
        }
        return rechargeScale;
    }

    public static String getAmountDecimal(long amount) {
        BigDecimal totalFeeDecimal = new BigDecimal(amount);
        return String.valueOf(totalFeeDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
    }

    public static String getAmountIntegerInCent(String amount) {
        BigDecimal b = new BigDecimal(amount);
        return String.valueOf(b.multiply(new BigDecimal(100)).longValue());
    }

    /**
     * 根据用户ID获取渠道溢价后的充值易币数
     *
     * <AUTHOR>
     */
    @Transactional
    public long calcEcoinByUid(long uid, long rmb) {
        User user = userService.getById(uid);
        return calcEcoinByCid(rmb, user.getCid(), user.getAppId());
    }

    private long calcEcoinByCid(long rmb, String cid, int appId) {
        RechargeScale rechargeScale = getRechargeScale(cid, PayPlatform.PPF_ALL);
        long ecoin = rmbToEcoin(rmb);

        if (rechargeScale != null) {
            ecoin = calcEcoinWithScale(ecoin, rechargeScale.getScale());
        } else {
            RechargeOption option = rechargeDao.getOptionByRmbAndAppId(RECHARGE_PLATFORM_ALL, rmb, appId);
            if (option == null) {
                option = rechargeDao.getOptionByRmbAndAppId(RECHARGE_PLATFORM_ALL, rmb, Constants.DEFAULT_APP_ID);
            }

            if( option != null ) {
                ecoin = option.getEcoin() + option.getFree();
            } else {
                ecoin = rmbToEcoin(rmb, getRmbToEcoinScale(appId));
            }
        }

        return ecoin;
    }

    private double getRmbToEcoinScale(int appId) {
        Double ret = scaleMap.get(appId);
        if( ret != null )
            return ret;
        return RMB_TO_ECOIN_SCALE;
    }

    private long calcEcoinWithScale(long ecoin, BigDecimal scale) {
        return scale.multiply(BigDecimal.valueOf(ecoin)).longValue();
    }

    /**
     * 组装充值选项的免费送抽奖次数
     *
     * <AUTHOR>
     */
    @Transactional(readOnly = true)
    public List<RechargeOptionItem> fillRechargeOptionLotteryTime(long uid, List<RechargeOption> list) {
        List<RechargeOptionItem> outList = new ArrayList<>();
        boolean isFirst = listRecordByUid(uid, 0, 1).size() == 0;

        list.forEach(option -> {
            RechargeOptionItem out = new RechargeOptionItem(option);
            if (isFirst) {
                long lotteryFreeTime = (long) Math.ceil(option.getEcoin() / 100.0);
                out.setLotteryTime(lotteryFreeTime);
            }
            outList.add(out);
        });

        return outList;
    }


    @Transactional(readOnly = true)
    public List<RechargeRecord> getSuccessRecordByDateAndPlatform(Date startDate, Date endDate, PayPlatform payPlatform) {
        return rechargeDao.getSuccessRecordByDateAndPlatform(startDate, endDate, payPlatform.getValue());
    }

    @Transactional
    public void rollback(String orderId) {
        RechargeRecord rechargeRecord = rechargeDao.getByOrderId(orderId);
        if (rechargeRecord.getStatus() == OrderStatus.ORS_SUCCESS.getValue()) {
            assetService.unaryOperate(AssetSystemID.RECHARGE_ROLLBACK, rechargeRecord.getUid(), rechargeRecord.getEcoin());
            rechargeRecord.setStatus(OrderStatus.ORS_ROLLBACK.getValue());
            rechargeDao.saveOrUpdate(rechargeRecord);
        } else {
            throw new ResponseException(ResponseError.E_RECHARGE_NOT_PAID);
        }

    }


    @Transactional
    public RechargeResult completeOrderById(String oderId) {
        RechargeRecord rechargeRecord = rechargeDao.getByOrderIdWithLock(oderId);
        if( rechargeRecord.getStatus() != OrderStatus.ORS_CREATE.getValue() ) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_STATUS,"订单状态不正确");
        }
        // 这里应该是需要去远程查询订单状态的
        String tradeOrderId = "0000";
        return recharge(rechargeRecord, tradeOrderId);
    }

    @Transactional(readOnly = true)
    public List<BuybackRecord> getBuybackRecordByDate(Date date) {
        Date end = new DateTime(date).plusDays(1).toDate();
        return rechargeDao.getBuybackRecordByDate(date, end);
    }

    @Transactional(readOnly = true)
    public RechargeOption getOptionByPlatformAndRmb(PayPlatform platform, long rmb) {
        return rechargeDao.getOptionByRmbAndPayPlatform(platform.getValue(), rmb);
    }

    @Transactional(readOnly = true)
    public void setOptionsTagType(List<RechargeOption> options, long appId) {
        if (options.size() == 0) {
            return;
        }
        List<Long> optionIds = options.stream().map(RechargeOption::getId).collect(Collectors.toList());
        List<RechargeOptionTag> optionTagListByOptions = rechargeDao.getOptionTagListByOptions(optionIds, appId);
        if (optionTagListByOptions.size() == 0) {
            return;
        }
        Map<Long, Integer> tagTypeMap = optionTagListByOptions.stream().collect(Collectors.toMap(RechargeOptionTag::getOptionId, RechargeOptionTag::getTagType));
        for (RechargeOption rechargeOption : options) {
            rechargeOption.setTagType(tagTypeMap.getOrDefault(rechargeOption.getId(), 0));
        }
    }
    @Transactional
    public void rechargePrize(String orderId, long prize) {
        logger.info("[rechargePrize] rechargeBonus orderId {}, prize:{}", orderId, prize);
        RechargeRecord rechargeRecord = rechargeDao.getByOrderId(orderId);
        rechargeRecord.setPrize(prize);
        rechargeDao.saveOrUpdate(rechargeRecord);
    }

    @Transactional
    public void addWrongTransferRecord(String orderId, long fromUid, long id, int ecoin) {
        RechargeWrongTransferRecord record = new RechargeWrongTransferRecord();
        record.setOrderId(orderId);
        record.setFromUid(fromUid);
        record.setToUid(id);
        record.setEcoin(ecoin);
        rechargeWrongTransferRepository.save(record);
    }

    public boolean isWrongTransferRecordExist(String orderId) {
        return rechargeWrongTransferRepository.findByOrderId(orderId) != null;
    }


}
