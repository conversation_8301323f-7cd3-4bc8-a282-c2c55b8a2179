package com.easylive.pay.service;

import com.easylive.pay.dao.ContributorDao;
import com.easylive.pay.enums.AssetBizType;
import com.easylive.pay.enums.AssetType;
import com.easylive.pay.model.event.AssetTransaction;
import com.easylive.pay.model.event.AssetTransactionRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * <AUTHOR>
 */
@Service
public class ContributorService {

    private static Logger logger = LoggerFactory.getLogger(ContributorService.class);
    @Autowired
    private AssetService assetService;
    @Autowired
    private ContributorDao contributorDao;

    private Queue<AssetTransaction> transactionQueue = new ConcurrentLinkedDeque<>();

    @PostConstruct
    public void init() {
        // load asset biz id and do the check
        assetService.addTransactionListener(this::processAssetTransaction);
    }

    @Transactional
    protected void addContributor(long riceroll, long fromUid, long toUid) {
        contributorDao.addContributor(riceroll, fromUid, toUid);
    }

    private void processAssetTransaction(AssetTransaction transaction) {
        transactionQueue.add(transaction);
    }

    public void buildContributor() {
        while (true) {
            AssetTransaction transaction = transactionQueue.poll();
            if (transaction == null)
                break;
            processContributor(transaction);
        }
    }


    private void processContributor(AssetTransaction transaction) {

        if (transaction.getBizType() != AssetBizType.BUSINESS.getValue())
            return;

        if (transaction.getRecords().size() != 2)
            return;

        AssetTransactionRecord from = transaction.getRecord(0);
        AssetTransactionRecord to = transaction.getRecord(1);

        if (!(to.getAssetType() == AssetType.RICEROLL.getValue() && to.getOriginValue() > 0))
            return;

        long riceroll = to.getOriginValue();
        long fromUid = from.getUid();
        long toUid = to.getUid();

        logger.info("Add contributor {}: {} -> {} : {}", transaction.getBizId(), fromUid, toUid, riceroll);
        //总榜
        addContributor(riceroll, fromUid, toUid);
    }
}
