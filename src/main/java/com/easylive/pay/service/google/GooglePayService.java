package com.easylive.pay.service.google;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.GooglePayConfig;
import com.easylive.pay.entity.AppEntity;
import com.easylive.pay.entity.RechargeOption;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.service.AppService;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.service.UserService;
import com.easylive.pay.utils.Base64;
import com.easylive.pay.utils.JsonUtils;
import com.easylive.pay.utils.UAgentInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/5
 */
@Slf4j
@Service
public class GooglePayService {

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private GooglePayConfig config;

    @Autowired
    private AppService appService;

    @Autowired
    private UserService userService;

    @PostConstruct
    protected void init() {
    }


    @Transactional
    public String createOrder(long uid, String appName, String productId, MobilePlatform mobilePlatform, String clientIp, long amount, String currency) {
        log.info("[RECHARGE][GOOGLE]Create order {}, {}, {}, {}, {}, {}, {}", uid, appName, productId, mobilePlatform, clientIp, amount, currency);
        if (amount <= 0) {
            throw new ResponseException(ResponseError.E_RECHARGE_MIN_AMOUNT_LIMIT);
        }
        int appId = appService.getAppIdByName(appName);

        if( !userService.isLiveUUser(uid) ) {
            log.info("Non LiveU user:{} uses google pay, rejected", uid);
            throw new ResponseException(ResponseError.E_AUTH,"请使用LiveU账号");
        }

        OrderInfo orderInfo = new OrderInfo(appId, uid, productId,  PayPlatform.PPF_GOOGLE, mobilePlatform, clientIp);
        orderInfo.setAmount(amount);
        orderInfo.setCurrency(currency);
        RechargeRecord rechargeRecord = rechargeService.createRechargeRecord(orderInfo);
        return rechargeRecord.getOrderId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void valid(long uid, String appName, String orderId, String data, String signature) {
        log.info("[RECHARGE][GOOGLE] valid order: {}, {}, {}, {}, {}", uid, appName, orderId, data, signature);

        RechargeRecord rr = rechargeService.getRecordByOrderIdWithLock(orderId);
        if (rr == null) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "Not found");
        }

        if (rr.getStatus() != OrderStatus.ORS_CREATE.getValue()) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_STATUS, "invalid order status");
        }

        if( rr.getUid() != uid) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID,"Uid mismatch");
        }

        if (rr.getPlatform() != PayPlatform.PPF_GOOGLE.getValue()) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "Invalid platform");
        }


        String licenseKey = loadLicenseKey(appName);

        if( signature.contains("%")) {
            try {
                signature = URLDecoder.decode(signature, "UTF-8");
            } catch (Exception e) {
                signature =  signature.replace("%2B","+");
            }
        }

        if (!doCheck(data, signature, licenseKey)) {
            log.error("[RECHARGE][GOOGLE]order validate failed");
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "验证错误");
        }

        OrderData orderData = JsonUtils.fromString(data, OrderData.class);
        if (orderData.getPurchaseState() != GoogleOrderStatus.SUCCESS.getStatus()) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_STATUS, "not pay success status");
        }

        RechargeOption option = rechargeService.getOptionByProductId(PayPlatform.PPF_GOOGLE.getValue(), orderData.productId );
        if (option == null) {
            log.error("Invalid recharge value. No option found: {}", rr.getRmb());
            throw new ResponseException(ResponseError.E_RECHARGE_OPTION, "Invalid recharge rmb");
        }
        rechargeService.rechargeTransaction(rr, uid, orderData.getOrderId(), option.getRmb(), option.getEcoin());
    }

    public List<RechargeOption> getRechargeOption(String appName) {
        AppEntity app = appService.getAppByName(appName);
        List<RechargeOption> options = rechargeService.getOptionListByPlatform(PayPlatform.PPF_GOOGLE.getValue());
        List<RechargeOption> ret = new ArrayList<>();

        if(app != null) {
            ret = getRechargeOptionWithPrefix(options ,app.getPackageName());
            if (UAgentInfo.OVERSEA.equals(app.getName())) {
                rechargeService.setOptionsTagType(ret, app.getId());
            }
        }
        return ret;
    }

    private List<RechargeOption> getRechargeOptionWithPrefix(List<RechargeOption> list,String prefix) {
       return list.stream()
                .filter( rechargeOption -> rechargeOption.getProductId().startsWith(prefix))
                .collect(Collectors.toList());
    }

    private String loadLicenseKey(String appName) {
        try {
            String fileName = config.getKeyDir() + "/" + appName + ".key";
            return FileUtils.readFileToString(new File(fileName));
        } catch(Exception e) {
            log.error("Error reading license key",e);
            throw new ResponseException(ResponseError.E_SERVER,"Incorrect google pay configuration");
        }
    }

    @Data
    private static class OrderData {
        private String orderId;
        private String packageName;
        private String productId;
        private String purchaseTime;
        private int purchaseState;
        private String purchaseToken;
        private boolean acknowledged;
    }

    private enum GoogleOrderStatus {
        /**
         * google pay 返回订单状态
         */
        SUCCESS(0),
        CANCEL(1),
        REFUND(2);

        private final int status;

        GoogleOrderStatus(final int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }
    }

    static boolean doCheck(String data, String sign, String publicKey) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = Base64.decode(publicKey.trim());
            PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));

            Signature signature = Signature
                    .getInstance("SHA1WithRSA");

            signature.initVerify(pubKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));

            return signature.verify(Base64.decode(sign));

        } catch (Exception e) {
            log.error("Error google sign:", e);
        }
        return false;
    }

    public static void main(String[] args) {
        String data = "{\"orderId\":\"GPA.3313-1597-2110-65550\",\"packageName\":\"com.magic.hotline\",\"productId\":\"com.magic.hotline_google_ecoin_990_hkd\",\"purchaseTime\":1684308800904,\"purchaseState\":0,\"purchaseToken\":\"ekhkfpcheagmanhcnglkkcab.AO-J1OxgcQ7KNcHt9X9KEF495DsJTky9mPZoff0JX7SJPF2QXUZP3JM_mkEMMiMKEf84DXUdCNXIry_rmHo_BdKbUS5yCvqdxw\",\"quantity\":1,\"acknowledged\":false}";
        String signature = "Oj+bpDCxd6nLiFdgZLQ+FPaUoANGJd0eFU3aUvSOe2KPI9nEDKesQhwczJkmEJvMbrRSk58fCo+j3i5n/rPqSQ/LIZh/fJ0MehOuiT/WrIniC4QctyqIXgp0fXq7EO0g8cZAdcMs8zxL/IGSoQL9puslESTiUoufOVjymMHJRDIhxLHTOKsy9DSFLtZMTBsB8OLIIJK5Upk+oeh8ynHRvJIofsUDKA3e7WZ6z3/i6B2aHSDF9+ikKbOS08TtxxN5RPBP3kL8Gk/yODUj1KKd0Pju3n584RAkPutiwQz96//NulNqs62orJzQba2JVNOW1sRpEWPQBlou2kMrCgclZw==";
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjKXagrHB16690pttmFhyFX2JSrzRj3tLU85641qYaHt/R1HyV0NnsUvG7wdP76Miq/6PuhBiiN8VepeFZIY4fSiBVeLQ4vfPxKHP2VHHiOlWlz/GNuSsFMfEmSWWof2WTeiloL5FYEs2qPYXf2OiQBujjhez+xJg27rqRGFujgbTszlyEqs0EQahTIRHIFkRJZWN9SbbfFnnjmENmnmjqiP27KHHMcdaA3Qbq+UqXO1zts9CBYXv9dpkY20PxRO9MWoIfmHKMgIrO6EfJK9dvy6h3w0zi4mIuP15xmwEhQkZlM95/uSlDvOnFDVaYXHN06RLVnYmd86F099/YekQNwIDAQAB";
        boolean b = doCheck(data,signature,publicKey);
        System.out.println(b);
    }
}
