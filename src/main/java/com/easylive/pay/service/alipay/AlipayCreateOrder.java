package com.easylive.pay.service.alipay;

import com.easylive.pay.enums.AliPayType;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.PayPlatform;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
public class AlipayCreateOrder {
    String appName;
    AliPayType type;
    long uid;
    long amount;
    PayPlatform payPlatform;
    MobilePlatform mobilePlatform;
    String clientIp;
    boolean officialOnly = false;
    boolean preferHttp = false;

    public AlipayCreateOrder(String appName, AliPayType payType, long uid, long amount, PayPlatform payPlatform, MobilePlatform platform, String clientIp) {
        this.appName = appName;
        this.type = payType;
        this.uid = uid;
        this.amount = amount;
        this.payPlatform = payPlatform;
        this.mobilePlatform = platform;
        this.clientIp = clientIp;
    }
}
