package com.easylive.pay.service.alipay;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.enums.AliPayProtocol;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/5
 */
@Slf4j
public class AlipayOfficialProvider implements AlipayProvider {
    private static final String ALIPAY_GATEWAY_URL = "https://openapi.alipay.com/gateway.do";
    private final AlipayClient client;
    private final AlipayOrderInfo info;
    private String returnUrl;
    private String notifyUrl;
    public AlipayOfficialProvider(AlipayOrderInfo info,AlipayClient client) {
        this.info = info;
        this.client = client;
    }

    @Override
    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    @Override
    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    @Override
    public AlipayOrderExecuteResult execute() {

        switch (this.info.type) {
            case APP:
                return new AlipayOrderExecuteResult(appOrder(), AliPayProtocol.APP);
            case WAP:
                return new AlipayOrderExecuteResult(wapOrder(), AliPayProtocol.GATEWAY);
            case PC:
                return new AlipayOrderExecuteResult(pcOrder(), AliPayProtocol.GATEWAY);
            default:
                throw new ResponseException(ResponseError.E_RECHARGE_ALIPAY_SERVICE,"不支持的支付类型");
        }
    }


    private String appOrder() {
        AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
        model.setBody(info.detail);
        model.setSubject(info.detail);
        model.setOutTradeNo(info.orderId);
        model.setTotalAmount(info.amount);
        model.setProductCode("QUICK_MSECURITY_PAY");

        AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
        request.setBizModel(model);
        request.setNotifyUrl(notifyUrl);

        try {
            AlipayTradeAppPayResponse response = this.client.sdkExecute(request);
            String orderStr = response.getBody();
            log.info("[Recharge][Alipay][CreateOrder] Order string: " + orderStr);
            return orderStr;
        } catch (Exception e) {
            log.error("Request alipay app order error", e);
        }
        return null;
    }

    private String wapOrder() {
        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
        model.setOutTradeNo(info.orderId);
        model.setSubject(info.detail);
        model.setTotalAmount(info.amount);
        model.setBody(info.detail);
        model.setProductCode("QUICK_WAP_WAY");

        AlipayTradeWapPayRequest wapPayRequest = new AlipayTradeWapPayRequest();
        wapPayRequest.setBizModel(model);
        wapPayRequest.setNotifyUrl(notifyUrl);
        wapPayRequest.setReturnUrl(returnUrl);
        try {
            String url = ALIPAY_GATEWAY_URL + "?" + this.client.sdkExecute(wapPayRequest).getBody();
            log.info("WAP URL: {}", url);
            return url;
        } catch (AlipayApiException e) {
            log.error("Error generating alipay wap url", e);
        }
        return null;
    }


    private String pcOrder() {
        AlipayTradePagePayModel model = new AlipayTradePagePayModel();
        model.setOutTradeNo(info.orderId);
        model.setSubject(info.detail);
        model.setTotalAmount(info.amount);
        model.setBody(info.detail);
        model.setProductCode("FAST_INSTANT_TRADE_PAY");

        AlipayTradePagePayRequest pagePayRequest = new AlipayTradePagePayRequest();
        pagePayRequest.setBizModel(model);
        pagePayRequest.setNotifyUrl(notifyUrl);
        pagePayRequest.setReturnUrl(returnUrl);

        try {
            String url = ALIPAY_GATEWAY_URL + "?" + this.client.sdkExecute(pagePayRequest).getBody();
            log.info("PC WEB Order URL: {}", url);
            return url;
        } catch (AlipayApiException e) {
            log.error("Error generating alipay PC url", e);
        }
        return null;
    }



}
