package com.easylive.pay.service.alipay;


import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.AliPayConfig;
import com.easylive.pay.dao.RechargeAliAppRepository;
import com.easylive.pay.dao.RechargeAliMapRepository;
import com.easylive.pay.dao.RechargeAliWebConfigRepository;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.*;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.model.RechargeResult;
import com.easylive.pay.model.User;
import com.easylive.pay.model.user.UserInfo;
import com.easylive.pay.service.*;
import com.easylive.pay.service.huifu.HuifuPayService;
import com.easylive.pay.service.lianlian.LianLianPayService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.URL;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;


/**
 * <AUTHOR>
 */
@Service
public class AliPayService {

    private static final String ALIPAY_GATEWAY_URL = "https://openapi.alipay.com/gateway.do";
    private static final String ALIPAY_CHARSET = "UTF-8";
    private static final String ALIPAY_FORMAT = "json";
    private static final String ALIPAY_SIGN_TYPE = "RSA2";
    private static final String APP_SIGN_TYPE_OLD = "RSA";
    private static final String TRADE_SUCCESS = "TRADE_SUCCESS";
    private static final String TRADE_FINISHED = "TRADE_FINISHED";
    private final static String APP_NAME_SPECIAL_CHANNEL = "special-channel";
    private static final Logger logger = LoggerFactory.getLogger(AliPayService.class);
    @Autowired
    private UserService userService;
    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private AliPayConfig aliPayConfig;
    @Autowired
    private SpecialUserService specialUserService;
    @Autowired
    private RechargeAliMapRepository aliMapRepository;
    @Autowired
    private RechargeAliAppRepository aliAppRepository;
    @Autowired
    private RechargeControlService rechargeControlService;

    @Autowired
    private RechargeAliWebConfigRepository webConfigRepository;

    @Autowired
    private AppService appService;

    @Autowired
    private PayService payService;

    @Autowired
    private LianLianPayService lianLianPayService;

    @Autowired
    private HuifuPayService huifuPayService;

    private volatile Map<String, RechargeAliApp> alipayApps = new HashMap<>();
    private volatile Map<String, RechargeAliApp> aliAppIdMap = new HashMap<>();
    private Map<Long, RechargeAliApp> idMap = new HashMap<>();

    private RechargeAliApp defaultApp;
    private final static String DEFAULT_APP_NAME = "yizhibo";

    private volatile Map<String, RechargeAliWebConfig> webConfigs = new HashMap<>();
    private final static String DEFAULT_WEB_APP_NAME = "yizhibo";

    private static Map<String, String> paraFilter(Map<String, String> sArray) {

        Map<String, String> result = new HashMap<>();

        if (sArray == null || sArray.size() <= 0) {
            return result;
        }

        for (String key : sArray.keySet()) {
            String value = sArray.get(key);
            if (value == null || value.equals("") || key.equalsIgnoreCase("sign")
                    || key.equalsIgnoreCase("sign_type")) {
                continue;
            }
            result.put(key, value);
        }

        return result;
    }

    private static String createSignString(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);


        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            logger.info("key:" + key + ", value:" + value);
            sb.append(key).append("=").append("\"").append(value).append("\"");
            if (i != keys.size() - 1) {
                sb.append("&");
            }
        }
        return sb.toString();
    }

    @PostConstruct
    public void init() {
        load();
    }

    synchronized public void load() {
        logger.info("Loading alipay app");

        Map<String, RechargeAliApp> tmpAppMap = new HashMap<>();
        Map<String, RechargeAliApp> tmpAliAppIdMap = new HashMap<>();
        Map<Long, RechargeAliApp> tmpIdMap = new HashMap<>();

        aliAppRepository.findAll().forEach(app -> {
            String appId = app.getAppId();
            tmpIdMap.put(app.getId(), app);
            tmpAliAppIdMap.put(appId, app);

            logger.info("Loading public and private key for app id {}", appId);

            app.setAliPublicKey(loadAlipayPublicKey(appId));
            app.setPrivateKey(loadAppPrivateKey(appId));
            if (app.isPrime()) {
                defaultApp = app;
            }
        });


        if (defaultApp == null) {
            logger.error("No default app configured");
            throw new ResponseException(ResponseError.E_SERVER, "No default app configured");
        }

        logger.info("Default ali pay app {}, {}", defaultApp.getAppId(), defaultApp.getPid());


        for (RechargeAliMap rechargeAliMap : aliMapRepository.findAll()) {
            //if (!app.isEnabled()) {
            //    logger.info("Alipay app {} - {} is not enabled", app.getAppId(), app.getPid());
            //    continue;
            //}

            String mgsAppName = appService.getAppById(rechargeAliMap.getMgsAppId()).getName();
            AliPayType type = AliPayType.fromValue(rechargeAliMap.getType());
            if (type == null) {
                String msg = "Invalid alipay type configured: " + rechargeAliMap.getType();
                logger.error(msg);
                throw new ResponseException(ResponseError.E_SERVER, msg);
            }

            long appId = rechargeAliMap.getAliApp().getId();
            RechargeAliApp app = tmpIdMap.get(appId);
            if (app != null) {
                tmpAppMap.put(getMapKey(mgsAppName, type), app);
                logger.info("Adding ali app map: {}, {}, {}", mgsAppName, type, app.getAppId());
            } else {
                logger.error("Invalid app map configured: {}, {}, {} ", mgsAppName, type, appId);
            }
        }


        this.aliAppIdMap = tmpAliAppIdMap;
        this.alipayApps = tmpAppMap;
        this.idMap = tmpIdMap;


        RechargeAliApp specialChannelApp = getAppByName("magiclive", AliPayType.APP);
        tmpAppMap.put(APP_NAME_SPECIAL_CHANNEL, specialChannelApp);

        logger.info("special channel alipay app: {}, {}", specialChannelApp.getAppId(), specialChannelApp.getPid());


        Map<String, RechargeAliWebConfig> config = new HashMap<>();
        webConfigRepository.findAll().forEach(webConfig -> {
            config.put(webConfig.getName(), webConfig);
        });

        this.webConfigs = config;


        huifuPayService.load(tmpAliAppIdMap);
    }


    private String getMapKey(String mgsAppName, AliPayType type) {
        return mgsAppName + "_" + type.getValue();
    }

    public RechargeAliApp getAppByAliAppId(String aliAppId) {
        return aliAppIdMap.get(aliAppId);
    }

    private RechargeAliApp getAppByName(String mgsAppName, AliPayType type) {
        RechargeAliApp app = alipayApps.get(getMapKey(mgsAppName, type));


        if (app == null) {
            logger.error("App name {} for type {} not found, use default alipay app", mgsAppName, type);
            return defaultApp;
        }
        return app;
    }

    private RechargeAliWebConfig getWebConfigByName(String name) {
        RechargeAliWebConfig config = webConfigs.get(name);
        if (config == null) {
            config = webConfigs.get(DEFAULT_WEB_APP_NAME);
        }
        return config;
    }

    private String loadAppPrivateKey(String appId) {
        String filename = aliPayConfig.getPrivateKeyPath() + "/" + appId + ".key";
        return loadFromFile(filename);
    }

    private String loadAlipayPublicKey(String appId) {
        String filename = aliPayConfig.getPublicKeyPath() + "/" + appId + ".key";
        return loadFromFile(filename);
    }

    private String loadFromFile(String filename) {
        try {
            return IOUtils.toString(new FileInputStream(filename));
        } catch (Exception e) {
            logger.error("unable to load private key ,{}", filename);
        }
        if (aliPayConfig.isInitKey()) {
            throw new RuntimeException("App Key must be init correct at start");
        }
        return "";
    }

    public PrivateKey getPemPrivateKey(String filename) {
        try {
            BufferedInputStream bis;
            File privateKeyFile = ResourceUtils.getFile(filename);
            bis = new BufferedInputStream(new FileInputStream(privateKeyFile));
            byte[] privateKeyBytes = new byte[(int) privateKeyFile.length()];
            bis.read(privateKeyBytes);
            bis.close();

            String temp = new String(privateKeyBytes);
            String privateKeyPEM = temp.replace("-----BEGIN PRIVATE KEY-----\n", "");
            privateKeyPEM = privateKeyPEM.replace("\n-----END PRIVATE KEY-----", "");


            byte[] decoded = com.easylive.pay.utils.Base64.decode(privateKeyPEM);

            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            KeySpec ks = new PKCS8EncodedKeySpec(decoded);
            return keyFactory.generatePrivate(ks);
        } catch (Exception e) {
            logger.error("load private key failed: " + e.getMessage());
        }
        return null;
    }

    private String signRSA(String seed, String filePath) {
        //  logger.info("rsa seed : " + seed);
        //  logger.info("rsa filepath : " + filePath);
        try {

            RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) getPemPrivateKey(filePath);
            Signature instance = Signature.getInstance("SHA1withRSA");
            instance.initSign(rsaPrivateKey);

            instance.update(seed.getBytes(ALIPAY_CHARSET));

            byte[] signText = instance.sign();
            return com.easylive.pay.utils.Base64.encode(signText);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return "";
    }


    /**
     * 获取支付宝App订单
     */
    @Transactional
    public Map<String, String> createAppOrder(Long amount, Long uid, String clientIp, MobilePlatform mobilePlatform) {


        AlipayOrderInfo info = createAliPayOrder("yizhibo", AliPayType.APP, uid, amount, PayPlatform.PPF_ALI_APP_PAY, mobilePlatform, clientIp, true);

        //构造参数,参看: https://doc.open.alipay.com/doc2/detail?treeId=59&articleId=103663&docType=1

        Map<String, String> args = new TreeMap<>();
        args.put("service", "mobile.securitypay.pay");
        args.put("partner", info.app.getPid());
        args.put("_input_charset", ALIPAY_CHARSET);
        args.put("notify_url", aliPayConfig.getNotifyUrl());
        args.put("out_trade_no", info.orderId);
        args.put("total_fee", info.amount);
        args.put("seller_id", info.app.getPid());
        args.put("app_id", info.app.getAppId());

        args.put("payment_type", "1");

        args.put("subject", info.detail);
        args.put("body", info.detail);


        String sign = signRSA(createSignString(paraFilter(args)), aliPayConfig.getRsaKey());
        logger.info("Sign:" + sign);
        args.put("sign_type", APP_SIGN_TYPE_OLD);
        args.put("sign", sign);

        return args;
    }

    public String createAppOrderSpecialChannel(Long amount, User user, String clientIp, MobilePlatform mobilePlatform) {
        if (!specialUserService.isSpecialChannel(user.getCid()))
            return null;
        return createAppOrderNew(APP_NAME_SPECIAL_CHANNEL, amount, user.getId(), clientIp, mobilePlatform);
    }

    @Transactional
    public String createAppOrderNew(String appName, Long amount, long uid, String clientIp, MobilePlatform mobilePlatform) {
        // 安全起见，这里也保护一下
        if (specialUserService.isSpecialChannel(appName))
            appName = APP_NAME_SPECIAL_CHANNEL;

        AlipayOrderInfo info = createAliPayOrder(appName, AliPayType.APP, uid, amount, PayPlatform.PPF_ALI_APP_PAY, mobilePlatform, clientIp, true);
        String orderStr = info.getProvider().execute().getData();
        if (orderStr == null)
            throw new ResponseException(ResponseError.E_RECHARGE_ALIPAY_SERVICE, "请求支付宝服务出错");
        return orderStr;
    }

    @Transactional
    public Map<String, String> createPCOrder(String appName, User user, long amount, String clientIp, MobilePlatform mobilePlatform) {
        AlipayOrderInfo info = createAliPayOrder(DEFAULT_APP_NAME, AliPayType.PC, user.getId(), amount, PayPlatform.PPF_ALI_PC_PAY, mobilePlatform, clientIp);
        String returnUrl = aliPayConfig.getWebReturnUrl() + "?name=" + user.getName() + "&token=" + appName;
        AlipayProvider provider = info.getProvider();
        provider.setReturnUrl(returnUrl);
        Map<String, String> retMap = new HashMap<>();
        String url = provider.execute().getData();
        retMap.put("url", url);
        retMap.put("order_id", info.orderId);
        retMap.put("amount", String.valueOf(amount));
        return retMap;
    }

    @Transactional
    public String createWapOrder(String mgsAppName, String appName, String resultUrl, User user, long amount, String sessionId, String clientIp, MobilePlatform mobilePlatform, boolean iosEmbed) {
        AlipayOrderInfo info = createAliPayOrder(mgsAppName, AliPayType.WAP, user.getId(), amount, PayPlatform.PPF_ALI_WAP_PAY, mobilePlatform, clientIp);
        StringBuilder returnUrl = new StringBuilder();
        returnUrl.append(aliPayConfig.getWebReturnUrl());
        returnUrl.append("?name=").append(user.getName());
        returnUrl.append("&token=").append(appName);
        returnUrl.append("&result=").append(resultUrl);
        if (iosEmbed) {
            returnUrl.append("&sessionid=").append(sessionId);
        }
        // String returnUrl = iosEmbed ? aliPayConfig.getWapIosReturnUrl() + "?sessionid=" + sessionId : aliPayConfig.getWapReturnUrl() + "?name=" + user.getName();
        info.getProvider().setReturnUrl(returnUrl.toString());
        String url = info.getProvider().execute().getData();
        return url != null ? url : getWebConfigByName(appName).getFailUrl();
    }


    protected AlipayOrderInfo createAliPayOrder(String appName, AliPayType type, long uid, long amount, PayPlatform payPlatform, MobilePlatform mobilePlatform, String clientIp) {
        return createAliPayOrder(appName, type, uid, amount, payPlatform, mobilePlatform, clientIp, false);
    }


    protected AlipayOrderInfo createAliPayOrder(String appName, AliPayType type, long uid, long amount, PayPlatform payPlatform, MobilePlatform mobilePlatform, String clientIp, boolean officialOnly) {
        AlipayCreateOrder info = new AlipayCreateOrder(appName, type, uid, amount, payPlatform, mobilePlatform, clientIp);
        info.officialOnly = officialOnly;
        return createAliPayOrder(info);
    }

    protected AlipayOrderInfo createAliPayOrder(AlipayCreateOrder info) {
        String appName = info.appName;
        AliPayType type = info.type;
        long uid = info.uid;
        long amount = info.amount;
        PayPlatform payPlatform = info.payPlatform;
        MobilePlatform mobilePlatform = info.mobilePlatform;
        String clientIp = info.clientIp;
        boolean preferHttp = info.preferHttp;
        boolean officialOnly = info.officialOnly;

        RechargeAliApp app = getAppByName(appName, type);

        int appId = appService.getAppIdByName(appName);

        int rechargeId = rechargeControlService.getAliRechargeId(appId, type.getValue(), amount);
        logger.info("Mapping {},{},{},{} -> {}", appId, type.getValue(), amount, app.getId(), rechargeId);

        if (rechargeId != -1 && rechargeId != app.getId()) {

            RechargeAliApp ra = this.idMap.get((long) rechargeId);
            // 对于APP支付类型，如果映射后的App的平台不是官方的，并且当前请求不支持
            boolean appOnlyOfficial = type == AliPayType.APP && ra.getPlatform() != ThirdPayPlatform.OFFICIAL.getValue() && officialOnly;
            if (appOnlyOfficial) {
                logger.info("Current APP prepare order only support official platform");
            } else if (ra != null) {
                logger.info("Remap recharge id to {}", rechargeId);
                app = ra;
            }
        }



        OrderInfo orderInfo = new OrderInfo(appId, uid, amount, payPlatform, mobilePlatform, clientIp);
        orderInfo.setMchId(app.getAppId());

        if (preferHttp) {
            //判断当前APP是否支持HTTP支付
            if( app.getType().contains("WAP")) {
                orderInfo.setRealPayPlatform(PayPlatform.PPF_ALI_WAP_PAY);
            }
        }

        RechargeRecord rechargeRecord = rechargeService.createRechargeRecord(orderInfo);
        return getOrderInfoFromRechargeRecord(rechargeRecord);
    }


    private AlipayOrderInfo getOrderInfoFromRechargeRecord(RechargeRecord rechargeRecord) {
        long uid = rechargeRecord.getUid();
        long amount = rechargeRecord.getRmb();
        AppEntity mgsApp = appService.getAppIdWithDefaultFallback(rechargeRecord.getAppId());
        RechargeAliApp app = this.getAppByAliAppId(rechargeRecord.getMerchantId());


        AlipayOrderInfo info = new AlipayOrderInfo();
        UserInfo userInfo = userService.getUserRegInfoByUid(uid);
        if (userInfo == null) {
            throw new ResponseException(ResponseError.E_RECHARGE_ALIPAY_SERVICE, "无效用户");
        }

        info.name = userInfo.getName();
        if (userInfo.getReg() != null)
            info.userRegTime = userInfo.getReg().getRegisterTime();
        else {
            info.userRegTime = new DateTime().withDayOfMonth(1).withTimeAtStartOfDay().toDate();
            logger.error("Unable to get user register time: {}, {} will be used", uid, info.userRegTime);
        }

        info.app = app;
        info.amount = RechargeService.getAmountDecimal(amount);
        long ecoin = rechargeService.calcEcoinByUid(uid, amount);
        info.detail = payService.getProductName(mgsApp.getName(), amount, ecoin);

        info.orderId = rechargeRecord.getOrderId();
        //info.type = getALiPayTypeFromPlatform(rechargeRecord.getPlatform());
        info.originType = getALiPayTypeFromPlatform(rechargeRecord.getPlatform());
        if(rechargeRecord.getRealPlatform() != null)
            info.type = getALiPayTypeFromPlatform(rechargeRecord.getRealPlatform());
        else
            info.type = info.originType;
        info.provider = getProvider(info);

        return info;
    }

    private AliPayType getALiPayTypeFromPlatform(int platform) {
        PayPlatform payPlatform = PayPlatform.fromValue(platform);
        if (payPlatform == PayPlatform.PPF_ALI_APP_PAY) {
            return AliPayType.APP;
        } else if (payPlatform == PayPlatform.PPF_ALI_WAP_PAY) {
            return AliPayType.WAP;
        } else if (payPlatform == PayPlatform.PPF_ALI_PC_PAY) {
            return AliPayType.PC;
        }
        throw new ResponseException(ResponseError.E_RECHARGE_ALIPAY_SERVICE, "Invalid pay platform");
    }

    private AlipayOrderInfo getOrderInfoByOrderId(String orderId) {

        RechargeRecord record = rechargeService.getRecordByOrderId(orderId);
        if (record == null)
            throw new ResponseException(ResponseError.E_RECHARGE_ALIPAY_SERVICE, "Invalid order id");
        return getOrderInfoFromRechargeRecord(record);
    }


    private AlipayProvider getProvider(AlipayOrderInfo info) {
        if (info.app.getPlatform() == ThirdPayPlatform.OFFICIAL.getValue()) {
            AlipayOfficialProvider provider = new AlipayOfficialProvider(info, getAlipayClient(info.app));
            provider.setNotifyUrl(aliPayConfig.getNotifyUrl());
            return provider;
        } else if (info.app.getPlatform() == ThirdPayPlatform.LIANLIAN.getValue()) {
            return lianLianPayService.getAliProvider(info);
        } else if (info.app.getPlatform() == ThirdPayPlatform.HUIFU.getValue()) {
            return huifuPayService.getAliProvider(info);
        }
        throw new ResponseException(ResponseError.E_RECHARGE_ALIPAY_SERVICE, "无效支付平台");
    }

    private AlipayClient getAlipayClient(RechargeAliApp app) {
        return new DefaultAlipayClient(ALIPAY_GATEWAY_URL, app.getAppId(), app.getPrivateKey(), ALIPAY_FORMAT, ALIPAY_CHARSET, app.getAliPublicKey(), ALIPAY_SIGN_TYPE);
    }


    public void rechargeControlFeedback(String aliAppId, int appId, int rmb) {
        RechargeAliApp app = getAppByAliAppId(aliAppId);
        if (app != null)
            rechargeControlService.increaseAliAmount(appId, (int) app.getId(), rmb);
        else {
            logger.error("Invalid mch id : {}, {}", aliAppId, appId);
        }
    }

    private boolean onPaymentReceived(String tradeNo, String aliTradeNo, String totalFee) {
        try {
            RechargeResult result = rechargeService.validAndRecharge(tradeNo, aliTradeNo, totalFee);
            if (!result.isDuplicated())
                rechargeControlFeedback(result.getMerchantId(), result.getAppId(), (int) result.getRmb());
            return true;
        } catch (Exception e) {
            logger.error("Payment received error", e);
        }
        return false;
    }

    private AliPayType getAliPayTypeByPlatform(int platform) {
        if (platform == PayPlatform.PPF_ALI_APP_PAY.getValue()) {
            return AliPayType.APP;
        } else if (platform == PayPlatform.PPF_ALI_WAP_PAY.getValue()) {
            return AliPayType.WAP;
        } else if (platform == PayPlatform.PPF_ALI_PC_PAY.getValue()) {
            return AliPayType.PC;
        }
        return null;
    }

    @Transactional
    public String webReturnValid(Map<String, String> args) {

        String name = args.remove("name");
        String sessionId = args.remove("sessionid");
        String appName = args.remove("token");
        String resultUrl = args.remove("result");

        boolean isValid = isReturnValid(args);
        RechargeAliWebConfig webConfig = getWebConfigByName(appName);
        String url = isValid ? webConfig.getSuccessUrl() : webConfig.getFailUrl();

        if (!StringUtils.isEmpty(resultUrl)) {
            String symbol = resultUrl.contains("?") ? "&" : "?";
            String param = symbol + "result=";
            param = param + (isValid ? "success" : "fail");
            url = resultUrl + param;
        }


        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
        if (name != null) {
            builder.queryParam("name", name);
        }

        if (sessionId != null) {
            builder.queryParam("sessionid", sessionId);
        }

        return builder.toUriString();
        //return aliPayConfig.getWebFailUrl();
    }


    private boolean isReturnValid(Map<String, String> args) {
        String orderId = args.get("out_trade_no");
        String aliOrderId = args.get("trade_no");
        String appId = args.get("app_id");
        RechargeAliApp app = getAppByAliAppId(appId);

        if (validAliPayOrder(args) && isPaid(app, orderId, aliOrderId)) {
            logger.info("[Alipay][Valid][Success] {} is valid", orderId);
            return true;
        }

        return false;
    }


    private boolean isPaid(RechargeAliApp app, String orderId, String aliOrderId) {


        RechargeRecord r = rechargeService.getRecordByOrderId(orderId);
        if (r == null) {
            logger.info("[Alipay][Valid][Failed] order {} not exist", orderId);
            return false;
        }

        if ( !r.isPaid() ) {  // r.getStatus() != OrderStatus.ORS_SUCCESS.getValue() && r.getStatus() != OrderStatus.ORS_VALID.getValue()) {
            logger.info("[Alipay][Valid][Failed] order {} not paid", orderId);
            return false;
        }

        String tradeStatus = getTradeStatus(app, orderId, aliOrderId);
        logger.info("[Alipay][Valid] {} trade status: {} ", orderId, tradeStatus);
        if (TRADE_SUCCESS.equals(tradeStatus) || TRADE_FINISHED.equals(tradeStatus)) {
            return true;
        }

        logger.error("[Alipay][Valid][Failed] {} order status is SUCCESS, but trade status is incorrect", orderId);
        return false;
    }

    @Transactional
    public boolean rechargeNotifyNew(Map<String, String> params) {


        if (!validAliPayOrder(params)) {
            return false;
        }

        String tradeStatus = params.get("trade_status");
        String aliTradeNo = params.get("trade_no");
        String ourTradeNo = params.get("out_trade_no");
        String totalFee = params.get("total_amount");


        // String totalFee = params.get("total_fee");

        logger.info("[Alipay][Notify] Recharge record {} status change to {} ", ourTradeNo, tradeStatus);

        if (TRADE_SUCCESS.equals(tradeStatus)) {
            return onPaymentReceived(ourTradeNo, aliTradeNo, totalFee);
        }

        logger.info("[Alipay][Notify] Trade status: {}", tradeStatus);
        return false;
    }

    private boolean validAliPayOrder(Map<String, String> params) {
        String appId = params.get("app_id");

        String sellerId = params.get("seller_id");
        RechargeAliApp app = getAppByAliAppId(appId);
        if (app == null) {
            logger.error("Invalid alipay app: {}", appId);
            return false;
        }

        if (!app.getPid().equals(sellerId)) {
            logger.error("[Alipay][Notify] seller id not match, {}, {}", sellerId, app.getPid());
        }


        try {
            if (!AlipaySignature.rsaCheckV1(params, app.getAliPublicKey(), ALIPAY_CHARSET, ALIPAY_SIGN_TYPE)) {
                logger.error("[Alipay][Notify] signature valid failed");
                return false;
            }
        } catch (AlipayApiException e) {
            logger.error("[Alipay][Notify] signature validation service error", e);
            return false;
        }

        return true;
    }

    // 从支付宝查询订单状态
    private String getTradeStatus(RechargeAliApp app, String orderId, String aliOrderId) {
        AlipayClient client = getAlipayClient(app);
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        AlipayTradeQueryModel model = new AlipayTradeQueryModel();
        model.setOutTradeNo(orderId);
        model.setTradeNo(aliOrderId);
        model.setOrgPid(app.getPid());
        request.setBizModel(model);

        try {
            AlipayTradeQueryResponse response = client.execute(request);
            return response.getTradeStatus();
        } catch (AlipayApiException e) {
            logger.error("Error query order Id {}, {}", orderId, e.getErrMsg());
        }
        return null;
    }


    @Transactional
    public boolean rechargeNotify(Map<String, String> params) {
        // 先验证是不是来自于阿里支付
        if (!params.containsKey("notify_id")) {
            return false;
        }

        /*
        String partner = web_partner;
        if (params.get("sign_type").compareTo(APP_SIGN_TYPE_OLD) == 0) {
            partner = APP_PARTNER;
        }
        */

        String partnerId = params.get("seller_id");

        String notifyValidUrl = String.format("https://mapi.alipay.com/gateway.do?service=notify_verify&partner=%s&notify_id=%s"
                , partnerId, params.get("notify_id"));

        StringBuilder s = new StringBuilder();
        String t;
        // 发起一个GET请求
        try {
            URL url = new URL(notifyValidUrl);
            logger.info("[Alipay][AsyncValid] VALID URL: " + notifyValidUrl);

            InputStream in = url.openStream();
            BufferedReader bin = new BufferedReader(new InputStreamReader(in, "GBK"));
            while ((t = bin.readLine()) != null) {
                s.append(t);
            }
            logger.info("ali receive:" + s);
            bin.close();
            logger.info("[Alipay][Notify][Monitor] Final ");
        } catch (Exception e) {
            logger.error("[Alipay][Notify][Failed] Notify ID Validation Exception %s" + params.get("trade_no") + "," + e.getMessage());
        }
        if ((s.length() > 0) && "true".contentEquals(s)) {

            String tradeStatus = params.get("trade_status");
            String aliTradeNo = params.get("trade_no");
            String ourTradeNo = params.get("out_trade_no");
            String totalFee = params.get("total_fee");

            if (tradeStatus == null) {
                logger.error("[Alipay] [Notify] Invalid trade status");
                return false;
            }

            logger.info("[Alipay][Notify] Recharge record {} status change to {} ", ourTradeNo, tradeStatus);

            if (TRADE_SUCCESS.equals(tradeStatus)) {
                return onPaymentReceived(ourTradeNo, aliTradeNo, totalFee);
            }


            /*else if( "WAIT_BUYER_PAY".equals(tradeStatus)) {

            } else if( "TRADE_FINISHED".equals(tradeStatus) ) {

            } else if( "TRADE_CLOSED".equals(tradeStatus)) {

            }
            */

        } else {
            logger.error("[Alipay][Notify][Failed] Notify ID Validation Failed");
        }
        return false;
    }

    public List<RechargeRecord> listExceptionalOrder(Date startDate, Date endDate) {
        List<RechargeRecord> records = rechargeService.getSuccessRecordByDateAndPlatform(startDate, endDate, PayPlatform.PPF_ALI_WAP_PAY);
        List<RechargeRecord> ret = new ArrayList<>();
        records.forEach(rechargeRecord -> {
            RechargeAliApp app = getAppByAliAppId(rechargeRecord.getMerchantId());
            String tradeStatus = getTradeStatus(app, rechargeRecord.getOrderId(), rechargeRecord.getPlatformOrderId());
            logger.info("Trade Status: {}, {}", rechargeRecord.getOrderId(), tradeStatus);
            if (!(TRADE_SUCCESS.equals(tradeStatus) || TRADE_FINISHED.equals(tradeStatus))) {
                ret.add(rechargeRecord);
            }
        });
        return ret;
    }

    public AliPrepareOrder prepareAppOrder(Integer mgsAppId, long amount, long uid, String clientIp, MobilePlatform platform, boolean preferHttp) {
        return prepareOrder(AliPayType.APP, mgsAppId, PayPlatform.PPF_ALI_APP_PAY, uid, amount, platform, clientIp, preferHttp);
    }

    public AliPrepareOrder prepareWapOrder(Integer mgsAppId, long amount, long uid, String clientIp, MobilePlatform platform) {
        return prepareOrder(AliPayType.WAP, mgsAppId, PayPlatform.PPF_ALI_WAP_PAY, uid, amount, platform, clientIp);
    }

    private AliPrepareOrder prepareOrder(AliPayType payType, Integer mgsAppId, PayPlatform payPlatform, long uid, long amount, MobilePlatform platform, String clientIp) {
        return prepareOrder(payType, mgsAppId, payPlatform, uid, amount, platform, clientIp, false);
    }

    private AliPrepareOrder prepareOrder(AliPayType payType, Integer mgsAppId, PayPlatform payPlatform, long uid, long amount, MobilePlatform platform, String clientIp, boolean preferHttp) {
        AppEntity app = appService.getAppById(mgsAppId);
        String appName = app.getName();

        AlipayCreateOrder createOrder = new AlipayCreateOrder(appName, payType, uid, amount, payPlatform, platform, clientIp);
        createOrder.preferHttp = preferHttp;
        AlipayOrderInfo info = createAliPayOrder(createOrder);
        AliPrepareOrder order = new AliPrepareOrder();
        order.setOrderId(info.getOrderId());
        order.setType(info.getType().getValue());
        order.setUrl("");
        return order;
    }

    public AliRequestOrder requestAppOrder(String orderId) {
        return requestAlipayOrder(orderId, AliPayType.APP);
    }

    public AliRequestOrder requestWapOrder(String orderId) {
        return requestAlipayOrder(orderId, AliPayType.WAP);
    }

    private AliRequestOrder requestAlipayOrder(String orderId, AliPayType payType) {
        AlipayOrderInfo info = getOrderInfoByOrderId(orderId);
        if (info.originType != payType) {
            logger.error("请求的支付类型和订单号的不一致: {} ！= {}", payType, info.type);
            throw new ResponseException(ResponseError.E_RECHARGE_ALIPAY_SERVICE, "请求的支付类型和订单不一致");
        }

        AlipayOrderExecuteResult result = info.getProvider().execute();
        AliRequestOrder order = new AliRequestOrder();
        order.setData(result.getData());
        order.setProtocol(result.getProtocol().getValue());
        return order;
    }
}
