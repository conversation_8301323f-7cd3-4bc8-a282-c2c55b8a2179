package com.easylive.pay.service.alipay;

import com.easylive.pay.entity.RechargeAliApp;
import com.easylive.pay.enums.AliPayType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/5
 */
@Data
public class AlipayOrderInfo {
    AliPayType type;
    AliPayType originType;
    String name;
    Date userRegTime;
    String detail;
    String orderId;
    String amount;
    RechargeAliApp app;
    AlipayProvider provider;
}
