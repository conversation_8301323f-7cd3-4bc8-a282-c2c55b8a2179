package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.mgs.BizException;
import com.easylive.pay.common.mgs.MgsServiceBuilder;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.enums.IdentityType;
import com.easylive.pay.model.NobleInfo;
import com.easylive.pay.model.User;
import com.easylive.pay.model.user.IdentityInfo;
import com.easylive.pay.model.user.UserInfo;
import com.easylive.pay.model.user.UserInfoOption;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

interface MgsUserService {
    @RequestLine("GET /base/info/uid?uid={uid}")
    User getById(@Param("uid") Long uid);

    @RequestLine("POST /base/info/uid/multi")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    List<User> getByUids(@Param("uids") String uids);

    @RequestLine("GET /base/info/name?name={name}")
    User getByName(@Param("name") String name);

    @RequestLine("GET /base/info/session?sid={session}")
    User getBySession(@Param("session") String session);

    @RequestLine("GET /noble/user?uid={uid}")
    NobleInfo getNobleInfo(@Param("uid") long uid);

    @RequestLine("GET /info/uid?uid={uid}&option={option}")
    UserInfo getInfoByUid(@Param("uid") Long uid, @Param("option") String option);

    @RequestLine("POST /info/uid/multi")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    List<UserInfo> multiGetInfoByUid(@Param("uids") String uids, @Param("option") String option);

    @RequestLine("POST /info/query/name/multi")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    List<UserInfo> multiGetInfoByName(@Param("names") String names, @Param("option") String option);

    @RequestLine("GET /base/info/name?name={name}&appId={appId}")
    User getByNameV2(@Param("name") String name, @Param("appId") Integer appId);
}

@Service
@Slf4j
public class UserService {

    private MgsUserService mgsUserService;

    @Autowired
    private URLConfig urlConfig;

    @PostConstruct
    public void init() {
        mgsUserService = MgsServiceBuilder.build(urlConfig.getUserServiceUrl(), MgsUserService.class);
    }

    public UserInfo getInfoByUid(long uid, String option) {
        try {
            return mgsUserService.getInfoByUid(uid, option);
        } catch (BizException e) {
            log.warn("uid {} not exists", uid);
        } catch (Exception e) {
            log.error("Error getting user by id {}", e.getMessage());
        }
        return null;
    }

    public UserInfo getUserRegInfoByUid(long uid) {
        return getInfoByUid(uid, UserInfoOption.REG.name());
    }

    public IdentityInfo getIdentityByUid(long uid) {
        UserInfo userInfo = getInfoByUid(uid, UserInfoOption.IDENTITY.name());
        if (userInfo == null) {
            return null;
        }
        return userInfo.getIdentity();
    }

    public User getById(long uid) {
        try {
            return mgsUserService.getById(uid);
        } catch (BizException e) {
            log.warn("uid {} not exists", uid);
        } catch (Exception e) {
            log.error("Error getting user by id {}", e.getMessage());
        }
        return null;
    }

    public User getByName(String name) {
        try {
            return mgsUserService.getByName(name);
        } catch (BizException e) {
            log.warn("user name {} not exists", name);
        } catch (Exception e) {
            log.error("Error getting user by name {}", e.getMessage());
        }
        return null;
    }

    public List<UserInfo> multiGetByUidWithAuth(List<Long> uids) {
        String query = StringUtils.join(uids, ",");
        try {
            return mgsUserService.multiGetInfoByUid(query, UserInfoOption.AUTH.name());
        } catch (Exception e) {

        }

        return new ArrayList<>();
    }


    public List<UserInfo> multiGetByName(List<String> names) {
        String query = StringUtils.join(names, ",");
        try {
            return mgsUserService.multiGetInfoByName(query, UserInfoOption.buildOptionString(UserInfoOption.BASE));
        } catch (Exception e) {
            log.error("Error getting user by name {}", names, e);
        }

        return new ArrayList<>();
    }

    public List<UserInfo> multiGetByUids(String uids, Set<UserInfoOption> options) {
        try {
            return mgsUserService.multiGetInfoByUid(uids, UserInfoOption.buildOptionString(options));
        } catch (Exception e) {
            log.error("Error getting user by uids {}", uids, e);
        }
        return new ArrayList<>();
    }

    public User ensureExists(String name) {
        User user = this.getByName(name);
        if (user == null)
            throw new ResponseException(ResponseError.E_USER_NOT_EXISTS);
        return user;
    }

    public User ensureExists(String name, int appId) {
        User user = mgsUserService.getByNameV2(name, appId);
        if (user == null)
            throw new ResponseException(ResponseError.E_USER_NOT_EXISTS);
        return user;
    }


    public User ensureExists(long uid) {
        User user = this.getById(uid);
        if (user == null)
            throw new ResponseException(ResponseError.E_USER_NOT_EXISTS);
        return user;
    }

    public User getBySessionOrName(String sessionId, String name)  {
        if (!StringUtils.isEmpty(sessionId)) {
            return this.getBySession(sessionId);
        } else if (!StringUtils.isEmpty(name)) {
            return this.ensureExists(name);
        }
        throw new ResponseException(ResponseError.E_USER_NOT_EXISTS,"Session or name is incorrect");
    }

    public User getBySession(String sessionId) {

        try {
            User user = mgsUserService.getBySession(sessionId);
            user.setSessionId(sessionId);
            return user;
        } catch (BizException e) {
            log.warn("session {} not valid", sessionId);
            throw new ResponseException(ResponseError.E_SESSION);
        } catch (Exception e) {
            log.error("Get session error {}: {} ", sessionId, e.getMessage());
        }
        throw new ResponseException(ResponseError.E_SERVER);
        /*
        long uid = sessionCache.getUidBySession(sessionId);
        User user = null;
        if (uid != -1) {
            user = userCache.getUserByUid(uid);
            if (user != null) {
                user.setSessionId(sessionId);
            }
        }

        if (user == null)
            return appGateService.getUserInfoBySession(sessionId);

        return user;
        */
    }

    private NobleInfo userNobleInfo(Long uid) {
        NobleInfo failSafe = new NobleInfo();
        try {
            return mgsUserService.getNobleInfo(uid);
        } catch (BizException e) {
            log.info("Noble info not exists for uid {}", uid);
        } catch (Exception e) {
            log.error("get noble error, {}, {}", uid, e.getMessage());
        }
        return failSafe;
    }

    public NobleInfo getNobleInfo(Long uid) {
        NobleInfo nobleInfo = userNobleInfo(uid);
        if (nobleInfo.getLevel() > 0 && nobleInfo.getEndTime().after(new Date())) {
            return nobleInfo;
        }

        return new NobleInfo();
    }

    public Map<Long, User> listByUids(List<Long> uids) {

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < uids.size(); ++i) {
            sb.append(uids.get(i));
            if (i != uids.size() - 1) {
                sb.append(",");
            }
        }

        List<User> users = null;
        try {
            users = mgsUserService.getByUids(sb.toString());
        } catch (Exception e) {
            log.error("get uids error, {}", e.getMessage());
        }

        if (users == null)
            users = new ArrayList<>();

        Map<Long, User> mapUser = new HashMap<>();
        users.forEach(user -> mapUser.put(user.getId(), user));
        return mapUser;
    }

    public Map<Long, UserInfo> listUserInfoByUids(Collection<?> uids, String... options) {

        if (uids.isEmpty()) {
            return new HashMap<>();
        }
        List<UserInfo> users = null;
        try {
            String uidStr = StringUtils.join(uids.toArray(), ",");
            String opt = StringUtils.join(options, ",");
            users = mgsUserService.multiGetInfoByUid(uidStr, opt);
        } catch (Exception e) {
            log.error("get uids error, {}", e.getMessage());
        }

        if (users == null)
            users = new ArrayList<>();

        Map<Long, UserInfo> mapUser = new HashMap<>();
        users.forEach(user -> mapUser.put(user.getUid(), user));
        return mapUser;
    }

    /**
     * 获得真实姓名
     */
    public IdentityInfo getUserIdentityInfoByUid(long uid) {
        UserInfo user = mgsUserService.getInfoByUid(uid, UserInfoOption.IDENTITY.name());
        if (user == null) {
            throw new ResponseException(ResponseError.E_USER_NOT_EXISTS);
        }
        IdentityInfo certificationInfo = user.getIdentity();
        if (StringUtils.isEmpty(certificationInfo.getRealName())) {
            throw new ResponseException(ResponseError.E_USER_BANK_CARD_NOT_AUTHENTICATION, "你还没有实名认证");
        }
        if (certificationInfo.getType() == IdentityType.ALIYUN.getType()) {
            throw new ResponseException(ResponseError.E_USER_BANK_CARD_NEED_AUTH, "你还没有绑定银行卡");
        }
        return certificationInfo;
    }

    public String getUserAuthPhoneNumber(long uid) {
        UserInfo user = mgsUserService.getInfoByUid(uid, UserInfoOption.AUTH.name());
        List<UserInfo.AuthInfo> authInfoList = user.getAuth();
        for (UserInfo.AuthInfo authInfo : authInfoList) {
            if (authInfo.getAuthType().compareTo("phone") == 0 && authInfo.getToken().length() < 32) {
                return authInfo.getToken();
            }
        }
        return null;
    }

    public boolean isLiveUUser(long uid) {
        User user = getById(uid);
        return isLiveUUser(user);
    }

    public boolean isLiveUUser(User user) {
        long appId = user.getAppId();
        return appId == Constants.LIVEU_APPID || appId == Constants.LIVEU_LITE_APPID;
    }

}

