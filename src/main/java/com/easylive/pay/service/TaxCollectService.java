package com.easylive.pay.service;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.CashoutSyd;
import com.easylive.pay.dao.UserBankCardRepository;
import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.entity.UserBankCard;
import com.easylive.pay.enums.bank.TaxCollectFunName;
import com.easylive.pay.enums.bank.TaxCollectHeadCode;
import com.easylive.pay.model.cashout.sc.*;
import com.easylive.pay.model.user.IdentityInfo;
import com.easylive.pay.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 税筹系统
 * <AUTHOR>
 * @date 2020/12/25
 */
@Service
@Slf4j
public class TaxCollectService {

    @Autowired
    private CashoutSyd sydConfig;

    @Autowired
    private UserService userService;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    private static String PAY_PATH_NO = "ef352091e2d64756b731344d5b7f1d2c";

    private String getScUrl() {
        return sydConfig.getCashoutScDomain();
    }

    /**
     * 1. 客户签约
     * @param cardName 账户名
     * @param idCard   身份证号
     * @param mobile   手机号
     * @param cardNo   银行卡号
     * @return
     */
    public boolean signContract(String cardName, String cardNo, String idCard, String mobile) {
        /**
         * 1. 生成原始报文头head并URL编码
         * 2. 构造body并URL编码
         * 3. AES加密
         * 4. MD5加密
         * 5. 签名
         * 6. 生成请求JSON
         * 7. 收到响应JSON
         * 8. 解析响应
         * 9. 验证MD5
         * 10. 验证签名
         * 11. body AES解密
         * 12. body URLDecoder 解码得到原始body
         */
        // 1. 生成原始报文头head并URL编码
        String srcHead = getHead(TaxCollectFunName.USER_SIGN);
        log.debug("[signContract] 1. srcHead URLEncoder={}", srcHead);

        // 2. 构造body并URL编码
        String srcBody = buildSignContractJson(cardName, idCard, AppStringUtils.simplePhone(mobile));
        log.debug("[signContract] 2. body URLEncoder：" + srcBody);

        // 3. AES加密
        String sendJson = getSendJson(srcBody, srcHead);
        String responseStr = HttpUtils.doPostJson(getScUrl(), sendJson);
        log.debug("[signContract] 3. 收到的响应：" + responseStr);

        CommonRequest response = JsonUtils.fromString(responseStr, CommonRequest.class);
        if (response == null) {
            log.error("[signContract] 1. parse response failed. responseStr={}", responseStr);
            return false;
        }

        String bodyResponse = getBodyResponse(response);
        if (bodyResponse == null) {
            log.error("[signContract] 2. validate response failed. response={}", response.toString());
            return false;
        }

        BankOperResponse res = JsonUtils.fromString(bodyResponse, BankOperResponse.class);
        if (!"0".equals(res.getBusinessStatus())) {
            log.error("[signContract] 3. sign error, res={}", JsonUtils.toString(res));
            return false;
        }

        return true;
    }

    /**
     * 2. 批量付款
     * @param cashoutRecord 提现信息
     * @param bankCard      银行卡号
     * @param idNo          身份证号
     */
    public void payment(CashoutRecord cashoutRecord, UserBankCard bankCard, String idNo) {
        /**
         * 1. 生成原始报文头head并URL编码
         * 2. 构造body并URL编码
         * 3. AES加密
         * 4. MD5加密
         * 5. 签名
         * 6. 生成请求JSON
         * 7. 收到响应JSON
         * 8. 解析响应
         * 9. 验证MD5
         * 10. 验证签名
         * 11. body AES解密
         * 12. body URLDecoder 解码得到原始body
         */
        log.info("[payment] 1. cashout start, cashoutRecord={}, bankCard={}, idNo={}", cashoutRecord.toString(), bankCard.toString(), idNo);

        IdentityInfo identityInfo = userService.getIdentityByUid(cashoutRecord.getUid());
        if (StringUtils.isEmpty(identityInfo.getPhone())) {
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现失败，手机号不存在");
        }
        String mobile = AppStringUtils.simplePhone(identityInfo.getPhone());

        long rmb = cashoutRecord.getRmb();
        //因为税筹提现只有正式环境，所以将测试环境提现金额改成1分
        if (sydConfig.isCashoutSydDevelopment()) {
            rmb = 1L;
        }
        //将分转换成元
        double rmbY = AppStringUtils.changeF2Y(rmb);

        // 1. 生成原始报文头head并URL编码
        String srcHead = getHead(TaxCollectFunName.BATCH_PAYMENT);
        log.info("[payment] 1. tax cashout request srcHead={}", srcHead);

        // 2. 构造body并URL编码
        BatchPayment batchPayment = buildBatchPayment(rmbY, cashoutRecord.getOrderid(), bankCard.getAccountName(), bankCard.getAccountNo(), idNo, mobile);
        String srcBody = JsonUtils.toString(batchPayment);
        log.info("[payment] 2. tax cashout srcBody：" + srcBody);
        srcBody = HttpUtils.URLEncode(srcBody);

        // 3. AES加密
        String sendJson = getSendJson(srcBody, srcHead);
        String responseStr = HttpUtils.doPost(getScUrl(), sendJson);
        log.info("[payment] 4. tax cashout responseStr：" + responseStr);

        CommonRequest response = JsonUtils.fromString(responseStr, CommonRequest.class);
        if (response == null) {
            log.error("[payment] 1. cashout failed, response={}", JsonUtils.printObject(response));
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现失败，请稍后再试");
        }

        String bodyResponse = getBodyResponse(response);
        if (bodyResponse == null) {
            log.error("[payment] 1. cashout failed, response={}", JsonUtils.printObject(response));
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现失败，请稍后再试");
        }

        BankOperResponse body = JsonUtils.fromString(bodyResponse, BankOperResponse.class);
        log.info("[payment] 5. parse body：" + JsonUtils.toString(body));
        if (!"0".equals(body.getBusinessStatus())) {
            log.error("[payment] 2. cashout failed, resData={}", JsonUtils.printObject(body));
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现失败，请稍后再试");
        }
    }

    /**
     * 3. 付款查询
     * @param merOrderId 商户订单号
     */
    public PaymentDetailInfo paymentQuery(String merOrderId) {
        // 1. 生成原始报文头head并URL编码
        String srcHead = getHead(TaxCollectFunName.QUERY_PAYMENT);
        log.debug("[paymentQuery] 1. srcHead={}", srcHead);

        // 2. 构造body并URL编码
        String srcBody = buildQueryPayment(merOrderId);
        log.debug("[paymentQuery] 2. srcBody URLEncoder：" + srcBody);

        // 3. AES加密
        String sendJson = getSendJson(srcBody, srcHead);
        String responseStr = HttpUtils.doPost(getScUrl(), sendJson);
        log.debug("[paymentQuery] 3. 收到的响应：" + responseStr);

        CommonRequest response = JsonUtils.fromString(responseStr, CommonRequest.class);
        if (response == null) {
            log.error("[paymentQuery] 1. parse response failed. responseStr={}", responseStr);
        }

        String bodyResponse = getBodyResponse(response);
        if (bodyResponse == null) {
            log.error("[paymentQuery] 2. validate response failed. response={}", JsonUtils.toString(response));
        }

        log.debug("[paymentQuery] 4. bodyResponse：" + bodyResponse);
        return JsonUtils.fromString(bodyResponse, PaymentDetailInfo.class);
    }

    public String getHead(TaxCollectFunName function) {
        RequestHead head = new RequestHead();
        head.setFunction(function.getValue());
        head.setMerCode(sydConfig.getCashoutScMerId());
        head.setVersion(Constants.SYD_VERSION);
        head.setReqTime(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX").format(new Date()));
        // head.setReqMsgId(IdGen.uuid());
        String s = JsonUtils.toString(head);
        log.debug("[getHead] s={}", s);
        return HttpUtils.URLEncode(s);
    }

    private String getBodyResponse(CommonRequest request) {
        if (request == null || request.getHead() == null || request.getBody() == null) {
            log.error("[getBodyResponse] 1. parse request null");
            return null;
        }

        // 2) MD5验证
        String cipherRes = getMd5Cipher(request.getHead(), request.getBody()).toUpperCase();
        log.debug("[getBodyResponse] 1. 收到的响应cipherRes={}", cipherRes);
        if (!cipherRes.equalsIgnoreCase(request.getCipher())) {
            log.error("[getBodyResponse] 2. cipher validate failed. exceptedCipher={}, realCipher={}", cipherRes, request.getCipher());
            return null;
        }

        String head = HttpUtils.URLDecode(request.getHead());
        ResponseHead responseHead = JsonUtils.fromString(head, ResponseHead.class);
        if (!TaxCollectHeadCode.SUCCESS.getValue().equals(responseHead.getCode())) {
            log.error("[getBodyResponse] 3. pare head error, responseHead={}", responseHead.toString());
            if (TaxCollectHeadCode.SYSTEM_ERROR.getValue().equals(responseHead.getCode())) {
                throw new ResponseException(ResponseError.E_CASHOUT_PAUSE_TRANSATION, "系统异常");
            } else {
                return null;
            }
        }

        // 签名
        /*if (!RSA.checkSignSHA256(request.getBody(), request.getSign(), sydConfig.getCashoutScPubKey())) {
            log.error("[getBodyResponse] 3. sign validate failed. sign={}", request.getSign());
            return null;
        }*/

        log.debug("[getBodyResponse] 1. 收到的响应body={}", request.getBody());
        String srcResBody = AES.decrypt32(request.getBody(), sydConfig.getCashoutScInterKey());
        log.debug("[getBodyResponse] 2. 收到的响应srcResBody={}", srcResBody);
        srcResBody = HttpUtils.URLDecode(srcResBody);

        return srcResBody;
    }

    private String buildSignContractJson(String name, String idCard, String mobile) {
        UserSign userSign = new UserSign(name, idCard, mobile);
        String s = JsonUtils.toString(userSign);
        log.debug("[buildSignContractJson] contract={}", s);
        return HttpUtils.URLEncode(s);
    }

    private String buildQueryPayment(String orderId) {
        QueryPayment queryPayment = new QueryPayment();
        queryPayment.setBatchNo(orderId);
        queryPayment.setOrderId(orderId);
        return HttpUtils.URLEncode(JsonUtils.toString(queryPayment));
    }

    private String getSendJson(String srcBody, String srcHead) {
        // 2) AES加密
        String aesBody = AES.encrypt32(srcBody, sydConfig.getCashoutScInterKey());
        log.debug("[getSendJson] 1. body AES密文={}", aesBody);

        // 3) MD5加密
        String cipher = getMd5Cipher(srcHead, aesBody);
        log.debug("[getSendJson] 2. cipher={}", cipher);

        // 4 ) 签名
        String sign = RSA.signSHA256(new String(aesBody.getBytes()), sydConfig.getCashoutScPriKey());
        log.debug("[getSendJson] 3. sign={}", sign);

        // 5) 生成请求JSON
        String sendJson = JsonUtils.toString(new CommonRequest(srcHead, cipher, sign, aesBody, AppStringUtils.uuid()));
        log.debug("[getSendJson] 4. reqMap={}", sendJson);
        return sendJson;
    }

    private String getMd5Cipher(String srcHead, String aesBody) {
        return Md5Utils.getMd5(srcHead + "#" + aesBody, sydConfig.getCashoutScInterKey(), "utf-8").toUpperCase();
    }

    private BatchPayment buildBatchPayment(double amount,
                                           String orderId,
                                           String accountName,
                                           String accountNo,
                                           String idCard,
                                           String phone) {
        List<BatchPaymentList> list = new ArrayList<>();
        BatchPaymentList p = new BatchPaymentList(orderId, accountName, idCard, accountNo, phone, amount);
        list.add(p);

        BatchPayment batchPayment = new BatchPayment();
        batchPayment.setBatchNo(orderId);
        batchPayment.setPathNo(PAY_PATH_NO);
        batchPayment.setTotalAmount(list.stream().mapToDouble(BatchPaymentList::getPayAmount).sum());
        batchPayment.setTotalNum((long) list.size());
        batchPayment.setPayInfos(list);

        return batchPayment;
    }

    /**
     * 同步签约信息
     */
    public boolean syncTaxSign(int start, int count) {
        List<UserBankCard> list = userBankCardRepository.listUSerBankCard(start, count);
        if (list.size() > 0) {
            int i = 0;
            //记录签约失败的uid
            List<Long> failedUid = new ArrayList<>();
            List<Long> successUid = new ArrayList<>();

            for (UserBankCard u : list) {
                IdentityInfo identityInfo = userService.getIdentityByUid(u.getUid());
                if (identityInfo != null
                        && StringUtils.isNotEmpty(identityInfo.getIdCard())
                        && StringUtils.isNotEmpty(identityInfo.getPhone())) {
                    String phone = AppStringUtils.simplePhone(identityInfo.getPhone());
                    boolean b = signContract(u.getAccountName(), u.getAccountNo(), identityInfo.getIdCard(), phone);
                    if (b) {
                        successUid.add(u.getUid());
                        log.info("[syncTaxSign][{}] batch sign success, uid={}", i, u.getUid());
                    } else {
                        failedUid.add(u.getUid());
                        log.error("[syncTaxSign][{}] tax sign failed, uid={}", i, u.getUid());
                    }
                } else {
                    failedUid.add(u.getUid());
                    log.error("[syncTaxSign][{}] get user phone failed, uid={}", i, u.getUid());
                }
                i++;
            }
            log.info("[syncTaxSignSuccess] start={}, count={}, size={}, uids={}", start, count, failedUid.size(), StringUtils.join(failedUid, ","));
            log.info("[syncTaxSignFailed] start={}, count={}, size={}, uids={}", start, count, failedUid.size(), StringUtils.join(failedUid, ","));
        }
        return true;
    }
}
