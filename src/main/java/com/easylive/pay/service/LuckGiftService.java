package com.easylive.pay.service;

import com.easylive.pay.common.event.EventProducer;
import com.easylive.pay.dao.LuckDao;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.AssetSystemID;
import com.easylive.pay.enums.LuckValue;
import com.easylive.pay.model.LotusLuckMsgInput;
import com.easylive.pay.model.User;
import com.easylive.pay.pub.event.LuckGiftEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

@Service
public class LuckGiftService {

    private static Logger logger = LoggerFactory.getLogger(LuckGiftService.class);

    private static final Random random = new Random();

    @Autowired
    private LuckDao luckDao;

    @Autowired
    private AssetService assetService;

    @Autowired
    private AppGateService appGateService;

    @Autowired
    private LotusCenterService lotusCenterService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private EventProducer eventProducer;

    @Value("${cache.prefix}")
    private String cachePrefix;

    private static final String KEY_LUCK_POOL = "mgs:pay:luck_pool";

    private volatile  List<LuckTrigger> luckTriggers = new ArrayList<>();

    @Async
    @Transactional
    public synchronized void afterBuyLuckGift(Goods goods, long number, User to, User from, String tid, String vid) {
        long start = System.currentTimeMillis();
        //记录流水
        LuckRecord lr = new LuckRecord();
        lr.set(goods, from.getId(), vid, to.getId(), number, tid);
        luckDao.addLuckRecord(lr);

        //更新奖池金额
        long costEcoin = goods.getCost() * number;
        LuckPool luckPool = luckDao.getLuckPoll(LuckValue.LV_PRIZE_POOL_ID.getValue());

        //奖池增加0.5的消费易币
        int  poolInsert = (int) Math.floor(costEcoin * 0.5);
        long poolCurrent = luckPool.getEcoin() + poolInsert;
        long newTotal = luckPool.getTotalEcoin() + poolInsert;
        luckPool.setTotalEcoin(newTotal);

        logger.info("Luck gift start  1 cost : {}, number : {} poolInsert: {}, poolCurrent : {} , poolTotal: {}",
                goods.getCost(), number, poolInsert, poolCurrent, newTotal);


        //计算爆奖值，非爆奖中0.35，爆奖中0.55
        double explosionValue = 0.35f;
        if (luckPool.getExplosionStatus().equals(LuckValue.EXPLOSION_STATUS_EXPLOSION.getValue())) {
            explosionValue = 0.55f;
        }

        //计算随机值 1-7的随机数
        double randomValue = random.nextFloat() * 6 + 1f;

        //计算出返奖易币= 消费易币X爆奖值X随机值/5 + 消费易币/10 + 1
        long awardEcoin = (int) Math.floor((double)(costEcoin * explosionValue * randomValue)/5 + (double)costEcoin/10) + 1;
        long rate = 1;

        //当计算出来的返奖大于当前奖池剩余的情况。直接反消费易币/2
        if (awardEcoin >= poolCurrent) {
            awardEcoin = (long) Math.floor((double)costEcoin / 2);
            logger.info("Luck gift award c>=s, award:{}", awardEcoin);
        }

        //当奖池>10000币的时候 百分之一的概率获得大奖+1000。
        double bigPrizeRandom = random.nextFloat() * 10000;
        if (poolCurrent > 10000 && bigPrizeRandom <= 5) {
            awardEcoin = awardEcoin + 1000;
            logger.info("User : {} get luck gift big 100000 prize {} , random is {}", from.getId(), awardEcoin, bigPrizeRandom);
        }

        //计算返奖倍数
        if (awardEcoin >= goods.getCost()) {
            rate = (long) Math.ceil((double)awardEcoin / goods.getCost());
        }

        logger.info("Luck gift award:{}, random:{}, explosion value:{} , rate:{}", awardEcoin, randomValue, explosionValue, rate);

        if (awardEcoin > 0) {
            //写入流水记录
            lr.setIsReward(1);
            lr.setRewardEcoin(awardEcoin);
            luckDao.save(lr);

            assetService.unaryOperate(AssetSystemID.LUCK_GIFT, from, awardEcoin);
        }


        //记录最新奖池
        luckPool.setEcoin(poolCurrent - awardEcoin);
        luckDao.save(luckPool);

        boolean isTriggerExplosion = checkLuckPoolExplosion(luckPool, newTotal, awardEcoin);
        publishMsgLotus(luckPool, awardEcoin, rate, to, from, vid, isTriggerExplosion, goods.getId());
        long end = System.currentTimeMillis();
        logger.info("Luck gift cost: {}", end - start);
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public synchronized void afterBuyLuckGiftOld(Goods goods, long number, User to, User from, String tid, String vid) {
        //写入记录和加入奖金池
        long start = System.currentTimeMillis();
        LuckRecord lr = new LuckRecord();
        lr.set(goods, from.getId(), vid, to.getId(), number, tid);
        luckDao.addLuckRecord(lr);

        //更新奖池金额
        LuckPool luckPool = luckDao.getLuckPoll(LuckValue.LV_PRIZE_POOL_ID.getValue());

        long oldPoolTotalEcoin = luckPool.getTotalEcoin();
        long newPoolTotalEcoin = oldPoolTotalEcoin;

        long luckEcoin = 0;
        long totalMutiple = 0;
        long needs = goods.getCost() / LuckValue.LV_REWARD_RATIO.getValue();
        logger.info("Luck gift old pool:" + oldPoolTotalEcoin + ", new pool:" + newPoolTotalEcoin + ", needs :" + needs);
        //触发奖项
        if (newPoolTotalEcoin > 0) {
            long totalCost = goods.getCost() * number;
            if (totalCost >= LuckValue.LUCK_GIFT_DISCOUNT_LIMIT.getValue()) {
                //发放奖励(如果送出的礼物价值大于200, 则打折直接返奖，非爆奖中47-50折，爆奖中63-66折)
                logger.info("Luck gift over 200, value is : {}", totalCost);
                if (luckPool.getExplosionStatus().equals(LuckValue.EXPLOSION_STATUS_NOT_EXPLOSION.getValue())) {
                    luckEcoin = (long)Math.floor(totalCost * (random.nextFloat() * 0.03f + 0.47f));
                } else {
                    luckEcoin = (long)Math.floor(totalCost * (random.nextFloat() * 0.03f + 0.63f));
                }
                long surplus = luckEcoin % 6;
                if (surplus != 0) {
                    luckEcoin += (goods.getCost() - surplus);
                }

                totalMutiple = (long)Math.floor(luckEcoin / goods.getCost());
                newPoolTotalEcoin += totalCost / LuckValue.LV_REWARD_RATIO.getValue();
            } else {
                //现在要单独算每一次单个幸运礼物是否中奖 所以要循环
                for (int i = 1; i <= number; i++) {
                    newPoolTotalEcoin += needs;
                    float multiple = getLuckTriggerRate(newPoolTotalEcoin, oldPoolTotalEcoin, luckPool.getExplosionStatus());

                    if (multiple > 0) {
                        totalMutiple += multiple;
                        luckEcoin += goods.getCost() * multiple;
                    }
                    oldPoolTotalEcoin = newPoolTotalEcoin;
                }
            }

            if (luckEcoin > 0) {
                //写入流水记录
                lr.setIsReward(1);
                lr.setRewardEcoin(luckEcoin);
                luckDao.save(lr);

                assetService.unaryOperate(AssetSystemID.LUCK_GIFT, from, luckEcoin);
            }

            //检查爆奖相关
            luckPool.setTotalEcoin(newPoolTotalEcoin);
            boolean istriggerExplosion = checkLuckPoolExplosion(luckPool, newPoolTotalEcoin, luckEcoin);

            //记录最新奖池
            luckDao.save(luckPool);

            //异步请求lotus
            logger.debug("Luck gift emit kafka start");
            publishMsgLotus(luckPool, luckEcoin, totalMutiple, to, from, vid, istriggerExplosion, goods.getId());
            logger.debug("Luck gift emit kafka end");
        } else {
            logger.error("Luck new prize pool err:" + newPoolTotalEcoin);
        }

        long end = System.currentTimeMillis();
        logger.info("Luck gift cost: {}", end - start);
    }

    /**
     * 根据当前奖池计算对应的中奖倍数
     *
     * @param totalEcoin 当前总奖池
     */
    private float getLuckTriggerRate(long totalEcoin, long oldTotalEcoin, Long explosionStatus) {
        float rate = 0;

        List<LuckTrigger> luckTriggers = getLuckTriggers();

        //从最大奖开始循环 中奖跳出循环
        for (LuckTrigger luckTrigger : luckTriggers) {
            if (explosionStatus.equals(LuckValue.EXPLOSION_STATUS_NOT_EXPLOSION.getValue())) {
                long currentTriggerNum = (oldTotalEcoin / luckTrigger.getEcoinTrigger()) + 1;
                long currentTriggerEcoin = currentTriggerNum * luckTrigger.getEcoinTrigger();

                if (currentTriggerEcoin <= totalEcoin) {
                    rate = luckTrigger.getTriggerRate() / 10f;
                    break;
                }
            } else {
                long currentTriggerNum = (oldTotalEcoin / luckTrigger.getExplosionEcoinTrigger()) + 1;
                long currentTriggerEcoin = currentTriggerNum * luckTrigger.getExplosionEcoinTrigger();

                if (currentTriggerEcoin <= totalEcoin) {
                    rate = luckTrigger.getExplosionTriggerRate() / 10f;
                    break;
                }
            }
        }

        return rate;
    }

    /**
     * 检查爆奖相关状态和奖池
     */
    private boolean checkLuckPoolExplosion(LuckPool luckPool, long newpPoolTotalEcoin, long award) {
        //取出最近一次爆奖记录，下面做修改
        LuckPoolExplosion lastLpe = luckDao.getLastExplosionRecord();
        boolean isTriggerExplosion = false;

        //爆奖中
        if (luckPool.getExplosionStatus() == LuckValue.EXPLOSION_STATUS_EXPLOSION.getValue()) {
            //奖池还有剩余 或者奖池为负数
            if (luckPool.getCurrentExplosionPool() < 0 || luckPool.getCurrentExplosionPool() - award >= 0) {
                long currentLeft = luckPool.getCurrentExplosionPool() - award;
                luckPool.setCurrentExplosionPool(currentLeft);
            } else {
                //奖池空了或者不够这次返奖
                lastLpe.setStopTime(new Date());
                lastLpe.setExplosionValue(luckPool.getCurrentExplosionPool());
                luckDao.save(lastLpe);

                //写下爆奖结束时间戳到redis，异步php解析延迟爆奖
                String key = cachePrefix + "last_luck_explosion_timestamp";
                Long timestamp = System.currentTimeMillis() / 1000;
                redisTemplate.opsForValue().set(key, timestamp.toString());

                long nextExplosionPool = luckPool.getTotalEcoin() + LuckValue.EXPLOSION_NEXT_STAGE_VALUE.getValue();
                luckPool.setExplosionStatus(LuckValue.EXPLOSION_STATUS_NOT_EXPLOSION.getValue());
                luckPool.setCurrentExplosionPool(0L);
                luckPool.setNextExplosionPool(nextExplosionPool);

                //写入下次爆奖记录
                LuckPoolExplosion lpe = new LuckPoolExplosion();
                lpe.set(luckPool.getTotalEcoin(), LuckValue.EXPLOSION_ONCE_POOL_VALUE.getValue());
                luckDao.addExplosionRecord(lpe);
            }
        } else {
            //没有爆奖 但是这次爆奖触发了爆奖
            if (newpPoolTotalEcoin >= luckPool.getNextExplosionPool()) {
                luckPool.setExplosionStatus(LuckValue.EXPLOSION_STATUS_EXPLOSION.getValue());
                luckPool.setCurrentExplosionPool(LuckValue.EXPLOSION_ONCE_POOL_VALUE.getValue());
                lastLpe.setExplosionTime(new Date());
                luckDao.save(lastLpe);

                isTriggerExplosion = true;
            }
        }
        return isTriggerExplosion;
    }

    /**
     * 获取爆奖触发概率
     *
     * <AUTHOR>
     */
    private List<LuckTrigger> getLuckTriggers() {
        if (this.luckTriggers.isEmpty()) {
            logger.debug("Luck triggers memory cache is null, get from db.");
            List<LuckTrigger> luckTriggers = luckDao.getLuckTriggers();

            this.luckTriggers = luckTriggers;

            return luckTriggers;
        } else {
            logger.debug("Luck triggers get from memory cache!");
            return this.luckTriggers;
        }
    }

    /**
     * 发消息到lotus
     *
     * <AUTHOR>
     */
    private void publishMsgLotus(LuckPool luckPool,
                                 long luckEcoin,
                                 long totalMutiple,
                                 User to,
                                 User from,
                                 String vid,
                                 boolean isTriggerExplosion,
                                 Long goodsId) {

        LotusLuckMsgInput input = new LotusLuckMsgInput();
        input.setEcoin(luckEcoin);
        input.setExplosionStatus(luckPool.getExplosionStatus());
        input.setNextExplosionPool(luckPool.getNextExplosionPool());
        input.setRate(totalMutiple);
        input.setToName(to.getName());
        input.setUserName(from.getName());
        input.setTotalEcoin(luckPool.getTotalEcoin());
        input.setVid(vid);
        input.setTriggerExplosion(isTriggerExplosion);
        input.setGoodsId(goodsId);

        lotusCenterService.publishLuckGiftMsg(input);
    }

}
