package com.easylive.pay.service;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.ApplicationConfig;
import com.easylive.pay.dao.SubRechargeDao;
import com.easylive.pay.entity.GashRechargeRecord;
import com.easylive.pay.model.SubOrderInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class SubRechargeService {

    private static final Logger logger = LoggerFactory.getLogger(SubRechargeService.class);

    private static final long RECHARGE_MIN_AMOUNT = 100;

    @Autowired
    private ApplicationConfig applicationConfig;

    @Autowired
    private SubRechargeDao subRechargeDao;

    @Transactional
    public GashRechargeRecord createRechargeRecord(SubOrderInfo info) {
        long amount = info.getAmount();

        if (amount < RECHARGE_MIN_AMOUNT) {
            if (applicationConfig.isDevMode()) {
                if (amount <= 0) {
                    throw new ResponseException(ResponseError.E_RECHARGE_MIN_AMOUNT_LIMIT, "充值金额必须大于0");
                }
            } else {
                throw new ResponseException(ResponseError.E_RECHARGE_MIN_AMOUNT_LIMIT, "充值金额最少单位1");
            }
        }

        GashRechargeRecord record = new GashRechargeRecord();
        record.setUid(info.getUid());
        record.setOrderId(info.getOrderId());
        record.setAppId(info.getAppId());
        record.setPlatformId(info.getPayPlatform().getValue());
        record.setPlatformCode(info.getPlatformCode());
        record.setPlatformStatus(info.getPlatformStatus());
        record.setPlatformAmount(info.getAmount());
        record.setPlatformCurrency(info.getCurrency());
        record.setPlatformMsg("");
        record.setPlatformOrderId("");



        Date today = new Date();
        record.setCreateTime(today);
        record.setUpdateTime(today);
        this.subRechargeDao.save(record);

        logger.info("Create sub recharge record: {}", record.toString());

        return record;
    }

    public void updateSubRecharge(GashRechargeRecord record) {
        this.subRechargeDao.save(record);
    }

    public GashRechargeRecord getRecordByOrderId(String orderId, Integer platformId, Integer status) {
        return this.subRechargeDao.findRecordByOrderId(orderId, platformId, status);
    }

    public List<GashRechargeRecord> listIncompleteOrder(Date fromDate, Integer platformId, List<Integer> status) {
        return this.subRechargeDao.listIncompleteOrder(fromDate, platformId, status);
    }

}