package com.easylive.pay.service.apple.iap.notification;


import com.easylive.pay.service.apple.iap.Environment;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@lombok.Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Data {
  private String appAppleId; // number
  private String bundleId;
  private String bundleVersion;
  private Environment environment;
  // JWSRenewalInfo
  private String signedRenewalInfo;

  private String signedTransactionInfo;
}
