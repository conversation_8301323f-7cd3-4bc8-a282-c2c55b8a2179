package com.easylive.pay.service.apple;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by yangsong on 15/12/16.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleReturnApp {
    /*
        // {
        // "quantity":"1",
        // "product_id":"com.cloudfocus.oupai001",
        // "transaction_id":"1000000185160350",
        // "original_transaction_id":"1000000185160350",
        // "purchase_date":"2015-12-15 14:54:28 Etc/GMT",
        // "purchase_date_ms":"1450191268000",
        // "purchase_date_pst":"2015-12-15 06:54:28 America/Los_Angeles",
        // "original_purchase_date":"2015-12-15 14:54:28 Etc/GMT",
        // "original_purchase_date_ms":"1450191268000",
        // "original_purchase_date_pst":"2015-12-15 06:54:28 America/Los_Angeles",
        // "is_trial_period":"false"}
     */

    private String quantity;
    private String product_id;
    private String transaction_id;
    private String original_transaction_id;
    private String purchase_date;
    private Long purchase_date_ms;
    private String purchase_date_pst;
    private String original_purchase_date;
    private Long original_purchase_date_ms;
    private String original_purchase_date_pst;
    private boolean is_trial_period;
    private Long expires_date_ms;


    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getOriginal_transaction_id() {
        return original_transaction_id;
    }

    public void setOriginal_transaction_id(String original_transaction_id) {
        this.original_transaction_id = original_transaction_id;
    }

    public String getPurchase_date() {
        return purchase_date;
    }

    public void setPurchase_date(String purchase_date) {
        this.purchase_date = purchase_date;
    }

    public Long getPurchase_date_ms() {
        return purchase_date_ms;
    }

    public void setPurchase_date_ms(Long purchase_date_ms) {
        this.purchase_date_ms = purchase_date_ms;
    }

    public String getPurchase_date_pst() {
        return purchase_date_pst;
    }

    public void setPurchase_date_pst(String purchase_date_pst) {
        this.purchase_date_pst = purchase_date_pst;
    }

    public String getOriginal_purchase_date() {
        return original_purchase_date;
    }

    public void setOriginal_purchase_date(String original_purchase_date) {
        this.original_purchase_date = original_purchase_date;
    }

    public Long getOriginal_purchase_date_ms() {
        return original_purchase_date_ms;
    }

    public void setOriginal_purchase_date_ms(Long original_purchase_date_ms) {
        this.original_purchase_date_ms = original_purchase_date_ms;
    }

    public String getOriginal_purchase_date_pst() {
        return original_purchase_date_pst;
    }

    public void setOriginal_purchase_date_pst(String original_purchase_date_pst) {
        this.original_purchase_date_pst = original_purchase_date_pst;
    }

    public boolean is_trial_period() {
        return is_trial_period;
    }

    public void setIs_trial_period(boolean is_trial_period) {
        this.is_trial_period = is_trial_period;
    }

    public boolean isIs_trial_period() {
        return is_trial_period;
    }

    public Long getExpires_date_ms() {
        return expires_date_ms;
    }

    public void setExpires_date_ms(Long expires_date_ms) {
        this.expires_date_ms = expires_date_ms;
    }
}
