package com.easylive.pay.service.apple.iap.receipt;

import com.easylive.pay.service.apple.iap.Environment;

@lombok.Data
public class ReceiptVerifyResult {

  private Environment environment;

  private Boolean isRetryable;
  // The latest Base64 encoded app receipt.
  // Only returned for receipts that contain auto-renewable subscriptions.
  private byte[] latest_receipt;

  private LatestReceiptInfo[] latest_receipt_info;

  private PendingRenewalInfo[] pending_renewal_info;

  private Receipt receipt;

  private int status;
}
