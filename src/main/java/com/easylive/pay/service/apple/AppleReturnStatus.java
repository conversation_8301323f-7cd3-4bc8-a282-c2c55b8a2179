package com.easylive.pay.service.apple;

import com.easylive.pay.service.apple.iap.receipt.LatestReceiptInfo;
import com.easylive.pay.service.apple.iap.receipt.PendingRenewalInfo;

/**
 * Created by yangsong on 15/12/16.
 */
public class AppleReturnStatus {
/*
//{"status":0, "environment":"Sandbox",
        // "receipt":
        // {"receipt_type":"ProductionSandbox",
        // "adam_id":0, "app_item_id":0,
        // "bundle_id":"com.cloudfocus.oupai",
        // "application_version":"20151204", "download_id":0,
        // "version_external_identifier":0,
        // "receipt_creation_date":"2015-12-15 14:54:30 Etc/GMT",
        // "receipt_creation_date_ms":"1450191270000",
        // "receipt_creation_date_pst":"2015-12-15 06:54:30 America/Los_Angeles",
        // "request_date":"2015-12-15 15:45:34 Etc/GMT",
        // "request_date_ms":"1450194334302",
        // "request_date_pst":"2015-12-15 07:45:34 America/Los_Angeles",
        // "original_purchase_date":"2013-08-01 07:00:00 Etc/GMT",
        // "original_purchase_date_ms":"1375340400000",
        // "original_purchase_date_pst":"2013-08-01 00:00:00 America/Los_Angeles", "original_application_version":"1.0",
        // "in_app":[
        // {
        // "quantity":"1",
        // "product_id":"com.cloudfocus.oupai001",
        // "transaction_id":"1000000185160350",
        // "original_transaction_id":"1000000185160350",
        // "purchase_date":"2015-12-15 14:54:28 Etc/GMT", "purchase_date_ms":"1450191268000",
        // "purchase_date_pst":"2015-12-15 06:54:28 America/Los_Angeles", "original_purchase_date":"2015-12-15 14:54:28 Etc/GMT",
        // "original_purchase_date_ms":"1450191268000", "original_purchase_date_pst":"2015-12-15 06:54:28 America/Los_Angeles",
        // "is_trial_period":"false"}
        // ]}}
 */

    private int status;
    private String environment;
    private AppleReturnReceipt receipt;

    private LatestReceiptInfo[] latest_receipt_info;
    private PendingRenewalInfo[] pending_renewal_info;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public AppleReturnReceipt getReceipt() {
        return receipt;
    }

    public void setReceipt(AppleReturnReceipt receipt) {
        this.receipt = receipt;
    }

    public LatestReceiptInfo[] getLatest_receipt_info() {
        return latest_receipt_info;
    }

    public void setLatest_receipt_info(LatestReceiptInfo[] latest_receipt_info) {
        this.latest_receipt_info = latest_receipt_info;
    }

    public PendingRenewalInfo[] getPending_renewal_info() {
        return pending_renewal_info;
    }

    public void setPending_renewal_info(PendingRenewalInfo[] pending_renewal_info) {
        this.pending_renewal_info = pending_renewal_info;
    }
}
