package com.easylive.pay.service.apple.iap.notification;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

@lombok.Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseBodyV2DecodedPayload {
  private NotificationType notificationType;
  private Subtype subtype;
  private String notificationUUID;
  private String version; // 2.0
  private Data data;
  private Date signedDate;
}

