package com.easylive.pay.service.apple;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.config.ApplicationConfig;
import com.easylive.pay.dao.AppleSubRecordRepository;
import com.easylive.pay.dao.RechargeAppleOrderRepository;
import com.easylive.pay.dao.UserAppleSubRepository;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.AppleOrderStatus;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.model.OrderInfo;
import com.easylive.pay.model.RechargeResult;
import com.easylive.pay.model.User;
import com.easylive.pay.service.*;
import com.easylive.pay.service.apple.iap.NotificationDecoder;
import com.easylive.pay.service.apple.iap.notification.*;
import com.easylive.pay.service.apple.iap.receipt.PendingRenewalInfo;
import com.easylive.pay.utils.HttpUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ApplePayService {
    private static final String APPLE_RECEIPT_VERIFY_URL = "https://buy.itunes.apple.com/verifyReceipt";
    private static final String APPLE_SANDBOX_RECEIPT_VERIFY_URL = "https://sandbox.itunes.apple.com/verifyReceipt";
    private static Logger logger = LoggerFactory.getLogger(ApplePayService.class);
    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private AppService appService;

    @Autowired
    private ApplicationConfig applicationConfig;

    @Autowired
    private ConfigService configService;

    @Autowired
    private RechargeAppleOrderRepository appleOrderRepository;

    @Autowired
    private AppleSubRecordRepository appleSubRecordRepository;

    @Autowired
    private UserAppleSubRepository userAppleSubRepository;

    @Autowired
    private BizcoreCenterService bizcoreCenterService;

    @Autowired
    private UserService userService;

    private volatile Set<Long> sandboxUidSet = new HashSet<>();
    private volatile Set<String> sandboxVersionSet = new HashSet<>();

    private static HashMap<String, String> appleAppSecretMap = new HashMap<String, String>() {{
        put("gargantua", "ff38b846d12d4fafbff176255e64be35");
    }};

    @PostConstruct
    public void init() {
        reload();
    }

    public void reload() {
        String uids = configService.getAppleSandboxUids();
        if( uids != null ) {
            Set<Long> uidSet = new HashSet<>();
            for(String uid: uids.trim().split(",")) {
                uidSet.add(NumberUtils.toLong(uid, 0));
            }
            uidSet.remove(0L);
            sandboxUidSet = uidSet;
        }

        String version = configService.getAppleSandboxWhitelistVersion();
        if( version != null ) {
            try {
                sandboxVersionSet = new HashSet<>(Arrays.asList(version.trim().split(",")));
            } catch (Exception ignored) {
            }
        }
    }

    @Transactional
    public RechargeRecord createOrder(long uid,int appId, String productId) {
        OrderInfo orderInfo = new OrderInfo(appId, uid, productId,  PayPlatform.PPF_APPLE, MobilePlatform.MPF_IOS, "");
        return rechargeService.createRechargeRecord(orderInfo);
    }

    @Transactional
    public Map valid(String encodedStr, long uid, String appName, String clientIp, MobilePlatform clientPlatform) {

        Map<String, String> ret = new TreeMap<>();
        ret.put("status", "0");
        ret.put("ecoin", "0");
        TreeMap<String, String> payloadMap = new TreeMap<>();
        payloadMap.put("receipt-data", encodedStr);

        String secretKey = appleAppSecretMap.get(appName);
        if (secretKey != null) {
            payloadMap.put("password", secretKey);
        }

        GsonBuilder builder = new GsonBuilder().setDateFormat("yyyy-MM-dd");
        Gson gson = builder.create();
        String payload = gson.toJson(payloadMap);

        PayPlatform payPlatform = PayPlatform.PPF_APPLE;
        String responseStr = appleVerify(APPLE_RECEIPT_VERIFY_URL, payload);
        logger.info("ApplePayValid:[Online Verify Status]: UID: {}, Return: {}", uid, responseStr);
        AppleReturnStatus ara = gson.fromJson(responseStr, AppleReturnStatus.class);
        logger.info("ApplePayValid:[Online Verify Status]: UID: {}, Status: {}, Return: {}", uid, ara.getStatus(), responseStr);
        if (ara.getStatus() == 21007) {
            payPlatform = PayPlatform.PPF_SANDBOX;
            responseStr = appleVerify(APPLE_SANDBOX_RECEIPT_VERIFY_URL, payload);
            ara = gson.fromJson(responseStr, AppleReturnStatus.class);
            logger.info("ApplePayValid:[SandBox Verify Status]: UID {}, status : {}, Return: {}", uid, ara.getStatus(), responseStr);

            if( !isAllowedSandboxRecharge( ara, uid)) {
                logger.info("Not allowed sandbox recharge: {}", uid);
                return ret;
            }
        }

        if (ara.getStatus() == 0) {

            //校验bundle_id
            String bundleId = ara.getReceipt().getBundle_id();
            AppEntity app = appService.getByBundleId(bundleId);
            if (app == null) {
                logger.error("ApplePayValid:[Bundle_id Error] bundle_id is not managed " + bundleId);
                throw new ResponseException(ResponseError.E_APPLEPAY_VALID_BUNDLE_ID);
            }

            String appStoreId = app.getAppStoreId();

            ret.put("status", "1");
            List<AppleReturnApp> appleReturnApps = ara.getReceipt().getIn_app();
            if (appleReturnApps.isEmpty()) {
                logger.info("ApplePayValid:[Failed]: Apple Return Null : {} ", uid);
                throw new ResponseException(ResponseError.E_APPLEPAY_VALID_USED);
            }

            // 可能会有多个transaction的情况

            long totalEcoin = 0;
            for (AppleReturnApp returnApp : appleReturnApps) {
                String transactionId = returnApp.getTransaction_id();
                // 验证这个transactionId是否存在,由于苹果这边是只要存在就会有订单,所以应该检查是否存在即可
                RechargeRecord exists = rechargeService.getByPlatformOrderId(transactionId);
                if (exists != null) {
                    logger.info("ApplePayValid:[Failed]: Order Already Exists, UID: {} ", uid);
                    continue;
                }

                Date commitDt = new Date(returnApp.getOriginal_purchase_date_ms());
                // Date completeDt = new Date(app.getPurchase_date_ms());

                int appId = appService.getAppIdByName(appName);
                // RechargeResult rechargeResult = rechargeService.recharge(uid, payPlatform.getValue(), 0, productId, clientPlatform.getValue());
                RechargeResult rechargeResult = rechargeService.rechargeByProductId(uid, returnApp.getProduct_id(), payPlatform, clientPlatform, appId, clientIp, transactionId, commitDt, appStoreId);
                if (rechargeResult == null) {
                    continue;
                }
                long ecoin = rechargeResult.getEcoin();
                long rmb = rechargeResult.getRmb();
                totalEcoin += ecoin;


                //createAppleOrder(uid, rechargeResult.getOrderId(), transactionId);
                logger.info("[advstat][recharge][success] total {} rmb {} uid {} platform {} order_id {}",
                        ecoin, rmb, uid, payPlatform.getValue(), transactionId);

                logger.info("ApplePayValid:[Success]:UID: {}, ecoin:{}", uid, ecoin);
            }

            if (totalEcoin == 0) {
                throw new ResponseException(ResponseError.E_APPLEPAY_VALID_USED, "Order already valid");
            }
            ret.put("ecoin", String.valueOf(totalEcoin));

        } else {
            logger.info("ApplePayValid:[Failed]:Valid Failed UID: :{}", uid);
            throw new ResponseException(ResponseError.E_APPLEPAY_VALID_FAILD);
        }
        return ret;
    }

    @Transactional
    public Map subValid(String encodedStr, long uid, String appName, String secretKey, String clientTransactionId) {

        Map<String, String> ret = new TreeMap<>();
        ret.put("status", "0");
        TreeMap<String, String> payloadMap = new TreeMap<>();
        payloadMap.put("receipt-data", encodedStr);

        if (secretKey != null) {
            payloadMap.put("password", secretKey);
        }

        if (appName != null) {
            secretKey = appleAppSecretMap.get(appName);
            if (secretKey != null) {
                payloadMap.put("password", secretKey);
            }
        }

        GsonBuilder builder = new GsonBuilder().setDateFormat("yyyy-MM-dd");
        Gson gson = builder.create();
        String payload = gson.toJson(payloadMap);

        String responseStr = appleVerify(APPLE_RECEIPT_VERIFY_URL, payload);
        logger.info("AppleSubPayValid:[Online Verify Status]: UID: {}, Return: {}", uid, responseStr);
        AppleReturnStatus ara = gson.fromJson(responseStr, AppleReturnStatus.class);
        logger.info("AppleSubPayValid:[Online Verify Status]: UID: {}, Status: {}, Return: {}", uid, ara.getStatus(), responseStr);

        if (ara.getStatus() == 21007) {
            responseStr = appleVerify(APPLE_SANDBOX_RECEIPT_VERIFY_URL, payload);
            ara = gson.fromJson(responseStr, AppleReturnStatus.class);

            logger.info("AppleSubPayValid:[SandBox Verify Status]: UID {}, status : {}", uid, ara.getStatus());

            if( !isAllowedSandboxRecharge( ara, uid)) {
                logger.info("Not allowed sandbox sub pay: {}", uid);
                return ret;
            }
        }

        if (ara.getStatus() == 0) {
            //校验bundle_id
            String bundleId = ara.getReceipt().getBundle_id();
            AppEntity app = appService.getByBundleId(bundleId);
            if (app == null) {
                logger.error("AppleSubPayValid:[Bundle_id Error] bundle_id is not managed " + bundleId);
                throw new ResponseException(ResponseError.E_APPLEPAY_VALID_BUNDLE_ID);
            }

            ret.put("status", "1");
            List<AppleReturnApp> appleReturnApps = ara.getReceipt().getIn_app();
            if (appleReturnApps.isEmpty()) {
                logger.info("AppleSubPayValid:[Failed]: Apple Return Null : {} ", uid);
                throw new ResponseException(ResponseError.E_APPLEPAY_VALID_USED);
            }

            PendingRenewalInfo[] pendingRenewalArray = ara.getPending_renewal_info();
            Map<String, PendingRenewalInfo>  pendingRenewalMap = new HashMap<>();
            if (pendingRenewalArray != null && pendingRenewalArray.length != 0) {
                pendingRenewalMap = Arrays.stream(pendingRenewalArray).collect(Collectors.toMap(PendingRenewalInfo::getOriginal_transaction_id, info -> info));
            }

            List<String> transactionIdList = new ArrayList<>();
            for (AppleReturnApp returnApp : appleReturnApps) {
                transactionIdList.add(returnApp.getTransaction_id());
            }

            //查询所有存在的订单号
            List<String> existsTransactionIdList = appleSubRecordRepository.getTransactionIds(transactionIdList);

            for (AppleReturnApp returnApp : appleReturnApps) {
                String transactionId = returnApp.getTransaction_id();
                // 验证这个transactionId是否存在,由于苹果这边是只要存在就会有订单,所以应该检查是否存在即可
                if (existsTransactionIdList.contains(transactionId)) {
                    continue;
                }

                String productId = returnApp.getProduct_id();
                String originalTransactionId = returnApp.getOriginal_transaction_id();
                PendingRenewalInfo pendingRenewalInfo = pendingRenewalMap.get(originalTransactionId);
                boolean autoRenew = pendingRenewalInfo != null && "1".equals(pendingRenewalInfo.getAuto_renew_status());


                //续期才会有过期时间
                Date expireDate = new Date();
                if (returnApp.getExpires_date_ms() != null) {
                    expireDate = new Date(returnApp.getExpires_date_ms());
                }

                if (autoRenew) {
                    createOrUpdateUserSub(uid, productId, originalTransactionId, encodedStr, secretKey, expireDate);
                }

                AppleSubRecord record = createSubRecord(uid, productId, originalTransactionId, transactionId, autoRenew, expireDate);
                if (record.getId() > 0) {
                    //发放vip app购买
                    bizcoreCenterService.appleBuyVIP(uid, productId, false);
                    logger.info("User {} sub apple productId:{} transactionId: {}, clientTransactionId:{} and recordId:{}", uid, productId, transactionId, clientTransactionId, record.getId());
                } else {
                    logger.error("User {} sub apple productId:{} transactionId: {}, clientTransactionId:{} not send success vip", uid, productId, transactionId, clientTransactionId);
                }
            }

            logger.info("Sub pay Apple valid done uid:{}", uid);

        } else {
            logger.info("AppleSubPayValid:[Failed]:Valid Failed UID: :{}", uid);
            throw new ResponseException(ResponseError.E_APPLEPAY_VALID_FAILD);
        }
        return ret;
    }

    private boolean isAllowedSandboxRecharge(AppleReturnStatus status, long uid) {
        if( applicationConfig.isDevMode() ) {
            return true;
        }

        if( sandboxUidSet.contains(uid)) {
            return true;
        }

        if( status != null && status.getReceipt() != null ) {
            String bundleId = status.getReceipt().getBundle_id();
            if( sandboxVersionSet.contains(bundleId))
                return true;
        }

        return false;
    }

    private String appleVerify(String url, String payload) {
        String response = HttpUtils.doPost(url, payload);

        if (response.isEmpty()) {
            logger.error("Apple Response Empty.");
            throw new ResponseException(ResponseError.E_APPLEPAY_VALID_EMPTYRESPONSE);
        }

        return response;
    }
    private NotificationDecoder notificationDecoder = new NotificationDecoderImpl();

    @Transactional(rollbackFor = Exception.class)
    public void handleAppStoreNotification(ResponseBodyV2 responseBodyV2) {
        try {
            ResponseBodyV2DecodedPayload decodedPayload =
                    notificationDecoder.decodePayload(responseBodyV2.getSignedPayload());

            String signedTransactionInfo = decodedPayload.getData().getSignedTransactionInfo();

            NotificationType notificationType = decodedPayload.getNotificationType();
            Subtype subtype = decodedPayload.getSubtype();

            if( signedTransactionInfo != null) {
                JWSTransactionDecodedPayload jwsTransactionDecodedPayload = notificationDecoder.decodeTransaction(signedTransactionInfo);
                logger.info("transaction: {}", jwsTransactionDecodedPayload );

                logger.info("Notification event  type:{} subType:{} originalTransactionId:{}, transactionId:{}, productId:{}", notificationType, subtype, jwsTransactionDecodedPayload.getOriginalTransactionId(), jwsTransactionDecodedPayload.getTransactionId(), jwsTransactionDecodedPayload.getProductId());


                //用户发起了退款。开发者可在12小时内，提供用户的信息（比如游戏金币是否已消费、用户充值过多少钱、退款过多少钱等）
                // 最后苹果收到这些信息，协助“退款决策系统” 来决定是否允许用户退款
                if( notificationType == NotificationType.CONSUMPTION_REQUEST ) {
                    //添加到退款表
                    onAppleOrderRefundRequest(jwsTransactionDecodedPayload.getTransactionId(),  new Date(jwsTransactionDecodedPayload.getSignedDate()));

                    //发送邮件
                } else if( notificationType == NotificationType.REFUND ) {
                    // 修改退款状态
                    onAppleOrderRefund(jwsTransactionDecodedPayload.getTransactionId(),  new Date(jwsTransactionDecodedPayload.getRevocationDate()));
                    //发送邮件
                }


                //续订成功
                if (notificationType == NotificationType.DID_RENEW) {
                    Date expireDate =  new Date(jwsTransactionDecodedPayload.getExpiresDate());
                    onAppleSub(jwsTransactionDecodedPayload.getOriginalTransactionId(), jwsTransactionDecodedPayload.getProductId(), jwsTransactionDecodedPayload.getTransactionId(), expireDate);
                }

                //更新订阅状态
                if (notificationType == NotificationType.DID_CHANGE_RENEWAL_STATUS) {
                    if (subtype == Subtype.AUTO_RENEW_ENABLED) {
                        //开启
                        updateUserSubStatus(jwsTransactionDecodedPayload.getOriginalTransactionId(),  jwsTransactionDecodedPayload.getProductId(), false);
                    }

                    if (subtype == Subtype.AUTO_RENEW_DISABLED) {
                        //关闭
                        updateUserSubStatus(jwsTransactionDecodedPayload.getOriginalTransactionId(),  jwsTransactionDecodedPayload.getProductId(), true);
                    }
                }

                //取消订阅
                if (notificationType == NotificationType.EXPIRED) {
                    updateUserSubStatus(jwsTransactionDecodedPayload.getOriginalTransactionId(),  jwsTransactionDecodedPayload.getProductId(), true);
                }
            }

        } catch(Exception e) {
            logger.error("Error parsing notification",e);
            return;
        }

        logger.info("Notification processing done");
    }

    public void onAppleSub(String originalTransactionId, String productId, String transactionId, Date expireDate) {
        AppleSubRecord exists = appleSubRecordRepository.findTopByTransactionId(transactionId);
        if (exists != null) {
            logger.info("AppleSubPayNotification:[Failed]: Order Already Exists, : transactionId{} ", transactionId);
            return;
        }

        final UserAppleSub userSub = userAppleSubRepository.findTopByOriginalTransactionIdAndProductId(originalTransactionId, productId);
        if (userSub == null) {
            logger.error("Apple not exists sub order originalTransactionId:{}, productId:{}", originalTransactionId, productId);
            return;
        }


        updateUserSubExpireTime(originalTransactionId, productId, expireDate);
        createSubRecord(userSub.getUid(), productId, originalTransactionId, transactionId, userSub.getAutoRenew(), expireDate);

        //发放vip  自动续费
        bizcoreCenterService.appleBuyVIP(userSub.getUid(), productId, true);
        logger.info("onAppleSub . uid:{}, productId:{}", userSub.getUid(), userSub.getProductId());
    }


    @Transactional
    public RechargeAppleOrder createAppleOrder(long uid, String orderId, String transactionId) {
        logger.info("Create apple order: {},{},{}", uid, orderId, transactionId);
        RechargeAppleOrder order = new RechargeAppleOrder();
        order.setUid(uid);
        order.setOrderId(orderId);
        order.setTransactionId(transactionId);
        order.setStatus(AppleOrderStatus.PURCHASED.getValue());
        appleOrderRepository.save(order);
        return order;
    }

    @Transactional
    public void onAppleOrderRefundRequest(String transactionId,Date refundRequestTime) {
        logger.info("Apple order {} receives refund request", transactionId );
        RechargeAppleOrder order = getOrCreateAppleOrder(transactionId);
        if( order != null ) {
            if( order.getStatus() < AppleOrderStatus.REFUND_REQUEST.getValue() ) {
                order.setStatus(AppleOrderStatus.REFUND_REQUEST.getValue());
            }
            order.setRefundRequestTime(refundRequestTime);
            appleOrderRepository.save(order);
        }
    }


    @Transactional
    public void onAppleOrderRefund(String transactionId,Date refundTime) {
        logger.info("Apple order {} completes refund", transactionId);
        RechargeAppleOrder order = getOrCreateAppleOrder(transactionId);
        if (order != null) {
            order.setStatus(AppleOrderStatus.REFUND.getValue());
            order.setRefundTime(refundTime);
            appleOrderRepository.save(order);
        }
    }

    @Transactional
    public RechargeAppleOrder getOrCreateAppleOrder(String transactionId) {

        RechargeAppleOrder order = appleOrderRepository.findByTransactionId(transactionId);
        if( order == null ) {
            RechargeRecord record = rechargeService.getByPlatformOrderId(transactionId);

            if( record != null ) {
                long uid = record.getUid();
                String orderId = record.getOrderId();
                order = createAppleOrder(uid, orderId, transactionId);
            } else {
                logger.error("Unable to find the apple order: {}", transactionId);
            }
        }
        return order;
    }

    /**
     * 是否存在连续有效订单
     * @param transactionId
     * @return
     */
    @Transactional
    public Map getUserSub(String transactionId, String originalTransactionId) {

        Map<String, Object> result = new HashMap<>();
        result.put("name", "");
        result.put("nickname", "");


        if (originalTransactionId == null || "".equals(originalTransactionId)) {
            AppleSubRecord record = appleSubRecordRepository.findTopByTransactionId(transactionId);
            if (record == null) {
                result.put("effective", false);
                return result;
            }
            originalTransactionId = record.getOriginalTransactionId();
        }


        UserAppleSub userAppleSub = userAppleSubRepository.findTopByOriginalTransactionId(originalTransactionId);

        //不存在有效续订订阅
        if (userAppleSub == null || userAppleSub.getCancel()) {
            result.put("effective", false);
            return result;
        }

        User subUser = userService.getById(userAppleSub.getUid());

        result.put("name", subUser.getName());
        result.put("nickname", subUser.getNickname());
        result.put("effective", true);
        return result;

    }


    @Transactional
    public void createOrUpdateUserSub(long uid, String productId, String originalTransactionId, String receipt, String secretKey, Date expireDate) {

        Date now = new Date();
        UserAppleSub userAppleSub = userAppleSubRepository.findTopByOriginalTransactionId(originalTransactionId);
        if (userAppleSub == null) {
            userAppleSub = new UserAppleSub();
            userAppleSub.setCreateDate(now);
        }

        userAppleSub.setUid(uid);

        userAppleSub.setProductId(productId);
        userAppleSub.setOriginalTransactionId(originalTransactionId);

        userAppleSub.setAutoRenew(true);
        userAppleSub.setCancel(false);
        userAppleSub.setReceipt(receipt);
        userAppleSub.setSecretKey(secretKey);
        userAppleSub.setExpireTime(expireDate);

        userAppleSub.setUpdateDate(now);

        userAppleSubRepository.saveAndFlush(userAppleSub);
    }

    @Transactional
    public void updateUserSubStatus(String tid, String pid, boolean cancel) {
        UserAppleSub userAppleSub = userAppleSubRepository.findTopByOriginalTransactionIdAndProductId(tid, pid);
        if (userAppleSub == null) {
            logger.error("Not exists originalTransactionId:{} sub status", tid);
        }


        userAppleSub.setCancel(cancel);
        userAppleSub.setUpdateDate(new Date());
        userAppleSubRepository.saveAndFlush(userAppleSub);

        //通知取消状态
        if (cancel) {
            bizcoreCenterService.cancelSubscribe(userAppleSub.getUid(), userAppleSub.getProductId());
        }
    }

    @Transactional
    public void updateUserSubExpireTime(String tid, String pid, Date expireTime) {
        UserAppleSub userAppleSub = userAppleSubRepository.findTopByOriginalTransactionIdAndProductId(tid, pid);
        if (userAppleSub == null) {
            logger.error("Not exists originalTransactionId:{} sub expireTime : {}", tid, expireTime);
        }

        userAppleSub.setExpireTime(expireTime);
        userAppleSub.setUpdateDate(new Date());
        userAppleSubRepository.saveAndFlush(userAppleSub);
    }


    @Transactional
    public AppleSubRecord createSubRecord(long uid, String productId, String originalTransactionId, String transactionId, Boolean autoRenew, Date expireTime) {
        AppleSubRecord appleSubRecord = new AppleSubRecord();
        appleSubRecord.setUid(uid);
        appleSubRecord.setProductId(productId);
        appleSubRecord.setOriginalTransactionId(originalTransactionId);
        appleSubRecord.setAutoRenew(autoRenew);

        appleSubRecord.setTransactionId(transactionId);
        appleSubRecord.setExpireTime(expireTime);
        appleSubRecord.setCreateDate(new Date());

        return appleSubRecordRepository.saveAndFlush(appleSubRecord);
    }


    public void checkUserSubRecord() {
        Date expireTime = new DateTime().plusHours(12).toDate();
        List<UserAppleSub> list = userAppleSubRepository.findByCancelAndExpireTimeBefore(false, expireTime);
        if (list == null || list.isEmpty()) {
            return;
        }

        for (UserAppleSub sub: list) {
            logger.info("Scheduled sub apple uid:{} originalTransactionId:{}", sub.getUid(), sub.getOriginalTransactionId());
            subValid(sub.getReceipt(), sub.getUid(), null, sub.getSecretKey(), null);
        }

    }

    public ApplePrepareOrder prepareOrder(Integer mgsAppId, long uid, String productId, String clientIp, MobilePlatform platform) {

        return null;
    }

    public AppleRequestOrder requestOrder(String orderId, String receipt) {
        return null;
    }

}
