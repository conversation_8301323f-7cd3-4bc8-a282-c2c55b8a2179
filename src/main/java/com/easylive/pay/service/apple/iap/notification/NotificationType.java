package com.easylive.pay.service.apple.iap.notification;

public enum NotificationType {

  /**
   * 退款请求
   */
  CONSUMPTION_REQUEST,

  /**
   * 更改订阅计划，会在下次订阅生效
   */
  DID_CHANGE_RENEWAL_PREF,

  /**
   * 表示续订状态有改变。可检查auto_renew_status_change_date_ms和auto_renew_status字段。
   */
  DID_CHANGE_RENEWAL_STATUS,

  /**
   * 表示由于账单问题，续订失败。可检查is_in_billing_retry_period
   */
  DID_FAIL_TO_RENEW,

  /**
   * 续订成功
   */
  DID_RENEW,

  EXPIRED,

  GRACE_PERIOD_EXPIRED,

  OFFER_REDEEMED,

  /**
   * 订阅涨价
   */
  PRICE_INCREASE,

  /**
   * 退款
   */
  REFUND,

  /**
   * 拒绝退款请求
   */
  REFUND_DECLINED,

  /**
   * 延长了特定订阅的订阅续订日期
   */
  RENEWAL_EXTENDED,

  REVOKE,

  SUBSCRIBED,

  ONE_TIME_CHARGE

}
