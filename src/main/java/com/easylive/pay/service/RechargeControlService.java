package com.easylive.pay.service;

import com.easylive.pay.dao.RechargeControlAmountRepository;
import com.easylive.pay.dao.RechargeControlMapRepository;
import com.easylive.pay.dao.RechargeControlVipLevelRepository;
import com.easylive.pay.entity.RechargeControlAmount;
import com.easylive.pay.entity.RechargeControlMap;
import com.easylive.pay.entity.RechargeControlVipLevel;
import com.easylive.pay.enums.WeixinPayType;
import com.easylive.pay.model.User;
import com.easylive.pay.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2021/7/16
 */
@Slf4j
@Service
public class RechargeControlService {


    @Autowired
    private RechargeControlMapRepository rechargeControlMapRepository;

    @Autowired
    private RechargeControlAmountRepository rechargeControlAmountRepository;

    @Autowired
    private RechargeControlVipLevelRepository rechargeControlVipLevelRepository;


    private static final int WEIXIN_PAY = 0;
    private static final int ALI_PAY = 1;

    @PostConstruct
    public void init() {
    }

    @Transactional
    public void increaseWeixinAmount(int appId, int rechargeId, int amount) {
        try {
            increaseAmount(appId, WEIXIN_PAY, rechargeId, amount);
        } catch (Exception e) {
            log.error("Increase weixin amount error", e);
        }
    }

    @Transactional
    public void increaseAliAmount(int appId, int rechargeId, int amount) {
        try {
            increaseAmount(appId, ALI_PAY, rechargeId, amount);
        } catch (Exception e) {
            log.error("Increase ali amount error", e);
        }
    }

    public int getWeixinRechargeId(User user, int appId, int subType, long amount) {
        int rechargeId = getRechargeId(appId, WEIXIN_PAY, subType, (int) amount);
        log.info("MAP Weixin [{},{},{}] to {}",appId, subType, amount, rechargeId);

        if( subType == WeixinPayType.H5.ordinal() ) {
            int id = decideWeixinRechargeId(user, appId, amount);
            if (id != -1) {
                log.info("REMAP Weixin {} to {}", rechargeId, id);
                rechargeId = id;
            }
        }
        return rechargeId;
    }

    public int decideWeixinRechargeId( User user, int appId, long amount) {
        int rechargeId = decideWithVipLevel( user.getVipLevel(), appId );
        if( rechargeId == -1 ) {
            rechargeId = decideWithAmount( rechargeId, amount, appId);
        }
        return rechargeId;
    }


    private int decideWithAmount(int rechargeId, long amount, int appId) {
        List<RechargeControlAmount> amounts = rechargeControlAmountRepository.findByMgsAppId(appId);
        for( RechargeControlAmount rca : amounts) {
            if( amount >= rca.getAmount() ) {
                rechargeId = rca.getRechargeId();
            }
        }
        return rechargeId;
    }

    private int decideWithVipLevel(long vipLevel, int appId) {
        int rechargeId = -1;
        List<RechargeControlVipLevel> levels  = rechargeControlVipLevelRepository.findByMgsAppId(appId);
        for ( RechargeControlVipLevel level : levels) {
            if( vipLevel >= level.getVipLevel() ) {
                rechargeId = level.getRechargeId();
            }
        }
        return rechargeId;
    }



    public int getAliRechargeId(int appId, int subType, long amount) {
        int rechargeId = getRechargeId(appId, ALI_PAY, subType, (int) amount);
        log.info("MAP Ali [{},{},{}] to {}",appId, subType, amount, rechargeId);
        return rechargeId;
    }

    public void increaseAmount(int appId, int type, int rechargeId, int amount) {
        List<RechargeControlMap> mlist = rechargeControlMapRepository.findByMgsAppIdAndTypeAndRechargeId(appId, type, rechargeId);
        if( mlist == null || mlist.isEmpty() )
            return;
        RechargeControlMap m = mlist.get(0);
        if (m != null) {
            resetRechargeDay(m);
            m.setAmountDay(m.getAmountDay() + amount);
            m.setTradeCountDay(m.getTradeCountDay() + 1);
            rechargeControlMapRepository.save(m);
        }
    }

    public int getRechargeId(int appId, int type, int subType, int amount) {
        AtomicInteger ret = new AtomicInteger(-1);

        List<RechargeControlMap> l = rechargeControlMapRepository.findByMgsAppIdAndTypeOrderById(appId, type);
        if (l != null) {
            for (RechargeControlMap rc : l) {
                if (rc.getEnabled() == null || !rc.getEnabled())
                    continue;

                if (resetRechargeDay(rc))
                    rechargeControlMapRepository.save(rc);

                if (isMatched(rc, subType, amount)) {
                    ret.set(rc.getRechargeId());
                    break;
                }
            }
        }
        return ret.get();
    }

    private boolean resetRechargeDay(RechargeControlMap m) {
        if (!DateUtils.isCurrentDay(m.getRechargeDay())) {
            m.setAmountDay(0);
            m.setTradeCountDay(0);
            m.setRechargeDay(DateUtils.getTodayDate());
            return true;
        }
        return false;
    }

    private boolean isMatched(RechargeControlMap rc, int subType, int amount) {

        //检查累积额度
        if (rc.getAccumAmountLimit() > 0 && rc.getAccumAmountLimit() <= rc.getAmountDay()) {
            return false;
        }

        //检查 sub type
        if (!isValidSubType(rc.getValidSubType(), subType))
            return false;


        //检查额度
        String amountStr = rc.getValidAmount();
        try {
            String[] ss = amountStr.split(",");
            if (ss.length == 2) {
                int min = Integer.parseInt(ss[0]);
                int max = Integer.parseInt(ss[1]);
                if (amount < min || amount > max)
                    return false;
            }
        } catch (Exception ignored) {
        }

        //检查时间
        String time = rc.getValidTime();
        if( !isTimeMatched(time))
            return false;

        return true;
    }

    private boolean isTimeMatched(String s) {
        List<Pair<Date, Date>> dates = parseValidTime(s);
        if (dates.isEmpty())
            return false;
        Date now = new Date();
        for (Pair<Date, Date> date : dates) {
            if (now.after(date.getLeft()) && now.before(date.getRight())) {
                return true;
            }
        }
        return false;
    }

    private boolean isValidSubType(String s, int subType) {
        if (StringUtils.isEmpty(s))
            return true;

        try {
            for (String x : s.split(",")) {
                if (subType == Integer.parseInt(x)) {
                    return true;
                }
            }
        } catch (Exception ignored) {
        }
        return false;
    }

    private static final DateTimeFormatter dateFormat = DateTimeFormat.forPattern("HH:mm");

    private Date parseDate(String s) {
        Date d = dateFormat.parseDateTime(s).toDate();
        DateTime now = DateTime.now();
        return new DateTime(d).withDate(now.getYear(), now.getMonthOfYear(), now.getDayOfMonth()).toDate();
    }

    // 20:00-21:00,15:00-17:00
    private List<Pair<Date, Date>> parseValidTime(String time) {
        List<Pair<Date, Date>> ret = new ArrayList<>();
        try {
            String[] cc = time.split(",");
            for (String c : cc) {
                String[] dd = c.split("-");
                if (dd.length == 2) {
                    try {
                        Date left = parseDate(dd[0]);
                        Date right = parseDate(dd[1]);
                        if (right.after(left)) {
                            ret.add(Pair.of(left, right));
                        }
                    } catch (Exception ignored) {
                    }
                }
            }
        } catch (Exception ignored) {
        }

        return ret;
    }
}
