package com.easylive.pay.service;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.dao.PackageToolsGainRepository;
import com.easylive.pay.dao.PackageToolsRepository;
import com.easylive.pay.dao.UserPackageToolsRepository;
import com.easylive.pay.entity.PackageTools;
import com.easylive.pay.entity.PackageToolsGain;
import com.easylive.pay.entity.UserPackageTools;
import com.easylive.pay.enums.PackageToolsFromType;
import com.easylive.pay.enums.PackageToolsType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PackageToolService {

    private static Logger logger = LoggerFactory.getLogger(PackageToolService.class);

    @Autowired
    private PackageToolsRepository packageToolsRepository;

    @Autowired
    private UserPackageToolsRepository userPackageToolsRepository;

    @Autowired
    private PackageToolsGainRepository packageToolsGainRepository;

    @Transactional(rollbackFor = Exception.class)
    public void addPeaToUserPackage(long uid, int number) {
        /**
         * 1. 查询豌豆道具ID
         * 2. 给用户背包添加豌豆道具
         * 3. 增加道具获得记录
         */
        //1. 查询豌豆道具ID
        PackageTools pts = getPackageToolsOfPea();
        logger.debug("[addPeaToUserPackage] 1.. query tools, pts={}", pts);
        if (pts == null) {
            throw new ResponseException(ResponseError.E_PACKAGE_TOOLS_NOT_EXISTS, "豌豆道具不存在");
        }
        //2. 给用户背包添加豌豆道具
        UserPackageTools upt = userPackageToolsRepository.findTopByUidAndToolId(uid, pts.getId());
        logger.debug("[addPeaToUserPackage] 2.. find package tools, upt={}", pts);
        if (upt == null) {
            upt = new UserPackageTools(uid, pts.getId(), number);
            userPackageToolsRepository.save(upt);
        } else {
            userPackageToolsRepository.incToolsById(upt.getId(), number);
        }
        //3. 增加道具获得记录
        PackageToolsGain ptg = new PackageToolsGain(uid);
        ptg.setToolId(pts.getId());
        ptg.setToolName(pts.getName());
        ptg.setFromType(PackageToolsFromType.SEND_GIFT.getValue());
        ptg.setPrice(pts.getPrice());
        ptg.setNumber(number);
        ptg.setCostType(pts.getCostType());
        logger.debug("[addPeaToUserPackage] 3.. package gain, ptg={}", ptg);
        packageToolsGainRepository.save(ptg);
    }

    public PackageTools getPackageToolsOfPea() {
        return packageToolsRepository.findTopByType(PackageToolsType.PEA.getValue());
    }

}
