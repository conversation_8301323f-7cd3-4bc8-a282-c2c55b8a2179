package com.easylive.pay.service;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.dao.AdminTransferRecordRepository;
import com.easylive.pay.dao.RechargeWrongTransferRepository;
import com.easylive.pay.dao.TransferDao;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.AssetSystemID;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.model.AssetUpdateResult;
import com.easylive.pay.model.User;
import com.easylive.pay.output.TransferQueryResult;
import com.easylive.pay.output.TransferResult;
import com.easylive.pay.output.common.PageableList;
import com.easylive.pay.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.*;

@Service
public class TransferService {
    private static Logger logger = LoggerFactory.getLogger(TransferService.class);
    private static DateTimeFormatter dateTimeFormat = DateTimeFormat.forPattern("yyyyMMddHHmmss");
    @Autowired
    private AssetService assetService;
    @Autowired
    private UserService userService;
    @Autowired
    private TransferDao transferDao;

    @Autowired
    private RechargeService rechargeService;
    @Autowired
    private RechargeWrongTransferRepository rechargeWrongTransferRepository;
    @Autowired
    private AdminTransferRecordRepository adminTransferRecordRepository;


    @Transactional
    public TransferResult transfer(long fromUid, long toUid, int ecoin) {
        return this.transfer(fromUid, toUid, ecoin, null);
    }

    @Transactional
    public TransferResult transfer(long fromUid, long toUid, int ecoin, String description) {
        logger.info("{} transfer {} ecoin to {}: {}", fromUid, ecoin, toUid, description);

        if (ecoin <= 0) {
            logger.error("Ecoin must be large than zero");
            throw new ResponseException(ResponseError.E_PARAM, "Ecoin must be positive");
        }

        if(StringUtils.length(description) > 64)
            description = description.substring(0, 64);

        String orderId = generateOrderId();

        AssetUpdateResult result = assetService.binaryOperate(AssetSystemID.ECOIN_TRANSFER, fromUid, ecoin, toUid, ecoin);
        transferDao.add(orderId, result.getTid(), fromUid, toUid, ecoin, description);

        TransferResult transferResult = new TransferResult();
        transferResult.setTid(result.getTid());
        transferResult.setFromUid(fromUid);
        transferResult.setToUid(toUid);
        transferResult.setEcoin(ecoin);
        transferResult.setOrderId(orderId);
        transferResult.setFromEcoinBalance(result.getUserCoin(0).getEcoin());
        return transferResult;
    }


    @Transactional
    public TransferResult adminTransfer(String from, String to, String reason, String operateUser, String orderId) {
        TransferResult result = this.transferForWrongRecharge(orderId, from, to);
        //添加后台记录
        AdminTransferRecord record = new AdminTransferRecord();
        record.setOrderId(result.getOrderId());
        record.setTid(result.getTid());
        record.setFromUid(result.getFromUid());
        record.setToUid(result.getToUid());
        record.setEcoin((int) result.getEcoin());
        record.setReason(reason);
        record.setOperateUser(operateUser);
        record.setErrOrderId(orderId);
        record.setCreateTime(new Date());
        adminTransferRecordRepository.save(record);
        return result;
    }

    public PageableList<AdminTransferRecord> listAdminTransferRecord(String fromName, String toName, Date startTime, Date endTime, int start, int count) {
        Long fromUid = getUidFromName(Strings.isBlank(fromName) ? null : fromName);
        Long toUid = getUidFromName(Strings.isBlank(toName) ? null : toName);
        List<AdminTransferRecord> list = adminTransferRecordRepository.listRecord(fromUid, toUid, startTime, endTime, start, count);
        BigInteger countObj = adminTransferRecordRepository.countRecord(fromUid, toUid, startTime, endTime);
        return new PageableList<>(list, start / count, count, countObj.intValue());
    }
    /**
     * 充值错误订单的易币转回,规则如下：
     * 1. 充值订单状态必须为成功
     * 2. 充值订单号不能超过一周
     * 3. 转出的用户名必须跟充值订单的用户名一致
     * 4. 一个订单只能处理一次
     * @param orderId 错误充值的充值订单号
     * @param toName 转回的账户
     * @return 转账结果
     */
    @Transactional
    public TransferResult transferForWrongRecharge(String orderId, String fromName, String toName) {
        RechargeRecord rechargeRecord = rechargeService.ensureRecordByOrderId(orderId);

        //已经处理过的订单号不重复处理
        if ( isWrongTransferRecordExist(orderId) ) {
            throw new ResponseException(ResponseError.E_PARAM, "该订单已处理过");
        }

        //  充值订单状态要正确
        if( rechargeRecord.getStatus() != OrderStatus.ORS_SUCCESS.getValue()) {
            throw new ResponseException(ResponseError.E_PARAM, "充值订单状态不正确");
        }

        // 充值订单号不能超过一周
        if( rechargeRecord.getCompleteTime().before(new Date(System.currentTimeMillis() - 7 * 24 * 3600 * 1000)) ) {
            throw new ResponseException(ResponseError.E_PARAM, "充值订单号不能超过一周");
        }
        long fromUid = rechargeRecord.getUid();
        User fromUser = userService.ensureExists(fromUid);

        if (!fromUser.getName().equals(fromName)) {
            throw new ResponseException(ResponseError.E_PARAM, "转出用户名不匹配");
        }

        UserCoin coin = assetService.getByUid(fromUid);
        if (coin.getEcoin() < rechargeRecord.getEcoin()) {
            throw new ResponseException(ResponseError.E_PARAM, "易币不足");
        }

        User toUser = userService.ensureExists(toName);
        int ecoin = (int)rechargeRecord.getEcoin();
        String description = String.format("充值错误订单的易币转回:%s",  orderId);
        addWrongTransferRecord(orderId, fromUid, toUser.getId(), ecoin);
        return transfer(fromUser.getId(), toUser.getId(), ecoin ,description);
    }

    private String generateOrderId() {
        return DateUtils.format(new Date(), dateTimeFormat) + new DecimalFormat("000000").format(new Random().nextInt(10000000));
    }

    private Long getUidFromName(String name) {
        if (name != null) {
            User user = userService.getByName(name);
            if (user != null) {
                return user.getId();
            }
        }
        return null;
    }

    @Transactional
    public PageableList<TransferQueryResult> query(String fromName, String toName, int page, int size) {

        Long fromUid = getUidFromName(fromName);
        Long toUid = getUidFromName(toName);

        int start = (page - 1) * size;
        if (start < 0 || size < 0)
            throw new ResponseException(ResponseError.E_PARAM, "Invalid paging parameter");

        List<TransferRecord> records = transferDao.query(fromUid, toUid, start, size);
        long total = transferDao.count(fromUid, toUid);


        List<TransferQueryResult> results = new ArrayList<>();

        Set<Long> uids = new HashSet<>();
        records.forEach(record -> {
            uids.add(record.getFromUid());
            uids.add(record.getToUid());
        });

        Map<Long, User> users = userService.listByUids(new ArrayList<>(uids));

        records.forEach(record -> {
            User fromUser = users.get(record.getFromUid());
            User toUser = users.get(record.getToUid());
            TransferQueryResult result = new TransferQueryResult(record, fromUser, toUser);
            results.add(result);
        });

        return new PageableList<>(results, page, size, total);
    }

    @Transactional
    public void addWrongTransferRecord(String orderId, long fromUid, long id, int ecoin) {
        RechargeWrongTransferRecord record = new RechargeWrongTransferRecord();
        record.setOrderId(orderId);
        record.setFromUid(fromUid);
        record.setToUid(id);
        record.setEcoin(ecoin);
        rechargeWrongTransferRepository.save(record);
    }

    public boolean isWrongTransferRecordExist(String orderId) {
        return rechargeWrongTransferRepository.findByOrderId(orderId) != null;
    }

}
