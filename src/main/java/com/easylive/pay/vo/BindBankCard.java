package com.easylive.pay.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 绑定银行卡
 *
 * <AUTHOR>
 * @date 2019/8/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BindBankCard {

    @NotNull
    private Long uid;

    private String accountName;

    @NotNull
    private String accountNo;

    @NotNull
    private String openBankName;

    @NotNull
    private String openBankNo;

    @NotNull
    private Long smsId;

    @NotNull
    private String smsCode;

    private Integer cardId;

}
