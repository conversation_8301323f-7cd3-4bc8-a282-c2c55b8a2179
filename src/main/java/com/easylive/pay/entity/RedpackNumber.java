package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by c on 2016/1/21.
 */
@Entity
@Table(name = "t_redpack_number")
public class RedpackNumber {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "redpack_code")
    private String redpackCode;

    @Column(name = "redpack_value")
    private long redpackValue;

    @Column(name = "value")
    private long value;

    private long number;

    private int status;

    private int best;

    @Column(name = "redpack_count")
    private long redpackCount;

    private Long uid;
    private String tid;

    private Date time;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getRedpackCode() {
        return redpackCode;
    }

    public void setRedpackCode(String redpackCode) {
        this.redpackCode = redpackCode;
    }

    public long getRedpackValue() {
        return redpackValue;
    }

    public void setRedpackValue(long redpackValue) {
        this.redpackValue = redpackValue;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public long getNumber() {
        return number;
    }

    public void setNumber(long number) {
        this.number = number;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getBest() {
        return best;
    }

    public void setBest(int best) {
        this.best = best;
    }

    public long getRedpackCount() {
        return redpackCount;
    }

    public void setRedpackCount(long redpackCount) {
        this.redpackCount = redpackCount;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }
}
