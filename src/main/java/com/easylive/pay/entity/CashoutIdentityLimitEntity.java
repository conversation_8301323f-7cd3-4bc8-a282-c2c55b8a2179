package com.easylive.pay.entity;

import com.easylive.pay.utils.DateUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/*
 * +---------------+-------------+------+-----+-------------------+-----------------------------+
 * | Field         | Type        | Null | Key | Default           | Extra                       |
 * +---------------+-------------+------+-----+-------------------+-----------------------------+
 * | id            | bigint(20)  | NO   | PRI | NULL              | auto_increment              |
 * | id_card       | varchar(32) | NO   | UNI | NULL              |                             |
 * | month_cashout | int(10)     | NO   |     | 0                 |                             |
 * | month_limit   | int(10)     | NO   |     | 620000            |                             |
 * | cashout_date  | date        | NO   |     | 1970-01-01        |                             |
 * | create_at     | timestamp   | NO   |     | CURRENT_TIMESTAMP |                             |
 * | updated_at    | timestamp   | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
 * +---------------+-------------+------+-----+-------------------+-----------------------------+
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_cashout_identity_limit")
@DynamicInsert
@DynamicUpdate
public class CashoutIdentityLimitEntity extends BaseEntityLong {

    private String idCard;

    private long dayCashout;

    private long dayLimit;

    private long monthCashout;

    private long monthLimit;

    private Date cashoutDate;

    public CashoutIdentityLimitEntity() {
        
    }

    public CashoutIdentityLimitEntity(String idCard) {
        this.idCard = idCard;
        this.dayCashout = 0;
        this.dayLimit = 20000;
        this.monthCashout = 0;
        this.monthLimit = 620000;
        this.cashoutDate = DateUtils.getFirstDate();
    }

}
