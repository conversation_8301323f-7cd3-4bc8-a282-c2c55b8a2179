package com.easylive.pay.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_cashout_record_queue")
public class CashoutRecordQueue {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;
    private long uid;
    private int status;
    private int app_status;
    private long rmb;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date commit_time;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date complete_time;
    private String orderid;
    private int platform;
    // 支付平台
    private int pfplatform;
    // 换的饭团数量
    private long riceroll;
    private int card_id;
    private String pforderid;

    @ApiModelProperty("真实姓名")
    @Column(columnDefinition = " varchar(60) NOT NULL DEFAULT '' COMMENT '真实姓名' ")
    private String account_name;

    @ApiModelProperty("银行卡号")
    @Column(columnDefinition = " varchar(20) NOT NULL DEFAULT '' COMMENT '银行卡号' ")
    private String account_no;

    @ApiModelProperty("开户行行名")
    @Column(columnDefinition = " varchar(6) NOT NULL DEFAULT '' COMMENT '开户行行名' ")
    private String open_bank_name;

    @ApiModelProperty("开户行行号")
    @Column(columnDefinition = " varchar(20) NOT NULL DEFAULT '' COMMENT '开户行行号' ")
    private String open_bank_no;

    @ApiModelProperty("支付宝账号")
    @Column(columnDefinition = " varchar(128) DEFAULT NULL COMMENT '支付宝账号' ")
    private String zfb_account;

    @ApiModelProperty("手机号")
    @Column(columnDefinition = " varchar(20) DEFAULT NULL COMMENT '手机号' ")
    private String mobile;

    @ApiModelProperty("提现类型：1 银行卡，2 支付宝")
    @Column(columnDefinition = " tinyint(3) DEFAULT NULL COMMENT '提现类型：1 银行卡，2 支付宝' ")
    private Integer withdraw_type;

    private long record_id;
    private String id_no;
    private String res_code = "";
    private String res_msg = "";
    private int bank_type;
    private int app_id;
    private int is_deleted = 0;

}
