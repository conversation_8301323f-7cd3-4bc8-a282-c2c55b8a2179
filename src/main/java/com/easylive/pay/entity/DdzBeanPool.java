package com.easylive.pay.entity;

import javax.persistence.*;
import java.math.BigInteger;

@Entity
@Table(name = "t_ddz_bean_pool")
public class DdzBeanPool {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "total_bean")
    private BigInteger totalBean;

    public DdzBeanPool() {
    }


    public DdzBeanPool(BigInteger totalBean) {
        this.totalBean = totalBean;
    }

    public BigInteger getTotalBean() {
        return totalBean;
    }

    public void setTotalBean(BigInteger totalBean) {
        this.totalBean = totalBean;
    }

}
