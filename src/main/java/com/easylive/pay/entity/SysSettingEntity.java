package com.easylive.pay.entity;

/*
 * +-------------+---------------+------+-----+-------------------+-----------------------------+
 * | Field       | Type          | Null | Key | Default           | Extra                       |
 * +-------------+---------------+------+-----+-------------------+-----------------------------+
 * | id          | int(10)       | NO   | PRI | NULL              | auto_increment              |
 * | meta_key    | varchar(32)   | NO   | MUL |                   |                             |
 * | meta_value  | varchar(2000) | NO   | MUL |                   |                             |
 * | description | varchar(255)  | NO   |     |                   |                             |
 * | create_at   | timestamp     | NO   |     | CURRENT_TIMESTAMP |                             |
 * | update_at   | timestamp     | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
 * | is_deleted  | tinyint(1)    | NO   |     | 0                 |                             |
 * +-------------+---------------+------+-----+-------------------+-----------------------------+
 */

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 系统设置
 *
 * <AUTHOR>
 * @date 2020/7/21
 */
@Data
@NoArgsConstructor
@Entity
@Table(name = "t_sys_setting")
@DynamicUpdate
@DynamicInsert
public class SysSettingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 键
     */
    @Column(name = "meta_key")
    private String metaKey;

    /**
     * 值
     */
    @Column(name = "meta_value")
    private String metaValue;

    /**
     * 描述
     */
    @Column(name = "description")
    private String description;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = "create_at", columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP")
    protected Date createAt;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = "update_at", columnDefinition = "datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    protected Date updateAt;

    @JsonIgnore
    @Column(name = "is_deleted", columnDefinition = "tinyint(1) NOT NULL DEFAULT 0")
    protected boolean isDeleted;

    @PrePersist
    private void beforeSave() {
        Date currentDate = new Date();
        this.createAt = currentDate;
        this.updateAt = currentDate;
    }

    @PreUpdate
    public void beforeUpdate() {
        this.updateAt = new Date();
    }

}
