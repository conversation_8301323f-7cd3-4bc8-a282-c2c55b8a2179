package com.easylive.pay.entity;

import javax.persistence.*;
import java.math.BigInteger;
import java.sql.Timestamp;

@Entity
@Table(name = "t_ddz_coin_record")
public class DdzCoinRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;


    private String name;

    private long uid;

    private String tid;

    private BigInteger bean;

    @Column(name = "coin_num")
    private long coinNum;

    private Integer type;
    @Column(name = "current_coin")
    private BigInteger currentCoin;

    @Column(name = "create_time")
    private Timestamp createTime;


    public DdzCoinRecord() {
    }

    public void set(String name, long uid, long coinNum, Integer type, BigInteger currentCoin, BigInteger bean) {

        this.name = name;
        this.uid = uid;
        this.coinNum = coinNum;
        this.type = type;
        this.currentCoin = currentCoin;
        this.bean = bean;

    }


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }


    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public BigInteger getBean() {
        return bean;
    }

    public void setBean(BigInteger bean) {
        this.bean = bean;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getCoinNum() {
        return coinNum;
    }

    public void setCoinNum(long coinNum) {
        this.coinNum = coinNum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigInteger getCurrentCoin() {
        return currentCoin;
    }

    public void setCurrentCoin(BigInteger currentCoin) {
        this.currentCoin = currentCoin;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "DdzCoinRecord{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", uid=" + uid +
                ", coinNum=" + coinNum +
                ", type=" + type +
                ", currentCoin=" + currentCoin +
                ", createTime=" + createTime +
                '}';
    }
}
