package com.easylive.pay.entity;

import lombok.Getter;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_control_vip_level")
public class RechargeControlVipLevel {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private int mgsAppId;

    @Getter
    @Column(name = "recharge_id")
    private int rechargeId;

    @Getter
    @Column(name = "vip_level")
    private int vipLevel;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }


    public int getMgsAppId() {
        return mgsAppId;
    }

    public void setMgsAppId(int mgsAppId) {
        this.mgsAppId = mgsAppId;
    }

    public void setRechargeId(int rechargeId) {
        this.rechargeId = rechargeId;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getRechargeId() {
        return rechargeId;
    }

    public int getVipLevel() {
        return vipLevel;
    }
}

