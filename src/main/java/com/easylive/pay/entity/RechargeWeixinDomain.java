package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_weixin_domain")
@Data
public class RechargeWeixinDomain {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "domain")
    private String domain;

    @Column(name = "prime")
    private boolean prime;

    @Column(name = "comment")
    private String comment;
}
