package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_control_map")
public class RechargeControlMap {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private int mgsAppId;

    @Column(name = "type")
    private int type;

    @Column(name = "recharge_id")
    private int rechargeId;

    @Column(name = "valid_time")
    private String validTime;

    @Column(name = "valid_amount")
    private String validAmount;

    @Column(name = "valid_subtype")
    private String validSubType;

    @Column(name = "accum_amount_limit")
    private int accumAmountLimit;

    @Column(name = "amount_day")
    private int amountDay;

    @Column(name = "trade_count_day")
    private int tradeCountDay;

    @Column(name = "recharge_day")
    private Date rechargeDay;

    @Column(name = "enabled")
    private Boolean enabled;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }


    public int getMgsAppId() {
        return mgsAppId;
    }

    public void setMgsAppId(int mgsAppId) {
        this.mgsAppId = mgsAppId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getRechargeId() {
        return rechargeId;
    }

    public void setRechargeId(int rechargeId) {
        this.rechargeId = rechargeId;
    }

    public String getValidTime() {
        return validTime;
    }

    public void setValidTime(String validTime) {
        this.validTime = validTime;
    }

    public String getValidAmount() {
        return validAmount;
    }

    public void setValidAmount(String validAmount) {
        this.validAmount = validAmount;
    }

    public int getAccumAmountLimit() {
        return accumAmountLimit;
    }

    public void setAccumAmountLimit(int accumAmountLimit) {
        this.accumAmountLimit = accumAmountLimit;
    }

    public int getAmountDay() {
        return amountDay;
    }

    public void setAmountDay(int amountDay) {
        this.amountDay = amountDay;
    }

    public int getTradeCountDay() {
        return tradeCountDay;
    }

    public void setTradeCountDay(int tradeCountDay) {
        this.tradeCountDay = tradeCountDay;
    }

    public Date getRechargeDay() {
        return rechargeDay;
    }

    public void setRechargeDay(Date rechargeDay) {
        this.rechargeDay = rechargeDay;
    }

    public String getValidSubType() {
        return validSubType;
    }

    public void setValidSubType(String validSubType) {
        this.validSubType = validSubType;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}

