package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@Table(name = "t_cashout_yzh_record")
public class YunzhanghuCashoutRecord implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "yzh_order_id")
    private String yzhOrderId;

    @Column(name = "bank_order_id")
    private String bankOrderId;

    @Column(name = "uid")
    private Long uid;

    @Column(name = "app_id")
    private Long appId;

    @Column(name = "amount")
    private Long amount;

    @Column(name = "real_name")
    private String realName;

    @Column(name = "phone")
    private String phone;

    @Column(name = "card_no")
    private String cardNo;

    @Column(name = "status")
    private int status;

    @Column(name = "status_detail")
    private int statusDetail;


    @Column(name = "status_msg")
    private String statusMsg;

    @Column(name = "status_msg_detail")
    private String statusMsgDetail;

    @Column(name = "complete_time")
    private Date completeTime;

}