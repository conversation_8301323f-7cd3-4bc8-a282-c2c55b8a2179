package com.easylive.pay.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户银行卡表
 *
 * <AUTHOR>
 * @date 2019/8/16
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户银行卡")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
@Table(name = "t_user_bank_card")
public class UserBankCard extends BaseSoftEntity {

    @ApiModelProperty("UID")
    @Column(columnDefinition = " int(20) NOT NULL DEFAULT '' COMMENT '用户UID' ")
    private Long uid;

    @ApiModelProperty("真实姓名")
    @Column(columnDefinition = " varchar(60) NOT NULL DEFAULT '' COMMENT '真实姓名' ")
    private String accountName;

    @ApiModelProperty("银行卡号")
    @Column(columnDefinition = " varchar(20) NOT NULL DEFAULT '' COMMENT '银行卡号' ")
    private String accountNo;

    @ApiModelProperty("开户行行名")
    @Column(columnDefinition = " varchar(6) NOT NULL DEFAULT '' COMMENT '开户行行名' ")
    private String openBankName;

    @ApiModelProperty("开户行行号")
    @Column(columnDefinition = " varchar(20) NOT NULL DEFAULT '' COMMENT '开户行行号' ")
    private String openBankNo;

    @ApiModelProperty("支付宝账号")
    @Column(columnDefinition = " varchar(128) DEFAULT NULL DEFAULT '' COMMENT '支付宝账号' ")
    private String zfbAccount;

    @ApiModelProperty("平台ID")
    @Column(columnDefinition = " varchar(256) DEFAULT NULL DEFAULT '' COMMENT '平台ID' ")
    private String platformId;


    @Override
    public String toString() {
        return "UserBankCard{" +
                "uid=" + uid +
                ", accountName='" + accountName + '\'' +
                ", accountNo='" + accountNo + '\'' +
                ", openBankName='" + openBankName + '\'' +
                ", openBankNo='" + openBankNo + '\'' +
                ", zfbAccount='" + zfbAccount + '\'' +
                ", platformId='" + platformId + '\'' +
                '}';
    }
}
