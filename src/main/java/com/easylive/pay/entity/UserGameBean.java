package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "t_user_game_bean")
public class UserGameBean {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;

    @Column(name = "bean_remain")
    private long beanRemain;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "platform_id")
    private long platformId;

    public void setId(long id) {
        this.id = id;
    }

    public long getPlatformId() {
        return platformId;
    }

    public void setPlatformId(long platformId) {
        this.platformId = platformId;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getBeanRemain() {
        return beanRemain;
    }

    public void setBeanRemain(long beanRemain) {
        this.beanRemain = beanRemain;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
