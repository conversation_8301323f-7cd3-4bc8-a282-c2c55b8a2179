package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/10/8.
 */
@Entity
@Table(name = "t_asset_biz")
public class AssetBiz {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private String ak;

    private String sk;

    private int active;

    private int type;

    private String description;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "bid")
    @OrderBy("idx")
    private List<AssetAuth> auths;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getAk() {
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }

    public int getActive() {
        return active;
    }

    public void setActive(int active) {
        this.active = active;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<AssetAuth> getAuths() {
        return auths;
    }

    public void setAuths(List<AssetAuth> auths) {
        this.auths = auths;
    }
}
