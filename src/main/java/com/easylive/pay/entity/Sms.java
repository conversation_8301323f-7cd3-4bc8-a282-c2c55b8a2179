package com.easylive.pay.entity;

/**
 * Create Table: CREATE TABLE `t_sms` (
 * `id` bigint(20) NOT NULL AUTO_INCREMENT,
 * `phone` varchar(24) COLLATE utf8_bin NOT NULL,
 * `code` varchar(16) COLLATE utf8_bin NOT NULL,
 * `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
 * `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0-created,1,sent,2,closed,3,send error, 4, timeout',
 * `createTime` datetime DEFAULT NULL,
 * `verify_time` datetime DEFAULT NULL,
 * `close_time` datetime DEFAULT NULL,
 * `provider` varchar(16) COLLATE utf8_bin DEFAULT NULL,
 * `sid` varchar(64) COLLATE utf8_bin DEFAULT NULL,
 * `result` varchar(1024) COLLATE utf8_bin DEFAULT NULL,
 * `type` tinyint(4) NOT NULL DEFAULT '0',
 * `app_id` bigint(20) NOT NULL DEFAULT '0',
 * PRIMARY KEY (`id`),
 * KEY `phone` (`phone`)
 * ) ENGINE=InnoDB AUTO_INCREMENT=4445208 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC
 */

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 短信模型
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
@ApiModel("短信")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_sms")
public class Sms {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;

    private String phone;

    private String code;

    private String content;

    private int status;

    private Date createTime;

    private Date verifyTime;

    private Date closeTime;

    private String provider;

    private String sid;

    private String result;

    private int type;

    private int appId;

}
