package com.easylive.pay.entity;

import com.easylive.pay.enums.CashoutLimitForbid;
import com.easylive.pay.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@Entity
@Table(name = "t_cashout_limit")
public class CashoutLimitEntity extends BaseEntityLong {

    private long uid;

    private long dayCashout;

    private long dayCashoutLimit;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date cashoutDate;

    private CashoutLimitForbid isForbid;

    public CashoutLimitEntity(long uid) {
        this.uid = uid;
        this.dayCashout = 0;
        this.dayCashoutLimit = 20000;
        this.cashoutDate = DateUtils.getFirstDate();
        this.isForbid = CashoutLimitForbid.NO;
    }

}
