package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "t_luck_pool_explosion")
public class LuckPoolExplosion {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "snap_pool")
    private long snapPool;

    @Column(name = "explosion_value")
    private long explosionValue;

    @Column(name = "explosion_time")
    private Date explosionTime;

    @Column(name = "stop_time")
    private Date stopTime;

    @Column(name = "create_time")
    private Date createTime;

    public void set(long snapPool, long explosionValue) {
        this.snapPool = snapPool;
        this.explosionValue = explosionValue;
        this.createTime = new Date();
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getSnapPool() {
        return snapPool;
    }

    public void setSnapPool(long snapPool) {
        this.snapPool = snapPool;
    }

    public long getExplosionValue() {
        return explosionValue;
    }

    public void setExplosionValue(long explosionValue) {
        this.explosionValue = explosionValue;
    }

    public Date getExplosionTime() {
        return explosionTime;
    }

    public void setExplosionTime(Date explosionTime) {
        this.explosionTime = explosionTime;
    }

    public Date getStopTime() {
        return stopTime;
    }

    public void setStopTime(Date stopTime) {
        this.stopTime = stopTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
