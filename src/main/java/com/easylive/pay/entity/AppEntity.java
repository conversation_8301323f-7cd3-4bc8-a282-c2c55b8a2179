package com.easylive.pay.entity;

import javax.persistence.*;

@Entity
@Table(name = "t_app")
public class AppEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "source_id")
    private int sourceId;

    private String name;

    @Column(name = "bundle_id")
    private String bundleId;

    @Column(name = "appstore_id")
    private String appStoreId;

    @Column(name = "package_name")
    private String packageName;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getSourceId() {
        return sourceId;
    }

    public void setSourceId(int sourceId) {
        this.sourceId = sourceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBundleId() {
        return bundleId;
    }

    public void setBundleId(String bundleId) {
        this.bundleId = bundleId;
    }

    public String getAppStoreId() {
        return appStoreId;
    }

    public void setAppStoreId(String appStoreId) {
        this.appStoreId = appStoreId;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }
}

