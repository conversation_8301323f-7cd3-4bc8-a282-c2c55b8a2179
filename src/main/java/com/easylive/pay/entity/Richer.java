package com.easylive.pay.entity;


import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "t_richlist")
public class Richer {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;               //user id

    private Long amount;            //amount

    private Date commit_dt;         //commit date

    private int status;            //是否有效, 1有效， 0 无效，其他不明

    private String description;     //账号用途

    public Richer() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Date getCommit_dt() {
        return commit_dt;
    }

    public void setCommit_dt(Date commit_dt) {
        this.commit_dt = commit_dt;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
