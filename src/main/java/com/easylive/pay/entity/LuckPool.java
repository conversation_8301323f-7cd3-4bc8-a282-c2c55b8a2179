package com.easylive.pay.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "t_luck_prize_pool")
public class LuckPool {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    private long ecoin;

    @Column(name = "total_ecoin")
    private Long totalEcoin;

    @Column(name = "next_explosion_pool")
    private Long nextExplosionPool;

    @Column(name = "current_explosion_pool")
    private Long currentExplosionPool;

    @Column(name = "min_prize_num")
    private Long minPrizeNum;

    @Column(name = "med_prize_num")
    private Long medPrizeNum;

    @Column(name = "max_prize_num")
    private Long maxPrizeNum;

    @Column(name = "explosion_status")
    private Long explosionStatus;

    public void set(LuckPool luckPool) {
        this.ecoin = luckPool.ecoin;
        this.totalEcoin = luckPool.totalEcoin;
        this.nextExplosionPool = luckPool.nextExplosionPool;
        this.currentExplosionPool = luckPool.currentExplosionPool;
        this.explosionStatus = luckPool.explosionStatus;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public Long getTotalEcoin() {
        return totalEcoin;
    }

    public void setTotalEcoin(Long totalEcoin) {
        this.totalEcoin = totalEcoin;
    }

    public Long getNextExplosionPool() {
        return nextExplosionPool;
    }

    public void setNextExplosionPool(Long nextExplosionPool) {
        this.nextExplosionPool = nextExplosionPool;
    }

    public Long getCurrentExplosionPool() {
        return currentExplosionPool;
    }

    public void setCurrentExplosionPool(Long currentExplosionPool) {
        this.currentExplosionPool = currentExplosionPool;
    }

    public Long getMinPrizeNum() {
        return minPrizeNum;
    }

    public void setMinPrizeNum(Long minPrizeNum) {
        this.minPrizeNum = minPrizeNum;
    }

    public Long getMedPrizeNum() {
        return medPrizeNum;
    }

    public void setMedPrizeNum(Long medPrizeNum) {
        this.medPrizeNum = medPrizeNum;
    }

    public Long getMaxPrizeNum() {
        return maxPrizeNum;
    }

    public void setMaxPrizeNum(Long maxPrizeNum) {
        this.maxPrizeNum = maxPrizeNum;
    }

    public Long getExplosionStatus() {
        return explosionStatus;
    }

    public void setExplosionStatus(Long explosionStatus) {
        this.explosionStatus = explosionStatus;
    }
}
