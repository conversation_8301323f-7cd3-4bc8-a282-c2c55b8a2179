package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "t_surplus_redpack_record")
public class SurplusRedpackRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "from_uid")
    private long fromUid;

    @Column(name = "to_uid")
    private long toUid;

    private String vid;
    private long ecoin;
    private long riceroll;
    private Date time;

    public void set(long fromUid, long toUid, String vid, long ecoin, long riceroll) {
        this.fromUid = fromUid;
        this.toUid = toUid;
        this.vid = vid;
        this.ecoin = ecoin;
        this.riceroll = riceroll;
        this.time = new Date();
    }
}
