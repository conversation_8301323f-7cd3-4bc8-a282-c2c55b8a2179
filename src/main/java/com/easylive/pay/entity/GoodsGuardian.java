package com.easylive.pay.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "t_goods_guardian")
@DynamicInsert
@DynamicUpdate
public class GoodsGuardian extends BaseEntity {
    @Column(name = "goods_id")
    private Long goodsId;// 礼物ID,
    @Column(name = "guardian_id")
    private Integer guardianId;//守护ID,
    @Column(name = "guardian_level")
    private Integer guardianLevel;//发送守护礼物等级限制,
    @Column(name = "is_deleted")
    private boolean isDeleted;//是否删除 0-否 1-是,


    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getGuardianId() {
        return guardianId;
    }

    public void setGuardianId(Integer guardianId) {
        this.guardianId = guardianId;
    }

    public Integer getGuardianLevel() {
        return guardianLevel;
    }

    public void setGuardianLevel(Integer guardianLevel) {
        this.guardianLevel = guardianLevel;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }

}
