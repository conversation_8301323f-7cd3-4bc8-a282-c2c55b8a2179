package com.easylive.pay.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@NoArgsConstructor
@Data
@Table(name = "t_cashout_guild_user")
public class CashoutGuildUser {

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;

    private String name;

    @Column(name = "cert_id")
    private String certId;

    @Column(name = "guild_name")
    private String guildName;

    private String description;

}
