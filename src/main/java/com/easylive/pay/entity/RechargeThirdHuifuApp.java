package com.easylive.pay.entity;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_third_huifu_app")
public class RechargeThirdHuifuApp {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "huifu_app_id")
    private String huifuAppId;

    @Column(name = "app_key")
    private String appKey;

    @Column(name = "mock_key")
    private String mockKey;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getHuifuAppId() {
        return huifuAppId;
    }

    public void setHuifuAppId(String huifuAppId) {
        this.huifuAppId = huifuAppId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getMockKey() {
        return mockKey;
    }

    public void setMockKey(String mockKey) {
        this.mockKey = mockKey;
    }
}