package com.easylive.pay.entity;

import lombok.Data;
import lombok.Getter;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Getter
@Entity
@Table(name = "t_recharge_weixin_domain_map")
@Data
public class RechargeWeixinDomainMap {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private Long mgsAppId;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "domain_id")
    private RechargeWeixinDomain domain;
}
