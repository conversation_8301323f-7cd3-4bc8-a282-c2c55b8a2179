package com.easylive.pay.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/*
 * +------------+------------+------+-----+-------------------+-----------------------------+
 * | Field      | Type       | Null | Key | Default           | Extra                       |
 * +------------+------------+------+-----+-------------------+-----------------------------+
 * | id         | bigint(20) | NO   | PRI | NULL              | auto_increment              |
 * | from_uid   | bigint(20) | NO   | MUL | 0                 |                             |
 * | to_uid     | bigint(20) | NO   |     | 0                 |                             |
 * | record_id  | bigint(20) | NO   | MUL | 0                 |                             |
 * | type       | tinyint(3) | NO   |     | 0                 |                             |
 * | create_at  | timestamp  | NO   |     | CURRENT_TIMESTAMP |                             |
 * | updated_at | timestamp  | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
 * | is_deleted | tinyint(1) | NO   |     | 0                 |                             |
 * +------------+------------+------+-----+-------------------+-----------------------------+
 */

/**
 * 个人中心购买礼物记录
 */
@Entity
@Table(name = "t_personal_gift_record")
@DynamicInsert
@DynamicUpdate
public class PersonalGiftRecord extends BaseEntityLong {

    private long fromUid;

    private long toUid;

    private long goodsId;

    private int goodsType;

    private int number;

    private long recordId;

    public PersonalGiftRecord() {
    }

    public PersonalGiftRecord(long fromUid, long toUid, long goodsId, int goodsType, int number, long recordId) {
        this.fromUid = fromUid;
        this.toUid = toUid;
        this.goodsId = goodsId;
        this.goodsType = goodsType;
        this.number = number;
        this.recordId = recordId;
    }

    public long getFromUid() {
        return fromUid;
    }

    public void setFromUid(long fromUid) {
        this.fromUid = fromUid;
    }

    public long getToUid() {
        return toUid;
    }

    public void setToUid(long toUid) {
        this.toUid = toUid;
    }

    public long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(long goodsId) {
        this.goodsId = goodsId;
    }

    public int getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(int goodsType) {
        this.goodsType = goodsType;
    }

    public long getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public long getRecordId() {
        return recordId;
    }

    public void setRecordId(long recordId) {
        this.recordId = recordId;
    }

}
