package com.easylive.pay.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * +----------------+--------------+------+-----+-------------------+-----------------------------+
 * | Field          | Type         | Null | Key | Default           | Extra                       |
 * +----------------+--------------+------+-----+-------------------+-----------------------------+
 * | id             | int(11)      | NO   | PRI | NULL              | auto_increment              |
 * | name           | varchar(255) | NO   |     |                   |                             |
 * | desc           | varchar(255) | NO   |     |                   |                             |
 * | price          | int(11)      | NO   |     | 0                 |                             |
 * | cost_type      | tinyint(3)   | NO   |     | 1                 |                             |
 * | type           | tinyint(2)   | NO   | MUL | 1                 |                             |
 * | target_id      | int(11)      | NO   |     | 0                 |                             |
 * | icon           | varchar(255) | NO   |     |                   |                             |
 * | active         | tinyint(1)   | NO   |     | 1                 |                             |
 * | is_lottery     | tinyint(1)   | NO   |     | 1                 |                             |
 * | limit_type     | tinyint(2)   | NO   |     | 1                 |                             |
 * | limit_end_time | timestamp    | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
 * | create_time    | timestamp    | NO   |     | CURRENT_TIMESTAMP |                             |
 * | app_source_id  | int(11)      | NO   |     | 1                 |                             |
 * +----------------+--------------+------+-----+-------------------+-----------------------------+
 */

@Entity
@Table(name = "t_package_tools")
@DynamicInsert
@DynamicUpdate
public class PackageTools {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 道具名称
     */
    private String name;

    /**
     * 道具描述
     */
    private String desc;

    /**
     * 道具价格
     */
    private int price;

    /**
     * 货币类型 1-易币 2-钻石
     */
    @Column(name = "cost_type")
    private int costType;

    /**
     * 道具类型 1-礼物道具 2-靓号 3-坐骑 4-认证 5-观影券 6-置顶票 7-合成碎片 8-豌豆
     */
    private int type;

    /**
     * 对应的业务id
     */
    @Column(name = "target_id")
    private int targetId;

    /**
     * 图标
     */
    private String icon;

    /**
     * 是否激活 0冻结 1激活
     */
    private boolean active;

    /**
     * 是否参与抽奖 1参与 0不参与
     */
    @Column(name = "is_lottery")
    private boolean isLottery;

    /**
     * 限时类型：1-不限时 2-整体限时
     */
    @Column(name = "limit_type")
    private int limitType;

    /**
     * 有效期到期时间
     */
    @Column(name = "limit_end_time")
    private Date limitEndTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 目标ID
     */
    @Column(name = "app_source_id")
    private int appSourceId;

    public PackageTools() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public int getCostType() {
        return costType;
    }

    public void setCostType(int costType) {
        this.costType = costType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public int getTargetId() {
        return targetId;
    }

    public void setTargetId(int targetId) {
        this.targetId = targetId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public boolean isLottery() {
        return isLottery;
    }

    public void setLottery(boolean lottery) {
        isLottery = lottery;
    }

    public int getLimitType() {
        return limitType;
    }

    public void setLimitType(int limitType) {
        this.limitType = limitType;
    }

    public Date getLimitEndTime() {
        return limitEndTime;
    }

    public void setLimitEndTime(Date limitEndTime) {
        this.limitEndTime = limitEndTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public int getAppSourceId() {
        return appSourceId;
    }

    public void setAppSourceId(int appSourceId) {
        this.appSourceId = appSourceId;
    }
}
