package com.easylive.pay.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Entity
@NoArgsConstructor
@Table(name = "t_bank_cashout")
@Data
public class CashoutOfflineInfoEntity {

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;

    @Column(name = "bank_num")
    private String bankCardNo;

    @Column(name = "id_card")
    private String idCard;

    @Column(name = "real_name")
    private String realName;

    @Column(name = "open_bank")
    private String openBank;

    @Column(name = "settlement_time")
    private Date lastSettlementTime;

    @Column(name = "settlement_num")
    private Date lastSettlementAmount;

}
