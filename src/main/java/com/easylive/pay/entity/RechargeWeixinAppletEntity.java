package com.easylive.pay.entity;

import lombok.Getter;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Entity
@Getter
@Table(name = "t_recharge_weixin_applet")
public class RechargeWeixinAppletEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "path")
    private String path;

    @Column(name = "env")
    private String env;
}
