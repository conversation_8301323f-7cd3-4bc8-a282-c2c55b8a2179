package com.easylive.pay.entity;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_weixin_app")
public class RechargeWeixinApp {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private String appId;

    @Column(name = "app_secret")
    private String appSecret;

    @Column(name = "mch_id")
    private String mchId;

    @Column(name = "type")
    private int type;

    @Column(name = "real_type")
    private Integer realType;

    @Column(name = "platform")
    private Integer platform;

    private String description;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public int getType() {
        return type;
    }

    public Integer getRealType() {
        return realType;
    }


    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setRealType(Integer realType) {
        this.realType = realType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
