package com.easylive.pay.entity;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_paypal_map")
public class RechargePaypalMap {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private int mgsAppId;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "mid")
    private RechargePaypalMerchant merchant;

    private boolean prime;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getMgsAppId() {
        return mgsAppId;
    }

    public void setMgsAppId(int mgsAppId) {
        this.mgsAppId = mgsAppId;
    }

    public RechargePaypalMerchant getMerchant() {
        return merchant;
    }

    public void setMerchant(RechargePaypalMerchant app) {
        this.merchant = app;
    }


    public boolean isPrime() {
        return prime;
    }

    public void setPrime(boolean primary) {
        this.prime = primary;
    }
}
