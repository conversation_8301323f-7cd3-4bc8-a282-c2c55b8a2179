package com.easylive.pay.entity;


import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "t_gash_recharge_record")
public class GashRechargeRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer id;

    private Long uid;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "app_id")
    private Integer appId;

    @Column(name = "platform_id")
    private Integer platformId;

    @Column(name = "platform_code")
    private String platformCode;

    @Column(name = "platform_status")
    private Integer platformStatus;

    @Column(name = "platform_order_id")
    private String platformOrderId;

    @Column(name = "platform_msg")
    private String platformMsg;

    @Column(name = "platform_amount")
    private Long platformAmount;

    @Column(name = "platform_currency")
    private String platformCurrency;

    @Column(name = "create_time")
    private Date createTime;


    @Column(name = "update_time")
    private Date updateTime;

    public GashRechargeRecord() {
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getAppId() {
        return appId;
    }

    public void setAppId(Integer appId) {
        this.appId = appId;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public Integer getPlatformStatus() {
        return platformStatus;
    }

    public void setPlatformStatus(Integer platformStatus) {
        this.platformStatus = platformStatus;
    }

    public String getPlatformOrderId() {
        return platformOrderId;
    }

    public void setPlatformOrderId(String platformOrderId) {
        this.platformOrderId = platformOrderId;
    }

    public String getPlatformMsg() {
        return platformMsg;
    }

    public void setPlatformMsg(String platformMsg) {
        this.platformMsg = platformMsg;
    }

    public Long getPlatformAmount() {
        return platformAmount;
    }

    public void setPlatformAmount(Long platformAmount) {
        this.platformAmount = platformAmount;
    }

    public String getPlatformCurrency() {
        return platformCurrency;
    }

    public void setPlatformCurrency(String platformCurrency) {
        this.platformCurrency = platformCurrency;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "GashRechargeRecord{" +
                "id=" + id +
                ", uid=" + uid +
                ", orderId='" + orderId + '\'' +
                ", appId=" + appId +
                ", platformId=" + platformId +
                ", platformCode=" + platformCode +
                ", platformStatus=" + platformStatus +
                ", platformOrderId='" + platformOrderId + '\'' +
                ", platformMsg='" + platformMsg + '\'' +
                ", platformAmount=" + platformAmount +
                ", platformCurrency='" + platformCurrency + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
