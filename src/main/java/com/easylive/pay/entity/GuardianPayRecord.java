package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "t_guardian_pay_record")
public class GuardianPayRecord {

    String tid;
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;
    @Column(name = "guardian_id")
    private long guardianId;

    @Column(name = "to_uid")
    private long toUid;

    @Column(name = "from_uid")
    private long fromUid;

    private long ecoin;
    private long riceroll;
    private Date time;

    public void set(String tid, GuardianOption guardianOption, long toUid, long fromUid, long month) {
        this.tid = tid;
        this.guardianId = guardianOption.getId();
        this.ecoin = guardianOption.getEcoin() * month;
        this.riceroll = guardianOption.getRiceroll() * month;
        this.toUid = toUid;
        this.fromUid = fromUid;
        this.time = new Date();
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public long getGuardianId() {
        return guardianId;
    }

    public void setGuardianId(long guardianId) {
        this.guardianId = guardianId;
    }

    public long getToUid() {
        return toUid;
    }

    public void setToUid(long toUid) {
        this.toUid = toUid;
    }

    public long getFromUid() {
        return fromUid;
    }

    public void setFromUid(long fromUid) {
        this.fromUid = fromUid;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }
}
