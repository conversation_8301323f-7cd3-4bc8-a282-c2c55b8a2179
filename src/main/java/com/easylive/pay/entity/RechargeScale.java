package com.easylive.pay.entity;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018/11/22
 */
@Entity
@Table(name = "t_recharge_scale")
public class RechargeScale {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private String cid;

    private BigDecimal scale;

    private int platform;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public BigDecimal getScale() {
        return scale;
    }

    public void setScale(BigDecimal scale) {
        this.scale = scale;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }
}
