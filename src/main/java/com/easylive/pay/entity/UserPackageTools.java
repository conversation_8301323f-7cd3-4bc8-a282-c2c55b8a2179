package com.easylive.pay.entity;

import com.easylive.pay.enums.PackageToolsLimitType;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * +----------------+------------+------+-----+-------------------+----------------+
 * | Field          | Type       | Null | Key | Default           | Extra          |
 * +----------------+------------+------+-----+-------------------+----------------+
 * | id             | int(11)    | NO   | PRI | NULL              | auto_increment |
 * | uid            | int(11)    | YES  | MUL | NULL              |                |
 * | tool_id        | int(11)    | YES  | MUL | NULL              |                |
 * | number         | int(11)    | YES  |     | NULL              |                |
 * | limit_type     | tinyint(2) | YES  |     | 1                 |                |
 * | limit_end_time | timestamp  | YES  |     | NULL              |                |
 * | create_time    | timestamp  | NO   |     | CURRENT_TIMESTAMP |                |
 * +----------------+------------+------+-----+-------------------+----------------+
 */

@Entity
@Table(name = "t_user_package_tools")
@DynamicInsert
@DynamicUpdate
public class UserPackageTools {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 用户UID
     */
    private Long uid;

    /**
     * 道具描述
     */
    @Column(name = "tool_id")
    private Integer toolId;

    /**
     * 道具数量
     */
    private Integer number;

    /**
     * 限时类型：1-不限时 2-整体限时
     */
    @Column(name = "limit_type")
    private Integer limitType;

    /**
     * 有效期到期时间
     */
    @Column(name = "limit_end_time")
    private Date limitEndTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    public UserPackageTools() {
    }

    public UserPackageTools(long uid, int toolId, int number) {
        this.uid = uid;
        this.toolId = toolId;
        this.number = number;
        this.limitType = PackageToolsLimitType.NO_LIMIT.getValue();
        this.createTime = new Date();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getToolId() {
        return toolId;
    }

    public void setToolId(Integer toolId) {
        this.toolId = toolId;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public int getLimitType() {
        return limitType;
    }

    public void setLimitType(int limitType) {
        this.limitType = limitType;
    }

    public Date getLimitEndTime() {
        return limitEndTime;
    }

    public void setLimitEndTime(Date limitEndTime) {
        this.limitEndTime = limitEndTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
