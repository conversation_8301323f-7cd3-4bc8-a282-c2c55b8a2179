package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "t_riceroll_contributor")
public class RicerollContributorEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "from_uid")
    private long fromUid;

    @Column(name = "to_uid")
    private long toUid;

    @Column(name = "riceroll")
    private long riceroll;

    @Column(name = "time")
    private Date updateTime;

    public RicerollContributorEntity() {
        fromUid = 0;
        toUid = 0;
        riceroll = 0;
        updateTime = new Date();
    }

    public RicerollContributorEntity(long fromUid, long toUid) {
        this.fromUid = fromUid;
        this.toUid = toUid;
        this.riceroll = 0;
        this.updateTime = new Date();
    }

    public long getFromUid() {
        return fromUid;
    }

    public void setFromUid(long fromUid) {
        this.fromUid = fromUid;
    }

    public long getToUid() {
        return toUid;
    }

    public void setToUid(long toUid) {
        this.toUid = toUid;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
        this.updateTime = new Date();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
