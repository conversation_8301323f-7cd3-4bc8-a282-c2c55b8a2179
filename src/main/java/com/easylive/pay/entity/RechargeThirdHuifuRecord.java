package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "t_recharge_third_huifu_record")
public class RechargeThirdHuifuRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "huifu_id")
    private String huifuId;

    @Column(name = "out_trans_id")
    private String outTransId;

    @Column(name = "party_order_id")
    private String partyOrderId;

    @Column(name = "channel")
    private String channel;

    @Column(name = "amount")
    private long amount;

    @Column(name = "status")
    private String status;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "end_time")
    private Date endTime;

}