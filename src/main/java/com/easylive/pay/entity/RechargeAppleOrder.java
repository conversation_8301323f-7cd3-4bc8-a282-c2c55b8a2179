package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "t_recharge_apple_order")
public class RechargeAppleOrder {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "uid")
    private long uid;

    @Column(name = "status")
    private int status;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "purchase_time")
    private Date purchaseTime;

    @Column(name = "refund_request_time")
    private Date refundRequestTime;

    @Column(name = "refund_time")
    private Date refundTime;
}

