package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "t_cashout_huifu_user_record")
public class CashoutHuifuUserRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "uid")
    private long uid;

    @Column(name = "member_id")
    private String memberId;

    @Column(name = "type")
    private Integer type;

    @Column(name = "settle_id")
    private String settleId;

    @Column(name = "name")
    private String name;

    @Column(name="phone")
    private String phone;

    @Column(name = "cert_id")
    private String certId;

    @Column(name = "card_id")
    private String cardId;

    @Column(name = "operate_time")
    private Date operateTime;


    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "error_msg")
    private String errorMsg;
}