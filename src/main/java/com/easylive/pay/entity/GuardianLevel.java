package com.easylive.pay.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "t_guardian_level")
public class GuardianLevel extends BaseEntityLong {
    @Column(name = "anchor_uid")
    private Long anchorUid;
    private Long uid;
    private Integer level;
    @Column(name = "old_level")
    private Integer oldLevel;
    private Integer exp;
    @Column(name = "is_down")
    private Integer isDown;
    @Column(name = "down_time")
    private Date downTime;
    @Column(name = "end_time")
    private Date endTime;
    @Column(name = "is_deleted")
    private Boolean isDeleted;

    public Long getAnchorUid() {
        return anchorUid;
    }

    public void setAnchorUid(Long anchorUid) {
        this.anchorUid = anchorUid;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getOldLevel() {
        return oldLevel;
    }

    public void setOldLevel(Integer oldLevel) {
        this.oldLevel = oldLevel;
    }

    public Integer getExp() {
        return exp;
    }

    public void setExp(Integer exp) {
        this.exp = exp;
    }

    public Integer getIsDown() {
        return isDown;
    }

    public void setIsDown(Integer isDown) {
        this.isDown = isDown;
    }

    public Date getDownTime() {
        return downTime;
    }

    public void setDownTime(Date downTime) {
        this.downTime = downTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }
}
