package com.easylive.pay.entity;

import javax.persistence.*;

/**
 * t_guardian_option
 * guoweitian
 */

@Entity
@Table(name = "t_guardian")
public class GuardianOption {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;


    private long ecoin;
    private int type;
    @Column(name = "anchor_riceroll")
    private long riceroll;
    private long exp;
    private String title;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public long getExp() {
        return exp;
    }

    public void setExp(long exp) {
        this.exp = exp;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
