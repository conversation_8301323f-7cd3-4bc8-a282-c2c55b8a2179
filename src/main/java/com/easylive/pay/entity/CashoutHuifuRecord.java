package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "t_cashout_huifu_record")
public class CashoutHuifuRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "pay_id")
    private String payId;

    @Column(name = "cashout_id")
    private String cashoutId;

    @Column(name = "member_id")
    private String memberId;

    @Column(name = "settle_id")
    private String settleId;

    @Column(name = "name")
    private String name;

    @Column(name = "cert_id")
    private String certId;

    @Column(name = "bank_card")
    private String bankCard;

    @Column(name = "amount")
    private long amount;

    @Column(name = "status")
    private int status;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "error_msg")
    private String errorMsg;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "complete_time")
    private Date completeTime;

}