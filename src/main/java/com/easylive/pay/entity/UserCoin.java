package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "t_user_coin")
public class UserCoin {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;               //user id

    private long barley;            //current barley

    private long accumbarley;       //accumulate barley

    private long ecoin;             //current ecoin

    private long accumecoin;        //accumulate ecoin

    private long costecoin;         //cost ecoin

    private long riceroll;          //current riceroll

    private long accumriceroll;     //accumulate riceroll


    @Column(name = "update_time")
    private Date updateTime;

    public UserCoin() {

    }

    public UserCoin(long uid) {
        this.uid = uid;
        this.barley = 0;
        this.accumbarley = 0;
        this.ecoin = 0;
        this.accumecoin = 0;
        this.riceroll = 0;
        this.accumriceroll = 0;
        this.costecoin = 0;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getBarley() {
        return barley;
    }

    public void setBarley(long barley) {
        this.barley = barley;
    }

    public long getAccumbarley() {
        return accumbarley;
    }

    public void setAccumbarley(long accumbarley) {
        this.accumbarley = accumbarley;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public long getAccumecoin() {
        return accumecoin;
    }

    public void setAccumecoin(long accumecoin) {
        this.accumecoin = accumecoin;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public long getAccumriceroll() {
        return accumriceroll;
    }

    public void setAccumriceroll(long accumriceroll) {
        this.accumriceroll = accumriceroll;
    }

    public long getCostecoin() {
        return costecoin;
    }

    public void setCostecoin(long costecoin) {
        this.costecoin = costecoin;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
