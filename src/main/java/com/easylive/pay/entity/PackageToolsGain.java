package com.easylive.pay.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * +-------------------+--------------+------+-----+-------------------+-----------------------------+
 * | Field             | Type         | Null | Key | Default           | Extra                       |
 * +-------------------+--------------+------+-----+-------------------+-----------------------------+
 * | id                | int(11)      | NO   | PRI | NULL              | auto_increment              |
 * | uid               | int(11)      | NO   | MUL | 0                 |                             |
 * | from_uid          | int(11)      | NO   | MUL | 0                 |                             |
 * | tool_id           | int(11)      | NO   | MUL | 0                 |                             |
 * | tool_name         | varchar(255) | NO   |     |                   |                             |
 * | from_type         | tinyint(2)   | NO   |     | 1                 |                             |
 * | business_id       | int(11)      | NO   |     | 0                 |                             |
 * | price             | int(11)      | NO   |     | 0                 |                             |
 * | number            | int(11)      | NO   |     | 0                 |                             |
 * | cost_type         | tinyint(1)   | NO   |     | 1                 |                             |
 * | validate_end_time | timestamp    | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
 * | create_time       | timestamp    | NO   |     | CURRENT_TIMESTAMP |                             |
 * +-------------------+--------------+------+-----+-------------------+-----------------------------+
 */

@Entity
@Table(name = "t_package_tools_gain")
@DynamicInsert
@DynamicUpdate
public class PackageToolsGain {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    /**
     * 用户UID
     */
    private Long uid;

    /**
     * 赠送人UID
     */
    @Column(name = "from_uid")
    private Integer fromUid = 0;

    /**
     * 道具ID
     */
    @Column(name = "tool_id")
    private int toolId;

    /**
     * 道具名称
     */
    @Column(name = "tool_name")
    private String toolName = "";

    /**
     * 获取类型 1-购买 2-活动 3-抽奖 4-赠送 5-签到 6-送礼物
     */
    @Column(name = "from_type")
    private int fromType;

    /**
     * 业务订单ID
     */
    @Column(name = "business_id")
    private Integer businessId = 0;

    /**
     * 价格
     */
    private Integer price = 0;

    /**
     * 数量
     */
    private int number;

    /**
     * 购买消费类型 1-易币 2-钻石
     */
    @Column(name = "cost_type")
    private Integer costType;

    /**
     * 有效期到期时间
     */
    @Column(name = "validate_end_time")
    private Date validateEndTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    public PackageToolsGain() {

    }

    public PackageToolsGain(long uid) {
        this.uid = uid;
        this.createTime = new Date();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public int getFromUid() {
        return fromUid;
    }

    public void setFromUid(int fromUid) {
        this.fromUid = fromUid;
    }

    public int getToolId() {
        return toolId;
    }

    public void setToolId(int toolId) {
        this.toolId = toolId;
    }

    public String getToolName() {
        return toolName;
    }

    public void setToolName(String toolName) {
        this.toolName = toolName;
    }

    public int getFromType() {
        return fromType;
    }

    public void setFromType(int fromType) {
        this.fromType = fromType;
    }

    public int getBusinessId() {
        return businessId;
    }

    public void setBusinessId(int businessId) {
        this.businessId = businessId;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public int getCostType() {
        return costType;
    }

    public void setCostType(int costType) {
        this.costType = costType;
    }

    public Date getValidateEndTime() {
        return validateEndTime;
    }

    public void setValidateEndTime(Date validateEndTime) {
        this.validateEndTime = validateEndTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
