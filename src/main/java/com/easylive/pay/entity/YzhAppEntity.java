package com.easylive.pay.entity;

import com.easylive.pay.service.yzh.YunzhanghuApi;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "t_cashout_yzh_app")
public class YzhAppEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "dealer_id")
    private String dealerId;

    @Column(name = "broker_id")
    private String brokerId;

    @Column(name = "app_key")
    private String appKey;

    @Column(name = "des_key")
    private String desKey;
    @Column(name = "description")
    private String description;

    @Column(name = "enabled")
    private Integer enabled;

    @Transient
    private YunzhanghuApi api;


}