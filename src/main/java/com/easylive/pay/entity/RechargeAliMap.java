package com.easylive.pay.entity;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_ali_map")
public class RechargeAliMap {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private int mgsAppId;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "ali_id")
    private RechargeAliApp aliApp;

    @Column(name = "type")
    private int type;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getMgsAppId() {
        return mgsAppId;
    }

    public void setMgsAppId(int mgsAppId) {
        this.mgsAppId = mgsAppId;
    }

    public RechargeAliApp getAliApp() {
        return aliApp;
    }

    public void setAliApp(RechargeAliApp aliApp) {
        this.aliApp = aliApp;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}

