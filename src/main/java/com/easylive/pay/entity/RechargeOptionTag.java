package com.easylive.pay.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.persistence.*;

/**
 * @Author: <PERSON><PERSON>ai
 * @Date: 2021/9/28
 */
 
@Entity
@Table(name = "t_recharge_option_tag")
public class RechargeOptionTag {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private long appId;

    @Column(name = "recharge_option_id")
    @JsonProperty(value = "optionId")
    private long optionId;

    @Column(name = "tag_type")
    @JsonProperty(value = "tagType")
    private int tagType;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getOptionId() {
        return optionId;
    }

    public void setOptionId(long optionId) {
        this.optionId = optionId;
    }

    public int getTagType() {
        return tagType;
    }

    public void setTagType(int tagType) {
        this.tagType = tagType;
    }

    public long getAppId() {
        return appId;
    }

    public void setAppId(long appId) {
        this.appId = appId;
    }
}
