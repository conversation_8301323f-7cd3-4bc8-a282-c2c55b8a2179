package com.easylive.pay.entity;

import javax.persistence.*;

@Entity
@Table(name = "t_user_gift_stat")
public class UserGiftStat {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;
    @Column(name = "recv_gift_count")
    private long recvGiftCount;
    @Column(name = "send_gift_count")
    private long sendGiftCount;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getRecvGiftCount() {
        return recvGiftCount;
    }

    public void setRecvGiftCount(long recvGiftCount) {
        this.recvGiftCount = recvGiftCount;
    }

    public long getSendGiftCount() {
        return sendGiftCount;
    }

    public void setSendGiftCount(long sendGiftCount) {
        this.sendGiftCount = sendGiftCount;
    }
}
