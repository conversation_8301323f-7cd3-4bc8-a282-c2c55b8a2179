package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by c on 2016/5/16.
 */
@Entity
@Table(name = "t_cashout_forbid")
public class CashoutForbid {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;

    private Date create_time;

    public CashoutForbid() {
        uid = 0;
        create_time = null;
    }

    public CashoutForbid(long uid) {
        this.uid = uid;
        this.create_time = new Date();
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }
}
