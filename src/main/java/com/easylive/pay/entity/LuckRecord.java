package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "t_buy_luck_gift_record")
public class LuckRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long goodsid;           //商品id
    private String goodsname;       //商品名称

    @Column(name = "from_uid")
    private long fromUid;          //消费的用户id

    @Column(name = "to_uid")
    private long toUid;            //送给的用户id  TODO 表情是没有接受者的

    private long cost;              //消费数目
    private long costtype;          //消费类型
    private long riceroll;          //增加的饭团数
    private Date time;              //交易时间
    private String vid;             //视频vid
    private String tid;             //订单号
    private long number;            //数目

    @Column(name = "is_reward")
    private long isReward;         //是否中奖

    @Column(name = "reward_ecoin")
    private long rewardEcoin;      //中奖获得的易币

    public LuckRecord() {
    }

    public void set(Goods goods, long fromUid, String vid, long toUid, long number, String tid) {
        this.goodsid = goods.getId();
        this.goodsname = goods.getName();
        this.fromUid = fromUid;
        this.toUid = toUid;
        this.cost = goods.getCost() * number;
        this.costtype = goods.getCostType();
        this.riceroll = goods.getRiceroll() * number;
        this.time = new Date();
        this.vid = vid;
        this.number = number;
        this.tid = tid;
    }

    public long getCost() {
        return cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getVid() {
        return vid;
    }

    public void setVid(String vid) {
        this.vid = vid;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getNumber() {
        return number;
    }

    public void setNumber(long number) {
        this.number = number;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public long getIsReward() {
        return isReward;
    }

    public void setIsReward(long isReward) {
        this.isReward = isReward;
    }

    public long getRewardEcoin() {
        return rewardEcoin;
    }

    public void setRewardEcoin(long rewardEcoin) {
        this.rewardEcoin = rewardEcoin;
    }
}
