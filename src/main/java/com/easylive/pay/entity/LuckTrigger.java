package com.easylive.pay.entity;

import javax.persistence.*;

@Entity
@Table(name = "t_luck_triggers")
public class LuckTrigger {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "ecoin_trigger")

    private long ecoinTrigger;

    @Column(name = "trigger_rate")
    private long triggerRate;

    @Column(name = "explosion_ecoin_trigger")
    private long explosionEcoinTrigger;

    @Column(name = "explosion_trigger_rate")
    private long explosionTriggerRate;

    public void set(LuckTrigger luckTrigger) {
        this.ecoinTrigger = luckTrigger.ecoinTrigger;
        this.triggerRate = luckTrigger.triggerRate;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getEcoinTrigger() {
        return ecoinTrigger;
    }

    public void setEcoinTrigger(long ecoinTrigger) {
        this.ecoinTrigger = ecoinTrigger;
    }

    public long getTriggerRate() {
        return triggerRate;
    }

    public void setTriggerRate(long triggerRate) {
        this.triggerRate = triggerRate;
    }

    public long getExplosionEcoinTrigger() {
        return explosionEcoinTrigger;
    }

    public void setExplosionEcoinTrigger(long explosionEcoinTrigger) {
        this.explosionEcoinTrigger = explosionEcoinTrigger;
    }

    public long getExplosionTriggerRate() {
        return explosionTriggerRate;
    }

    public void setExplosionTriggerRate(long explosionTriggerRate) {
        this.explosionTriggerRate = explosionTriggerRate;
    }
}
