package com.easylive.pay.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

/**
 * <AUTHOR>
 * @date 2019/8/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
public abstract class BaseSoftEntity extends BaseEntity {

    @Column(columnDefinition = "tinyint(1) NOT NULL DEFAULT 0")
    protected boolean isDeleted;

}
