package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "t_recharge_wrong_transfer")
public class RechargeWrongTransferRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;
    @Column(name = "from_uid")
    private long fromUid;

    @Column(name = "to_uid")
    private long toUid;

    @Column(name = "order_id")
    private String orderId;

    private long ecoin;

    @Column(name = "create_time")
    private Date createTime;
}
