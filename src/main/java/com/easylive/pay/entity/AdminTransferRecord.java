package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "t_admin_transfer_record")
@Data
public class AdminTransferRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "order_id")
    private String orderId;

    private String tid;

    @Column(name = "err_order_id")
    private String errOrderId;

    @Column(name = "from_uid")
    private long fromUid;

    @Column(name = "to_uid")
    private long toUid;

    private int ecoin;

    private String reason;

    @Column(name = "operate_user")
    private String operateUser;

    @Column(name = "create_time")
    private Date createTime;
}
