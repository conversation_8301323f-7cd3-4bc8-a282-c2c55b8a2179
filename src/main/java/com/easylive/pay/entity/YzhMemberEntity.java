package com.easylive.pay.entity;

import lombok.Data;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@Table(name = "t_cashout_yzh_member")
public class YzhMemberEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "uid")
    private Long uid;

    @Column(name = "app_id")
    private Long appId;

    @Column(name = "real_name")
    private String realName;

    @Column(name = "id_card")
    private String idCard;

    /**
     * 0: 未签约
     * 1: 已签约
     * 2: 已解约
     */
    @Column(name = "sign_status")
    private String signStatus;

    @Column(name = "signed_at")
    private Date signedAt;
} 