package com.easylive.pay.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_cashout_huifu_user")
@Data
public class CashoutHuifuUserEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private int appId;

    @Column(name = "uid")
    private Long uid;

    @Column(name = "member_id")
    private String memberId;

    @Column(name = "settle_id")
    private String settleId;

    @Column(name = "name")
    private String name;

    @Column(name="phone")
    private String phone;

    @Column(name = "cert_id")
    private String certId;

    @Column(name = "card_id")
    private String cardId;

    @Column(name = "last_cashout_time")
    private Date lastCashoutTime;

    @Column(name = "create_time")
    private Date createTime;
}