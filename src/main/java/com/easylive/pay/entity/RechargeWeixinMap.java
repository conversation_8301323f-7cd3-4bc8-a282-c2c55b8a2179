package com.easylive.pay.entity;

import javax.persistence.*;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_weixin_map")
public class RechargeWeixinMap {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "app_id")
    private int mgsAppId;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "weixin_id")
    private RechargeWeixinApp weixinApp;

    @Column(name = "type")
    private int type;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getMgsAppId() {
        return mgsAppId;
    }

    public void setMgsAppId(int mgsAppId) {
        this.mgsAppId = mgsAppId;
    }

    public RechargeWeixinApp getWeixinApp() {
        return weixinApp;
    }

    public void setWeixinApp(RechargeWeixinApp weixinApp) {
        this.weixinApp = weixinApp;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
