package com.easylive.pay.entity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "t_watch_pay_record")
public class WatchPayRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;
    private String tid;
    private String vid;
    private long ecoin;
    private long living;
    @Column(name = "create_time")
    private Date createTime;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getVid() {
        return vid;
    }

    public void setVid(String vid) {
        this.vid = vid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public long getLiving() {
        return living;
    }

    public void setLiving(long living) {
        this.living = living;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
