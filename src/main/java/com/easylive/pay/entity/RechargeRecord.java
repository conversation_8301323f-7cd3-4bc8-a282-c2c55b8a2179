package com.easylive.pay.entity;

import com.easylive.pay.enums.OrderStatus;

import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_recharge_record")
public class RechargeRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;
    private long uid;
    private int status;
    private long rmb;

    private String currency;
    private Long amount;

    private long ecoin;
    private String tid;

    private boolean deleted;

    @Column(name = "mid")
    private String merchantId;

    @Column(name = "mcurrency")
    private String merchantCurrency;

    @Column(name = "commit_time")
    private Date commitTime;

    @Column(name = "complete_time")
    private Date completeTime;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "client_ip")
    private String clientIp;

    @Column(name = "platform")
    private int platform;

    @Column(name = "real_platform")
    private Integer realPlatform;

    @Column(name = "clientplatform")
    private int clientPlatform;
    // 支付平台的订单号

    @Column(name = "pforderid")
    private String platformOrderId;

    @Column(name = "app_id")
    private int appId;

    private long prize;

    public RechargeRecord() {
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public String getCurrency() { return currency; }

    public void setCurrency(String currency) { this.currency = currency; }

    public Long getAmount() { return amount; }

    public void setAmount(Long amount) { this.amount = amount; }

    public String getPlatformOrderId() {
        return platformOrderId;
    }

    public void setPlatformOrderId(String platformOrderId) {
        this.platformOrderId = platformOrderId;
    }


    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    /*
    public Integer getRealPlatform() {
        return realPlatform;
    }

    public void setRealPlatform(Integer realPlatform) {
        this.realPlatform = realPlatform;
    }
    */

    public int getClientPlatform() {
        return clientPlatform;
    }

    public void setClientPlatform(int clientPlatform) {
        this.clientPlatform = clientPlatform;
    }


    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getRmb() {
        return rmb;
    }

    public void setRmb(long rmb) {
        this.rmb = rmb;
    }

    public Date getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public int getAppId() {
        return appId;
    }

    public void setAppId(int appId) {
        this.appId = appId;
    }

    public String getMerchantCurrency() {
        return merchantCurrency;
    }

    public void setMerchantCurrency(String merchantCurrency) {
        this.merchantCurrency = merchantCurrency;
    }

    public Integer getRealPlatform() {
        return realPlatform;
    }

    public void setRealPlatform(Integer realPlatform) {
        this.realPlatform = realPlatform;
    }

    public long getPrize() {
        return prize;
    }

    public void setPrize(long prize) {
        this.prize = prize;
    }

    public boolean isPaid() {
        return status == OrderStatus.ORS_SUCCESS.getValue() || status == OrderStatus.ORS_VALID.getValue();
    }
}
