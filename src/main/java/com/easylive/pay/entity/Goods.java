package com.easylive.pay.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.persistence.*;

/**
 * Created by c on 2015/12/14.
 */
@Entity
@Table(name = "t_goods")
public class Goods {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private String name;
    private int type;

    private String picture;

    private String animation;
    private long riceroll;
    private long active;
    private long cost;
    @Column(name = "costtype")
    @JsonProperty(value = "costtype")
    private int costType;
    private long exp;

    @Column(name = "anitype")
    @JsonProperty(value = "anitype")
    private int animationType;

    @Column(name = "noble_level")
    private int nobleLevel;

    @Column(name = "msg")
    private String giftMsg;

    @Column(name = "show_runway")
    private Boolean showRunway;

    public String getGiftMsg() { return giftMsg; }

    public void setGiftMsg(String giftMsg) { this.giftMsg = giftMsg; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getAnimation() {
        return animation;
    }

    public void setAnimation(String animation) {
        this.animation = animation;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public long getActive() {
        return active;
    }

    public void setActive(long active) {
        this.active = active;
    }

    public long getCost() {
        return cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }

    public int getCostType() {
        return costType;
    }

    public void setCostType(int costType) {
        this.costType = costType;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getExp() {
        return exp;
    }

    public void setExp(long exp) {
        this.exp = exp;
    }

    public int getAnimationType() {
        return animationType;
    }

    public void setAnimationType(int animationType) {
        this.animationType = animationType;
    }

    public int getNobleLevel() {
        return nobleLevel;
    }

    public void setNobleLevel(int nobleLevel) {
        this.nobleLevel = nobleLevel;
    }

    public Boolean getShowRunway() {
        return showRunway;
    }

    public void setShowRunway(Boolean showRunway) {
        this.showRunway = showRunway;
    }
}
