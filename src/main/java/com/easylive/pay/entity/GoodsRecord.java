package com.easylive.pay.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by c on 2015/12/15.
 */
@Entity
@Table(name = "t_goods_record")
public class GoodsRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    @Column(name = "goodsid")
    @JsonProperty(value = "goodsid")
    private long giftId;

    @Column(name = "goodsname")
    @JsonProperty(value = "goodsname")
    private String giftName;

    @Column(name = "goodstype")
    @JsonProperty(value = "goodstype")
    private int giftType;

    @Column(name = "from_uid")
    @JsonProperty(value = "from_uid")
    private long fromUid;


    @Column(name = "to_uid")
    @JsonProperty(value = "to_uid")
    private long toUid;

    private long cost;

    @Column(name = "costtype")
    @JsonProperty(value = "costtype")
    private int costType;

    private long riceroll;
    private Date time;
    private String vid;
    private String tid;
    private int number;

    public GoodsRecord() {
    }

    public void set(Goods goods, long fromUid, String vid, long toUid, int number) {
        this.giftId = goods.getId();
        this.giftName = goods.getName();
        this.giftType = goods.getType();
        this.fromUid = fromUid;
        this.toUid = toUid;
        this.cost = goods.getCost();
        this.costType = goods.getCostType();
        this.riceroll = goods.getRiceroll();
        this.time = new Date();
        this.vid = vid;
        this.number = number;
    }

    public long getGiftId() {
        return giftId;
    }

    public void setGiftId(long giftId) {
        this.giftId = giftId;
    }

    public long getFromUid() {
        return fromUid;
    }

    public void setFromUid(long fromUid) {
        this.fromUid = fromUid;
    }

    public long getToUid() {
        return toUid;
    }

    public void setToUid(long toUid) {
        this.toUid = toUid;
    }

    public long getCost() {
        return cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }

    public int getCostType() {
        return costType;
    }

    public void setCostType(int costType) {
        this.costType = costType;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getVid() {
        return vid;
    }

    public void setVid(String vid) {
        this.vid = vid;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public int getGiftType() {
        return giftType;
    }

    public void setGiftType(int giftType) {
        this.giftType = giftType;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }
}
