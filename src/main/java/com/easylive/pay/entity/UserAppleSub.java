package com.easylive.pay.entity;


import javax.persistence.*;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "t_user_apple_sub")
public class UserAppleSub {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;

    private long uid;

    @Column(name = "product_id")
    private String productId;

    @Column(name = "original_transaction_id")
    private String originalTransactionId;

    @Column(name = "auto_renew_status")
    private Boolean autoRenew;

    private Boolean cancel;

    private String receipt;

    @Column(name = "secret_key")
    private String secretKey;

    @Column(name = "expire_time")
    private Date expireTime;

    @Column(name = "create_time")
    private Date createDate;

    @Column(name = "update_time")
    private Date updateDate;


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getOriginalTransactionId() {
        return originalTransactionId;
    }

    public void setOriginalTransactionId(String originalTransactionId) {
        this.originalTransactionId = originalTransactionId;
    }

    public Boolean getAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(Boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    public Boolean getCancel() {
        return cancel;
    }

    public void setCancel(Boolean cancel) {
        this.cancel = cancel;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }
}

