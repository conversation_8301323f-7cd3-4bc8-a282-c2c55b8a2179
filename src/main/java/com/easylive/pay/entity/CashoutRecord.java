package com.easylive.pay.entity;

import com.easylive.pay.enums.OrderStatus;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "t_cashout_record")
public class CashoutRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;
    private long uid;
    private String tid;
    private int status;
    private int app_status;
    private long rmb;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date commit_time;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date complete_time;
    private String orderid;
    private int platform;
    // 支付平台
    private int pfplatform;
    // 客户端IP
    private String client_ip;
    // 服务器IP
    private String server_ip;
    // 换的饭团数量
    private long riceroll;
    // Openid
    private String openid;
    private int card_id;
    private String remark = "";
    private Integer app_id;

    @Column(name = "pforderid")
    private String platformOrderId;

    public CashoutRecord() {
    }

    public CashoutRecord(long uid, long rmb, String orderid, int mp, String client_ip, String server_ip,
                              long riceroll, int pfplatform, String openid) {
        this.uid = uid;
        this.rmb = rmb;
        this.riceroll = riceroll;
        this.commit_time = new Date();
        this.complete_time = new Date();
        this.orderid = orderid;
        this.status = OrderStatus.ORS_CREATE.getValue();
        this.platform = mp;
        this.server_ip = server_ip;
        this.client_ip = client_ip;
        this.pfplatform = pfplatform;
        this.openid = openid;
        this.app_id = 1;
    }

    public CashoutRecord(long uid, long rmb, String orderid, int mp, String client_ip, String server_ip,
                         long riceroll, int pfplatform, String openid, String remark) {
        this.uid = uid;
        this.rmb = rmb;
        this.riceroll = riceroll;
        this.commit_time = new Date();
        this.complete_time = new Date();
        this.orderid = orderid;
        this.status = OrderStatus.ORS_CREATE.getValue();
        this.platform = mp;
        this.server_ip = server_ip;
        this.client_ip = client_ip;
        this.pfplatform = pfplatform;
        this.openid = openid;
        this.remark = remark;
        this.app_id = 1;
    }

    public CashoutRecord(long uid, long rmb, String orderid, int mp, String client_ip, String server_ip,
                         long riceroll, int pfplatform, String openid, String remark, int appId) {
        this(uid, rmb, orderid, mp, client_ip, server_ip, riceroll, pfplatform, openid, remark);
        this.app_id = appId;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getApp_status() {
        return app_status;
    }

    public void setApp_status(int app_status) {
        this.app_status = app_status;
    }

    public long getRmb() {
        return rmb;
    }

    public void setRmb(long rmb) {
        this.rmb = rmb;
    }

    public Date getCommit_time() {
        return commit_time;
    }

    public void setCommit_time(Date commit_time) {
        this.commit_time = commit_time;
    }

    public Date getComplete_time() {
        return complete_time;
    }

    public void setComplete_time(Date complete_time) {
        this.complete_time = complete_time;
    }

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }


    public int getPfplatform() {
        return pfplatform;
    }

    public void setPfplatform(int pfplatform) {
        this.pfplatform = pfplatform;
    }

    public String getClient_ip() {
        return client_ip;
    }

    public void setClient_ip(String client_ip) {
        this.client_ip = client_ip;
    }

    public String getServer_ip() {
        return server_ip;
    }

    public void setServer_ip(String server_ip) {
        this.server_ip = server_ip;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public int getCard_id() {
        return card_id;
    }

    public void setCard_id(int card_id) {
        this.card_id = card_id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getApp_id() {
        return app_id;
    }

    public void setApp_id(Integer app_id) {
        this.app_id = app_id;
    }

    public String getPlatformOrderId() {
        return platformOrderId;
    }

    public void setPlatformOrderId(String platformOrderId) {
        this.platformOrderId = platformOrderId;
    }

    @Override
    public String toString() {
        return "CashoutRecord{" +
                "id=" + id +
                ", uid=" + uid +
                ", tid='" + tid + '\'' +
                ", status=" + status +
                ", app_status=" + app_status +
                ", rmb=" + rmb +
                ", commit_time=" + commit_time +
                ", complete_time=" + complete_time +
                ", orderid='" + orderid + '\'' +
                ", platform=" + platform +
                ", pfplatform=" + pfplatform +
                ", client_ip='" + client_ip + '\'' +
                ", server_ip='" + server_ip + '\'' +
                ", riceroll=" + riceroll +
                ", openid='" + openid + '\'' +
                ", card_id=" + card_id +
                ", remark='" + remark + '\'' +
                '}';
    }
}
