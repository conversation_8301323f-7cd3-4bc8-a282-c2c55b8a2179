package com.easylive.pay.common;

import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by xia<PERSON><PERSON> on 15/12/14.
 */
public class RequestAttributeResolver implements HandlerMethodArgumentResolver {

    public Object resolveArgument(MethodParameter methodParameter,
                                  NativeWebRequest nativeWebRequest) {

        RequestAttribute requestAttributeAnnotation = methodParameter.getParameterAnnotation(RequestAttribute.class);

        if (requestAttributeAnnotation != null) {
            HttpServletRequest request = (HttpServletRequest) nativeWebRequest.getNativeRequest();
            return request.getAttribute(requestAttributeAnnotation.value());
        }

        return WebArgumentResolver.UNRESOLVED;
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        RequestAttribute requestAttributeAnnotation = parameter.getParameterAnnotation(RequestAttribute.class);
        return requestAttributeAnnotation != null;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        RequestAttribute requestAttributeAnnotation = parameter.getParameterAnnotation(RequestAttribute.class);
        HttpServletRequest request = (HttpServletRequest) webRequest.getNativeRequest();
        String attrName = requestAttributeAnnotation.value();
        if (attrName == null || attrName.length() == 0) {
            SessionCheck check = parameter.getMethodAnnotation(SessionCheck.class);
            AssetBizCheck bizCheck = parameter.getMethodAnnotation(AssetBizCheck.class);
            if (check == null && bizCheck == null) {
                throw new ResponseException(ResponseError.E_SERVER, "SessionCheck or AssetBizCheck must be defined");
            }

            if (check != null) attrName = "sessionUser";
            if (bizCheck != null) attrName = "assetBiz";
        }
        return request.getAttribute(attrName);
    }
}
