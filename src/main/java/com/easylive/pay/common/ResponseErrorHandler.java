package com.easylive.pay.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Created by xiaomou on 15/12/14.
 */
@ControllerAdvice
public class ResponseErrorHandler {
    private static Logger logger = LoggerFactory.getLogger(ResponseErrorHandler.class);

    @ExceptionHandler(ResponseException.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String handleResponseException(ResponseException exception) {
        if (exception.getError().equals(ResponseError.E_SERVER)) {
            logger.error("Response E_SERVER Error caught", exception);
            return getBasicInternalErrorResponse();
        }
        ResponseObject ro = new ResponseObject(exception.getError(), exception.getMessage());
        return encodeResponse(ro);
    }


    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String handleException(Exception exception) {
        if (exception instanceof org.springframework.web.bind.MissingServletRequestParameterException) {
            ResponseObject ro = new ResponseObject(ResponseError.E_PARAM, "Missing Parameter");
            return encodeResponse(ro);
        } else if (exception instanceof org.apache.catalina.connector.ClientAbortException) {
            logger.warn("Client abort the connection");
            return null;
        } else if (exception instanceof java.io.IOException) {
            if (exception.toString().contains("Broken pipe")) {
                logger.error("Base Exception caught IOException Broken pipe");
                return null;
            } else {
                logger.error("Base Exception caught IOException", exception);
            }
        } else {
            logger.error("Base Exception caught", exception);
        }


        return getBasicInternalErrorResponse();
    }


    private String encodeResponse(ResponseObject ro) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(ro);
        } catch (Exception e) {
            logger.error("Write Error", e);
            return "{\"retval\":\"E_SERVER\",\"reterr\":\"Server Internal Error\"}";
        }

    }

    private String getBasicInternalErrorResponse() {
        ResponseObject ro = new ResponseObject(ResponseError.E_SERVER, "Internal Server Error");
        return encodeResponse(ro);
    }


}
