package com.easylive.pay.common;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;

/**
 * <AUTHOR>
 * @date 15/12/11
 */
public class ResponseObject {
    public final static ResponseObject SUCCESS_INSTANCE = new ResponseObject(ResponseError.E_OK, "");

    private String retval = "ok";
    private Object retinfo = new EmptyResponse();
    private String reterr = "";

    public ResponseObject(Object retinfo) {
        this.retinfo = retinfo;
    }

    public ResponseObject() {
    }

    public ResponseObject(ResponseError error, String msg) {
        if (error.equals(ResponseError.E_OK)) {
            this.retval = "ok";
        } else {
            this.retval = error.toString();
        }
        this.reterr = msg;
    }

    public String getRetval() {
        return retval;
    }

    public void setRetval(String retval) {
        this.retval = retval;
    }

    public Object getRetinfo() {
        return retinfo;
    }

    public void setRetinfo(Object retinfo) {
        this.retinfo = retinfo;
    }

    public String getReterr() {
        return reterr;
    }

    public void setReterr(String reterr) {
        this.reterr = reterr;
    }

    @JsonSerialize
    class EmptyResponse {
        @Override
        public String toString() {
            return "{}";
        }
    }
}


