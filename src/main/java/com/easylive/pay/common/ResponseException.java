package com.easylive.pay.common;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/12/14.
 */
public class ResponseException extends RuntimeException {
    private ResponseError error;

    public ResponseException(ResponseError error) {
        super(error.toString());
        this.error = error;
    }

    public ResponseException(ResponseError error, String msg) {
        super(msg);
        this.error = error;
    }

    public ResponseException(ResponseError error, String msg, Throwable cause) {
        super(error.toString() + ", " + msg, cause);
        this.error = error;
    }

    public ResponseError getError() {
        return this.error;
    }

}
