package com.easylive.pay.common.mgs;

import com.easylive.pay.common.Constants;
import com.easylive.pay.utils.HttpUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.form.FormEncoder;
import feign.httpclient.ApacheHttpClient;
import feign.jackson.JacksonDecoder;

import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2024/1/17
 */
public class MgsServiceBuilder {


    public static <T> T build(String serviceUrl, Class<T> serviceClass) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));

        return  Feign.builder()
                .client(new ApacheHttpClient(HttpUtils.getHttpClient()))
                .encoder(new FormEncoder())
                .decoder(new JacksonDecoder(mapper))
                .errorDecoder(new ResponseErrorDecoder())
                .requestInterceptor(new FeignRequestInterceptor(Constants.PAY_USER_AGENT))
                .target(serviceClass, serviceUrl);
    }
}
