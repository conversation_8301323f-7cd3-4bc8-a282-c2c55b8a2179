package com.easylive.pay.common.mgs;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import feign.codec.ErrorDecoder;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.nio.charset.StandardCharsets;

import static feign.FeignException.errorStatus;

/**
 * <AUTHOR>
 * @date 2019-06-30
 */
public class ResponseErrorDecoder implements ErrorDecoder {
    private Logger logger = LoggerFactory.getLogger(ResponseErrorDecoder.class);

    @Override
    public Exception decode(String methodKey, Response response) {
        String resStr = null;
        try {
            resStr = IOUtils.toString(response.body().asInputStream(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("Error reading feign response: {} ", e.getMessage());
        }

        if (response.status() == HttpStatus.UNPROCESSABLE_ENTITY.value()) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                ResponseModel responseModel = objectMapper.readValue(resStr, ResponseModel.class);
                return new BizException(responseModel.getCode(), responseModel.getMessage());
            } catch (Exception e) {
                logger.error("Error http 422 response: {},  exception: {}", resStr, e.getMessage());
            }
        }

        logger.error("Error http response: {}, {} , {}", resStr, response.status(), response.reason());
        return errorStatus(methodKey, response);
    }
}
