package com.easylive.pay.common.mgs;

/**
 * <AUTHOR>
 * @date 2019-07-05
 */
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

public class FeignRequestInterceptor implements RequestInterceptor{

    private String userAgent;
    public FeignRequestInterceptor(String userAgent) {
        this.userAgent = userAgent;
    }

    @Override
    public void apply(RequestTemplate requestTemplate){
        requestTemplate.header("User-Agent", userAgent );
    }
}
