package com.easylive.pay.common.mgs;

/**
 * <AUTHOR>
 * @date 2019-07-03
 */
public class BizException extends RuntimeException {
    private int code;

    public BizException(int code, String message) {
        super(message);
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String toString() {
        return "code=" + code + ", message=" + getMessage();
    }
}
