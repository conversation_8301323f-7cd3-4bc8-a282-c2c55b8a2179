package com.easylive.pay.common.event;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;

/**
 * Kafka事件类，KafkaEventEmitter中直接使用此类，同时采用Json序列化发送出去
 *
 * <AUTHOR>
 * @date 2019-06-13
 */
public class EventItem<T> {

    /**
     * Topic名称
     */
    @JsonIgnore
    private String topic;

    /**
     * 消息所在Topic的Partition，接收端有效
     */
    @JsonIgnore
    private int partition;

    /**
     * 消息所在Partition的Offset，接收端有效
     */
    @JsonIgnore
    private long offset;

    /**
     * 事件类型，多个事件Model通过一个Topic发送的时候,data可以不同，使用type来区分。type各个Topic自己定义
     */
    private int type;

    /**
     * 事件时间，如果不指定则为创建对象的当前时间
     */
    private Date time;

    /**
     * 事件Model
     */
    private T data;

    /**
     * Kafka Partition用的Key，一般不提供
     */
    @JsonIgnore
    private String key;

    public EventItem() {

    }

    public EventItem(String topic, T data) {
        this.topic = topic;
        this.data = data;
        this.time = new Date();
    }

    public EventItem(String topic, String key, T data) {
        this(topic,data);
        this.key = key;
    }

    public EventItem(String topic, T data, int type) {
        this(topic, data);
        this.type = type;
    }

    public EventItem(String topic, String key, T data, int type) {
        this(topic, data, type);
        this.key = key;
    }

    public EventItem(String topic, String key, T data, int type, Date time) {
        this(topic,key, data, type);
        this.time = time;
    }


    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public int getPartition() {
        return partition;
    }

    public void setPartition(int partition) {
        this.partition = partition;
    }

    public long getOffset() {
        return offset;
    }

    public void setOffset(long offset) {
        this.offset = offset;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public String toString() {
        return "{" +
                "topic='" + topic + '\'' +
                ", key='" + key + '\'' +
                ", type=" + type +
                ", time=" + time.getTime() +
                ", data=" + data.toString() +
                '}';
    }
}
