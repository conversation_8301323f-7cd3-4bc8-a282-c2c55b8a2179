package com.easylive.pay.common.event;

import com.easylive.pay.utils.SystemUtil;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/12/4
 */

public class EventUtil {
    private static final Logger logger = LoggerFactory.getLogger(EventUtil.class);

    public static void resetOffset(String hosts, String group, String topic, int partition, long offset) {

        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, hosts);
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, group);
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);

        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(configProps);
        final TopicPartition[] targetPartition = {null};

        while( true ) {
            consumer.subscribe(Collections.singleton(topic), new ConsumerRebalanceListener() {
                @Override
                public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
                    for (TopicPartition p : partitions) {
                        logger.info("Partition revoked: {}#{}", p.topic(), p.partition());
                        if (p.partition() == partition)
                            targetPartition[0] = null;
                    }
                }

                @Override
                public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
                    logger.info("Some partition assigned. Waiting 3 seconds for revoking");
                    SystemUtil.sleep(3000);
                    for (TopicPartition p : partitions) {
                        logger.info("Partition assigned: {}#{}", p.topic(), p.partition());
                        if (p.partition() == partition)
                            targetPartition[0] = p;
                    }
                }
            });
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(5000));

            if (targetPartition[0] != null) {
                break;
            }
            consumer.unsubscribe();
        }

        Map<TopicPartition, OffsetAndMetadata> offsets = new HashMap<>();
        OffsetAndMetadata offsetMeta = new OffsetAndMetadata(offset, Optional.of(0), null);
        offsets.put(targetPartition[0], offsetMeta);
        consumer.commitSync(offsets);
        consumer.unsubscribe();
        consumer.close();
    }

}
