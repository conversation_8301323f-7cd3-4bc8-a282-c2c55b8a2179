package com.easylive.pay.common.event;

import com.easylive.pay.utils.JsonUtils;
import com.easylive.pay.utils.SystemUtil;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Deque;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
public class EventProducer {

    private static Logger logger = LoggerFactory.getLogger(EventProducer.class);

    private KafkaProducer<String, String> kafkaProducer;
    private Deque<EventItem> eventQueue = new ConcurrentLinkedDeque<>();
    private boolean running;

    private final Object lock = new Object();

    public EventProducer(Map<String, Object> props) {

        Map<String, Object> configProps = new HashMap<>();

        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, "true");
        configProps.put(ProducerConfig.ACKS_CONFIG, "all");
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, "15000");

        configProps.putAll(props);

        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        kafkaProducer = new KafkaProducer<>(configProps);
    }

    public void start(Executor executor) {
        logger.info("Kafka event emitter start...");
        running = true;
        executor.execute(this::loopProcess);
    }

    public void stop() {
        running = false;
        logger.info("Check event that have not been sent");
        int size = eventQueue.size();
        if (size > 0) {
            logger.warn("There are {} transactions not sent. Ready to process or save", size);
            process();
            logger.info("Process complete");
        }
    }

    public <T> void emit(String topic, T data) {
        emit(new EventItem<T>(topic, data));
    }

    public <T> void emit(String topic, String key, T data) {
        emit(new EventItem<T>(topic, key, data));
    }

    public <T> void emit(String topic, int type, String key, T data) {
        emit(new EventItem<T>(topic, key, data, type));
    }


    public void emit(EventItem event) {
        internalSend(event);
    }


    public <T> void emitAsync(String topic, String key, T data) {
        emitAsync(new EventItem<T>(topic, key, data));
    }

    public <T> void emitAsync(String topic, String key, int type, T data) {
        emitAsync(new EventItem<T>(topic, key, data, type));
    }

    public void emitAsync(EventItem event) {
        if (event == null || event.getTopic() == null)
            throw new IllegalArgumentException("Invalid event");

        eventQueue.add(event);

        synchronized (lock) {
            lock.notify();
        }
    }

    private void loopProcess() {

        while (running) {
            try {
                synchronized (lock) {
                    try {
                        lock.wait(3000);
                    } catch (InterruptedException ignored) {
                    }
                }
                process();
            } catch (Exception e) {
                logger.error("Error during loop process", e);
                SystemUtil.sleep(5000);
            }
        }
    }

    synchronized private void process() {

        // TODO: 如果和kafka网络连接出现问题，会出现堆积问题，这时候需要有一种恢复机制。例如保存到本地，或者从数据库恢复。
        //  并且同时需要有基于日志报警机制

        while (true) {
            EventItem event = eventQueue.peek();
            if (event == null)
                break;
            if (internalSend(event)) {
                eventQueue.poll();
            } else {
                SystemUtil.sleep(3000);
            }
        }
    }

    private boolean internalSend(EventItem event) {
        try {
            String msg = JsonUtils.printObject(event);
            Future<RecordMetadata> result = kafkaProducer.send(new ProducerRecord<>(event.getTopic(), event.getKey(), msg));
            RecordMetadata record = result.get();
            event.setPartition(record.partition());
            event.setOffset(record.offset());
            logger.info("Event sent to {} at offset {}, {}", event.getTopic(), record.offset(), event.toString());
            return true;
        } catch (Exception e) {
            logger.error("Error sending kafka event {} , {}", event.toString(), e.getMessage());
        }
        return false;
    }
}

