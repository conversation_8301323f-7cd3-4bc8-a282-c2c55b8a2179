package com.easylive.pay.common.event;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22
 */
public class EventTopic {

    private String topic;

    private Map<Integer, EventTypeInfo> callbacks = new HashMap<>();

    public EventTopic(String topic) {
        this.topic = topic;
    }

    public <T> EventTopic register(int type, Class<T> eventClass, EventListener<T> listener) {
        EventTypeInfo<T> callback = new EventTypeInfo<>();
        callback.setCls(eventClass);
        callback.setListener(listener);
        callbacks.put(type, callback);
        return this;
    }

    public EventTypeInfo getEventTypeInfo(int type) {
        return this.callbacks.get(type);
    }

    public String getTopic() {
        return topic;
    }
}
