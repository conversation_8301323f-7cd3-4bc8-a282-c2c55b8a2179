package com.easylive.pay.common.event;

/**
 * <AUTHOR>
 * @date 2019/11/22
 */
class EventTypeInfo<T> {
    private Class<T> cls;
    private EventListener<T> listener;

    public Class<T> getCls() {
        return cls;
    }

    public void setCls(Class<T> cls) {
        this.cls = cls;
    }

    public EventListener<T> getListener() {
        return listener;
    }

    public void setListener(EventListener<T> listener) {
        this.listener = listener;
    }
}
