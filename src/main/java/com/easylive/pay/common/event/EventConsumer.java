package com.easylive.pay.common.event;

import com.easylive.pay.utils.JsonUtils;
import com.easylive.pay.utils.SystemUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;


/***
 * EventConsumer
 * kafka事件消费封装，主要功能如下：
 * 1. 可以实现多Topic订阅消费，根据Topic进行回调，以及Topic内不同事件的分离回调
 * 2. 支持简单的同步回调，以及复杂的异步回调。异步回调不同Topic之间不会相互阻塞。
 * 3. 支持不同的消费模型，普通消费不需要保存Offset，只需要接收就行了，同时队列超出之后会丢弃数据。可靠消费需要提供Offset保存的接口，同时队列会卡
 * <AUTHOR>
 */
public class EventConsumer {

    private static final Logger logger = LoggerFactory.getLogger(EventConsumer.class);

    enum ConsumerState {
        /**
         * 事件状态,
         */
        STOPPED,
        RUNNING,
        STOPPING;
    }

    private volatile ConsumerState state = ConsumerState.STOPPED;

    private Map<String, PartitionInfo> partitions = new HashMap<>();

    static class PartitionInfo {
        long lastOffset;
        long lastSavedOffset;
        TopicPartition partition;
    }


    private KafkaConsumer<String, String> consumer;
    private Map<String, EventTopic> topics = new HashMap<>();
    private ObjectMapper objectMapper = new ObjectMapper();
    private EventListener<Object> unhandledEventListener = null;

    public EventConsumer(Map<String, Object> props) {

        Map<String, Object> configProps = new HashMap<>(props);
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);

        consumer = new KafkaConsumer<>(configProps);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public EventTopic addTopic(String topic) {
        EventTopic eventTopic = new EventTopic(topic);
        this.topics.put(topic, eventTopic);
        return eventTopic;
    }

    public void setUnhandledEventListener(EventListener<Object> unhandledEventListener) {
        this.unhandledEventListener = unhandledEventListener;
    }

    public EventTopic getTopic(String topic) {
        return this.topics.get(topic);
    }

    public void start(Executor executor) {
        if (state == ConsumerState.STOPPED) {
            executor.execute(this::consumerThread);
            //TODO: offset save
//            executor.execute(this::offsetSavingThread);
        }
    }

    public void stop() {
        logger.info("Stopping consumer service");
        state = ConsumerState.STOPPING;

        while (state != ConsumerState.STOPPED) {
            logger.info("Waiting for consumer service to exit");
            SystemUtil.sleep(1000);
        }
        logger.info("Consumer service exited. Checking offset to save");
        saveAllOffset();
    }


    @SuppressWarnings("unchecked")
    private boolean processMessage(ConsumerRecord<String, String> record, PartitionInfo partition) {
        long offset = record.offset();
        String message = record.value();
        logger.debug("Processing message {}: {}", offset, message);
        try {
            // call back
            EventItem event = JsonUtils.fromString(message, EventItem.class);
            event.setTopic(record.topic());
            event.setOffset(record.offset());
            event.setPartition(record.partition());
            //logger.debug("Event received: {}", event.toString());

            EventTypeInfo typeInfo = null;
            EventTopic topic = getTopic(record.topic());
            if (topic != null)
                typeInfo = topic.getEventTypeInfo(event.getType());


            if (typeInfo != null) {
                String dataString = null;
                try {
                    dataString = JsonUtils.printObject(event.getData());
                    Object x = objectMapper.readValue(dataString, typeInfo.getCls());
                    event.setData(x);
                    //logger.debug("event type:{}", x.getClass().getName());
                    typeInfo.getListener().onTriggered(event);
                } catch (Exception e) {
                    logger.info("Error deserialize data: {},{},{}, {}", event.getType(), typeInfo.getCls().getName(), dataString, e.getMessage());
                    return false;
                }
            } else {
                if( this.unhandledEventListener != null )
                    this.unhandledEventListener.onTriggered(event);
                else
                    logger.debug("unregistered event type:{},{}", event.getTopic(), event.getType());
            }

            logger.debug("Message {} process completes", offset);
            partition.lastOffset = offset;
        } catch (Exception e) {
            logger.info("Error processing {}, {}.", offset, e.getMessage());
            return false;
        }
        return true;
    }


    private void consumerThread() {
        state = ConsumerState.RUNNING;
        consumer.subscribe(topics.keySet(), new ConsumerRebalanceListener() {
            @Override
            public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
                for (TopicPartition partition : partitions) {
                    logger.info("Partition revoked: {}#{}", partition.topic(), partition.partition());
                    revokePartition(partition);
                }
            }

            @Override
            public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
                logger.info("Some partition assigned. Waiting 3 seconds for revoking");
                SystemUtil.sleep(3000);
                for (TopicPartition partition : partitions) {
                    logger.info("Partition assigned: {}#{}", partition.topic(), partition.partition());
                    assignPartition(partition);
                }
            }
        });

        while (state == ConsumerState.RUNNING) {
            runLoop();
        }

        state = ConsumerState.STOPPED;
    }

    private void runLoop() {

        final int maxErrorCount = 3;
        int errorCount = 0;
        do {
            try {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                if (records.count() > 0)
                    logger.debug("{} records retrieved", records.count());
                for (ConsumerRecord<String, String> record : records) {
                    PartitionInfo partition = getPartition(record.topic(), record.partition());
                    if (partition != null) {
                        if (!processMessage(record, partition)) {
                            saveOffset(partition);
                            seekToLastOffset(partition);
                            errorCount++;
                        //    break;
                        } else {
                        }
                    } else {
                        logger.warn("Partition revoked before consumed: {}#{}@{}", record.topic(), record.partition(), record.offset());
                    }
                }

                if (errorCount >= maxErrorCount) {
                    logger.info("Max Error count reached. Sleep for 5 seconds");
                    SystemUtil.sleep(5000);
                    errorCount = 0;
                }

            } catch (org.apache.kafka.common.errors.InterruptException e) {
                logger.info("Consumer polling interrupted");
                SystemUtil.sleep(5000);
            } catch (Throwable e) {
                logger.error("Error when process consumer event.", e);
                SystemUtil.sleep(5000);
            }
        } while (state == ConsumerState.RUNNING && partitionAssigned());

        //   logger.info("Exit Loop");

    }

    private boolean partitionAssigned() {
        return partitions.size() > 0;
    }

    private void saveAllOffset() {
        partitions.forEach((key, partition) -> {
            saveOffset(partition);
        });
    }

    private void saveOffset(PartitionInfo partition) {
        try {
            if (partition.lastSavedOffset != partition.lastOffset) {
                // TODO: offset saver
                partition.lastSavedOffset = partition.lastOffset;
            }
        } catch (Exception ignored) {
        }
    }

    private void assignPartition(TopicPartition partition) {
        PartitionInfo partitionInfo = new PartitionInfo();
        partitionInfo.partition = partition;
        this.partitions.put(getKey(partition), partitionInfo);
        restoreLastOffset(partitionInfo);
        seekToLastOffset(partitionInfo);
    }

    private void revokePartition(TopicPartition partition) {
        String key = getKey(partition);
        PartitionInfo partitionInfo = this.partitions.get(key);
        saveOffset(partitionInfo);
        this.partitions.remove(key);
    }

    private PartitionInfo getPartition(String topic, int partition) {
        return this.partitions.get(getKey(topic, partition));
    }

    private String getKey(TopicPartition partition) {
        return getKey(partition.topic(), partition.partition());
    }

    private String getKey(String topic, int partition) {
        return topic + "_" + partition;
    }

    private void restoreLastOffset(PartitionInfo partition) {
        // lastSavedOffset = lastOffset = configService.getKafkaAssetOffset();
    }

    private void seekToLastOffset(PartitionInfo partition) {

        /*
        if (partition != null) {
            logger.info("Seek to last saved offset {}", partition.lastOffset + 1);
            try {
                    consumer.seek(partition.partition, partition.lastOffset + 1);
            } catch (IllegalStateException e) {
                logger.info("Partition not assigned: {} ", e.getMessage());
                revokePartition(partition.partition);
            }
        } else {
            logger.warn("No partition assigned. Can not seek");
        }
        */

    }

    private void offsetSavingThread() {
        while (state != ConsumerState.STOPPED) {
            saveAllOffset();
            SystemUtil.sleep(1000);
        }
    }
}

