package com.easylive.pay.common;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * Created by w on 2016/12/16.
 */
@Component
@Aspect
public class DataSourceAspect implements Ordered {

    @Pointcut("execution(* com.easylive.pay.service..*(..))")
    public void aspect() {
    }

    @Before("aspect()")
    public void doBefore(JoinPoint point) throws Throwable {
        final MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Method method = methodSignature.getMethod();
        if (method.getDeclaringClass().isInterface()) {
            method = point.getTarget().getClass().getMethod(method.getName(), method.getParameterTypes());
        }
        DataSource dataSource = method.getAnnotation(DataSource.class);
        if (null != dataSource) {
            DataSourceRouteHolder.setDataSourceKey(dataSource.name());
        }
    }

    @After("aspect()")
    public void doAfter() {
        DataSourceRouteHolder.clearDataSourceKey();
    }

    @Override
    public int getOrder() {
        return 1;
    }
}
