package com.easylive.pay.common;

import org.springframework.core.MethodParameter;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * Created by <PERSON>iaomou on 15/12/12.
 */
public class ResponseBodyWrapHandler implements HandlerMethodReturnValueHandler {
    private final HandlerMethodReturnValueHandler delegate;

    public ResponseBodyWrapHandler(HandlerMethodReturnValueHandler delegate) {
        this.delegate = delegate;
    }

    @Override
    public boolean supportsReturnType(MethodParameter returnType) {
        //return returnType.getMethodAnnotation(ResponseObjectBody.class) != null;
        return delegate.supportsReturnType(returnType);
    }

    @Override
    public void handleReturnValue(Object returnValue, MethodParameter returnType, ModelAndViewContainer mavContainer, NativeWebRequest webRequest) throws Exception {
        if (returnType.getMethodAnnotation(ResponseObjectBody.class) != null) {
            ResponseObject result = new ResponseObject(returnValue);
            delegate.handleReturnValue(result, returnType, mavContainer, webRequest);
        } else {
            delegate.handleReturnValue(returnValue, returnType, mavContainer, webRequest);
        }
    }
}
