package com.easylive.pay.common;

/**
 * Created by x<PERSON><PERSON><PERSON> on 15/12/14.
 */
public enum ResponseError {

    /**
     * OK, success
     */
    E_OK(0),
    E_GENERIC(1),
    E_PARAM(2),

    E_SESSION(100),
    E_SERVICE(101),
    E_SERVER(500),
    E_<PERSON>(502),
    E_IMPL(503),
    E_USER(530),
    E_USER_NOT_EXISTS(533),

    E_AUTH(600),

    E_REDPACK_OPEN_REPEAT(700),
    E_REDPACK_NOT_EXISTS(701),
    E_REDPACK_EMPTY(702),
    E_REDPACK_NOT_GROUP_TYPE(703),

    <PERSON>_GUARDIAN_OPTION_NOT_EXIST(710),

    E_GAME_EXCHANGE_FAILD(720),
    E_GAME_SIGN_FAILD(721),
    E_GAME_EXCHANGE_FORBID(722),
    E_GAME_TOTAL_BEAN_NOT_ENOUGH(723),
    E_GAME_COIN_NOT_ENOUGH(724),

    E_GOODS_NOT_EXIST(800),
    E_INVALID_GIFT_TYPE(801),
    E_INVALID_USER_PACKAGE_GIFT(802),
    E_USER_GUARDIAN_LEVEL_LIMIT(803),
    E_USER_NOBLE_LEVEL_LIMIT(804),
    E_FREE_GOODS_NOT_ALLOW(805),


    E_ASSET_AUTH(1000),
    E_ASSET_OPERATION(1001),
    E_ASSET_ECOIN_NOT_ENOUGH(1002),
    E_ASSET_BARLEY_NOT_ENOUGH(1003),
    E_ASSET_RICEROLL_NOT_ENOUGH(1004),
    E_ASSET_ECOIN_INVALID(1005),
    E_ASSET_RICEROLL_INVALID(1006),
    E_ASSET_BARLEY_INVALID(1007),



    E_RECHARGE_OPTION(1100),
    E_RECHARGE_PLATFORM(1101),
    E_RECHARGE_INVALID_APPID(1102),
    E_RECHARGE_ALIPAY_SERVICE(1103),
    E_RECHARGE_MIN_AMOUNT_LIMIT(1104),
    E_RECHARGE_NOT_PAID(1105),
    E_RECHARGE_MGS_APPID(1106),
    E_RECHARGE_INVALID_ORDER_ID(1107),
    E_RECHARGE_INVALID_STATUS(1108),
    E_RECHARGE_OFFLINE_INTERVAL(1109),
    E_RECHARGE_INVALID_FEE(1110),


    E_CASHOUT(1200),
    E_CASHOUT_DAY_LIMIT(1201),
    E_CASHOUT_WEIXIN(1202),
    E_CASHOUT_INVALID(1203),
    E_CASHOUT_INTERVAL(1204),
    E_CASHOUT_NOAUTH(1205),
    E_CASHOUT_NUMLIMIT(1206),
    E_CASHOUT_AMOUNT_LIMIT(1207),
    E_CASHOUT_STATUS(1208),
    E_CASHOUT_WEIXIN_SERVICE(1209),
    E_CASHOUT_OFFLINE(1210),
    E_CASHOUT_NOT_OFFLINE(1211),
    E_CASHOUT_OFFLINE_LIMIT(1212),
    E_CASHOUT_SIGN(1215),


    E_CASHOUT_BANK_CARD_NOT_EXISTS(1213),
    E_CASHOUT_BANK_CARD_BANK_SERVICE(1214),
    E_CASHOUT_CHANNEL_NOT_EXISTS(1216),
    E_CASHOUT_PAUSE_TRANSATION(1230),
    E_USER_BANK_CARD_NOT_AUTHENTICATION(1231),
    E_USER_BANK_CARD_NEED_AUTH(1232),
    E_USER_BANK_CARD_EXISTS(1233),
    E_USER_BANK_CARD_NOT_EXISTS(1234),
    E_USER_BANK_CARD_NOT_YOURS(1235),
    E_USER_BANK_CARD_ACCOUNT_NO_EXISTS(1236),

    //验证错误次数超过5次,请重新发送验证码
    E_SMS_VALIDATE_NUM_OVER_LIMIT(1237),
    //验证码已过有效期
    E_SMS_VALIDATE_HAS_EXPIRED(1238),
    //验证码不在有效期
    E_SMS_VALIDATE_EXPIRED(1239),
    //短信验证码错误，请确认手机号
    E_SMS_PHONE_NOT_RIGHT(1240),
    //短信验证码错误
    E_SMS_CODE_NOT_RIGHT(1241),

    /**
     * oppo支付相关
     */
    E_OPPO_AUTH(1215),

    /**
     * 背包相关
     */
    E_PACKAGE_TOOLS_NOT_EXISTS(1220),

    /**
     * GASH
     */
    E_GASH_WAIT(1230),
    E_GASH_ERROR(1231),
    E_GASH_ORDER(1232),
    E_GASH_ORDER_STATUS(1233),
    E_GASH_ORDER_AMOUNT(1234),
    E_GASH_ORDER_NOT_EXISTS(1235),
    E_GASH_SUB_ORDER_NOT_EXISTS(1236),
    E_GASH_SIGN(1237),

    /**
     * 三方支付通用错误码
     */
    E_THIRD_ERROR(1240),
    E_THIRD_ORDER_STATUS(1241),
    E_THIRD_SIGN(1242),




    //钱包相关
    E_USER_WALLET_MODIFY_ERROR(1237),



    E_APPLEPAY_VALID_FAILD(2001),
    E_APPLEPAY_VALID_NOTIOS(2002),
    E_APPLEPAY_VALID_EMPTYRESPONSE(2003),
    E_APPLEPAY_VALID_USED(2004),
    E_APPLEPAY_VALID_BUNDLE_ID(2005),
    E_WEIXINPAY(2010),
    E_WEIXINPAY_VALID_FAILD(2011),
    E_WEIXINPAY_TYPE(2012),
    E_WEIXINPAY_TRADE_TYPE(2013),
    E_WEIXINPAY_APPLET(2014),
    E_WEIXINPAY_CONFIG(2015),


    E_END(100000);


    private final int value;

    ResponseError(final int newValue) {
        value = newValue;
    }

    public int getValue() {
        return value;
    }
}
