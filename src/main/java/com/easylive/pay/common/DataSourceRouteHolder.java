package com.easylive.pay.common;

/**
 * Created by w on 2016/12/16.
 */
public class DataSourceRouteHolder {

    private static final ThreadLocal<String> dataSources = new ThreadLocal<>();

    public static String getDataSourceKey() {
        return dataSources.get();
    }

    public static void setDataSourceKey(String dataSourceKey) {
        dataSources.set(dataSourceKey);
    }

    public static void clearDataSourceKey() {
        dataSources.remove();
    }
}
