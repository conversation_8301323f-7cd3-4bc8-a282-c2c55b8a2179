package com.easylive.pay.common;

import com.easylive.pay.entity.AssetBiz;
import com.easylive.pay.service.AssetService;
import com.easylive.pay.utils.HttpUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * Created by liweidong on 2016/10/27.
 */
public class AssetBizInterceptor extends HandlerInterceptorAdapter {

    private static Logger logger = LoggerFactory.getLogger(AssetBizInterceptor.class);
    @Autowired
    private AssetService assetService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        HandlerMethod method = (HandlerMethod) handler;
        AssetBizCheck assetBizCheck = method.getMethod().getAnnotation(AssetBizCheck.class);
        if (assetBizCheck == null) {
            return true;
        }
        String ak = request.getParameter("ak");
        String sn = request.getParameter("sn");
        String tp = request.getParameter("tp");
        if (StringUtils.isEmpty(ak) || StringUtils.isEmpty(sn) || StringUtils.isEmpty(tp)) {
            logger.error("[AuthCheck] 缺少认证参数,ak or sn or tp ");
            throw new ResponseException(ResponseError.E_PARAM);
        }

        boolean timeCheck;
        long remote = new Date(Long.parseLong(tp)).getTime();
        long local = Calendar.getInstance().getTime().getTime();
        timeCheck = local - remote <= 300000L;
        if (!timeCheck) {
            logger.error("[AuthCheck] 请求时效认证检查不通过!");
            throw new ResponseException(ResponseError.E_AUTH);
        }

        AssetBiz assetBiz = assetService.getAssetBizByAK(ak);
        if (null == assetBiz || assetBiz.getActive() != 1) {
            logger.error("[AuthCheck] ak=" + ak + ",查找无有效认证信息，认证失败");
            throw new ResponseException(ResponseError.E_AUTH);
        }

        Map<String, String> paramsMap = new TreeMap<>();
        for (Map.Entry<String, String[]> entry : request.getParameterMap().entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().length > 0 ? entry.getValue()[0] : "";
            if (!key.equals("sn")) {
                paramsMap.put(key, value);
            }
        }
        String paramsStr = HttpUtils.toParamString(paramsMap);
        paramsStr = paramsStr + assetBiz.getSk();
        logger.debug("[AuthCheck] param str:" + paramsStr);
        String localSign = DigestUtils.md5Hex(paramsStr);
        logger.debug("[AuthCheck] local sign=" + localSign + "  remote sign=" + sn);
        if (!sn.equals(localSign)) {
            logger.error("[AuthCheck] 签名错误,无法通过认证!");
            throw new ResponseException(ResponseError.E_AUTH);
        }
        request.setAttribute("assetBiz", assetBiz);
        return super.preHandle(request, response, handler);
    }

}
