package com.easylive.pay.common;

import com.easylive.pay.model.User;
import com.easylive.pay.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 15/12/14
 */
public class SessionInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private UserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        HandlerMethod method = (HandlerMethod) handler;
        SessionCheck check = method.getMethod().getAnnotation(SessionCheck.class);
        if (check == null) {
            return true;
        }

        String sessionId = request.getParameter("sessionid");
        if (StringUtils.isEmpty(sessionId)) {
            throw new ResponseException(ResponseError.E_PARAM, "no session");
        }

        User user = userService.getBySession(sessionId);
        request.setAttribute("sessionUser", user);

        return super.preHandle(request, response, handler);
    }
}
