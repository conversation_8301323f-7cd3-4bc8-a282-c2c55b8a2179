package com.easylive.pay.dao;

import com.easylive.pay.entity.CashoutLimitEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import java.util.List;

/**
 * 提现限额
 *
 * <AUTHOR>
 * @date 2019/9/16
 */
@Repository
public interface CashoutLimitRepository extends JpaRepository<CashoutLimitEntity, Integer> {

    CashoutLimitEntity findByUid(long uid);

    @Transactional
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query(value = "from CashoutLimitEntity where uid = ?1")
    CashoutLimitEntity findByUidWithLock(long uid);

    List<CashoutLimitEntity> findByUidIn(List<Long> uidList);
}
