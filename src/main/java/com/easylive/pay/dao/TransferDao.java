package com.easylive.pay.dao;

import com.easylive.pay.entity.TransferRecord;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public class TransferDao extends BaseDao {

    @Transactional
    public void add(String orderId, String tid, long fromUid, long toUid, int ecoin, String description) {
        TransferRecord record = new TransferRecord();
        record.setOrderId(orderId);
        record.setTid(tid);
        record.setFromUid(fromUid);
        record.setToUid(toUid);
        record.setEcoin(ecoin);
        record.setDescription(description);
        record.setCreateTime(new Date());
        this.save(record);
    }


    @SuppressWarnings("unchecked")
    public List<TransferRecord> query(Long fromUid, Long toUid, int start, int limit) {
        Query query = createQuery(fromUid, toUid, false);
        query.setMaxResults(limit);
        query.setFirstResult(start);
        return query.list();
    }

    public long count(Long fromUid, Long toUid) {
        Query query = createQuery(fromUid, toUid, true);
        return (Long) query.uniqueResult();
    }

    private Query createQuery(Long fromUid, Long toUid, boolean count) {
        String sql = "from TransferRecord where 1=1";

        if (count) {
            sql = "select count(*) " + sql;
        }

        if (fromUid != null) {
            sql += " and fromUid = :fromUid";
        }

        if (toUid != null) {
            sql += " and toUid = :toUid";
        }

        if (!count)
            sql += " order by id desc";


        Query query = this.getSession().createQuery(sql);
        if (fromUid != null)
            query.setParameter("fromUid", fromUid);
        if (toUid != null)
            query.setParameter("toUid", toUid);

        return query;
    }
}
