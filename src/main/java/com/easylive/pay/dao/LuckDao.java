package com.easylive.pay.dao;

import com.easylive.pay.entity.LuckPool;
import com.easylive.pay.entity.LuckPoolExplosion;
import com.easylive.pay.entity.LuckRecord;
import com.easylive.pay.entity.LuckTrigger;
import org.hibernate.LockMode;
import org.hibernate.LockOptions;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
@Transactional
public class Luck<PERSON><PERSON> extends BaseDao {
    public void addLuckRecord(LuckRecord lr) {
        this.save(lr);
    }

    public LuckPool getLuckPoll(long id) {
        return (LuckPool) getSession().createQuery("from LuckPool where id=:id")
                .setParameter("id", id).setLockOptions(new LockOptions(LockMode.PESSIMISTIC_WRITE))
                .uniqueResult();
    }

    @SuppressWarnings("unchecked")
    public List<LuckTrigger> getLuckTriggers() {
        return (List<LuckTrigger>) getSession().createQuery("from LuckTrigger order by triggerRate desc")
                .list();
    }

    public void addExplosionRecord(LuckPoolExplosion explosion) {
        this.save(explosion);
    }

    public LuckPoolExplosion getLastExplosionRecord() {
        return (LuckPoolExplosion) getSession().createQuery("from LuckPoolExplosion order by id desc")
                .setMaxResults(1)
                .uniqueResult();
    }
}
