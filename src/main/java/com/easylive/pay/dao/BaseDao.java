package com.easylive.pay.dao;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

@Repository
public class BaseDao {
    private static Logger logger = LoggerFactory.getLogger(BaseDao.class);

    @Autowired
    private SessionFactory _sessionFactory;

    protected Session getSession() {
        return _sessionFactory.getCurrentSession();
    }

    public Serializable save(Object entity) {
        return this.getSession().save(entity);
    }

    public void evict(Object persist) {
        this.getSession().evict(persist);
    }

    public void saveOrUpdate(Object entity) {
        this.getSession().saveOrUpdate(entity);
    }

    public void update(Object entity) {
        this.getSession().update(entity);
    }

    public void mergeUpdate(Object entity) {
        try {
            Method m = entity.getClass().getDeclaredMethod("getId");
            Long id = (Long) m.invoke(entity);
            if (id == null) {
                this.getSession().save(entity);
            } else {
                Object target = this.getById(entity.getClass(), id);
                this.merge(target, entity);
                this.getSession().saveOrUpdate(target);
                this.merge(entity, target);
            }
        } catch (Exception e) {
            logger.error("Error when merge update", e);
        }
    }

    public int deleteById(Class<?> clazz, Long id) {
        String hql = "delete from " + clazz.getName() + " where id=" + id;
        return this.executeUpdateHql(hql);
    }

    public void delete(Object entity) {
        getSession().delete(entity);
    }

    public Object getById(Class<?> clazz, Long id) {
        Session session = getSession();
        return session.get(clazz, id);
//		return session.createQuery("from "+clazz.getName()+" where id="+id).uniqueResult();
    }

    public Object getUniqueResultByHql(String hql) {
        Session session = getSession();
        return session.createQuery(hql).uniqueResult();
    }


    public int executeUpdateHql(String hql) {
//        return this.getSession().bulkUpdate(hql);
        return 0;
    }

    public int executeUpdateHql(String hql, Object o) {
        return 0;
        //       return this.getHibernateTemplate().bulkUpdate(hql, o);
    }

    public int executeQuery(String hql) {
        return this.getSession().createQuery(hql).executeUpdate();
    }

    @SuppressWarnings("rawtypes")
    public List findByHql(String hql) {
        return this.findByHql(hql, 0, 0);
    }

    @SuppressWarnings("rawtypes")
    public List findByHql(String hql, int limit) {
        return this.findByHql(hql, 0, limit);
    }

    @SuppressWarnings("rawtypes")
    public List findByHql(String hql, int start, int limit) {
        Session session = getSession();
        Query query = session.createQuery(hql);
        if (limit != 0) {
            query.setFirstResult(start);
            query.setMaxResults(limit);
        }
        return query.list();
    }

    public void merge(Object dest, Object source) {
        try {

            Field[] fields = source.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                    continue;
                }
                field.setAccessible(true);
                Object value = field.get(source);
                if (value != null) {
                    if (field.getType().isPrimitive()) {
                        String type = field.getType().getName();
                        if ("int".equals(type) && Integer.valueOf(0).equals(value)) {
                            continue;
                        } else if ("long".equals(type) && Long.valueOf(0).equals(value)) {
                            continue;
                        } else if ("double".equals(type) && Double.valueOf(0).equals(value)) {
                            continue;
                        } else if ("boolean".equals(type) && Boolean.FALSE.equals(value)) {
                            continue;
                        } else if ("float".equals(type) && Float.valueOf(0).equals(value)) {
                            continue;
                        }
                    }
                    field.set(dest, value);
                }
            }
        } catch (Exception e) {
            logger.error("error when merging objects", e);
        }
    }

    protected boolean buildArrayInQuery(StringBuilder sb, List<?> values, String columnName, String join) {
        if (values != null && !values.isEmpty()) {
            sb.append(" ").append(join).append(" ");
            sb.append(columnName).append(" ");
            if (values.size() == 1) {
                sb.append(" = ").append(values.get(0));
            } else {
                String s = StringUtils.join(values, ",");
                sb.append(" in (").append(s).append(")");
            }
            return true;
        }
        return false;
    }
}
