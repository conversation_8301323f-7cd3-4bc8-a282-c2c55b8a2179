package com.easylive.pay.dao;

import com.easylive.pay.entity.RechargeControlMap;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface RechargeControlMapRepository extends JpaRepository<RechargeControlMap, Long> {
    List<RechargeControlMap> findByMgsAppIdAndTypeOrderById(int mgsAppId, int type);
    List<RechargeControlMap> findByMgsAppIdAndTypeAndRechargeId(int mgsAppId, int type, int rechargeId);
}
