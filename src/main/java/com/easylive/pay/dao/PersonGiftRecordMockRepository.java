package com.easylive.pay.dao;

import com.easylive.pay.entity.PersonalGiftRecordMock;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PersonGiftRecordMockRepository extends JpaRepository<PersonalGiftRecordMock, Long> {

    @Query(value = "select * from t_personal_gift_record_mock where to_uid=?1 order by id desc limit ?2,?3", nativeQuery = true)
    List<PersonalGiftRecordMock> listPersonGiftRecord(long to, int start, int count);

    List<PersonalGiftRecordMock> findAllByToUid(Long toUid);
}
