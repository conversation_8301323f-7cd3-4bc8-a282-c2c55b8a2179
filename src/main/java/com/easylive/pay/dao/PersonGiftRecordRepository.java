package com.easylive.pay.dao;

import com.easylive.pay.entity.PersonalGiftRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PersonGiftRecordRepository extends JpaRepository<PersonalGiftRecord, Long> {

    @Query(value = "select * from t_personal_gift_record where to_uid=?1 order by id desc limit ?2,?3", nativeQuery = true)
    List<PersonalGiftRecord> listPersonGiftRecord(long to, int start, int count);

}
