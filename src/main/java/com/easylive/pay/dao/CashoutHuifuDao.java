package com.easylive.pay.dao;

import com.easylive.pay.entity.CashoutHuifuRecord;
import com.easylive.pay.entity.CashoutHuifuUserEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public class CashoutHuifuDao extends BaseDao {

    @Transactional(readOnly = true)
    public List<CashoutHuifuRecord> findByStatusIn(List<Integer> status) {
        String hql = "from CashoutHuifuRecord where status in (:status)";
        return getSession().createQuery(hql).setParameterList("status", status).list();
    }


    public List<CashoutHuifuUserEntity> findUserByUid(long uid) {
        String hql = "from CashoutHuifuUserEntity where uid = :uid";
        return (List<CashoutHuifuUserEntity>) getSession().createQuery(hql).setParameter("uid", uid).list();
    }

    public CashoutHuifuUserEntity findUserByAppIdAndUidAndName(int appId, long uid, String name) {
        String hql = "from CashoutHuifuUserEntity where uid = :uid and name = :name and appId = :appId";
        return (CashoutHuifuUserEntity) getSession().createQuery(hql).
                setParameter("uid", uid).
                setParameter("name", name).
                setParameter("appId", appId).
                uniqueResult();
    }

    public CashoutHuifuUserEntity findUserByMemberIdAndAppId(String memberId, int appId) {
        String hql = "from CashoutHuifuUserEntity where memberId = :memberId and appId = :appId";
        return (CashoutHuifuUserEntity) getSession().createQuery(hql)
                .setParameter("memberId", memberId)
                .setParameter("appId", appId)
                .uniqueResult();
    }
}
