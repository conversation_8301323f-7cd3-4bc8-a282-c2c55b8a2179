package com.easylive.pay.dao.jpa;

import com.easylive.pay.entity.YzhMemberEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface YzhMemberRepository extends JpaRepository<YzhMemberEntity, Long>, JpaSpecificationExecutor<YzhMemberEntity> {

    Optional<YzhMemberEntity> findByUidAndAppId(Long uid, Long appId);
} 