package com.easylive.pay.dao.jpa;

import com.easylive.pay.entity.YunzhanghuCashoutRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface YunzhanghuCashoutRecordRepository extends JpaRepository<YunzhanghuCashoutRecord, Long>, JpaSpecificationExecutor<YunzhanghuCashoutRecord> {

    Optional<YunzhanghuCashoutRecord> findByOrderId(String orderId);
} 