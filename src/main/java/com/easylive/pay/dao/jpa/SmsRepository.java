package com.easylive.pay.dao.jpa;

import com.easylive.pay.entity.Sms;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/8/16
 */
@Repository
public interface SmsRepository extends JpaRepository<Sms, Integer> {

    /**
     * 查询短信ID是否在20分钟内存在
     */
    Sms findTopByIdAndAndCreateTimeGreaterThanEqual(Long id, Date createTime);

    /**
     * 更新短信验证状态已验证、时间、关闭时间
     */
    @Transactional
    @Modifying
    @Query(value = "update Sms set status = ?2, verifyTime = ?3, closeTime = ?4 where id = ?1")
    Integer updateStatusAndVerifyTimeAndCloseTime(long id, int status, Date verifyTime, Date closeTime);

    @Transactional
    @Modifying
    @Query(value = "update Sms set verifyTime = ?2 where id = ?1")
    Integer updateVerifyTime(long id, Date verifyTime);

    /**
     * 获取验证码验证
     * @param phone
     * @param appId
     * @param createTime
     * @return
     */
    @Query(value = "select id from t_sms where phone = ?1 and app_id = ?2 and create_time > ?3 and close_time is not null order by id desc limit 1", nativeQuery = true)
    Optional<Integer> findVerifySms(String phone, int appId, Date createTime);
}
