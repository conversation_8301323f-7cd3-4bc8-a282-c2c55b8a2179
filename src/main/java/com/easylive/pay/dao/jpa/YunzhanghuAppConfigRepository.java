package com.easylive.pay.dao.jpa;

import com.easylive.pay.entity.YzhAppEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface YunzhanghuAppConfigRepository extends JpaRepository<YzhAppEntity, Long>, JpaSpecificationExecutor<YzhAppEntity> {

    Optional<YzhAppEntity> findByName(String name);
} 