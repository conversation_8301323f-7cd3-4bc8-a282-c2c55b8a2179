package com.easylive.pay.dao;

import com.easylive.pay.entity.AppleSubRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import java.util.List;

@Repository
public interface AppleSubRecordRepository extends JpaRepository<AppleSubRecord, Long> {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    AppleSubRecord findTopByTransactionId(String transactionId);

    //通过transactionId集合查询订单
    @Query(value = "select transaction_id from t_apple_sub_record where transaction_id in (:transactionIds)", nativeQuery = true)
    List<String> getTransactionIds(@Param("transactionIds") List<String> transactionIds);
}
