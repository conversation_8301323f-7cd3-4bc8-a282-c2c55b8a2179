package com.easylive.pay.dao;

import com.easylive.pay.entity.SysSettingEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SysSettingRepository extends JpaRepository<SysSettingEntity, Integer> {

    Optional<SysSettingEntity> findTopByMetaKeyAndIsDeletedIsFalse(String metaKey);

}
