package com.easylive.pay.dao;

import com.easylive.pay.entity.AppEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public class AppDao extends BaseDao {


    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<AppEntity> getAllApp() {
        return getSession().createQuery("from AppEntity where 1 = 1").list();
    }
}
