package com.easylive.pay.dao;

import com.easylive.pay.entity.UserAppleSub;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface UserAppleSubRepository extends JpaRepository<UserAppleSub, Long> {
    UserAppleSub findTopByOriginalTransactionIdAndProductId(String tid, String productId);

    UserAppleSub findTopByOriginalTransactionId(String tid);

    List<UserAppleSub> findByCancelAndExpireTimeBefore(Boolean cancel, Date expireTime);
}
