package com.easylive.pay.dao;

import com.easylive.pay.entity.GashRechargeRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

@Repository
@Transactional
public interface SubRechargeDao extends JpaRepository<GashRechargeRecord, Integer> {

    @Query(value = "from GashRechargeRecord where orderId = :orderId and platformId = :platformId and platformStatus = :platformStatus")
    GashRechargeRecord findRecordByOrderId(@Param("orderId") String orderId, @Param("platformId") Integer platformId, @Param("platformStatus") Integer platformStatus);

    @Query(value = "from GashRechargeRecord where updateTime > :fromDate and platformId = :platformId and platformStatus in (:platformStatus)")
    List<GashRechargeRecord> listIncompleteOrder(@Param("fromDate") Date fromDate, @Param("platformId") Integer platformId, @Param("platformStatus") List<Integer> platformStatus);

}

