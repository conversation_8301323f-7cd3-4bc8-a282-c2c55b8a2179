package com.easylive.pay.dao;

import com.easylive.pay.entity.GuardianLevel;
import com.easylive.pay.entity.GuardianRelationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * 守护等级
 */
public interface GuardianLevelRepository extends JpaRepository<GuardianLevel, Long> {
    GuardianLevel findByAnchorUidAndUidAndIsDeletedIsFalseAndEndTimeAfter(
            Long anchorUid, Long uid, Date now);
}
