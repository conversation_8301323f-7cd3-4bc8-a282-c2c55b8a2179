package com.easylive.pay.dao;

import com.easylive.pay.entity.AssetBiz;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/10/8.
 */
@Repository
public class AssetDao extends BaseDao {


    public AssetBiz getAssetBizByAK(String ak) {
        return (AssetBiz) getSession().createQuery("from AssetBiz where ak = :ak and active = :active")
                .setParameter("ak", ak).setParameter("active", 1)
                .uniqueResult();
    }

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<AssetBiz> getAllAssetBiz() {
        return getSession().createQuery("from AssetBiz where active = 1").list();
    }
}
