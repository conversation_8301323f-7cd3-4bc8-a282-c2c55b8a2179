package com.easylive.pay.dao;

import com.easylive.pay.entity.AdminTransferRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Repository
public interface AdminTransferRecordRepository extends JpaRepository<AdminTransferRecord, Long> {


    @Query(value = "select * from t_admin_transfer_record where (:to is null or to_uid=:to) and (:from is null or from_uid=:from)" +
            " and (:startTime is null or create_time between :startTime and :endTime) order by id desc limit :start,:count", nativeQuery = true)
    List<AdminTransferRecord> listRecord(@Param("from") Long from, @Param("to") Long to, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("start") Integer start, @Param("count") Integer count);


    @Query(value = "select count(id) as cnt from t_admin_transfer_record where (:to is null or to_uid=:to) and (:from is null or from_uid=:from)" +
            " and (:startTime is null or create_time between :startTime and :endTime)", nativeQuery = true)
    BigInteger countRecord(@Param("from") Long from, @Param("to") Long to, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
