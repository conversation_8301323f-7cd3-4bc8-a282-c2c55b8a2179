package com.easylive.pay.dao;

import com.easylive.pay.entity.*;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.model.RechargeSearchCriteria;
import com.easylive.pay.model.RechargeSearchResult;
import com.easylive.pay.model.RechargeStatByPlatform;
import com.easylive.pay.output.RechargeCount;
import com.easylive.pay.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.LockOptions;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class RechargeDao extends BaseDao {


    public RechargeRecord getByOrderId(String orderId) {
        String hql = "FROM RechargeRecord WHERE order_id = :orderId";
        Query query = this.getSession().createQuery(hql);
        query.setParameter("orderId", orderId);
        return (RechargeRecord) query.uniqueResult();
    }

    public RechargeRecord getByOrderIdWithLock(String orderId) {
        String hql = "FROM RechargeRecord WHERE order_id = :orderId";
        Query query = this.getSession().createQuery(hql);
        query.setLockOptions(LockOptions.UPGRADE);
        query.setParameter("orderId", orderId);
        return (RechargeRecord) query.uniqueResult();
    }

    public RechargeRecord getByPlatformOrderId(String orderId) {
        String hql = "FROM RechargeRecord WHERE pforderid = :orderId";
        Query query = this.getSession().createQuery(hql);
        query.setParameter("orderId", orderId);
        return (RechargeRecord) query.uniqueResult();
    }


    public void completeTradeNo(RechargeRecord r, String tid, String pforderid, long ecoin) {
        r.setTid(tid);
        r.setPlatformOrderId(pforderid);
        r.setCompleteTime(new Date());
        r.setEcoin(ecoin);
        r.setStatus(OrderStatus.ORS_SUCCESS.getValue());
        this.save(r);
    }

    public void completeOrderStatus(RechargeRecord r, int status) {
        r.setCompleteTime(new Date());
        r.setStatus(status);
        this.update(r);
    }

    @SuppressWarnings("unchecked")
    public List<RechargeRecord> getByUid(Long uid, int start, int limit) {

        String query = "from RechargeRecord where uid = :from_uid and " + getValidStatusQuery() + " order by id desc";
        return (List<RechargeRecord>) getSession().createQuery(query)
                .setFirstResult(start).setMaxResults(limit)
                .setParameter("from_uid", uid)
                .list();
    }

    private String getValidStatusQuery() {
        return String.format(" ( ( status = %d or status = %d ) and deleted = 0 ) ", OrderStatus.ORS_SUCCESS.getValue(), OrderStatus.ORS_VALID.getValue());
    }

    @SuppressWarnings("unchecked")
    public RechargeCount getTotalByUid(Long uid) {
        String query = "select sum(rmb), sum(ecoin) + sum(prize) from RechargeRecord where uid = :from_uid and " + getValidStatusQuery();
        List<Object[]> list = (List<Object[]>) getSession().createQuery(query)
                .setParameter("from_uid", uid)
                .list();
        if (list == null) {
            return new RechargeCount();
        }
        Object[] value = list.get(0);
        if (null == value[0]) {
            return new RechargeCount();
        }

        return new RechargeCount((Long) value[0], (Long) value[1]);
    }

    @SuppressWarnings("unchecked")
    public RechargeCount getTotalByUidAndTime(Long uid, Date start, Date end) {
        String query = "select sum(rmb), sum(ecoin) from RechargeRecord where uid = :from_uid and " + getValidStatusQuery() + " and completeTime > :start and completeTime < :end";
        List<Object[]> list = (List<Object[]>) getSession().createQuery(query)
                .setParameter("from_uid", uid)
                .setParameter("start", start)
                .setParameter("end", end)
                .list();

        if (list == null) {
            return new RechargeCount();
        }
        Object[] value = list.get(0);
        if (null == value[0]) {
            return new RechargeCount();
        }

        return new RechargeCount((Long) value[0], (Long) value[1]);
    }

    @SuppressWarnings("unchecked")
    public List<RechargeRecord> getRecordByDate(final int start, final int limit, final Date startDate, final Date endDate) {

        return (List<RechargeRecord>) getSession()
                .createQuery("from RechargeRecord where complete_time >= :startdate and complete_time < :enddate order by complete_time desc")
                .setFirstResult(start).setMaxResults(limit)
                .setTimestamp("startdate", startDate).setTimestamp("enddate", endDate)
                .list();
    }

    @SuppressWarnings("unchecked")
    public List<RechargeRecord> getSuccessRecordByDateAndPlatform(final Date startDate, final Date endDate, int platform) {

        return (List<RechargeRecord>) getSession()
                .createQuery("from RechargeRecord where complete_time >= :startdate and complete_time < :enddate and status = 1 and platform = :platform")
                .setTimestamp("startdate", startDate).setTimestamp("enddate", endDate)
                .setParameter("platform", platform)
                .list();
    }

    @SuppressWarnings("unchecked")
    public List<RechargeRecord> getRecordByUser(long uid, Date startDate, Date endDate) {

        return (List<RechargeRecord>) getSession()
                .createQuery("from RechargeRecord where complete_time >= :startdate and complete_time < :enddate and uid = :from_uid order by complete_time desc")
                .setTimestamp("startdate", startDate).setTimestamp("enddate", endDate).setParameter("from_uid", uid)
                .list();
    }

    @SuppressWarnings("unchecked")
    public List<RechargeRecord> searchRecord(RechargeSearchCriteria criteria) {

        String hql = "from RechargeRecord where 1=1 ";
        String s = buildSearchQuery(criteria);
        if (s.isEmpty()) {
            return new ArrayList<>();
        }

        hql += s;
        hql += " order by id desc";

        log.debug("search query: {}", s);
        Query query = getSession().createQuery(hql);
        if( criteria.getOrderId() == null && criteria.getPlatformOrderId() == null )
            query.setMaxResults(criteria.getCount()).setFirstResult(criteria.getStart());

        return (List<RechargeRecord>) query.list();
    }

    public int countSearchRecord(RechargeSearchCriteria criteria) {
        String hql = "select count(*) from RechargeRecord where 1=1";
        hql = hql + buildSearchQuery(criteria);
        log.debug("search query: {}", hql);
        Query query = getSession().createQuery(hql);
        Long count = (Long) query.uniqueResult();
        return count == null ? 0 : count.intValue();
    }

    public RechargeSearchResult.RechargeSearchStat statAllSearchRecord(RechargeSearchCriteria criteria) {
        String hql = "select sum(rmb), sum(ecoin),count(*),count(distinct uid) from RechargeRecord where 1=1";
        return statSearchRecord(hql, null, criteria);
    }

    public List<RechargeStatByPlatform> statSuccessSearchRecordWithPlatformAndCurrency(RechargeSearchCriteria criteria) {
        String hql = "select sum(rmb), sum(ecoin),count(*),count(distinct uid), platform, currency from RechargeRecord where status in (1)";
        hql += buildSearchQuery(criteria);
        hql += " group by platform, currency";
        Query query = getSession().createQuery(hql);

        log.debug("stat by platform:{}",hql);

        List<RechargeStatByPlatform> ret = new ArrayList<>();
        List<Object[]> list = (List<Object[]>) query.list();
        if( list == null )
            return ret;

        list.forEach( objects -> {
            RechargeStatByPlatform stat = new RechargeStatByPlatform();
            stat.setRmb( (Long)objects[0]);
            stat.setEcoin( (Long)objects[1]);
            stat.setCount( (Long)objects[2] );
            stat.setCountByUser((Long)objects[3]);
            stat.setPlatform((Integer)objects[4]);
            stat.setCurrency((String)objects[5]);
            ret.add(stat);
        });
        return ret;
    }

    public RechargeSearchResult.RechargeSearchStat statSuccessSearchRecord(RechargeSearchCriteria criteria) {
        String hql = "select sum(rmb), sum(ecoin),count(*),count(distinct uid) from RechargeRecord where status in (1)";
        return statSearchRecord(hql, null, criteria);
    }

    public RechargeSearchResult.RechargeSearchStat statSearchRecord(String hql,String hqlSuffix, RechargeSearchCriteria criteria) {
        hql = hql + buildSearchQuery(criteria);
        if( hqlSuffix != null )
            hql = hql + " " + hqlSuffix;

        log.debug("stat query: {}", hql);
        Query query = getSession().createQuery(hql);
        List<Object[]> list = (List<Object[]>) query.list();

        RechargeSearchResult.RechargeSearchStat stat = new RechargeSearchResult.RechargeSearchStat();

        if (list == null) {
            return stat;
        }
        Object[] value = list.get(0);
        if (null == value[0]) {
            return stat;
        }

        stat.setRmb((Long) value[0]);
        stat.setEcoin((Long) value[1]);
        stat.setCount((Long) value[2]);
        stat.setCountByUser((Long) value[3]);


        return stat;
    }


    private String buildSearchQuery(RechargeSearchCriteria criteria) {
        StringBuilder sb = new StringBuilder();

        if( criteria.getOrderId() != null ) {
            sb.append(" and (orderId = '").append(criteria.getOrderId()).append("'");
            sb.append(" or platformOrderId = '").append(criteria.getOrderId()).append("')");
            return sb.toString();
        }

        if( criteria.getPlatformOrderId() != null ) {
            sb.append(" and platformOrderId = '").append(criteria.getPlatformOrderId()).append("'");
            return sb.toString();
        }

        buildArrayInQuery(sb, criteria.getUid(), "uid", "and");
        buildArrayInQuery(sb, criteria.getAppId(), "appId", "and");
        buildArrayInQuery(sb, criteria.getPlatform(), "platform", "and");

        if (criteria.getStartDate() != null) {
            sb.append(" and completeTime > '");
            sb.append(DateUtils.format(criteria.getStartDate()));
            sb.append("'");
        }

        if (criteria.getEndDate() != null) {
            sb.append(" and completeTime < '");
            sb.append(DateUtils.format(criteria.getEndDate()));
            sb.append("'");
        }
        return sb.toString();
    }



    private String buildCompleteTimeQueryRange(Date start, Date end) {
        StringBuilder sb = new StringBuilder();

        if (start != null) {
            sb.append(String.format(" complete_time >= '%s'", DateUtils.format(start)));

        }

        if (end != null) {
            if (start != null) {
                sb.append(" and");
            }
            sb.append(String.format(" complete_time < '%s'", DateUtils.format(end)));
        }

        return sb.toString();
    }

    public long getTotalRechargeRmb(Date start, Date end) {
        String sql = String.format("select sum(rmb) from t_recharge_record where %s and status = 1",
                buildCompleteTimeQueryRange(start, end));
        BigDecimal rmb = (BigDecimal) getSession().createSQLQuery(sql).uniqueResult();

        return rmb == null ? 0 : rmb.longValue();
    }

    public long getTotalRechargePersons(Date start, Date end) {
        String sql = String.format("select count(distinct(uid))from t_recharge_record where %s and status = 1",
                buildCompleteTimeQueryRange(start, end));

        BigInteger persons = (BigInteger) getSession().createSQLQuery(sql).uniqueResult();
        return persons == null ? 0 : persons.longValue();
    }

    public long getTotalRechargeCount(Date start, Date end) {
        String sql = String.format("select count(1) from t_recharge_record where %s and status = 1",
                buildCompleteTimeQueryRange(start, end));
        BigInteger count = (BigInteger) getSession().createSQLQuery(sql).uniqueResult();
        return count == null ? 0 : count.longValue();
    }

    // 已经废除
    @SuppressWarnings("unchecked")
    public List<RechargeOption> getOptionListByDevice(int device) {
        return (List<RechargeOption>) this.getSession().createQuery("from RechargeOption where active = :active and device = :device order by platform asc, rmb asc")
                .setParameter("device", device).setParameter("active", 1L).list();
    }


    // 主要使用
    @SuppressWarnings("unchecked")
    public List<RechargeOption> getOptionListByPlatform(long platform) {
        return (List<RechargeOption>) this.getSession().createQuery("from RechargeOption where active = :active and platform = :platform and app_id = 1 order by rmb asc")
                .setParameter("active", 1L).setParameter("platform", platform).list();
    }

    @SuppressWarnings("unchecked")
    public List<RechargeOption> getOptionListByPlatformAndAppId(long platform, int appId) {
        return (List<RechargeOption>) this.getSession().createQuery("from RechargeOption where active = :active and platform = :platform and app_id = :appid order by rmb asc")
                .setParameter("active", 1L).setParameter("platform", platform)
                .setParameter("appid", appId).list();
    }


    //测试模式使用
    public RechargeOption getOptionByRmb(long platform, long rmb, int device) {
        return (RechargeOption) this.getSession().createQuery("from RechargeOption where platform = :platform and rmb = :rmb and device = :device")
                .setParameter("platform", platform).setParameter("rmb", rmb).setParameter("device", device)
                .uniqueResult();
    }

    public RechargeOption getOptionByRmbAndAppId(long platform, long rmb, int appId) {
        return (RechargeOption) this.getSession().createQuery("from RechargeOption where platform = :platform and rmb = :rmb and app_id = :appId")
                .setParameter("platform", platform).setParameter("rmb", rmb).setParameter("appId", appId)
                .uniqueResult();
    }

    //paypal使用
    public RechargeOption getOptionByRmbAndPayPlatform(long platform, long rmb) {
        return (RechargeOption) this.getSession().createQuery("from RechargeOption where platform = :platform and rmb = :rmb")
                .setParameter("platform", platform).setParameter("rmb", rmb)
                .uniqueResult();
    }

    //苹果 Google 使用
    public RechargeOption getOptionByProductId(long platform, String productid) {
        return (RechargeOption) this.getSession().createQuery("from RechargeOption where platform = :platform and productid = :productid")
                .setParameter("platform", platform).setParameter("productid", productid)
                .uniqueResult();
    }

    public RechargeScale getScaleByCidAndPlatform(String cid, int platform) {
        return (RechargeScale) this.getSession().createQuery("from RechargeScale where cid = :cid and platform = :platform")
                .setParameter("cid", cid)
                .setParameter("platform", platform)
                .uniqueResult();
    }

    @SuppressWarnings("unchecked")
    public List<BuybackRecord> getBuybackRecordByDate(Date start, Date end) {
        return this.getSession().createQuery("from BuybackRecord where createTime >= :start and createTime < :end")
                .setDate("start", start)
                .setDate("end", end).list();
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public List<RechargeWeixinMap> getAllWeixinMap() {
        return this.getSession().createQuery("from RechargeWeixinMap where 1=1").list();
    }

    @Transactional
    public List<RechargeWeixinApp> getAllWeixinApp() {
        return this.getSession().createQuery("from RechargeWeixinApp where 1=1").list();
    }

    //选项标签
    @SuppressWarnings("unchecked")
    public List<RechargeOptionTag> getOptionTagListByOptions(List<Long> options, long appId) {
        return this.getSession().createQuery("from RechargeOptionTag where appId = :appId and optionId in :optionIds")
                .setParameterList("optionIds", options).setParameter("appId", appId).list();
    }
}
