package com.easylive.pay.dao;

import com.easylive.pay.entity.CashoutHuifuUserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CashoutHuifuUserRepository extends JpaRepository<CashoutHuifuUserEntity, Long> {
    CashoutHuifuUserEntity findByUid(Long uid);
    CashoutHuifuUserEntity findByAppIdAndUidAndNameAndCertIdAndCardId(Integer appId, Long uid, String name, String certId, String cardId);
}
