package com.easylive.pay.dao;

import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.entity.WatchPayRecord;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class WatchPayDao extends BaseDao {
    @Transactional(readOnly = true)
    public WatchPayRecord getByUid(long uid, String vid) {
        return (WatchPayRecord) getSession().createQuery("from WatchPayRecord where uid = :uid and vid = :vid")
                .setParameter("uid", uid)
                .setParameter("vid", vid)
                .uniqueResult();
    }

}
