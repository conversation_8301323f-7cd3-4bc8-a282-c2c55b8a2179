package com.easylive.pay.dao;

import com.easylive.pay.entity.GuardianOption;
import com.easylive.pay.entity.GuardianPayRecord;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;


@Repository
@Transactional
public class GuardianDao extends BaseDao {

    public GuardianOption getGuardianById(long id) {
        return (GuardianOption) getById(GuardianOption.class, id);
    }

    public void addPayGuardianRecord(GuardianPayRecord gpr) {
        this.save(gpr);
    }

}

