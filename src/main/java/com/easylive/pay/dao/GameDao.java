package com.easylive.pay.dao;

import com.easylive.pay.entity.DdzBeanPool;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;


@Repository
@Transactional
public class GameDao extends BaseDao {


    public DdzBeanPool getTotal(long id) {

        return (DdzBeanPool) getSession().createQuery("from DdzBeanPool where id = :id")
                .setParameter("id", id)
                .uniqueResult();

    }
}
