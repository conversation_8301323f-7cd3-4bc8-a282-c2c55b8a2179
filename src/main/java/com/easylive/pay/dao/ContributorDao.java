package com.easylive.pay.dao;

import com.easylive.pay.entity.RicerollContributorEntity;
import org.hibernate.LockMode;
import org.hibernate.LockOptions;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public class ContributorDao extends BaseDao {

    @Transactional
    public void addContributor(long riceroll, long fromUid, long toUid) {
        RicerollContributorEntity con = this.getContributor(fromUid, toUid);
        if (con == null) {
            con = new RicerollContributorEntity(fromUid, toUid);
            con.setRiceroll(riceroll);
            this.save(con);
        } else {
            con.setRiceroll(con.getRiceroll() + riceroll);
            this.update(con);
        }
    }

    public RicerollContributorEntity getContributor(long setterId, long getterId) {
        return (RicerollContributorEntity) this.getSession().createQuery("from RicerollContributorEntity where from_uid = :from_uid and to_uid = :to_uid")
                .setParameter("from_uid", setterId).setParameter("to_uid", getterId)
                .setLockOptions(new LockOptions(LockMode.PESSIMISTIC_WRITE))
                .uniqueResult();
    }
}
