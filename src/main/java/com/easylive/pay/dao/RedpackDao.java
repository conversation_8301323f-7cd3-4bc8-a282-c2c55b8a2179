package com.easylive.pay.dao;

import com.easylive.pay.entity.Redpack;
import com.easylive.pay.entity.RedpackNumber;
import com.easylive.pay.entity.SurplusRedpackRecord;
import org.hibernate.LockMode;
import org.hibernate.LockOptions;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Created by c on 2016/1/21.
 */
@Repository
public class RedpackDao extends BaseDao {

    public Redpack getRedpackByCode(String code) {
        return (Redpack) getSession().createQuery("from Redpack where redpack_code = :code")
                .setParameter("code", code).setLockOptions(new LockOptions(LockMode.PESSIMISTIC_WRITE))
                .uniqueResult();
    }

    public int getRedpackStatusByCode(String code) {
        Redpack redpack = (Redpack) getSession().createQuery("from Redpack where redpack_code = :code")
                .setParameter("code", code).uniqueResult();
        if (redpack != null) {
            return redpack.getCount() > redpack.getNumber() ? 0 : 1;
        }
        return -1;
    }

    public RedpackNumber getRedpackNumber(String code, long number) {
        return (RedpackNumber) getSession().createQuery("from RedpackNumber where redpack_code = :code and number = :number")
                .setParameter("code", code).setParameter("number", number).setLockOptions(new LockOptions(LockMode.PESSIMISTIC_WRITE))
                .uniqueResult();
    }

    public RedpackNumber getByCodeAndUid(String code, long uid) {

        return (RedpackNumber) getSession().createQuery("from RedpackNumber where redpack_code = :code and uid = :uid")
                .setParameter("code", code).setParameter("uid", uid)
                .uniqueResult();
    }

    @SuppressWarnings("unchecked")
    public List<RedpackNumber> getListByCode(String code) {

        return (List<RedpackNumber>) getSession().createQuery("from RedpackNumber where redpack_code = :code and status = 1")
                .setParameter("code", code).list();
    }

    @SuppressWarnings("unchecked")
    public void updateSurplusRedPack(Set<String> codes) {
        getSession().createQuery("update from RedpackNumber set status = 2 where redpack_code in :codes and status = 0")
                .setParameterList("codes", codes)
                .executeUpdate();
    }

    @SuppressWarnings("unchecked")
    public void insertSurplusRedPackRecod(long fromUid, long toUid, String vid, long ecoin, long riceroll) {
        SurplusRedpackRecord srr = new SurplusRedpackRecord();
        srr.set(fromUid, toUid, vid, ecoin, riceroll);
        getSession().save(srr);
    }
}
