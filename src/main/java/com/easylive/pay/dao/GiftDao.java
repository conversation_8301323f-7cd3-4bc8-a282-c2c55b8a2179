package com.easylive.pay.dao;


import com.easylive.pay.entity.Goods;
import com.easylive.pay.entity.PersonalGiftRecord;
import com.easylive.pay.entity.GoodsRecord;
import com.easylive.pay.entity.UserGiftStat;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Created by c on 2015/12/14.
 */
@Repository
public class GiftDao extends BaseDao {

    public UserGiftStat getUserGiftStat(long uid) {
        return (UserGiftStat) getSession().createQuery("from UserGiftStat where uid = :uid")
                .setParameter("uid", uid)
                .uniqueResult();
    }

    public Goods getGoodsById(long id) {
        return (Goods) this.getById(Goods.class, id);
    }


    @SuppressWarnings("unchecked")
    public List<Goods> getGoodsList() {
        return (List<Goods>) this.getSession()
                .createQuery("from Goods where active = 1 order by type asc, costtype asc, cost asc")
                .list();
    }

    public void addGoodsRecord(GoodsRecord gr) {
        this.save(gr);
    }

    public void addGoodsPositionRecord(PersonalGiftRecord gr) {
        this.save(gr);
    }

    @SuppressWarnings("unchecked")
    public List<GoodsRecord> getGoodsRecordByDate(final int start, final int limit, final Date startDate, final Date endDate) {

        return (List<GoodsRecord>) getSession()
                .createQuery("from GoodsRecord where time >= :startdate and time < :enddate")
                //.createQuery("from GoodsRecord")
                .setFirstResult(start).setMaxResults(limit)
                .setTimestamp("startdate", startDate).setTimestamp("enddate", endDate)
                .list();
    }

    @SuppressWarnings("unchecked")
    public List<GoodsRecord> getGoodsRecordByUser(final long uid, final Date startDate, final Date endDate, final int type) {
        List<GoodsRecord> goodsList;
        if (type == 1) {
            goodsList = (List<GoodsRecord>) getSession()
                    .createQuery("from GoodsRecord where time >= :startdate and time < :enddate and from_uid =:uid")
                    //.createQuery("from GoodsRecord")
                    .setParameter("uid", uid).setTimestamp("startdate", startDate).setTimestamp("enddate", endDate)
                    .list();
        } else {
            goodsList = (List<GoodsRecord>) getSession()
                    .createQuery("from GoodsRecord where time >= :startdate and time < :enddate and to_uid =:uid")
                    //.createQuery("from GoodsRecord")
                    .setParameter("uid", uid).setTimestamp("startdate", startDate).setTimestamp("enddate", endDate)
                    .list();
        }

        return goodsList;
    }


}
