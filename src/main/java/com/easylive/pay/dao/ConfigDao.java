package com.easylive.pay.dao;

import com.easylive.pay.entity.PayConfig;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public class ConfigDao extends BaseDao {


    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<PayConfig> findAll() {
        return getSession().createQuery("from PayConfig").list();
    }
}
