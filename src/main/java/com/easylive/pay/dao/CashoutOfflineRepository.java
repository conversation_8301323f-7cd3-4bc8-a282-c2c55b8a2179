package com.easylive.pay.dao;

import com.easylive.pay.entity.CashoutOffline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 提现限额
 *
 * <AUTHOR>
 * @date 2019/9/16
 */
@Repository
public interface CashoutOfflineRepository extends JpaRepository<CashoutOffline, Long> {

    @Query(value = "select t1.uid as uid, t1.create_time as createTime, t1.last_time as lastTime, t2.id_card as idCard from t_cashout_offline as t1 left join t_bank_cashout as t2 on t1.uid = t2.uid ", nativeQuery = true)
    List<Object> listCashoutOffline();

    @Query(value = "select t1.uid as uid, t1.create_time as createTime, t1.last_time as lastTime, t2.id_card as idCard from t_cashout_offline as t1 left join t_bank_cashout as t2 on t1.uid = t2.uid " +
            "where t1.uid in (:uids)", nativeQuery = true)
    List<Object> listCashoutOfflineByUids(@Param("uids") List<Long> uids);

}
