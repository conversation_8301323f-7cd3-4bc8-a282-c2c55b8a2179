package com.easylive.pay.dao;

import com.easylive.pay.entity.*;
import com.easylive.pay.utils.DateUtils;
import org.hibernate.LockMode;
import org.hibernate.LockOptions;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public class UserCoinDao extends BaseDao {

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public UserCoin getByUid(long uid) {
        return (UserCoin) getSession().createQuery("from UserCoin where uid = :uid")
                .setParameter("uid", uid)
                .uniqueResult();
    }

    @Transactional(readOnly = true)
    public UserCoin getByUidLock(long uid) {
        return (UserCoin) getSession().createQuery("from UserCoin where uid = :uid")
                .setParameter("uid", uid).setLockOptions(new LockOptions(LockMode.PESSIMISTIC_WRITE))
                .uniqueResult();
    }

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<UserCoin> multiGetByUids(List<Long> uids) {
        return getSession().createQuery("from UserCoin where uid in :uids")
                .setParameterList("uids", uids)
                .list();
    }

    @Transactional

    public UserCoin create(long uid) {
        UserCoin uc = new UserCoin(uid);
        this.save(uc);
        return uc;
    }


    /**
     * 退款恢复当日额度
     *
     * @param rmb
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetAppDayCashout(CashoutLimitEntity cl, CashoutIdentityLimitEntity il, long rmb, Date commitTime) {
        /**
         * 1.获得提现时间，如果是当日则恢复额度，否则不处理;
         * 2.恢复额度 = 当日额度 - 退款额度，同时恢复两张表[t_user_coin][t_cashout_limit]
         * 3.如果当月有退款，恢复身份证月额度
         */
        if (DateUtils.isCurrentDay(commitTime)) {

            long clRecoveryCash = (cl.getDayCashout() - rmb) < 0 ? 0 : (cl.getDayCashout() - rmb);
            getSession().createQuery("update from CashoutLimitEntity set day_cashout = :day_cashout where id = :id")
                    .setParameter("day_cashout", clRecoveryCash)
                    .setParameter("id", cl.getId())
                    .executeUpdate();

            if (il != null) {
                long revCash = (il.getDayCashout() - rmb) < 0 ? 0 : (il.getDayCashout() - rmb);
                getSession().createQuery("update from CashoutIdentityLimitEntity set day_cashout = :day_cashout where id = :id")
                        .setParameter("day_cashout", revCash)
                        .setParameter("id", il.getId())
                        .executeUpdate();
            }
        }

        //3.如果当月有退款，恢复身份证月额度
        if (il != null && DateUtils.isCurrentMonth(commitTime)) {
            long ilRecoveryCash = (il.getMonthCashout() - rmb) < 0 ? 0 : (il.getMonthCashout() - rmb);
            getSession().createQuery("update from CashoutIdentityLimitEntity set month_cashout = :month_cashout where id = :id")
                    .setParameter("month_cashout", ilRecoveryCash)
                    .setParameter("id", il.getId())
                    .executeUpdate();
        }
    }

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<UserCoin> getRecvGiftRank() {
        return (List<UserCoin>) getSession().createQuery("from UserCoin order by accumriceroll desc")
                .setFirstResult(0).setMaxResults(100)
                .list();
    }

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<UserCoin> getSendGiftRank() {
        return (List<UserCoin>) getSession().createQuery("from UserCoin order by costecoin desc")
                .setFirstResult(0).setMaxResults(100)
                .list();
    }

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<UserCoin> getRechargeRank(int start, int count) {
        return (List<UserCoin>) getSession().createQuery("from UserCoin order by accumecoin desc")
                .setFirstResult(start).setMaxResults(count)
                .list();
    }

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<UserCoin> getRechargeRankByEcoin(int start, int count) {
        return (List<UserCoin>) getSession().createQuery("from UserCoin order by ecoin desc")
                .setFirstResult(start).setMaxResults(count)
                .list();
    }

    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public List<Object[]> getTotalAsset() {
        String hql = "select sum(ecoin), sum(barley), sum(riceroll) from UserCoin";
        return (List<Object[]>) getSession().createQuery(hql).list();
    }

    @Transactional(readOnly = true)
    public UserGameBean getUserGameBean(long uid, long platformId) {
        return (UserGameBean) getSession().createQuery("from UserGameBean where uid = :uid and platform_id = :platformId")
                .setParameter("uid", uid)
                .setParameter("platformId", platformId)
                .uniqueResult();
    }
}
