package com.easylive.pay.dao;

import com.easylive.pay.entity.*;
import com.easylive.pay.enums.CashoutPlatform;
import com.easylive.pay.enums.CashoutRecordAppStatus;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.enums.bank.BankType;
import com.easylive.pay.utils.DateUtils;
import org.hibernate.LockMode;
import org.hibernate.LockOptions;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public class CashoutDao extends BaseDao {

    @SuppressWarnings("unchecked")
    @Transactional
    public List<CashoutRecord> getByUid(Long uid, int start, int limit) {

        return (List<CashoutRecord>) getSession().createQuery("from CashoutRecord where uid = :from_uid and commit_time >= :commit_time and pfplatform != :pfplatform order by commit_time desc")
                .setFirstResult(start).setMaxResults(limit)
                .setParameter("from_uid", uid)
                .setParameter("commit_time", DateUtils.getMonthDate(-1))
                .setParameter("pfplatform", CashoutPlatform.Offline.getValue()).list();
    }


    public List<Long> getCashoutUidsByDate(Date start, Date end) {
        return (List<Long>) getSession().createQuery("select distinct(uid) from CashoutRecord where commit_time >= :start and commit_time <= :end")
                .setParameter("start", start)
                .setParameter("end", end)
                .list();
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public long getTotalByUid(Long uid) {
        List<Long> list = (List<Long>) getSession().createQuery("select sum(rmb) from CashoutRecord where uid = :from_uid and status = :status")
                .setParameter("from_uid", uid)
                .setParameter("status", OrderStatus.ORS_SUCCESS.getValue())
                .list();

        if (list.get(0) == null) {
            return 0;
        }
        return list.get(0);
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public long getAppTotalByUid(Long uid) {
        List<Long> list = (List<Long>) getSession().createQuery("select sum(rmb) from CashoutRecord where uid = :from_uid and status = :status and commit_time >= :commit_time and pfplatform != :pfplatform")
                .setParameter("from_uid", uid)
                .setParameter("status", OrderStatus.ORS_SUCCESS.getValue())
                .setParameter("commit_time", DateUtils.getMonthDate(-1))
                .setParameter("pfplatform", CashoutPlatform.Offline.getValue())
                .list();

        if (list.get(0) == null) {
            return 0;
        }
        return list.get(0);
    }

    @SuppressWarnings("unchecked")
    public List<CashoutRecord> getRecordByDate(final int start, final int limit, final Date startDate, final Date endDate) {
        return (List<CashoutRecord>) getSession()
                .createQuery("from CashoutRecord where complete_time >= :startdate and complete_time < :enddate order by complete_time desc")
                .setFirstResult(start).setMaxResults(limit)
                .setTimestamp("startdate", startDate).setTimestamp("enddate", endDate)
                .list();
    }

    @SuppressWarnings("unchecked")
    public List<CashoutRecord> getRecordByUser(final long uid, final Date startDate, final Date endDate) {
        return (List<CashoutRecord>) getSession()
                .createQuery("from CashoutRecord where complete_time >= :startdate and complete_time < :enddate and uid = :from_uid order by complete_time desc")
                .setParameter("from_uid", uid)
                .setTimestamp("startdate", startDate).setTimestamp("enddate", endDate)
                .list();
    }

    public CashoutForbid getByUid(long uid) {
        return (CashoutForbid) getSession().createQuery("from CashoutForbid where uid = :uid")
                .setParameter("uid", uid)
                .uniqueResult();
    }


    @Transactional
    public int delCashoutForbid(long uid) {
        String hql = "delete from CashoutForbid where uid =" + uid;
        return this.getSession().createQuery(hql).executeUpdate();
    }

    @SuppressWarnings("unchecked")
    public List<CashoutWhiteEntity> getAllCashoutWhite() {
        return (List<CashoutWhiteEntity>) getSession().createQuery("from CashoutWhiteEntity where active=1").list();
    }

    public CashoutWhiteEntity getCashoutWhiteByUid(long uid) {
        return (CashoutWhiteEntity) this.getSession().createQuery("from CashoutWhiteEntity where uid = :uid").
                setParameter("uid", uid).uniqueResult();
    }

    @SuppressWarnings("unchecked")
    public List<Richer> getAllRicher() {
        return (List<Richer>) getSession().createQuery("from Richer where status=1").list();
    }

    public void deleteWhiteListByUid(long uid) {
        String hql = "delete from CashoutWhiteEntity where uid =" + uid;
        this.getSession().createQuery(hql).executeUpdate();
    }


    public void addOfflineCashout(long uid) {
        CashoutOffline offline = new CashoutOffline();
        offline.setUid(uid);
        offline.setCreateTime(new Date());
        this.save(offline);
    }

    @Transactional(readOnly = true)
    public CashoutOffline getCashoutOfflineByUid(long uid) {
        String hql = "from CashoutOffline where uid = :uid";
        return (CashoutOffline) this.getSession().createQuery(hql).setParameter("uid", uid).uniqueResult();
    }

    @Transactional
    public void removeOfflineCashout(long uid) {
        String hql = "delete from CashoutOffline where uid =" + uid;
        this.getSession().createQuery(hql).executeUpdate();
    }

    @SuppressWarnings("unchecked")
    public List<CashoutOffline> findAll() {
        String hql = "from CashoutOffline where 1=1";
        return this.findByHql(hql);
    }

    @SuppressWarnings("unchecked")
    public List<CashoutOffline> findByUids(List<Long> uids) {
        String hql = "from CashoutOffline where uid in :uids";
        return this.getSession().createQuery(hql).setParameterList("uids", uids).list();
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public List<CashoutRecord> listAllRecordByUid(Long uid, int start, int limit) {
        String sql = "from CashoutRecord where uid = :uid and commit_time >= :commit_time and pfplatform != :pfplatform order by commit_time desc";
        return (List<CashoutRecord>) getSession().createQuery(sql)
                .setParameter("uid", uid)
                .setParameter("commit_time", DateUtils.getMonthDate(-1))
                .setParameter("pfplatform", CashoutPlatform.Offline.getValue())
                .setFetchSize(start).setMaxResults(limit).list();
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public List<CashoutRecord> listAppRecordByUidAndPlatform(Long uid, CashoutPlatform platform, int start, int limit) {
        String sql = "from CashoutRecord where uid = :uid and pfplatform = :pfplatform and commit_time >= :commit_time order by commit_time desc";
        return (List<CashoutRecord>) getSession().createQuery(sql)
                .setParameter("uid", uid)
                .setParameter("commit_time", DateUtils.getMonthDate(-1))
                .setParameter("pfplatform", platform.getValue())
                .setFetchSize(start).setMaxResults(limit).list();
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public CashoutRecord getRecordById(long id) {
        return (CashoutRecord) getSession().createQuery("from CashoutRecord where id = :id")
                .setParameter("id", id)
                .uniqueResult();
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public CashoutRecordQueue getRecordQueueById(long id) {
        return (CashoutRecordQueue) getSession().createQuery("from CashoutRecordQueue where id = :id")
                .setParameter("id", id)
                .uniqueResult();
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public CashoutRecordQueue getRecordQueueByRecordIdLock(long recordId) {
        return (CashoutRecordQueue) getSession().createQuery("from CashoutRecordQueue where record_id = :record_id")
                .setParameter("record_id", recordId)
                .setLockOptions(new LockOptions(LockMode.PESSIMISTIC_WRITE))
                .uniqueResult();
    }

    @Transactional
    public List<CashoutRecordQueue> getRecordQueueByOrderIds(List<String> orderIds) {
        return (List<CashoutRecordQueue>) getSession().createQuery("from CashoutRecordQueue where orderid in :orderid and is_deleted = :is_deleted")
                .setParameterList("orderid", orderIds)
                .setParameter("is_deleted", 0)
                .list();
    }

    public CashoutRecordQueue getRecordQueueByOrderId(String orderId, BankType bankType, CashoutRecordAppStatus appStatus) {
        return (CashoutRecordQueue) getSession().createQuery("from CashoutRecordQueue where orderid = :orderid and app_status != :app_status and bank_type = :bank_type")
                .setParameter("orderid", orderId)
                .setParameter("app_status", appStatus.getValue())
                .setParameter("bank_type", bankType.getValue())
                .setLockOptions(new LockOptions(LockMode.PESSIMISTIC_WRITE))
                .uniqueResult();
    }

    public CashoutRecordQueue getRecordQueueByOrderId(String orderId, BankType bankType) {
        return (CashoutRecordQueue) getSession().createQuery("from CashoutRecordQueue where orderid = :orderid and bank_type = :bank_type")
                .setParameter("orderid", orderId)
                .setParameter("bank_type", bankType.getValue())
                .setLockOptions(new LockOptions(LockMode.PESSIMISTIC_WRITE))
                .uniqueResult();
    }

    /**
     * 获得所有未确认的三方提现订单
     */
    @SuppressWarnings("unchecked")
    @Transactional
    public List<CashoutRecordQueue> listUnconfirmedAppCashoutRecord(BankType bankType, int appStatus) {
        return (List<CashoutRecordQueue>) getSession().createQuery("from CashoutRecordQueue where app_status = :app_status and platform = :platform and bank_type = :bank_type and is_deleted = :is_deleted")
                .setParameter("app_status", appStatus)
                .setParameter("platform", MobilePlatform.MPF_APP.getValue())
                .setParameter("bank_type", bankType.getValue())
                .setParameter("is_deleted", 0)
                .setMaxResults(100)
                .list();
    }

    /**
     * 获得所有3天前成功的三方提现订单
     */
    @SuppressWarnings("unchecked")
    @Transactional
    public List<CashoutRecordQueue> listSuccessAppCashoutRecord(Date completeTime, int bankType) {
        return (List<CashoutRecordQueue>) getSession().createQuery("from CashoutRecordQueue where complete_time <= :complete_time and app_status = :app_status and platform = :platform and bank_type = :bank_type and is_check = :is_check and is_deleted = :is_deleted")
                .setParameter("complete_time", completeTime)
                .setParameter("app_status", CashoutRecordAppStatus.ACCOUNT_SUCCESS.getValue())
                .setParameter("platform", MobilePlatform.MPF_APP.getValue())
                .setParameter("bank_type", bankType)
                .setParameter("is_check", 0)
                .setParameter("is_deleted", 1)
                .setMaxResults(100)
                .list();
    }

    @SuppressWarnings("unchecked")
    @Transactional
    public List<CashoutRecordQueue> listRequestingAppCashoutRecord(Date commitTime, int bankType, int appStatus) {
        return (List<CashoutRecordQueue>) getSession().createQuery("from CashoutRecordQueue where commit_time <= :commit_time and app_status = :app_status and platform = :platform and bank_type = :bank_type and is_deleted = :is_deleted")
                .setParameter("commit_time", commitTime)
                .setParameter("app_status", appStatus)
                .setParameter("platform", MobilePlatform.MPF_APP.getValue())
                .setParameter("bank_type", bankType)
                .setParameter("is_deleted", 0)
                .list();
    }

    @Transactional
    public List<CashoutRecordQueue> queryTimeRangeRequestingAppCashoutRecord(Date commitTime, int bankType, int appStatus) {
        return (List<CashoutRecordQueue>) getSession().createQuery("from CashoutRecordQueue where commit_time >= :commit_time and app_status = :app_status and platform = :platform and bank_type = :bank_type and is_deleted = :is_deleted")
                .setParameter("commit_time", commitTime)
                .setParameter("app_status", appStatus)
                .setParameter("platform", MobilePlatform.MPF_APP.getValue())
                .setParameter("bank_type", bankType)
                .setParameter("is_deleted", 0)
                .list();
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateCashRecordSuccess(CashoutRecordQueue recordQueue,
                                        CashoutRecordAppStatus appStatus,
                                        OrderStatus orderStatus,
                                        String resCode,
                                        String resMsg,
                                        String resStatus) {
        int recordCount = getSession().createQuery("update from CashoutRecord set status = :status, app_status = :app_status, complete_time = :complete_time where id = :id and app_status = :db_app_status")
                .setParameter("status", orderStatus.getValue())
                .setParameter("app_status", appStatus.getValue())
                .setParameter("complete_time", new Date())
                .setParameter("id", recordQueue.getRecord_id())
                .setParameter("db_app_status", recordQueue.getApp_status())
                .executeUpdate();

        int queueCount = getSession().createQuery("update from CashoutRecordQueue set status = :status, app_status = :app_status, complete_time = :complete_time, pforderid = :pforderid, res_code = :res_code, res_msg = :res_msg, res_status = :res_status, is_deleted = :is_deleted where id = :id and app_status = :db_app_status")
                .setParameter("status", orderStatus.getValue())
                .setParameter("app_status", appStatus.getValue())
                .setParameter("complete_time", new Date())
                .setParameter("pforderid", recordQueue.getPforderid())
                .setParameter("res_code", resCode)
                .setParameter("res_msg", resMsg)
                .setParameter("res_status", resStatus)
                .setParameter("is_deleted", 1)
                .setParameter("id", recordQueue.getId())
                .setParameter("db_app_status", recordQueue.getApp_status())
                .executeUpdate();
        return recordCount != 0 && queueCount!= 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCashRecordCheckStatusById(long id) {
        getSession().createQuery("update from CashoutRecordQueue set is_check = :is_check where id = :id")
                .setParameter("is_check", 1)
                .setParameter("id", id)
                .executeUpdate();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCashRecordCheckStatusById(long id, int isCheck) {
        getSession().createQuery("update from CashoutRecordQueue set is_check = :is_check where id = :id")
                .setParameter("is_check", isCheck)
                .setParameter("id", id)
                .executeUpdate();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateQueueByRecordId(long recordId,
                                      String resCode,
                                      String resMsg,
                                      String resStatus) {
        getSession().createQuery("update from CashoutRecordQueue set res_code = :res_code, res_msg = :res_msg, res_status = :res_status where record_id = :record_id")
                .setParameter("res_code", resCode)
                .setParameter("res_msg", resMsg)
                .setParameter("res_status", resStatus)
                .setParameter("record_id", recordId)
                .executeUpdate();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRecordAndQueueByRecordId(long recordId,
                                      String pforderid,
                                      int appStatus,
                                      int dbAppStatus) {

        getSession().createQuery("update from CashoutRecord set app_status = :app_status where id = :id and app_status = :db_app_status")
                .setParameter("app_status", appStatus)
                .setParameter("id", recordId)
                .setParameter("db_app_status", dbAppStatus)
                .executeUpdate();

        getSession().createQuery("update from CashoutRecordQueue set pforderid = :pforderid, app_status = :app_status where record_id = :record_id and app_status = :db_app_status")
                .setParameter("app_status", appStatus)
                .setParameter("record_id", recordId)
                .setParameter("db_app_status", dbAppStatus)
                .setParameter("pforderid", pforderid)
                .executeUpdate();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLastCashoutTime(long uid, Date date) {
        getSession().createQuery("update from CashoutOffline set last_time = :last_time where uid = :uid")
                .setParameter("last_time", date)
                .setParameter("uid", uid)
                .executeUpdate();
    }

    public CashoutOfflineInfoEntity getCashoutOfflineInfoByUid(long uid) {
        return (CashoutOfflineInfoEntity) getSession().createQuery("from CashoutOfflineInfoEntity where uid = :uid")
                .setParameter("uid", uid)
                .uniqueResult();
    }
}
