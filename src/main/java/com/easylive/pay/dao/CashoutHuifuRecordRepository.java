package com.easylive.pay.dao;

import com.easylive.pay.entity.CashoutHuifuRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CashoutHuifuRecordRepository extends JpaRepository<CashoutHuifuRecord, Long> {
    CashoutHuifuRecord findByOrderId(String orderId);
    List<CashoutHuifuRecord> findByStatusIn(List<Integer> status);
}
