package com.easylive.pay.dao;

import com.easylive.pay.entity.CashoutIdentityLimitEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;

/**
 * 每月限额
 *
 * <AUTHOR>
 * @date 2019/12/11
 */
@Repository
public interface CashoutIdentityLimitRepository extends JpaRepository<CashoutIdentityLimitEntity, Integer> {

    CashoutIdentityLimitEntity findByIdCard(String idCard);

    @Transactional
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    CashoutIdentityLimitEntity findTopByIdCard(String idCard);

}
