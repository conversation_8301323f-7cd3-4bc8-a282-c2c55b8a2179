package com.easylive.pay.dao;

import com.easylive.pay.entity.UserPackageTools;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface UserPackageToolsRepository extends JpaRepository<UserPackageTools, Integer> {

    UserPackageTools findTopByUidAndToolId(long uid, int toolId);

    @Modifying
    @Transactional
    @Query("update UserPackageTools set number = number + ?3 where uid = ?1 and tool_id = ?2")
    Integer incTools(long uid, int toolsId, int number);

    @Modifying
    @Transactional
    @Query("update UserPackageTools set number = number + ?2 where id = ?1")
    Integer incToolsById(int id, int number);

}
