package com.easylive.pay.dao;

import com.easylive.pay.entity.UserBankCard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 银行卡
 *
 * <AUTHOR>
 * @date 2019/8/16
 */
@Repository
public interface UserBankCardRepository extends JpaRepository<UserBankCard, Integer> {

    /**
     * 查询用户银行卡
     */
    UserBankCard findTopByUidAndIsDeletedIsFalse(Long uid);

    /**
     * 查询用户银行卡
     */
    UserBankCard findTopById(Integer id);

    /**
     * 查询所有银行卡
     */
    List<UserBankCard> findAllByIsDeletedIsFalse();

    /**
     * 查询所有银行卡
     */
    @Query(value = "select * from t_user_bank_card where is_deleted = 0 order by id asc limit :start, :cnt", nativeQuery = true)
    List<UserBankCard> listUSerBankCard(@Param("start") int start, @Param("cnt") int cnt);

    /**
     * 查询银行卡号是否存在
     */
    UserBankCard findTopByAccountNoAndIsDeletedIsFalse(String accountNo);
    UserBankCard findTopByAccountNoAndIdNotAndIsDeletedIsFalse(String accountNo, Integer id);

    List<UserBankCard> findByUidIn(List<Long> uids);
}
