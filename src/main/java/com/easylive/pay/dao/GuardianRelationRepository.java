package com.easylive.pay.dao;

import com.easylive.pay.entity.GuardianRelationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface GuardianRelationRepository extends JpaRepository<GuardianRelationEntity, Long> {
    GuardianRelationEntity findByAnchorUidAndUidAndGuardianIdAndIsDeletedIsFalseAndEndTimeAfter(
            Long anchorUid, Long uid, Integer guardianId, Date now);

    @Query(value = "from GuardianRelationEntity where uid = ?1 and anchorUid = ?2 and endTime > ?3 and isDeleted = false")
    List<GuardianRelationEntity> getRelation(long fromUid, long toUid, Date endTime);

    @Query(value = "from GuardianRelationEntity where anchorUid = ?1 and uid in (?2) and endTime > ?3 and isDeleted = false and isDown = 0")
    List<GuardianRelationEntity> getRelationOfAnchorAndUsers(long anchorUid, List<Long> uids, Date endTime);
}
