package com.easylive.pay.job;

import com.easylive.pay.service.ContributorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019-03-13
 */
@Service
public class ContributorBuilder {
    private static Logger logger = LoggerFactory.getLogger(ContributorBuilder.class);

    @Autowired
    private ContributorService contributorService;

    @Scheduled(fixedDelay = 1000)
    public void buildRank() {
        contributorService.buildContributor();
    }

}
