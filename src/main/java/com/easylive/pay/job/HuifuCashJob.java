package com.easylive.pay.job;

import com.easylive.pay.entity.CashoutHuifuRecord;
import com.easylive.pay.service.huifu.HuifuPayService;
import com.easylive.pay.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;

@Slf4j
@Service
public class HuifuCashJob extends ZkBaseJob {

    private final static String name = "huifu-cash";

    public HuifuCashJob() {
        super(name);
    }

    @Autowired
    private CuratorFramework curator;

    @Autowired
    private HuifuPayService huifuPayService;

    @PostConstruct
    private void init () {
        this.init(curator);
    }

    @PreDestroy
    private void destroy () {
        this.stop();
    }

    @Scheduled(initialDelay = 5*1000L, fixedDelay = 60*1000L)
    private void execute() {
        this.execute(this::huifuCheckOrder);
    }

    private void huifuCheckOrder() {
        log.debug("Check Huifu order");
        List<CashoutHuifuRecord> records = huifuPayService.listCashoutRecordForProcessing();
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        log.info("{} huifu cashout records needs to process", records.size());
        records.forEach(record -> {
            log.info("HuifuBankCashout processOrders process order. record={}", record.toString());
            try {
                huifuPayService.processOrder(record);
                SystemUtil.sleep(3000);
            } catch (Exception e) {
                log.error("HuifuBankCashout processOrders error. record={}", record, e);
            }
        });
    }
}
