package com.easylive.pay.job;

import com.easylive.pay.component.OssComponent;
import com.easylive.pay.service.*;
import com.easylive.pay.service.apple.ApplePayService;
import com.easylive.pay.service.yzh.YunzhanghuService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019-03-13
 */
@Service
public class DataReloader {
    private static Logger logger = LoggerFactory.getLogger(DataReloader.class);
    @Autowired
    private AssetService assetService;

    @Autowired
    private SpecialUserService specialUserService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private AppService appService;

    @Autowired
    private ApplePayService applePayService;

    @Autowired
    private GiftService giftService;

    @Autowired
    private OssComponent ossComponent;


    @Autowired
    private YunzhanghuService yunzhanghuService;

    @Scheduled(fixedDelay = 300000, initialDelay = 300000)
    public void reloadAssetBiz() {
        assetService.loadAssetBiz();
    }


    @Scheduled(fixedDelay = 60000, initialDelay = 0)
    public void reloadSpecialUser() {
        specialUserService.load();
    }


    @Scheduled(fixedDelay = 180000, initialDelay = 60000)
    public void reloadConfig() {
        configService.reload();
        applePayService.reload();
    }

    @Scheduled(fixedDelay = 120000, initialDelay = 60000)
    public void reloadApp() {
        appService.load();
    }

    @Scheduled(fixedDelay = 120000, initialDelay = 60000)
    public void reloadUpload() {
        ossComponent.domainLoad();
    }

    @Scheduled(fixedDelay = 600000, initialDelay = 60000)
    public void updateYzhContractInfo() {
        yunzhanghuService.updateContractInfo();
    }
}
