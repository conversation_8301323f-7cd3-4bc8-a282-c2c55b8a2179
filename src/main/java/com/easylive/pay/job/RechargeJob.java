package com.easylive.pay.job;

import com.easylive.pay.service.RedpackService;
import com.easylive.pay.service.gash.GashPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * <AUTHOR>
 * @date 2019/12/9
 */
@Slf4j
@Service
public class RechargeJob extends ZkBaseJob {

    private final static String name = "gash-recharge";

    public RechargeJob() {
        super(name);
    }

    @Autowired
    private CuratorFramework curator;

    @Autowired
    private GashPayService gashPayService;

    @PostConstruct
    private void init () {
        this.init(curator);
    }

    @PreDestroy
    private void destroy () {
        this.stop();
    }

    @Scheduled(initialDelay = 30*1000L, fixedDelay = 300*1000L)
    private void execute() {
        this.execute(this::gashCheckOrder);
    }

    private void gashCheckOrder() {
        gashPayService.gashCheckOrder();
    }
}
