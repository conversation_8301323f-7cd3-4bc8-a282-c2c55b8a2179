package com.easylive.pay.job;

import com.easylive.pay.component.RedisOperator;
import com.easylive.pay.enums.bank.BankType;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.apple.ApplePayService;
import com.easylive.pay.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 银行定时任务
 *
 * <AUTHOR>
 * @date 2019/8/22
 */
@Service
public class BankCron {

    @Autowired
    private CashoutService cashoutService;

    @Autowired
    private RedisOperator redisOperator;

    @Autowired
    private ApplePayService applePayService;

    private final String redisClientId = UUID.randomUUID().toString();

    private static final Logger logger = LoggerFactory.getLogger(DataReloader.class);


    /**
     * 检查apple订阅vip没回调情况
     */
    @Scheduled(initialDelay = 60*1000L, fixedDelay = 3600*1000L)
    public void checkUserSubRecord() {
        logger.info("Check apple sub record Start! redisClientId={}", redisClientId);
        if (redisOperator.getDistributedLock(redisClientId)) {
            applePayService.checkUserSubRecord();
        }
    }


    /**
     * 定时查询第三方银行是否到账（每5分钟/次）
     */
//    @Scheduled(cron = "0 */5 * * * *")
    public void checkBankAccountStatus() {
        logger.info("Check Bank Account Status Start! redisClientId={}", redisClientId);
        if (redisOperator.getDistributedLock(redisClientId)) {
            logger.info("Check Bank Account Status Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.checkProcessingOrder(BankType.XZYQ);
        }
    }


    /**
     * 校准查询第三方银行不到账情况3天前(每天10分钟/次)
     */
//    @Scheduled(cron = "0 */10 * * * *")
    public void checkBankInfoStatus() {
        logger.info("Check Bank Info Status Start! redisClientId={}", redisClientId);
        if (redisOperator.getDistributedLock(redisClientId)) {
            logger.info("Check Bank Info Status Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.checkSuccessOrder(BankType.XZYQ,DateUtils.day3BeforeNow());
        }
    }

    /**
     * 定时查询税优地是否到账（每5分钟/次）
     */
 //   @Scheduled(cron = "0 */5 * * * *")
    public void checkSydAccountStatus() {
        logger.info("Check Syd Account Status Start! redisClientId={}", redisClientId);
        if (redisOperator.getDistributedLock(redisClientId)) {
            logger.info("Check Syd Account Status Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.checkProcessingOrder(BankType.SYD);
        }
    }

    /**
     * 定时查询税筹是否到账（每5分钟/次）
     */
 //   @Scheduled(cron = "0 */5 * * * *")
    public void checkScAccountStatus() {
        logger.info("Check Sc Status Start! redisClientId={}", redisClientId);
        if (redisOperator.getDistributedLock(redisClientId)) {
            logger.info("Check Sc Status Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.checkRequestingOrder(BankType.TAX);
        }
    }

    /**
     * 校准提现请求是否真是成功（每2分钟/次）
     */
//    @Scheduled(initialDelay = 3000, fixedDelay = 120000)
    public void checkBankAccountRequesting() {
        logger.info("[checkBankAccountRequesting] Check bank Account requesting Start! redisClientId={}", redisClientId);
        if (redisOperator.getDistributedLock(redisClientId)) {
            logger.info("[checkBankAccountRequesting] Check bank Account requesting Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.checkRequestingOrder(BankType.XZYQ);
        }
    }

    /**
     * 玖合订单批次请求提现任务,1小时执行一次
     * <AUTHOR>
     * @date 2023/11/21
     **/
//    @Scheduled(initialDelay = 3000, fixedDelay = 1000 * 60 * 5)
   // @Scheduled(cron = "0 0 0/1 * * ?")
    public void jiuHeWithDrawScheduled() {
        logger.info("[jiuHeWithDrawScheduled] order batch requesting Start! redisClientId={}", redisClientId);
        if (redisOperator.getOrderDistributedLock(redisClientId)) {
            logger.info("[jiuHeWithDrawScheduled] order batch requesting Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.batchCheckRequestingOrder(BankType.JIUHE, DateUtils.getBeforeDateForDay(1));
        }
    }

    /**
     * 轮询玖合未完成的提现订单
     * <AUTHOR>
     * @date 2023/11/21
     **/
    //@Scheduled(initialDelay = 5000, fixedDelay = 1000 * 60 * 3)
    public void jiuHeProcessOrderScheduled() {
        logger.info("[jiuHeProcessOrderScheduled] order query requesting Start! redisClientId={}", redisClientId);
        if (redisOperator.getOrderDistributedLock(redisClientId)) {
            logger.info("[jiuHeProcessOrderScheduled] order query requesting Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.checkProcessingOrder(BankType.JIUHE, DateUtils.getBeforeDateForMinute(2));
        }
    }

    /**
     * 轮询玖合3小时之前 未成功请求的提现订单
     * <AUTHOR>
     * @date 2023/11/21
     **/
    //@Scheduled(initialDelay = 10000, fixedDelay = 1000 * 60 * 60)
    public void jiuHeNotRequestWithdrawOrderScheduled() {
        logger.info("[jiuHeNotRequestWithdrawOrderScheduled] order query requesting Start! redisClientId={}", redisClientId);
        if (redisOperator.getOrderDistributedLock(redisClientId)) {
            logger.info("[jiuHeNotRequestWithdrawOrderScheduled] order query requesting Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.checkRequestingOrder(BankType.JIUHE, DateUtils.getBeforeDateForMinute(180));
        }
    }


    /**
     * 未成功请求的提现订单
     **/
    @Scheduled(initialDelay = 10000, fixedDelay = 1000 * 60 * 10)
    public void yzhNotRequestWithdrawOrderScheduled() {
        logger.info("[yzhNotRequestWithdrawOrderScheduled] order query requesting Start! redisClientId={}", redisClientId);
        if (redisOperator.getOrderDistributedLock(redisClientId)) {
            logger.info("[yzhNotRequestWithdrawOrderScheduled] order query requesting Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.checkRequestingOrder(BankType.YUNZHANGHU, DateUtils.getBeforeDateForMinute(30));
        }
    }



    /*
    public void processNotSuccessOrderScheduled() {
        logger.info("[processNotSuccessOrderScheduled] Check Bank Account Status Start! redisClientId={}", redisClientId);
        if (redisOperator.getDistributedLock(redisClientId)) {
            logger.info("[processNotSuccessOrderScheduled] Check Bank Account Status Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.xinYunBankCashoutNotSuccessOrder();
        }
    }*/

    /*
    @Scheduled(initialDelay = 3000, fixedDelay = 120000)
    public void processConfirmingOrderScheduled() {
        logger.info("[processConfirmingOrderScheduled] Check bank Account requesting Start! redisClientId={}", redisClientId);
        if (redisOperator.getDistributedLock(redisClientId)) {
            logger.info("[processConfirmingOrderScheduled] Check bank Account requesting Get Lock Success! redisClientId={}", redisClientId);
            cashoutService.xinYunBankCashoutConfirmingOrder();
        }
    }*/

    /**
     * 轮询汇付未完成的提现订单
     **/
    @Scheduled(initialDelay = 5000, fixedDelay = 30000)
    public void huifuProcessOrderScheduled() {
        logger.info("[huifuProcessOrderScheduled] order query requesting Start! redisClientId={}", redisClientId);
        if (redisOperator.getOrderDistributedLock(redisClientId)) {
            logger.info("[huifuProcessOrderScheduled] order query requesting Get Lock Success! redisClientId={}", redisClientId);
            try {
                cashoutService.checkProcessingOrder(BankType.HUIFU, DateUtils.getBeforeDateForMinute(1));
            } catch (Exception e) {
                logger.error("huifuProcessOrderScheduled error", e);
            }
        }
    }
}
