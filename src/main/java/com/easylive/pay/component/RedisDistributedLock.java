package com.easylive.pay.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@Slf4j
public class RedisDistributedLock {

    private final StringRedisTemplate template;
    private final String lockKey;
    private final long expireTimeOutInMillis;

    private static final String LOCK_VALUE = "locked";

    public RedisDistributedLock(StringRedisTemplate template, String lockKey, long expireTimeOutInMillis) {
        this.template = template;
        this.lockKey = lockKey;
        this.expireTimeOutInMillis = expireTimeOutInMillis;
    }

    public synchronized boolean tryLock() {
        String script =
                "if redis.call('setnx', KEYS[1], ARGV[1]) == 1 then " +
                        "redis.call('pexpire', KEYS[1], ARGV[2]) " +
                        "return 1 " +
                        "else " +
                        "return 0 " +
                        "end";
        RedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);
        Long result = template.execute(redisScript, Collections.singletonList(lockKey), LOCK_VALUE, String.valueOf(expireTimeOutInMillis));
        return result != null && result == 1;
    }

    public boolean lock() {
        try {
            return tryLock(expireTimeOutInMillis);
        } catch (Exception e) {
            log.error("RedisDistributedLock tryLock error", e);
        }
        return false;
    }

    public boolean tryLock(long waitTimeInMillis) throws InterruptedException {
        long end = System.currentTimeMillis() + waitTimeInMillis;
        while (System.currentTimeMillis() < end) {
            if (tryLock()) {
                return true;
            }
            Thread.sleep(100); // 等待100毫秒再尝试获取锁
        }
        return false;
    }

    public boolean releaseLock() {
        String script =
                "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                        "return redis.call('del', KEYS[1]) " +
                        "else " +
                        "return 0 " +
                        "end";
        RedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);
        Long result = this.template.execute(redisScript, Collections.singletonList(lockKey));
        return RELEASE_SUCCESS.equals(result);
    }
    private static final Long RELEASE_SUCCESS = 1L;
}
