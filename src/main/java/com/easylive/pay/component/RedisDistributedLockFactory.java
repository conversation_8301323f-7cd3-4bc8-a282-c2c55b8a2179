package com.easylive.pay.component;

import com.easylive.pay.config.CacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@Component
@Slf4j
public class RedisDistributedLockFactory {
    @Autowired
    private StringRedisTemplate template;

    @Autowired
    private CacheConfig cacheConfig;

    private final Map<String,RedisDistributedLock> lockMap = new HashMap<>();
    private static final String PAY_KEY_DISTRIBUTED_PREFIX = "pay:lock:";

    public synchronized RedisDistributedLock getLock(String lockId, long expireTimeInMillis) {
        return lockMap.computeIfAbsent(lockId, i -> new RedisDistributedLock(template, getLockKey(i), expireTimeInMillis));
    }

    private String getLockKey(String lockId) {
        return cacheConfig + PAY_KEY_DISTRIBUTED_PREFIX + lockId;
    }
}
