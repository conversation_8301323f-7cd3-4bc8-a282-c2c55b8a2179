package com.easylive.pay.component;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.mgs.FeignRequestInterceptor;
import com.easylive.pay.common.mgs.ResponseErrorDecoder;
import com.easylive.pay.config.URLConfig;
import com.easylive.pay.model.DomainModel;
import com.easylive.pay.utils.HttpUtils;
import feign.Feign;
import feign.RequestLine;
import feign.form.FormEncoder;
import feign.httpclient.ApacheHttpClient;
import feign.jackson.JacksonDecoder;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


interface CenterService {

    @RequestLine("GET /domain/load/all")
    List<DomainModel> loadAll();
}

/**
 * OSS
 */
@Component
@Service
public class OssComponent {

    private static final Logger logger = LoggerFactory.getLogger(OssComponent.class);
    private static final String URL_SEPARATOR = "/";

    @Value("${ali.oss.app}")
    private String aliOssApp;

    @Autowired
    private URLConfig urlConfig;

    private List<DomainModel> domainList;

    private CenterService centerService;

    private final static String IMG_CDN_DOMAIN_NAME = "IMG_CDN_DOMAIN";

    @PostConstruct
    public void init() {
        domainLoad();
    }

    public void domainLoad() {
        uploadServiceInit();

        domainList =  centerService.loadAll();
    }

    private void uploadServiceInit() {
        centerService = Feign.builder()
                .client(new ApacheHttpClient(HttpUtils.getHttpClient()))
                .encoder(new FormEncoder())
                .decoder(new JacksonDecoder())
                .errorDecoder(new ResponseErrorDecoder())
                .requestInterceptor(new FeignRequestInterceptor("Pay Service v1.9"))
                .target(CenterService.class, urlConfig.getCenterServiceUrl());
    }

    private Map<String, String> getOssCdnUrl(String model, List<String> fileNames, String style, Integer appId) {
        if (null == fileNames || fileNames.isEmpty() || null == model) {
            logger.error("empty file name or user");
            return new HashMap<>();
        }
        String domain = getAliImgOssURL(appId);
        Map<String, String> map = new HashMap<>();
        fileNames.forEach(fileName -> {
            List<String> urlStringList = new ArrayList<>();
            urlStringList.add(domain);
            urlStringList.add(aliOssApp);
            urlStringList.add(model);
            urlStringList.addAll(fileNameHash(fileName, style));

            map.put(fileName, StringUtils.join(urlStringList.toArray(), URL_SEPARATOR));
        });
        return map;
    }

    private String getOssCdnUrl(String model, String fileName, String style, Integer appId) {
        if (null == fileName || null == model) {
            logger.error("empty file name or user");
            return null;
        }
        List<String> urlStringList = new ArrayList<>();
        urlStringList.add(getAliImgOssURL(appId));
        urlStringList.add(aliOssApp);
        urlStringList.add(model);
        urlStringList.addAll(fileNameHash(fileName, style));
        return StringUtils.join(urlStringList.toArray(), URL_SEPARATOR);
    }

    private String getAliImgOssURL(Integer appId) {
        appId = null == appId || appId <= 0 ? Constants.DEFAULT_APP_ID : appId;
        return getDomain(IMG_CDN_DOMAIN_NAME, appId);
    }

    private String getDomain(String key, int appId) {
        List<DomainModel> list = listAppDomain(appId);
        for (DomainModel v : list) {
            if (v.getCategoryName().equals(key)) {
                return v.getCategoryDomain();
            }
        }
        return "";
    }

    private List<DomainModel> listAppDomain(int appId) {
        List<DomainModel> data = new ArrayList<>();

        if (null == domainList || domainList.size() == 0) {
            return data;
        }
        for (DomainModel model : domainList) {
            if (model.getAppId() == appId) {
                data.add(model);
            }
        }
        return data;
    }

    private List<String> fileNameHash(String file, String style) {

        String[] fileNameArray = file.split("\\?");
        String fileName = fileNameArray[0].trim();
        String md5Hash = DigestUtils.md5Hex(fileName);
        List<String> stringList = new ArrayList<>();
        stringList.add(md5Hash.substring(0, 2));
        stringList.add(md5Hash.substring(2, 4));
        String styleStr = fileName;
        if (style != null) {
            styleStr += style;
        }
        stringList.add(styleStr);

        if (fileNameArray.length > 1) {
            stringList.add("?" + fileNameArray[1]);
        }
        return stringList;
    }

    public Map<String, String> getGoodsImageUrl(List<String> urls, int appId) {
        return getOssCdnUrl("goods", urls, null, appId);
    }

    public String getGoodsImageUrl(String url) {
        return getOssCdnUrl("goods", url, null, null);
    }
}
