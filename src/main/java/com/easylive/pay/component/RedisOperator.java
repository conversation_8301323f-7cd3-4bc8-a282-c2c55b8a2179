package com.easylive.pay.component;

import com.easylive.pay.config.CacheConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Redis服务
 *
 * <AUTHOR>
 * @date 2019/9/6
 */
@Component
public class RedisOperator {

    public static Logger logger = LoggerFactory.getLogger(RedisOperator.class);

    @Autowired
    private StringRedisTemplate template;

    @Autowired
    private CacheConfig cacheConfig;

    static class RedisKey {

        /**
         * 分布式锁前缀
         */
        private static final String KEY_DISTRIBUTED_PREFIX = "pay:distributed_lock";

        private static final String KEY_ORDER_DISTRIBUTED_LOCK_PREFIX = "pay:order:distributed_lock";

    }

    /**
     * 获得分布式锁
     *
     * @param redisClientId Redis客户端ID
     * @return bool
     */
    @Deprecated
    public boolean getDistributedLock(String redisClientId) {

        ValueOperations<String, String> ops = this.template.opsForValue();
        String key = cacheConfig.getPrefix() + RedisKey.KEY_DISTRIBUTED_PREFIX;
        if (ops.setIfAbsent(key, redisClientId)) {
            this.template.expire(key, 1, TimeUnit.HOURS);
            return true;
        }

        String cacheClientId = ops.get(key);
        if (cacheClientId.equals(redisClientId)) {
            this.template.expire(key, 1, TimeUnit.HOURS);
            return true;
        }

        return false;

    }

    /**
     * 获得订单分布式锁
     *
     * @param redisClientId 客户端ID
     * @return bool
     */
    public boolean getOrderDistributedLock(String redisClientId) {

        ValueOperations<String, String> ops = this.template.opsForValue();
        String key = cacheConfig.getPrefix() + RedisKey.KEY_ORDER_DISTRIBUTED_LOCK_PREFIX;
        if (ops.setIfAbsent(key, redisClientId)) {
            this.template.expire(key, 10, TimeUnit.MINUTES);
            return true;
        }

        String cacheClientId = ops.get(key);
        //获取值时可能存在key过期或者为null的情况，双重检查
        if (redisClientId.equals(cacheClientId)) {
            this.template.expire(key, 10, TimeUnit.MINUTES);
            return redisClientId.equals(ops.get(key));
        }
        return false;
    }

    public void releaseOrderLock(String redisClientId) {
        ValueOperations<String, String> ops = this.template.opsForValue();
        String key = cacheConfig.getPrefix() + RedisKey.KEY_ORDER_DISTRIBUTED_LOCK_PREFIX;
        if (ops.get(key).equals(redisClientId)) {
            this.template.delete(key);
        }
    }

}
