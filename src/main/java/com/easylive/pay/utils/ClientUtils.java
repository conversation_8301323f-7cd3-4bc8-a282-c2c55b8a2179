package com.easylive.pay.utils;

import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
/**
 * Created by yangsong on 15/12/14.
 */
public class ClientUtils {

    public static String getClientIPAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        if( ip != null) {
            String[] ips =  ip.split(",");
            if( ip.length() > 1 ) {
                ip = StringUtils.trimAllWhitespace(ips[0]);
            }
        }

        return ip;
    }
}
