package com.easylive.pay.utils;

public class SexUtils {

    /**
     * 根据身份证号码获取性别
     *
     * @param idCardNo 身份证号码
     * @return 性别 1：男 2：女
     */
    public static Integer getSexByIdCardNo(String idCardNo) {

        if (idCardNo == null || idCardNo.length() != 18) {
            //默认为女性，身份证号码不合法
            return 2;
        }
        char sex = idCardNo.charAt(16);
        return Integer.parseInt(String.valueOf(sex)) % 2 == 0 ? 2 : 1;
    }
}
