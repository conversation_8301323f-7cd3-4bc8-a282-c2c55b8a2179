package com.easylive.pay.utils;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


public class DateUtils {

    private static DateTimeFormatter defaultDateTimeFormat = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    private static DateTimeFormatter dateFormat = DateTimeFormat.forPattern("yyyy-MM-dd");
    public static final String NORM_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static DateTimeFormatter getDefaultFormatter() {
        return DateTimeFormat.forPattern("yyyy-MM-dd");
    }

    public static Date now() {
        return new Date();
    }

    /**
     * 解决Date存入数据库中毫秒>500时进一位。
     */
    public static Date nowDb() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static String format(Date date) {
        return new DateTime(date).toString(defaultDateTimeFormat);
    }

    public static String format(Date date, DateTimeFormatter formatter) {
        return new DateTime(date).toString(formatter);
    }


    public static String formatAsDate(Date date) {
        return new DateTime(date).toString(dateFormat);
    }

    public static Date parse(String date) {
        try {
            return dateFormat.parseDateTime(date).toDate();
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date string");
        }
    }

    public static Date parseDateTime(String dateString) {
        SimpleDateFormat format = new SimpleDateFormat(NORM_DATE_TIME_PATTERN);
        try {
            return format.parse(dateString);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid date string");
        }
    }

    public static Date parse2(String date) {
        return getDefaultFormatter().parseDateTime(date).toDate();
    }

    public static Date getTodayDate() {
        LocalDate localdate = new LocalDate();
        return localdate.toDate();
    }

    public static Date getFirstDate() {
        return new DateTime(new Date(0)).withTimeAtStartOfDay().toDate();
    }

    public static Date getTomorrowDate() {
        return new DateTime().plusDays(1).withTimeAtStartOfDay().toDate();
    }

    public static Date getStartOfNextWeek() {
        return new DateTime().plusWeeks(1).withDayOfWeek(1).withTimeAtStartOfDay().toDate();
    }

    public static Date getStartOfNextMonth() {
        return new DateTime().plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay().toDate();
    }

    public static boolean isToday(Date date) {
        return date.compareTo(getTodayDate()) == 0;
    }

    public static Date yesterdayNow() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 1);
        return calendar.getTime();
    }

    public static Date beforeTime(int sec) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, calendar.get(Calendar.SECOND) - sec);
        return calendar.getTime();
    }

    public static Date day3BeforeNow() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 3);
        return calendar.getTime();
    }

    public static Date getBeforeDateForDay(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -days);
        return calendar.getTime();
    }

    public static Date getBeforeDateForDay(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -days);
        return calendar.getTime();
    }

    public static Date getBeforeDateForMinute(int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -minutes);
        return calendar.getTime();
    }


    /**
     * 是否当前月
     */
    public static boolean isCurrentMonth(Date date) {
        //本月
        Calendar calendar1 = Calendar.getInstance();
        int year1 = calendar1.get(Calendar.YEAR);
        int month1 = calendar1.get(Calendar.MONTH);
        //当前月
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year2 = calendar2.get(Calendar.YEAR);
        int month2 = calendar2.get(Calendar.MONTH);

        return year1 == year2 && month1 == month2;
    }

    public static boolean isCurrentDay(Date date) {
        if( date == null )
            return false;

        //本月
        Calendar calendar1 = Calendar.getInstance();
        int year1 = calendar1.get(Calendar.YEAR);
        int month1 = calendar1.get(Calendar.MONTH);
        int day1 = calendar1.get(Calendar.DATE);

        //当前月
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date);
        int year2 = calendar2.get(Calendar.YEAR);
        int month2 = calendar2.get(Calendar.MONTH);
        int day2 = calendar2.get(Calendar.DATE);

        return year1 == year2 && month1 == month2 && day1 == day2;
    }

    /**
     * 获得几个月以前的时间
     *
     * @param amount
     * @return Date
     */
    public static Date getMonthDate(int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, amount);
        return calendar.getTime();
    }
}
