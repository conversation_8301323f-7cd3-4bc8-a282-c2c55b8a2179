package com.easylive.pay.utils;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.Charset;

public class SystemUtil {
    private static final Logger logger = LoggerFactory.getLogger(SystemUtil.class);

    public static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            logger.error("Interrupted sleep: {}", e.getMessage());
        }
    }

    public static String shellExecuteCommand(String... command) {
        ProcessBuilder builder = new ProcessBuilder(command);
        try {
            Process process = builder.start();
            int exitCode = process.waitFor();
            String ret = IOUtils.toString(process.getInputStream(), Charset.defaultCharset());
            if (exitCode != 0) {
                return null;
            }
            return ret;

        } catch (Exception e) {
            logger.error("Error executing command: {}", e.getMessage());
        }
        return null;
    }
}
