package com.easylive.pay.utils;

import org.apache.commons.codec.digest.DigestUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by liwd on 2016/12/25.
 */
public class Md5Utils {

    /**
     * 把数组所有元素，按字母排序，然后按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要签名的参数
     * @return 签名的字符串
     */
    private static StringBuilder createLinkString(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder prestr = new StringBuilder();
        String key;
        String value;
        for (int i = 0; i < keys.size(); i++) {
            key = keys.get(i);
            value = params.get(key);
            if ("".equals(value) || value == null ||
                    key.equalsIgnoreCase("sign") || key.equalsIgnoreCase("sign_type")) {
                continue;
            }
            prestr.append(key).append("=").append(value).append("&");
        }
        return prestr.deleteCharAt(prestr.length() - 1);
    }

    /**
     * 生成MD5签名
     *
     * @param sArray 需要签名的参数
     * @param key    密钥
     * @return 签名结果
     */
    public static String buildMySign(Map<String, String> sArray, String key) {
        if (sArray != null && sArray.size() > 0) {
            StringBuilder str = createLinkString(sArray);
            return DigestUtils.md5Hex(str.toString() + key);
        }
        return null;
    }

    /**
     * 银行MD5
     * @param srcSignString
     * @param key
     * @param charset
     * @return
     */
    public static String getMd5(String srcSignString, String key, String charset) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(srcSignString.getBytes(charset));

            String result = "";
            byte[] temp;
            temp = md5.digest(key.getBytes(charset));
            for (int i = 0; i < temp.length; i++) {
                result += Integer.toHexString((0x000000ff & temp[i]) | 0xffffff00).substring(6);
            }

            return result;

        } catch (NoSuchAlgorithmException e) {

            e.printStackTrace();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
