package com.easylive.pay.utils;


import org.apache.commons.lang3.RandomStringUtils;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Created by liwd on 2016/12/25.
 */
public class ReapalDecipher {


    private static final Random random = new Random();

    /**
     * 解密
     *
     * @param privatekey
     * @param encryptkey
     * @param data
     */
    public static String decryptData(String privatekey, String privatekeyPwd, String encryptkey, String data) throws Exception {
        //获取自己私钥解密
        PrivateKey pvkformPfx = RSA.getPvkformPfx(privatekey, privatekeyPwd);
        String decryptData = RSA.decrypt(encryptkey, pvkformPfx);

        return AES.decryptFromBase64(data, decryptData);
    }

    /**
     * 加密
     */
    public static Map<String, String> encryptData(String pubkey, String json) throws Exception {
        System.out.println("json数据=============>" + json);
        //商户获取融宝公钥
        PublicKey pubKeyFromCrt = RSA.getPubKeyFromCRT(pubkey);
        //随机生成16数字
        String key = RandomStringUtils.randomAlphanumeric(16);

        // 使用RSA算法将商户自己随机生成的AESkey加密
        String encryptKey = RSA.encrypt(key, pubKeyFromCrt);
        // 使用AES算法用随机生成的AESkey，对json串进行加密
        String encryData = AES.encryptToBase64(json, key);

        System.out.println("密文key============>" + encryptKey);
        System.out.println("密文数据===========>" + encryData);

        Map<String, String> map = new HashMap<>();
        map.put("data", encryData);
        map.put("encryptkey", encryptKey);
        return map;
    }

}
