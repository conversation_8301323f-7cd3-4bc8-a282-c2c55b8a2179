package com.easylive.pay.utils;

import java.util.Currency;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/15
 */
public class CurrencyUtils {

    private static Map<String,String> currencies = new HashMap<>();
    static {
        Currency.getAvailableCurrencies().forEach(currency -> {
            currencies.put(currency.getCurrencyCode(), currency.getDisplayName());
        });
        currencies.put("CNY","元");
    }

    public static String getCurrencyDisplayName(String currencyCode) {
        String name = currencies.get(currencyCode);
        return name == null ? currencyCode: name;
    }

    public static String getCurrencyDisplayName(String currencyCode, Locale locale) {
        Currency currency =  Currency.getInstance(currencyCode);
        if( currency != null) {
            return currency.getDisplayName(locale);
        }
        return currencyCode;
    }
}
