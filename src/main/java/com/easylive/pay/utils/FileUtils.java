package com.easylive.pay.utils;

import org.apache.commons.io.output.ByteArrayOutputStream;

import java.io.IOException;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;

public class FileUtils {
    public static byte[] getURLBinaryContentNIO(String urlString) {
        try {
            URL url = new URL(urlString);

            try (ReadableByteChannel channel = Channels.newChannel(url.openStream())) {
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                ByteBuffer buffer = ByteBuffer.allocate(1024);

                while (channel.read(buffer) > 0) {
                    buffer.flip();
                    while (buffer.hasRemaining()) {
                        outputStream.write(buffer.get());
                    }
                    buffer.clear();
                }

                return outputStream.toByteArray();
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return new byte[0];
    }
}
