package com.easylive.pay.utils;

import io.netty.util.internal.StringUtil;

import java.util.regex.Pattern;

/**
 * 验证工具类
 *
 * <AUTHOR>
 * @date 2019/8/16
 */
public class ValidatorUtils {

    /**
     * 验证字符串长度
     */
    public static boolean checkLength(String data, int min, int max) {
        if (data.length() >= min && data.length() <= max) {
            return true;
        }
        return false;
    }

    /**
     * 是否是数字字符串
     */
    public static boolean isNumeric(String data) {
        for (int i = 0; i < data.length(); i++) {
            if (!Character.isDigit(data.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证手机号
     * @param phone
     * @return
     */
    public static boolean validPhone(String phone) {
        if (StringUtil.isNullOrEmpty(phone)) {
            return false;
        }

        String[] data = phone.split("_");
        if (data.length > 2) {
            //处理前缀2_86_136**** 哇塞
            phone = data[1] + '_' + data[2];
        }

        String regex = "^[0-9]{1,5}_[0-9]{8,12}$";
        if (phone.startsWith("86_")) {
            regex = "^86_(((1[356789][0-9])|(147)|(170))\\d{8})$";
        }
        return Pattern.compile(regex).matcher(phone).matches();
    }

}
