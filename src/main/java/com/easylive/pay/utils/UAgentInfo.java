package com.easylive.pay.utils;

/**
 * Created by yangsong on 15/12/14.
 */
/* *******************************************
// LICENSE INFORMATION
// The code, "Detecting Smartphones Using PHP"
// by <PERSON>, is licensed under a Creative Commons
// Attribution 3.0 United States License.
//
// Updated 01 March 2010 by <PERSON>
//   - Remove un-needed if statements instead just returning the boolean
//     inside the if clause
//
// Updated 14 December 2009 by A Hand
//   - Added the method for detecting BlackBerry Touch
//		devices like the Storm 1 and 2.
//
// Updated 5 December 2009 by A Hand
//   - Fixed the DetectPalmOS method. It should filter
//	   out WebOS devices.
//
// Updated 8 November 2009 by A Hand
//   - Added the deviceWebOS variable.
//   - Added Palm's WebOS to the DetectPalmOS method.
//   - Created a new method to check for Palm's WebOS devices.
//   - Added Palm's WebOS to the DetectTierIphone method.
//
// Updated 4 April 2009 by A Hand
//   - Changed the name of the class from DetectSmartPhone to UAgentInfo.
//	   New name is more consistent with PHP and JavaScript classes.
//   - Added a method to detect Opera Mobile and Mini. Opera Mobile is new.
//   - Updated the algorithm for detecting a Sony Mylo.
//	 - Added Android to the DetectTierIphone method.
//   - Updated comments for Detect Tier methods.
//
// Updated 22 January 2009
//   - Ported to Java from the PHP code by
//     Satish Kumar Nookala, <EMAIL>
//
// Anthony Hand, <EMAIL>
// Web: www.hand-interactive.com
//
// License info: http://creativecommons.org/licenses/by/3.0/us/
//
// This code is provided AS IS with no expressed or implied warranty.
// You have the right to use this code or any portion of it
// so long as you provide credit toward myself as the original author.
//
// *******************************************
*/

import com.easylive.pay.enums.MobilePlatform;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The DetectSmartPhone class encapsulates information about
 * a browser's connection to your web site.
 * You can use it to find out whether the browser asking for
 * your site's content is probably running on a mobile device.
 * The methods were written so you can be as granular as you want.
 * For example, enquiring whether it's as specific as an iPod Touch or
 * as general as a smartphone class device.
 * The object's methods return true, or false.
 */
public class UAgentInfo {

    // OS Detection
    public static final String windows = "windows";

    public static final String YIZHIBO = "yizhibo";
    public static final String ENTERPRISE = "oupaienterprise";
    public static final String MAGICLIVE = "magiclive";
    public static final String YMLIVE = "ymlive";
    public static final String OPLIVE = "oplive";
    public static final String EASYLOVE = "easylove";
    public static final String ELLITE = "ellite";
    public static final String FADELIVE = "fadelive";
    public static final String YIBO = "yibo";
    public static final String SIZHIBO = "sizhibo";
    public static final String OVERSEA = "oversea";

//    private static final Pattern APP_PATTERN = Pattern.compile("(yizhibo|oupaienterprise|magiclive|ymlive|oplive|easylove|ellite|fadelive|yibo|sizhibo|bclive|yipinlive|oversea|ylivetool) (.+) rv:([^(]+) \\((.+); .+os (.+)");
//    private static final Pattern ANDROID_PATTERN = Pattern.compile("; android (.+); (.+)\\); (yizhibo|magiclive|ymlive|oplive|easylove|ellite|fadelive|yibo|sizhibo|bclive|yipinlive|oversea|ylivetool) android v(.+)");

    private static final Pattern IOS_PATTERN = Pattern.compile("(.+) (.+) rv:([^(]+) \\((.+)[;|,] .+os (.+)[;|,]");
    private static final Pattern IOS_PATTERN2 = Pattern.compile("(.+) (.+) rv:([^(]+) \\((.+)[;|,] .+os (.+)");
    //private static final Pattern ANDROID_PATTERN = Pattern.compile("[;|,] android (.+)[;|,] (.+)\\)[;|,] (.+) android v(.+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern ANDROID_PATTERN = Pattern.compile("[;|,] android (\\d+)[;|\\s|,](.+)\\)[;|,] (.+) android v(.+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern ANDROID_PATTERN_2 = Pattern.compile("android\\s+(\\d+\\.\\d+\\.\\d+);\\s+([^)]+)\\);\\s+(.+?)\\s+android\\s+v(.+)", Pattern.CASE_INSENSITIVE);


    private static final Pattern ANDROID_WEB_PATTERN = Pattern.compile("[;|,] android (.+)[;|,] (.+)\\).+[;|,]web (.+) android v(.+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern H5_PATTERN = Pattern.compile("(.+) (.+) rv:([^(]+) online \\(h5\\)", Pattern.CASE_INSENSITIVE);

    private static final String PC_KEYWORD = "the king of the world";
    private static final String WINDOWS_KEYWORD = "windows";
    private static final String PC_KEYWORD_EASYLIVE = "easylive";


    private static final Logger logger = LoggerFactory.getLogger(UAgentInfo.class);
    // User-Agent and Accept HTTP request headers
    private String userAgent = "";
    private String appVersion = "";
    private String appName = "yizhibo";
    private String osVersion = "";
    private String deviceModel = "";
    private String buildType = "";
    private String buildVersion = "";
    private MobilePlatform deviceType = MobilePlatform.MPF_UNKNOWN;

    /**
     * Initialize the userAgent and httpAccept variables
     *
     * @param userAgent the User-Agent header
     */
    public UAgentInfo(String userAgent) {
        init(userAgent);
    }

    /**
     * Initialize the userAgent and httpAccept variables by getting the headers
     * from the HttpServletRequest
     *
     * @param request the HttpServletRequest to get the header information from
     */
    public UAgentInfo(HttpServletRequest request) {
        String uagent = request.getHeader("User-Agent");
        String newua = null;
        try {
            newua = new String(uagent.getBytes("iso8859-1"), StandardCharsets.UTF_8);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        init(newua);
    }

    private void init(String userAgent) {
        if (userAgent != null) {
            this.userAgent = userAgent.toLowerCase();
        }
        this.detection();
    }

    private void detection() {


        logger.debug("userAgent {}", userAgent);
        Matcher iosMatcher1 = IOS_PATTERN.matcher(userAgent);
        Matcher iosMatcher2 = IOS_PATTERN2.matcher(userAgent);
        Matcher androidMatcher = ANDROID_PATTERN.matcher(userAgent);
        Matcher androidMatcher2 = ANDROID_PATTERN_2.matcher(userAgent);
        Matcher webAndroidMatcher = ANDROID_WEB_PATTERN.matcher(userAgent);
        Matcher h5Matcher = H5_PATTERN.matcher(userAgent);

        if( h5Matcher.find() ) {
            this.appName = h5Matcher.group(1);
            this.appVersion = h5Matcher.group(2);
            if( this.appVersion.startsWith("v")) {
                this.appVersion = this.appVersion.substring(1);
            }
            this.deviceType = MobilePlatform.MPF_H5;
        } else if (iosMatcher1.find()) {
            this.appName = iosMatcher1.group(1);
            this.appVersion = iosMatcher1.group(2);
            this.osVersion = iosMatcher1.group(5);
            this.deviceModel = iosMatcher1.group(4);
            this.buildVersion = iosMatcher1.group(3);
            this.deviceType = MobilePlatform.MPF_IOS;
            String[] builds = this.buildVersion.split(" ");
            if (builds.length == 2) {
                this.buildType = builds[1];
                this.buildVersion = builds[0];
            } else if( builds.length == 3 ) {
                this.buildType = builds[2];
                this.buildVersion = builds[0];
            }
        }
        else if (iosMatcher2.find()) {
            this.appName = iosMatcher2.group(1);
            this.appVersion = iosMatcher2.group(2);
            this.osVersion = iosMatcher2.group(5);
            this.deviceModel = iosMatcher2.group(4);
            this.buildVersion = iosMatcher2.group(3);
            String[] builds = this.buildVersion.split(" ");
            if (builds.length == 2) {
                this.buildType = builds[1];
                this.buildVersion = builds[0];
            }
            this.deviceType = MobilePlatform.MPF_IOS;
        } else if (androidMatcher.find()) {
            buildAndroidInfo(androidMatcher);
        } else if (androidMatcher2.find()) {
            buildAndroidInfo(androidMatcher2);
        }else if (userAgent.contains(PC_KEYWORD) ||(userAgent.contains(WINDOWS_KEYWORD) && userAgent.contains(PC_KEYWORD_EASYLIVE))) {
            this.appVersion = "3";
            this.deviceType = MobilePlatform.MPF_PC;
        } else if( webAndroidMatcher.find() ) {
            buildAndroidInfo(webAndroidMatcher);
        } else {
                this.appVersion = "0";
                this.deviceType = MobilePlatform.MPF_UNKNOWN;
        }
        logger.debug("Device Type: {} ", this.deviceType.getValue());
    }

    private void buildAndroidInfo(Matcher androidMatcher) {
        this.deviceType = MobilePlatform.MPF_ANDROID;

        this.appName = androidMatcher.group(3).trim();
        this.appVersion = androidMatcher.group(4).trim();
        this.osVersion = androidMatcher.group(1).trim();
        this.deviceModel = androidMatcher.group(2).trim();

        String[] builds = this.appVersion.split("-");
        if (builds.length >= 2) {
            this.appVersion = builds[0];
            String[] versionInfo = this.appVersion.split("\\.");
            if( versionInfo.length >= 4) {
                this.buildVersion = versionInfo[3];
                this.appVersion = versionInfo[0] + "." + versionInfo[1] + "." + versionInfo[2];
            }


            if (builds[builds.length - 1].equals("mp")) {
                String[] _subbuilds = this.appVersion.split("_");
                if (_subbuilds.length == 2) {
                    String[] __subbuilds = _subbuilds[0].split(".");
                    this.buildType = _subbuilds[1].substring(0, _subbuilds[1].length() - 3);
                    if (__subbuilds.length >= 3) {
                        this.appVersion = __subbuilds[0] + "." + __subbuilds[1] + "." + __subbuilds[2];
                        if (__subbuilds.length >= 4) {
                            this.buildVersion = __subbuilds[3];
                        }
                    }

                }
            } else {
                this.buildType = builds[1];
                String[] __subbuilds = builds[0].split(".");
                if (__subbuilds.length >= 3) {
                    this.appVersion = __subbuilds[0] + "." + __subbuilds[1] + "." + __subbuilds[2];
                    if (__subbuilds.length >= 4) {
                        this.buildVersion = __subbuilds[3];
                    }
                }
            }
        }
    }

    public MobilePlatform getDeviceType() {
        return this.deviceType;
    }

    public String getAppName() {
        return this.appName;
    }
    public String getAppVersion() {
        return this.appVersion;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public String getBuildType() {
        return buildType;
    }

    public String getBuildVersion() {
        return buildVersion;
    }

    public String toString() {
        return "UserAgent [userAgent=" + userAgent + ", appName=" + appName + ", appVersion=" + appVersion + ", osVersion=" + osVersion + ", deviceModel=" + deviceModel + ", buildType=" + buildType + ", buildVersion=" + buildVersion + ", deviceType=" + deviceType + "]";
    }
}