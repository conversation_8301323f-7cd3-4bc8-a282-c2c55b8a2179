package com.easylive.pay.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class UrlUtils {
    /**
     * 把数组所有元素，按字母排序，然后按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要签名的参数
     * @return 签名的字符串
     */
    public static String createLinkString(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder prestr = new StringBuilder();
        String key;
        String value;
        for (int i = 0; i < keys.size(); i++) {
            key = keys.get(i);
            value = params.get(key);
            if (value == null || "sign".equalsIgnoreCase(key)) {
                continue;
            }
            prestr.append(key).append("=").append(value).append("&");
        }
        return prestr.deleteCharAt(prestr.length() - 1).toString();
    }


    public static String createSortString(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder prestr = new StringBuilder();
        String key;
        Object value;
        for (int i = 0; i < keys.size(); i++) {
            key = keys.get(i);
            value = params.get(key);
            if (value == null || "sign".equalsIgnoreCase(key)) {
                continue;
            }
            prestr.append(key).append("=").append(value).append("&");
        }
        return prestr.deleteCharAt(prestr.length() - 1).toString();
    }

}
