package com.easylive.pay.utils;

import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019/8/26
 */
public class AppStringUtils {



    public static String simplePhone(String phone) {
        if (phone == null) {
            return null;
        }
        int index = phone.lastIndexOf("_");
        if (index != -1) {
            return phone.substring(index + 1);
        }
        return phone;
    }

    private static SecureRandom random = new SecureRandom();

    /**
     * 封装JDK自带的UUID, 通过Random数字生成, 中间无-分割.
     */
    public static String uuid() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 使用SecureRandom随机生成Long.
     */
    public static long randomLong() {
        return Math.abs(random.nextLong());
    }

    /**
     * 将分为单位的转换为元 （除100）
     */
    public static double changeF2Y(long amount) {
        BigDecimal decimal = BigDecimal.valueOf(amount).divide(new BigDecimal(100));
        return decimal.doubleValue();
    }

    /**
     * 将元为单位的转换为分 （乘100）
     */
    public static String changeY2F(long amount) {
        return BigDecimal.valueOf(amount).multiply(new BigDecimal(100)).toString();
    }

    /**
     * 将ids=1,2,3转换程set[1,2,3]
     */
    public static Set<Long> idToSet(String ids) {
        return Stream.of(ids).map(Long::valueOf).collect(Collectors.toSet());
    }

    public static String toIdString(List<?> idList, String separator) {
        return org.apache.commons.lang3.StringUtils.join(idList, separator);
    }

    public static String toIdString(List<?> idList) {
        return toIdString(idList, ",");
    }



    public static String getMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return "";
        }
        return mobile.contains("_") ? mobile.split("_")[1] : mobile;
    }

}
