package com.easylive.pay.utils;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.UriUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.bouncycastle.asn1.cms.CMSAttributes.contentType;

public class HttpUtils {
    private static final int MAX_TIMEOUT = 15000;
    private static Logger logger = LoggerFactory.getLogger(HttpUtils.class);
    private static RequestConfig requestConfig;
    private static HttpClient httpClient;

    static {
        // 设置连接池
        PoolingHttpClientConnectionManager connMgr = new PoolingHttpClientConnectionManager();
        // 设置连接池大小
        connMgr.setMaxTotal(100);
        connMgr.setDefaultMaxPerRoute(connMgr.getMaxTotal());

        RequestConfig.Builder configBuilder = RequestConfig.custom();
        // 设置连接超时
        configBuilder.setConnectTimeout(MAX_TIMEOUT);
        // 设置读取超时
        configBuilder.setSocketTimeout(MAX_TIMEOUT);
        // 设置从连接池获取连接实例的超时
        configBuilder.setConnectionRequestTimeout(MAX_TIMEOUT);
        // 在提交请求之前 测试连接是否可用
        // configBuilder.setStaleConnectionCheckEnabled(true);
        requestConfig = configBuilder.build();
    }

    public static String toParamString(Map<?, ?> data)  {
        try {
            StringBuilder queryString = new StringBuilder();
            for (Map.Entry<?, ?> pair : data.entrySet()) {
                queryString.append(pair.getKey()).append("=");
                queryString.append(URLEncoder.encode((String) pair.getValue(), "UTF-8")).append("&");
            }
            if (queryString.length() > 0) {
                queryString.deleteCharAt(queryString.length() - 1);
            }
            return queryString.toString();
        } catch (Exception e) {
            throw new RuntimeException("Invalid encode");
        }
    }
    /**
     * 发送 GET 请求（HTTP），不带输入数据
     */
    public static String doGet(String url) {
        return doGet(url, new HashMap<>(), new HashMap<>());
    }

    /**
     * 发送 GET 请求（HTTP），不带输入数据
     */
    public static String doGet(String url, Map<String, Object> params) {
        return doGet(url, params, new HashMap<>());
    }


    /**
     * 发送 GET 请求（HTTP），K-V形式，包含header
     */
    public static String doGet(String url, Map<String, Object> params, Map<String, String> headers) {
        String apiUrl = url;
        StringBuilder param = new StringBuilder();
        int i = 0;
        for (String key : params.keySet()) {
            if (i == 0) {
                param.append("?");
            } else {
                param.append("&");
            }
            param.append(key).append("=").append(URLEncode(params.get(key).toString()));
            i++;
        }
        apiUrl += param.toString();
        String result = null;
        HttpClient httpclient = HttpClients.createDefault();
        try {
            HttpGet httpGet = new HttpGet(apiUrl);

            //设置header
            if (headers.size() > 0) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpGet.setHeader(entry.getKey(), entry.getValue());
                }
            }

            HttpResponse response = httpclient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                logger.error("Request Error status code is {}, url is {}", statusCode, apiUrl);
            }

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                InputStream in = entity.getContent();
                result = IOUtils.toString(in, "UTF-8");
            }
        } catch (IOException e) {
            logger.error("HTTP doGet failed", e);
        }
        return result;
    }

    public static String URLEncode(String source) {
        try {
            return UriUtils.encode(source, "utf-8");
        } catch (Exception e) {
            return source;
        }
    }

    public static String URLDecode(String source) {
        try {
            return UriUtils.decode(source, "utf-8");
        } catch (Exception e) {
            return source;
        }
    }

    /**
     * 发送 POST 请求（HTTP），不带输入数据
     */
    public static String doPost(String apiUrl) {
        return doPost(apiUrl, new HashMap<>());
    }

    /**
     * 发送 POST 请求（HTTP），K-V形式
     */
    public static String doPost(String apiUrl, Map<String, Object> params) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String httpStr = null;
        HttpPost httpPost = new HttpPost(apiUrl);
        CloseableHttpResponse response = null;

        try {
            httpPost.setConfig(requestConfig);
            List<NameValuePair> pairList = new ArrayList<>(params.size());
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                NameValuePair pair = new BasicNameValuePair(entry.getKey(), entry
                        .getValue().toString());
                pairList.add(pair);
            }
            httpPost.setEntity(new UrlEncodedFormEntity(pairList, StandardCharsets.UTF_8));
            response = httpClient.execute(httpPost);
            System.out.println(response.toString());
            HttpEntity entity = response.getEntity();
            httpStr = EntityUtils.toString(entity, "UTF-8");
        } catch (IOException e) {
            logger.error("HTTP Post error", e);
        } finally {
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {
                    logger.error("Error consume response", e);
                }
            }
        }
        return httpStr;
    }


    /**
     * 发送 POST 请求（HTTP），JSON形式
     */
    public static String doPost(String apiUrl, String payload) {
        return doPost(apiUrl,payload, null, ContentType.DEFAULT_TEXT);
    }

    public static String doPost(String url, String body, Map<String,String> headers) {
        return doPost(url, body, headers, ContentType.DEFAULT_TEXT);
    }

    public static String doPost(String url, String body, ContentType contentType) {
        return doPost(url, body, null, contentType);
    }

    public static String doPostUpload(String url, String body, File file) {
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);

            final StringEntity stringEntity = new StringEntity(body, ContentType.APPLICATION_JSON);
            httpPost.setEntity(stringEntity);

            EntityBuilder entityBuilder = EntityBuilder.create();

            // 设置文件参数
            entityBuilder.setFile(file);

            // 构建包含文本参数和文件参数的 multipart/form-data 实体
            HttpEntity multipartEntity = entityBuilder.build();
            httpPost.setEntity(multipartEntity);

            // 执行请求
            HttpResponse response = httpClient.execute(httpPost);

            // 处理响应
            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    public static String doPost(String url, String body, Map<String,String> headers, ContentType contentType) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String httpStr = "";
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;

        try {
            httpPost.setConfig(requestConfig);
            StringEntity stringEntity = new StringEntity(body, contentType);
            if(headers != null && !headers.isEmpty() ) {
                headers.forEach(httpPost::setHeader);
            }

            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            httpStr = EntityUtils.toString(entity);
        } catch (IOException e) {
            logger.error("HTTP Post error", e);
        } finally {
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {
                    logger.error("Error consume response", e);
                }
            }
        }
        return httpStr;
    }

    public static String doPostJson(String apiUrl, String payload) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String httpStr = "";
        HttpPost httpPost = new HttpPost(apiUrl);
        CloseableHttpResponse response = null;

        try {
            httpPost.setConfig(requestConfig);
            StringEntity stringEntity = new StringEntity(payload);
            stringEntity.setContentEncoding("UTF-8");
            stringEntity.setContentType("application/json");
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            httpStr = EntityUtils.toString(entity);
        } catch (IOException e) {
            logger.error("HTTP Post error", e);
        } finally {
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {
                    logger.error("Error consume response", e);
                }
            }
        }
        return httpStr;
    }

    public static HttpClient getHttpClient() {
        if (null == httpClient) {
            httpClient = createHttpclient();
        }
        return httpClient;
    }

    private static HttpClient createHttpclient() {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setMaxTotal(100);
        cm.setDefaultMaxPerRoute(cm.getMaxTotal());
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(1500)
                .setSocketTimeout(5 * 1000)
                .setConnectionRequestTimeout(500)
                .build();
        return HttpClients.custom()
                .setConnectionManager(cm)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }
}
