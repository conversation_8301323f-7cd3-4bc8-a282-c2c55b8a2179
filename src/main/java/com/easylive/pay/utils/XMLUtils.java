package com.easylive.pay.utils;

import com.easylive.pay.model.cashout.BaseResponseXML;
import com.easylive.pay.model.cashout.ResponseXMLBody;
import com.easylive.pay.model.cashout.ResponseXMLHead;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.mapper.MapperWrapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/8/12
 */
@Service
public class XMLUtils {

    private static final String DEFAULT_ENCODING = "GBK";

    private static XStream xStream = new XStream(new DomDriver("GBK")) {
        /**
         * 重写此方法，忽略掉从XML转换成功Bean中不存在的属性
         */
        @Override
        protected MapperWrapper wrapMapper(MapperWrapper next) {
            return new MapperWrapper(next) {
                @Override
                public boolean shouldSerializeMember(Class definedIn, String fieldName) {
                    if (definedIn == Object.class) {
                        return false;
                    }
                    return super.shouldSerializeMember(definedIn, fieldName);
                }
            };
        }
    };

    XMLUtils() {
        xStream.autodetectAnnotations(true);
        //去掉class属性
        xStream.aliasSystemAttribute(null, "class");
        XStream.setupDefaultSecurity(xStream);
        xStream.allowTypes(new Class[]{ResponseXMLBody.class, BaseResponseXML.class, ResponseXMLHead.class});
    }

    private static String xmlTag() {
        return xmlTag(null);
    }

    private static String xmlTag(String encoding) {
        if (encoding == null) {
            encoding = DEFAULT_ENCODING;
        }
        return "<?xml version=\"1.0\" encoding=\"" + encoding + "\"?>";
    }

    public static String toXML(Class<?> clazz, Object obj) {
        xStream.processAnnotations(clazz);
        return xmlTag() + xStream.toXML(obj);
    }

    public static Class fromXML(Class<?> clazz, String xml) {
        xStream.processAnnotations(clazz);
        return (Class) xStream.fromXML(xml);
    }

    /**
     * 将xml字符串转化为java对象
     */
    public static <T> T xmlToBean(String xml, Class<T> clazz) {
        xStream.processAnnotations(clazz);
        Object object = xmlToBean(xml);
        T cast = clazz.cast(object);
        return cast;
    }

    /**
     * 将xml字符串转化为java对象
     */
    public static Object xmlToBean(String xml) {
        return xStream.fromXML(xml);
    }
}
