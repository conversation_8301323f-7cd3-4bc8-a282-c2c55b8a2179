package com.easylive.pay.utils;

import com.easylive.pay.controller.AliPayController;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2019/10/15
 */

@Slf4j
public class ServletUtils {
    public static Map<String, String> getServletParameters(HttpServletRequest request) {
        TreeMap<String, String> aliArgs = new TreeMap<>();

        Map<String, String[]> parameters = request.getParameterMap();

        log.info("parameters: {}" + JsonUtils.printObject(parameters));
        for (String key : parameters.keySet()) {
            if (parameters.get(key).length > 0) {
                aliArgs.put(key, parameters.get(key)[0]);
            }
        }
        return aliArgs;
    }
}
