package com.easylive.pay.utils;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * 使用的签名算法是 "SHA256withRSA"，这是基于 SHA-256 的 RSA 签名算法，与 RSA2 通常指的是相符的。如果您在具体场景中使用的是其他哈希算法，请相应地更改签名算法
 */

public class RSA2 {

    // 使用私钥进行签名
    public static String sign(String privateKey, String data) throws Exception {
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(getPrivateKey(privateKey));
        signature.update(data.getBytes());
        byte[] signatureBytes = signature.sign();
        return Base64.getEncoder().encodeToString(signatureBytes);
    }

    // 使用公钥进行验签
    public static boolean verify(String publicKey, String data, String signature) throws Exception {
        Signature verifySignature = Signature.getInstance("SHA256withRSA");
        verifySignature.initVerify(getPublicKey(publicKey));
        verifySignature.update(data.getBytes());
        byte[] signatureBytes = Base64.getDecoder().decode(signature);
        return verifySignature.verify(signatureBytes);
    }

    // 获取私钥
    private static PrivateKey getPrivateKey(String privateKeyStr) throws Exception {
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyStr);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(privateKeyBytes));
    }

    // 获取公钥
    private static PublicKey getPublicKey(String publicKeyStr) throws Exception {
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyStr);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(new X509EncodedKeySpec(publicKeyBytes));
    }
}
