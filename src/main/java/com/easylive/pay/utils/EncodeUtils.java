package com.easylive.pay.utils;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseErrorMessage;
import com.easylive.pay.common.ResponseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

/**
 * 转码工具
 *
 * <AUTHOR>
 * @date 2019/8/14
 */
public class EncodeUtils {

    private static final Logger logger = LoggerFactory.getLogger(EncodeUtils.class);

    public static final String CHARSET_GBK = "GBK";

    public static final String CHARSET_GB2312 = "GB2312";

    /**
     * UTF-8编码转换成GBK
     *
     * @param utf8
     * @return
     */
    public static String utf8ToGbk(String utf8) {
        try {
            return new String(utf8.getBytes(CHARSET_GBK), CHARSET_GBK).trim();
        } catch (UnsupportedEncodingException e) {
            logger.error("[EncodeUtils][utf8ToGbk] Code Convert utf8 To gbk String Failed, utf8={}", utf8);
            throw new ResponseException(ResponseError.E_PARAM, ResponseErrorMessage.E_PARAM.getMessage());
        }
    }

    /**
     * GBK编码转换成UTF-8
     *
     * @param gbk
     * @return
     */
    public static String gbkToUtf8(String gbk) {
        return new String(gbk.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8).trim();
    }

    /**
     * UTF-8编码转换成ISO_8859_1
     *
     * @param utf8
     * @return
     */
    public static String utf8ToIso(String utf8) {
        return new String(utf8.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1).trim();
    }

    /**
     * ISO_8859_1编码转换成UTF-8
     *
     * @param gbk
     * @return
     */
    public static String isoToUtf8(String gbk) {
        return new String(gbk.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8).trim();
    }

    /**
     * UTF-8编码转换成GB2312
     *
     * @param utf8
     * @return
     */
    public static String utf8ToGb2312(String utf8) throws UnsupportedEncodingException {
        return new String(utf8.getBytes(StandardCharsets.UTF_8), CHARSET_GB2312).trim();
    }

    /**
     * GB2312编码转换成UTF-8
     *
     * @param gbk
     * @return
     */
    public static String gb2312ToUtf8(String gbk) throws UnsupportedEncodingException {
        return new String(gbk.getBytes(CHARSET_GB2312), StandardCharsets.UTF_8).trim();
    }
}
