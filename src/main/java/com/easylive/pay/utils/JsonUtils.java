package com.easylive.pay.utils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 */
public class JsonUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static String printObject(Object o) {
        try {
            return (objectMapper.writeValueAsString(o));
        } catch (Exception e) {
        }
        return "";
    }

    public static <T> T fromString(String json, Class<T> clazz) {
        if (json == null) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Unable to parse Json String.", e);
        }
    }

    public static <T> T convertValue(Object fromValue, TypeReference<?> clazz) {
        if (fromValue == null) {
            return null;
        }
        try {
            return (T) objectMapper.convertValue(fromValue, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Unable to parse Json String.", e);
        }
    }

    public static <T> T fromString(String s, TypeReference<T> typeReference) {

        try {
            return objectMapper.readValue(s, typeReference);
        } catch (Exception e) {
            throw new RuntimeException("Error parse string to json object", e);
        }
    }

    public static String toString(Object o) {
        try {
            return (objectMapper.writeValueAsString(o));
        } catch (Exception e) {
            throw new RuntimeException("Error writing json object: {}", e);
        }
    }

    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    public static boolean isJSON(String str) {
        boolean result = false;
        try {
            Object obj = JSON.parse(str);
            if (null != obj) {
                result = true;
            }
        } catch (Exception e) {
            result = false;
        }
        return result;
    }
}
