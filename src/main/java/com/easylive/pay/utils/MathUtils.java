package com.easylive.pay.utils;

import java.text.NumberFormat;

/**
 * <AUTHOR>
 * @date 2021/9/7 11:34
 */
public class MathUtils {

    public static String numberFormat(Double number) {
        return numberFormat(number, 2);
    }

    /**
     * 格式化保留2位小数
     */
    public static String numberFormat(Double number, int val) {
        NumberFormat numberInstance = NumberFormat.getNumberInstance();
        numberInstance.setMaximumFractionDigits(val);
        return numberInstance.format(number);
    }

}
