package com.easylive.pay.config;

import com.easylive.pay.config.apollo.ServerApiConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AliPayConfig {

    @Autowired
    private ServerApiConfig serverApiConfig;

    @Value("${pay.ali.init.key:true}")
    private boolean initKey;

    @Value("${pay.ali.public.key.path}")
    private String publicKeyPath;

    @Value("${pay.ali.private.key.path}")
    private String privateKeyPath;

    @Value("${pay.ali.rsakey}")
    private String rsaKey;

    @Value("${pay.ali.notify.path}")
    private String notifyPath;


    @Value("${pay.ali.return.path}")
    private String returnPath;


    public String getWebReturnUrl() {
        return serverApiConfig.getPayCallbackDomain() + returnPath;
    }
    public boolean isInitKey() {
        return initKey;
    }

    public String getPublicKeyPath() {
        return publicKeyPath;
    }

    public String getPrivateKeyPath() {
        return privateKeyPath;
    }

    public String getNotifyUrl() {
        return serverApiConfig.getPayCallbackDomain() + notifyPath;
    }

    public String getRsaKey() {
        return rsaKey;
    }
}
