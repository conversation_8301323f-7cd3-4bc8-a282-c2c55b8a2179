package com.easylive.pay.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 玖合云配置
 */
@Configuration
public class JiuheCashConfig {

    @Value("${cashout.jiuhe.platform.pubKey}")
    private String pubKey;

    @Value("${cashout.jiuhe.client.priKey}")
    private String priKey;

    @Value("${cashout.jiuhe.appId}")
    private String appId;

    @Value("${cashout.jiuhe.domain}")
    private String domain;

    @Value("${cashout.jiuhe.projectId}")
    private String projectId;

    @Value("${cashout.jiuhe.fundId}")
    private String fundId;

    /**
     * 创建自由职业者
     */
    private String AddMemberUrl = "/v2/soho/create/";

    /**
     * 批次支付
     */
    private String batchPay = "/v2/remit/batch/pay/";

    /**
     * 查询订单
     */
    private String queryOrder = "/v2/remit/order/query/";

    /**
     * 文件上传
     */
    private String uploadFile = "/v2/file/upload/";


    public String getPubKey() {
        return pubKey;
    }

    public void setPubKey(String pubKey) {
        this.pubKey = pubKey;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getPriKey() {
        return priKey;
    }

    public void setPriKey(String priKey) {
        this.priKey = priKey;
    }


    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getAddMemberUrl() {
        return AddMemberUrl;
    }

    public void setAddMemberUrl(String addMemberUrl) {
        AddMemberUrl = addMemberUrl;
    }

    public String getBatchPay() {
        return batchPay;
    }

    public String getQueryOrder() {
        return queryOrder;
    }

    public String getUploadFile() {
        return uploadFile;
    }

    public String getProjectId() {
        return projectId;
    }

    public String getFundId() {
        return fundId;
    }

}
