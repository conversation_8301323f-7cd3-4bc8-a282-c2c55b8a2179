package com.easylive.pay.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Configuration
public class CashConfig {

    @Value("${cash.limit}")
    private long limit;

    @Value("${cash.rate.rmb.to.riceroll}")
    private long rateOfRmbToRiceroll;

    @Value("${cash.fee}")
    private int fee;

    @Value("${cash.riceuid}")
    private String riceUid;

    @Value("${cash.rate.ecoin.to.riceroll}")
    private long rateOfEcoinToRiceroll;

    @Value("${cash.gift.ecoin.to.riceroll}")
    private long giftRateOfEcoinToRiceroll;

    @Value("${cash.payliving.ecoin.to.riceroll}")
    private long payLivingOfEcoinToRiceroll;

    @Value("${cash.payrecord.ecoin.to.riceroll}")
    private long payRecordOfEcoinToRiceroll;

    @Value("${cash.rate.ecoin.to.rmb}")
    private long rateOfEcoinToRmb;

    @Value("${cash.rate.with.draw.extra:0.2}")
    private BigDecimal rateOfWithDrawExtra;

    @Value("${cash.with.draw.extra.begin.time:2022-08-01}")
    private String withDrawExtraBeginTime;

    public long getLimit() {
        return limit;
    }

    public long getRateOfRmbToRiceroll() {
        return rateOfRmbToRiceroll;
    }

    public int getFee() {
        return fee;
    }

    public List<Long> getRiceNameList() {
        String[] uidStrArray = riceUid.split(",");
        Long[] uidLongArray = new Long[uidStrArray.length];
        for (int i = 0; i < uidStrArray.length; i++) {
            uidLongArray[i] = Long.parseLong(uidStrArray[i]);
        }

        return Arrays.asList(uidLongArray);
    }

    public long getRateOfEcoinToRiceroll() {
        return rateOfEcoinToRiceroll;
    }


    public long getGiftRateOfEcoinToRiceroll() {
        return giftRateOfEcoinToRiceroll;
    }

    public long getPayLivingOfEcoinToRiceroll() {
        return payLivingOfEcoinToRiceroll;
    }

    public long getPayRecordOfEcoinToRiceroll() {
        return payRecordOfEcoinToRiceroll;
    }

    public long getRateOfEcoinToRmb() {
        return rateOfEcoinToRmb;
    }

    public BigDecimal getRateOfWithDrawExtra() {
        return rateOfWithDrawExtra;
    }

    public void setRateOfWithDrawExtra(BigDecimal rateOfWithDrawExtra) {
        this.rateOfWithDrawExtra = rateOfWithDrawExtra;
    }

    public String getWithDrawExtraBeginTime() {
        return withDrawExtraBeginTime;
    }

    public void setWithDrawExtraBeginTime(String withDrawExtraBeginTime) {
        this.withDrawExtraBeginTime = withDrawExtraBeginTime;
    }
}

