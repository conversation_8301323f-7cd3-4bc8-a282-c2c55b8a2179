package com.easylive.pay.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 薪云配置
 */
@Configuration
public class XinyunCashConfig {

    @Value("${cashout.xinyun.pubKey}")
    private String pubKey;

    @Value("${cashout.xinyun.priKey}")
    private String priKey;

    @Value("${cashout.xinyun.merId}")
    private String merId;

    @Value("${cashout.xinyun.domain}")
    private String domain;

    /**
     * 添加会员
     */
    private String AddMemberUrl = "/apienterprise/Undertaker/addmember";
    /**
     * 提现地址
     **/
    private String withdrawUrl = "/apienterprise/Undertaker/paymentorderdata";

    /**
     * 付款进度查询
     **/
    private String paymentschedule = "/apienterprise/Undertaker/paymentschedule";

    /**
     * 支付宝更新
     */
    private String UpdateMemberZfbUrl = "/apienterprise/Undertaker/updatememberzfb";

    /**
     * 银行信息更新
     */
    private String UpdateMemberBankUrl = "/apienterprise/Undertaker/updatememberbank";

    public String getPubKey() {
        return pubKey;
    }

    public void setPubKey(String pubKey) {
        this.pubKey = pubKey;
    }

    public String getPriKey() {
        return priKey;
    }

    public void setPriKey(String priKey) {
        this.priKey = priKey;
    }

    public String getMerId() {
        return merId;
    }

    public void setMerId(String merId) {
        this.merId = merId;
    }

    public String getAddMemberUrl() {
        return domain + AddMemberUrl;
    }

    public String getUpdateMemberZfbUrl() { return domain + UpdateMemberZfbUrl;}

    public String getUpdateMemberBankUrl() { return domain + UpdateMemberBankUrl;}

    public String getWithdrawUrl() {
        return domain + withdrawUrl;
    }

    public String getPaymentschedule() {
        return domain + paymentschedule;
    }
}
