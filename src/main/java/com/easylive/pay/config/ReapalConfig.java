package com.easylive.pay.config;

import com.easylive.pay.config.apollo.ServerApiConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Created by liwd on 2016/12/25.
 */
@Configuration
public class ReapalConfig {

    @Autowired
    private ServerApiConfig serverApiConfig;

    @Value("${pay.reapal.merchantid}")
    private String merchantid;

    @Value("${pay.reapal.key}")
    private String key;

    @Value("${pay.reapal.seller-email}")
    private String sellerEmail;

    @Value("${pay.reapal.rongpay-api}")
    private String rongpayApi;

    @Value("${pay.reapal.privatekey}")
    private String privatekey;

    @Value("${pay.reapal.privatekey-pwd}")
    private String privatekeyPwd;

    @Value("${pay.reapal.pubkey}")
    private String pubkey;

    @Value("${pay.reapal.charset:utf-8}")
    private String charset;

    @Value("${pay.reapal.signtype:MD5}")
    private String signtype;

    @Value("${pay.reapal.transport:http}")
    private String transport;

    public String getMerchantid() {
        return merchantid;
    }

    public String getKey() {
        return key;
    }

    public String getSellerEmail() {
        return sellerEmail;
    }

    public String getRongpayApi() {
        return rongpayApi;
    }

    public String getNotifyUrl() {
        return serverApiConfig.getPayCallbackDomain() + "/pay/reapal";
    }

    public String getPrivatekey() {
        return privatekey;
    }

    public String getPubkey() {
        return pubkey;
    }

    public String getCharset() {
        return charset;
    }

    public String getSigntype() {
        return signtype;
    }

    public String getTransport() {
        return transport;
    }

    public String getPrivatekeyPwd() {
        return privatekeyPwd;
    }

}
