package com.easylive.pay.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.easylive.pay.common.DataSourceRouteHolder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.orm.hibernate4.HibernateTransactionManager;
import org.springframework.orm.hibernate4.LocalSessionFactoryBean;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PreDestroy;
import javax.sql.DataSource;
import java.util.*;

@Configuration
@EnableTransactionManagement(order = 2)
public class DatabaseConfig {

    private final DataSourceManager dataSourceManager = new DataSourceManager();
    @Value("${db.master.url}")
    String DB_MASTER_URL;
    @Value("${db.master.username}")
    String DB_MASTER_USERNAME;
    @Value("${db.master.password}")
    String DB_MASTER_PASSWORD;
    @Value("${db.slave.url}")
    String DB_SLAVE_URL;
    @Value("${db.slave.username}")
    String DB_SLAVE_USERNAME;
    @Value("${db.slave.password}")
    String DB_SLAVE_PASSWORD;
    @Value("${db.driver:com.mysql.cj.jdbc.Driver}")
    String DB_DRIVER;
    @Value("${db.pool.max-active:128}")
    Integer DB_POOL_MAX_ACTIVE;
    @Value("${db.pool.min-idle:4}")
    Integer DB_POOL_MIN_IDLE;
    @Value("${db.pool.initial-size:8}")
    Integer DB_POOL_INITIAL_SIZE;
    @Value("${db.pool.type:druid}")
    String DB_POOL_TYPE = DataSourceManager.POOL_TYPE_DRUID;
    @Value("${hibernate.dialect:org.hibernate.dialect.MySQL5Dialect}")
    private String HIBERNATE_DIALECT;

    //@Value("${hibernate.hbm2ddl.auto}")
    //private String HIBERNATE_HBM2DDL_AUTO;
    @Value("${hibernate.show_sql:false}")
    private String HIBERNATE_SHOW_SQL;
    @Value("${hibernate.entitymanager.packagesToScan:com.easylive.pay}")
    private String ENTITYMANAGER_PACKAGES_TO_SCAN;

    @Bean
    public DataSource dataSource() {
        return dataSourceManager.getDataSource(this);
    }

    @PreDestroy
    public void close() {
        dataSourceManager.close();
    }

    @Bean
    public LocalSessionFactoryBean sessionFactory() {
        LocalSessionFactoryBean sessionFactoryBean = new LocalSessionFactoryBean();
        sessionFactoryBean.setDataSource(dataSource());
        sessionFactoryBean.setPackagesToScan(ENTITYMANAGER_PACKAGES_TO_SCAN);
        Properties hibernateProperties = new Properties();
        hibernateProperties.put("hibernate.dialect", HIBERNATE_DIALECT);
        hibernateProperties.put("hibernate.show_sql", HIBERNATE_SHOW_SQL);
        //  hibernateProperties.put("hibernate.hbm2ddl.auto", HIBERNATE_HBM2DDL_AUTO);
        sessionFactoryBean.setHibernateProperties(hibernateProperties);

        return sessionFactoryBean;
    }

    @Bean
    public HibernateTransactionManager transactionManager() {
        HibernateTransactionManager transactionManager =
                new HibernateTransactionManager();
        transactionManager.setSessionFactory(sessionFactory().getObject());

        return transactionManager;
    }

} // class DatabaseConfig

class DynamicDataSource extends AbstractRoutingDataSource {

    @Override
    protected Object determineCurrentLookupKey() {
        return DataSourceRouteHolder.getDataSourceKey();
    }

}

class DataSourceManager {
    static final String POOL_TYPE_DRUID = "druid";
    static final String POOL_TYPE_DEFAULT = "default";
    static final String POOL_TYPE_TOMCAT = "tomcat";

    private DataSource dataSource = null;

    public DataSource getDataSource(DatabaseConfig config) {
        if (this.dataSource == null) {
            DynamicDataSource dynamicDataSource = new DynamicDataSource();
            switch (config.DB_POOL_TYPE) {
                case POOL_TYPE_DEFAULT: {
                    DriverManagerDataSource masterDataSource = new DriverManagerDataSource();
                    masterDataSource.setDriverClassName(config.DB_DRIVER);
                    masterDataSource.setUrl(config.DB_MASTER_URL);
                    masterDataSource.setUsername(config.DB_MASTER_USERNAME);
                    masterDataSource.setPassword(config.DB_MASTER_PASSWORD);
                    DriverManagerDataSource slaveDataSource = new DriverManagerDataSource();
                    slaveDataSource.setDriverClassName(config.DB_DRIVER);
                    slaveDataSource.setUrl(config.DB_SLAVE_URL);
                    slaveDataSource.setUsername(config.DB_SLAVE_USERNAME);
                    slaveDataSource.setPassword(config.DB_SLAVE_PASSWORD);
                    dynamicDataSource.setDefaultTargetDataSource(masterDataSource);
                    Map<Object, Object> targetDataSources = new HashMap<>();
                    targetDataSources.put(com.easylive.pay.common.DataSource.master, masterDataSource);
                    targetDataSources.put(com.easylive.pay.common.DataSource.slave, slaveDataSource);
                    dynamicDataSource.setTargetDataSources(targetDataSources);
                    this.dataSource = dynamicDataSource;
                    break;
                }
                case POOL_TYPE_DRUID: {
                    List<String> initSqls = new ArrayList<>();
                    initSqls.add("SET NAMES utf8mb4");
                    DruidDataSource masterDataSource = new DruidDataSource();
                    masterDataSource.setDriverClassName(config.DB_DRIVER);
                    masterDataSource.setUrl(config.DB_MASTER_URL);
                    masterDataSource.setUsername(config.DB_MASTER_USERNAME);
                    masterDataSource.setPassword(config.DB_MASTER_PASSWORD);
                    masterDataSource.setMaxActive(config.DB_POOL_MAX_ACTIVE);
                    masterDataSource.setMinIdle(config.DB_POOL_MIN_IDLE);
                    masterDataSource.setInitialSize(config.DB_POOL_INITIAL_SIZE);
                    masterDataSource.setConnectionInitSqls(initSqls);
                    //masterDataSource.setRemoveAbandoned(true);
                    DruidDataSource slaveDataSource = new DruidDataSource();
                    slaveDataSource.setDriverClassName(config.DB_DRIVER);
                    slaveDataSource.setUrl(config.DB_SLAVE_URL);
                    slaveDataSource.setUsername(config.DB_SLAVE_USERNAME);
                    slaveDataSource.setPassword(config.DB_SLAVE_PASSWORD);
                    slaveDataSource.setMaxActive(config.DB_POOL_MAX_ACTIVE);
                    slaveDataSource.setMinIdle(config.DB_POOL_MIN_IDLE);
                    slaveDataSource.setInitialSize(config.DB_POOL_INITIAL_SIZE);
                    slaveDataSource.setConnectionInitSqls(initSqls);
                    dynamicDataSource.setDefaultTargetDataSource(masterDataSource);
                    Map<Object, Object> targetDataSources = new HashMap<>();
                    targetDataSources.put(com.easylive.pay.common.DataSource.master, masterDataSource);
                    targetDataSources.put(com.easylive.pay.common.DataSource.slave, slaveDataSource);
                    dynamicDataSource.setTargetDataSources(targetDataSources);
                    this.dataSource = dynamicDataSource;
                    break;
                }
                default: { //tomcat as the default pool
                    org.apache.tomcat.jdbc.pool.DataSource masterDataSource = new org.apache.tomcat.jdbc.pool.DataSource();
                    masterDataSource.setDriverClassName(config.DB_DRIVER);
                    masterDataSource.setUrl(config.DB_MASTER_URL);
                    masterDataSource.setUsername(config.DB_MASTER_USERNAME);
                    masterDataSource.setPassword(config.DB_MASTER_PASSWORD);
                    masterDataSource.setMaxActive(config.DB_POOL_MAX_ACTIVE);
                    masterDataSource.setMinIdle(config.DB_POOL_MIN_IDLE);
                    masterDataSource.setInitialSize(config.DB_POOL_INITIAL_SIZE);
                    org.apache.tomcat.jdbc.pool.DataSource slaveDataSource = new org.apache.tomcat.jdbc.pool.DataSource();
                    slaveDataSource.setDriverClassName(config.DB_DRIVER);
                    slaveDataSource.setUrl(config.DB_SLAVE_URL);
                    slaveDataSource.setUsername(config.DB_SLAVE_USERNAME);
                    slaveDataSource.setPassword(config.DB_SLAVE_PASSWORD);
                    slaveDataSource.setMaxActive(config.DB_POOL_MAX_ACTIVE);
                    slaveDataSource.setMinIdle(config.DB_POOL_MIN_IDLE);
                    slaveDataSource.setInitialSize(config.DB_POOL_INITIAL_SIZE);
                    dynamicDataSource.setDefaultTargetDataSource(masterDataSource);
                    Map<Object, Object> targetDataSources = new HashMap<>();
                    targetDataSources.put(com.easylive.pay.common.DataSource.master, masterDataSource);
                    targetDataSources.put(com.easylive.pay.common.DataSource.slave, slaveDataSource);
                    dynamicDataSource.setTargetDataSources(targetDataSources);
                    this.dataSource = dynamicDataSource;
                    break;
                }
            }
        }
        return this.dataSource;
    }

    public void close() {
        if (this.dataSource instanceof DruidDataSource) {
            ((DruidDataSource) this.dataSource).close();
        } else if (this.dataSource instanceof org.apache.tomcat.jdbc.pool.DataSource) {
            ((org.apache.tomcat.jdbc.pool.DataSource) this.dataSource).close();
        }
    }


}

