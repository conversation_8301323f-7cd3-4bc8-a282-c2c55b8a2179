package com.easylive.pay.config;

import com.easylive.pay.GracefulShutdown;
import com.easylive.pay.common.AssetBizInterceptor;
import com.easylive.pay.common.RequestAttributeResolver;
import com.easylive.pay.common.ResponseBodyWrapFactoryBean;
import com.easylive.pay.common.SessionInterceptor;
import com.easylive.pay.enums.RankInterval;
import com.easylive.pay.enums.RankItemType;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.curator.RetryPolicy;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.RetryForever;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.embedded.EmbeddedServletContainerCustomizer;
import org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.support.DefaultFormattingConversionService;
import org.springframework.format.support.FormattingConversionService;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 15/12/12
 */
@EnableWebMvc
@Configuration
public class ApplicationConfig extends WebMvcConfigurerAdapter {
    @Value("${spring.profiles.active:Unknown}")
    private String activeProfile;

    @Value("${system.zookeeper.hosts}")
    private String zooKeeperHost;

    @Bean
    public TaskScheduler taskScheduler() {
        final ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10);
        return scheduler;
    }

    @Bean
    public SessionInterceptor sessionInterceptor() {
        return new SessionInterceptor();
    }

    @Bean
    public AssetBizInterceptor authInterceptor() {
        return new AssetBizInterceptor();
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new RequestAttributeResolver());
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(sessionInterceptor());
        registry.addInterceptor(authInterceptor());
    }

    @Bean
    public ResponseBodyWrapFactoryBean getResponseBodyWrap() {
        return new ResponseBodyWrapFactoryBean();
    }

    @Bean
    public StringHttpMessageConverter stringConverter() {
        final StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(Charset.forName("UTF-8"));
        stringConverter.setSupportedMediaTypes(Arrays.asList(
                MediaType.TEXT_PLAIN,
                MediaType.TEXT_HTML,
                MediaType.APPLICATION_JSON));
        return stringConverter;
    }

    @Bean
    public FormattingConversionService mvcConversionService() {
        FormattingConversionService conversionService = new DefaultFormattingConversionService();
        conversionService.addConverter(new RankIntervalConvert());
        conversionService.addConverter(new RankItemTypeConvert());
        return conversionService;
    }

    @Bean
    public GracefulShutdown gracefulShutdown() {
        return new GracefulShutdown();
    }

    @Bean
    public EmbeddedServletContainerCustomizer tomcatCustomizer() {
        return container -> {
            if (container instanceof TomcatEmbeddedServletContainerFactory) {
                ((TomcatEmbeddedServletContainerFactory) container).addConnectorCustomizers(gracefulShutdown());
            }
        };
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder();
//        builder.serializationInclusion(JsonInclude.Include.NON_NULL);
//        builder.propertyNamingStrategy(PropertyNamingStrategy.CAMEL_CASE_TO_LOWER_CASE_WITH_UNDERSCORES);
//        builder.serializationInclusion(JsonInclude.Include.NON_EMPTY);
//        builder.timeZone("GMT+8");

        builder.dateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        builder.featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        converters.add(stringConverter());
        converters.add(new MappingJackson2HttpMessageConverter(builder.build()));
//        converters.add(new MappingJackson2XmlHttpMessageConverter(builder.createXmlMapper(true).build()));
    }

    public boolean isDevMode() {
        return "dev".equals(activeProfile);
    }



    @Bean
    public CuratorFramework curatorFramework() {
        RetryPolicy retryPolicy = new RetryForever(5000);
        CuratorFramework curator = CuratorFrameworkFactory.newClient(zooKeeperHost, retryPolicy);
        curator.start();
        return curator;
    }

}

class RankIntervalConvert implements Converter<String, RankInterval> {

    @Override
    public RankInterval convert(String source) {
        return RankInterval.of(source);
    }
}

class RankItemTypeConvert implements Converter<String, RankItemType> {

    @Override
    public RankItemType convert(String source) {
        return RankItemType.of(source);
    }
}