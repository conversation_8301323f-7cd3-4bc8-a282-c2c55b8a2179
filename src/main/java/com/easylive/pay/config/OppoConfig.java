package com.easylive.pay.config;

import com.easylive.pay.config.apollo.ServerApiConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

@Configuration
public class OppoConfig {

    @Autowired
    private ServerApiConfig serverApiConfig;

    @Value("${pay.oppo.recharge.scale:0.7}")
    private BigDecimal rechargeScale;

    @Value("${pay.oppo.notify.key}")
    private String notifyPublicKey;

    @Value("${pay.oppo.app.key}")
    private String appKey;

    @Value("${pay.oppo.app.secret}")
    private String appSecret;

    @Value("${pay.oppo.user.check.url}")
    private String userCheckUrl;


    public BigDecimal getRechargeScale() {
        return rechargeScale;
    }

    public String getNotifyUrl() {
        return serverApiConfig.getPayCallbackDomain() + "/pay/oppo";
    }

    public String getNotifyPublicKey() {
        return notifyPublicKey;
    }

    public String getAppKey() {
        return appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public String getUserCheckUrl() {
        return userCheckUrl;
    }
}
