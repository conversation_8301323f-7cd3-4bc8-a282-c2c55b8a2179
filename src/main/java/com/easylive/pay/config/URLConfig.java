package com.easylive.pay.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class URLConfig {

    @Value("${url.serverip}")
    private String serverIp;

    @Value("${url.appgate}")
    private String appGateService;

    @Value("${url.appgate.yaomer}")
    private String newAppGateService;

    @Value("${url.redpack.icon:}")
    private String redpackIcon;

    @Value("${url.redpack.icon.yaomer:}")
    private String redpackIconMagicLive;

    @Value("${url.service.user}")
    private String userServiceUrl;

    @Value("${url.service.chat}")
    private String chatServiceUrl;

    @Value("${url.service.imsever}")
    private String imServerServiceUrl;

    @Value("${url.service.lotus}")
    private String loutsServiceUrl;

    @Value("${url.service.ieasy}")
    private String ieasyServiceUrl;

    @Value("${url.service.bizcore}")
    private String bizcoreServiceUrl;

    @Value("${url.service.asset}")
    private String assetServiceUrl;

    @Value("${url.service.center}")
    private String centerServiceUrl;

    @Value("${url.service.provider}")
    private String providerServiceUrl;


    public String getBizcoreServiceUrl() {
        return bizcoreServiceUrl;
    }

    public String getAssetServiceUrl() {
        return assetServiceUrl;
    }

    public String getServerIp() {
        return serverIp;
    }

    public String getAppGateService() {
        return appGateService;
    }

    public String getRedpackIcon() {
        return redpackIcon;
    }

    public String getNewAppGateService() {
        return newAppGateService;
    }

    public String getUserServiceUrl() {
        return userServiceUrl;
    }

    public String getLoutsServiceUrl() {
        return loutsServiceUrl;
    }

    public String getRedpackIconMagicLive() {
        return redpackIconMagicLive;
    }

    public String getChatServiceUrl() {
        return chatServiceUrl;
    }

    public String getImServerServiceUrl() {
        return imServerServiceUrl;
    }

    public String getIeasyServiceUrl() {
        return ieasyServiceUrl;
    }

    public String getCenterServiceUrl() {
        return centerServiceUrl;
    }

    public String getProviderServiceUrl() {
        return providerServiceUrl;
    }
}
