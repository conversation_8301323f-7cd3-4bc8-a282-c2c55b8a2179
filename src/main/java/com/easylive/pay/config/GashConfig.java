
package com.easylive.pay.config;

import com.easylive.pay.config.apollo.ServerApiConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

@Configuration
public class GashConfig {

    @Autowired
    private ServerApiConfig serverApiConfig;

    @Value("${pay.gash.recharge.scale:0.2}")
    private BigDecimal rechargeScale;

    @Value("${pay.gash.recharge.return_path:unknown}")
    private String returnPath;

    @Value("${pay.gash.pcode:unknown}")
    private String PCODE;

    @Value("${pay.gash.key:unknown}")
    private String KEY;

    @Value("${pay.gash.iv:unknown}")
    private String IV;

    @Value("${pay.gash.pwd:unknown}")
    private String PWD;

    @Value("${pay.gash.cid:unknown}")
    private String CID;

    @Value("${pay.gash.paid:unknown}")
    private String PAID;

    @Value("${pay.gash.mid:unknown}")
    private String MID;

    public BigDecimal getRechargeScale() {
        return rechargeScale;
    }

    public String getReturnUrl() {
        return serverApiConfig.getPayCallbackDomain() + returnPath;
    }

    public String getPCODE() {
        return PCODE;
    }

    public String getKEY() {
        return KEY;
    }

    public String getIV() {
        return IV;
    }

    public String getPWD() {
        return PWD;
    }

    public String getCID() {
        return CID;
    }

    public String getMID() {
        return MID;
    }
}
