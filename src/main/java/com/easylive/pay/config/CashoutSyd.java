package com.easylive.pay.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 税优地系统配置
 */
@Configuration
public class CashoutSyd {

    @Value("${cashout.syd.priKey}")
    private String cashoutSydPriKey;

    @Value("${cashout.syd.pubKey}")
    private String cashoutSydPubKey;

    @Value("${cashout.syd.interKey}")
    private String cashoutSydInterKey;

    @Value("${cashout.syd.merId}")
    private String cashoutSydMerId;

    @Value("${cashout.syd.domain}")
    private String cashoutSydDomain;

    @Value("${cashout.syd.development}")
    private boolean cashoutSydDevelopment;

    /**
     * 税筹2.0
     */
    @Value("${cashout.sc.priKey}")
    private String cashoutScPriKey;

    @Value("${cashout.sc.pubKey}")
    private String cashoutScPubKey;

    @Value("${cashout.sc.merId}")
    private String cashoutScMerId;

    @Value("${cashout.sc.domain}")
    private String cashoutScDomain;

    @Value("${cashout.sc.interKey}")
    private String cashoutScInterKey;

    @Value("${cashout.sc.callback.ip}")
    private String cashoutScCallbackIp;

    public String getCashoutSydPriKey() {
        return cashoutSydPriKey;
    }

    public String getCashoutSydPubKey() {
        return cashoutSydPubKey;
    }

    public String getCashoutSydInterKey() {
        return cashoutSydInterKey;
    }

    public String getCashoutSydMerId() {
        return cashoutSydMerId;
    }

    public String getCashoutSydDomain() {
        return cashoutSydDomain;
    }

    public boolean isCashoutSydDevelopment() {
        return cashoutSydDevelopment;
    }

    public String getCashoutScPriKey() {
        return cashoutScPriKey;
    }

    public String getCashoutScPubKey() {
        return cashoutScPubKey;
    }

    public String getCashoutScMerId() {
        return cashoutScMerId;
    }

    public String getCashoutScDomain() {
        return cashoutScDomain;
    }

    public String getCashoutScInterKey() {
        return cashoutScInterKey;
    }

    public String getCashoutScCallbackIp() {
        return cashoutScCallbackIp;
    }
}
