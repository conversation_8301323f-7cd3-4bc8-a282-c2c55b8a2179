package com.easylive.pay.config;

import com.easylive.pay.common.event.EventProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */

@Slf4j
@Configuration
public class KafkaConfig {

    @Value("${kafka.server.bootstrap}")
    private String bootStrapServer;

    @Autowired
    private Executor taskExecutor;

    @Bean
    public EventProducer eventProducer() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootStrapServer);
        EventProducer eventProducer = new EventProducer(configProps);
        eventProducer.start(taskExecutor);
        return eventProducer;
    }


    @Bean
    DisposableBean eventStopBean() {
        return new DisposableBean() {

            @Autowired
            private EventProducer eventProducer;

            @Override
            public void destroy() {
                eventProducer.stop();
            }
        };
    }
}
