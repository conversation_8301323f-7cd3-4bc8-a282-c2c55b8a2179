package com.easylive.pay.config.apollo;

import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


@Configuration
@EnableApolloConfig(value = {"server-api"})
@Data
public class ServerApiConfig {

    @Value("${pay.callback.domain:unknown}")
    private String payCallbackDomain;

    protected static final Logger logger = LoggerFactory.getLogger(ServerApiConfig.class);

    @ApolloConfigChangeListener(value = {"server-api"})
    private void onChange(ConfigChangeEvent changeEvent) {

        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
            if(!changeEvent.isChanged(key)){
                continue;
            }

            if("pay.callback.domain".equals(change.getPropertyName())){
                this.setPayCallbackDomain(change.getNewValue());
                logger.info("pay.callback.domain has changed to new value:{}",change.getNewValue());
            }
        }
    }
}
