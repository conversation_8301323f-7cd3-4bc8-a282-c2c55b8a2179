package com.easylive.pay.config.apollo;

import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


@Configuration
@EnableApolloConfig(value = {"cdn-statics"})
@Data
public class CdnConfig {

    @Value("${aliimg.oss.url:unknown}")
    private String aliImgOssURL;

    @Value("${hd.oss.url:unknown}")
    private String hdOssURL;

    @Value("${video.thumb.base.url:unknown}")
    private String videoThumbURL;


    protected static final Logger logger = LoggerFactory.getLogger(CdnConfig.class);

    @ApolloConfigChangeListener(value = {"cdn-statics"})
    private void onChange(ConfigChangeEvent changeEvent) {

        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
            if(!changeEvent.isChanged(key)){
                continue;
            }

            if("aliimg.oss.url".equals(change.getPropertyName())){
                this.setAliImgOssURL(change.getNewValue());
                logger.info("aliimg.oss.url has changed to new value:{}",change.getNewValue());
            }

            if("hd.oss.url".equals(change.getPropertyName())){
                this.setHdOssURL(change.getNewValue());
                logger.info("hd.oss.url has changed to new value:{}",change.getNewValue());
            }

            if("video.thumb.base.url".equals(change.getPropertyName())){
                this.setVideoThumbURL(change.getNewValue());
                logger.info("video.thumb.base.url has changed to new value:{}",change.getNewValue());
            }
        }
    }
}
