package com.easylive.pay.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 提现配置
 */
@Configuration
public class ThirdBankConfig {

    @Value("${xzyq.mac.secret_key}")
    private String secretKey;

    @Value("${xzyq.bank.url}")
    private String bankUrl;

    /**
     * 企业代码
     */
    @Value("${xzyq.bank.enterprise.code}")
    private String enterpriseCode;

    /**
     * 企业代码
     */
    @Value("${xzyq.bank.enterprise.code.new}")
    private String enterpriseCodeNew;

    /**
     * 交易代码
     */
    @Value("${xzyq.bank.bank-no}")
    private String bankNo;

    /**
     * 渠道类型
     */
    @Value("${xzyq.bank.channel.type}")
    private String channelType;

    public String getSecretKey() {
        return secretKey;
    }

    public String getBankUrl() {
        return bankUrl;
    }

    public String getEnterpriseCode() {
        return enterpriseCode;
    }

    public String getEnterpriseCodeNew() {
        return enterpriseCodeNew;
    }

    public String getBankNo() {
        return bankNo;
    }

    public String getChannelType() {
        return channelType;
    }

}
