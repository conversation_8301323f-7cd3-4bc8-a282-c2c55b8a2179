package com.easylive.pay.config;

import com.easylive.pay.config.apollo.ServerApiConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Created by c on 2015/12/19.
 */
@Configuration
public class WeixinConfig {

    @Autowired
    private ServerApiConfig serverApiConfig;

    @Value("${weixin.send_name}")
    String sendName;

    @Value("${weixin.wishing}")
    String wishing;

    @Value("${weixin.act_name}")
    String actName;

    @Value("${weixin.remark}")
    String remark;


    @Value("${pay.weixin.miniapp.appid}")
    String miniappAppId;

    @Value("${pay.weixin.miniapp.app.notify}")
    String miniappAppNotifyUrl;

    @Value("${pay.weixin.redpack.cert.file}")
    private String redpackCertFile;

    @Value("${pay.weixin.redpack.cert.pass}")
    private String redpackCertPass;

    @Value("${pay.weixin.mp.appid}")
    private String mpAppId;

    @Value("${pay.weixin.app.appid}")
    private String appAppId;

    @Value("${pay.weixin.code.appid}")
    private String codeAppId;

    @Value("${pay.weixin.h5.appid}")
    private String h5AppId;

    @Value("${pay.weixin.notify.path}")
    private String notifyPath;

    @Value("${pay.weixin.h5.path}")
    private String h5PayPath;


    public String getSendName() {
        return sendName;
    }

    public String getWishing() {
        return wishing;
    }

    public String getActName() {
        return actName;
    }

    public String getRemark() {
        return remark;
    }

    public String getMiniappAppId() {
        return miniappAppId;
    }

    public String getMiniappAppNotifyUrl() {
        return miniappAppNotifyUrl;
    }

    public String getNotifyUrl() {
        return serverApiConfig.getPayCallbackDomain() + notifyPath;
    }

    public String getRedpackCertFile() {
        return redpackCertFile;
    }

    public String getRedpackCertPass() {
        return redpackCertPass;
    }

    public String getMpAppId() {
        return mpAppId;
    }

    public String getAppAppId() {
        return appAppId;
    }

    public String getCodeAppId() {
        return codeAppId;
    }

    public String getH5AppId() {
        return h5AppId;
    }

    public String getH5PayPath() {
        return h5PayPath;
    }
}
