package com.easylive.pay.output;

import com.easylive.pay.model.User;

public class BuyGuardianOut {

    private long ecoin;
    private long riceroll;
    private long barley;
    private String nickname;
    private String name;
    private String logourl;
    private long vip;
    private long viplevel;
    private long level;
    private long anchorlevel;

    public BuyGuardianOut(User user, BaseAsset asset) {
        ecoin = asset.getEcoin();
        riceroll = asset.getRiceroll();
        barley = asset.getBarley();
        nickname = user.getNickname();
        name = user.getName();
        logourl = user.getLogoUrl();
        vip = user.getVip();
        viplevel = user.getVipLevel();
        level = user.getLevel();
        anchorlevel = user.getAnchorLevel();
    }

    public long getEcoin() {
        return this.ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public long getRiceroll() {
        return this.riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public long getBarley() {
        return this.barley;
    }

    public void setBarley(long barley) {
        this.barley = barley;
    }

    public String getNickname() {
        return this.nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogourl() {
        return this.logourl;
    }

    public void setLogourl(String logourl) {
        this.logourl = logourl;
    }

    public long getVip() {
        return this.vip;
    }

    public void setVip(long vip) {
        this.vip = vip;
    }

    public long getViplevel() {
        return this.viplevel;
    }

    public void setViplevel(long viplevel) {
        this.viplevel = viplevel;
    }

    public long getAnchorlevel() {
        return this.anchorlevel;
    }

    public void setAnchorlevel(long anchorlevel) {
        this.anchorlevel = anchorlevel;
    }

    public long getLevel() {
        return this.level;
    }

    public void setlevel(long anchorlevel) {
        this.level = level;
    }
}
