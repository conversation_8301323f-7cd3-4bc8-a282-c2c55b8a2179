package com.easylive.pay.output.common;

import java.util.List;

public class ScrollablePageList<T> {
    private final List<T> list;
    private int page;
    private int size;
    private int next;

    public ScrollablePageList(List<T> list, int page, int size) {
        this.list = list;
        this.page = page;
        this.size = list.size();
        if (list.size() != size)
            this.next = -1;
        else
            this.next = page + 1;
    }

    public List<T> getList() {
        return list;
    }

    public int getPage() {
        return page;
    }

    public int getSize() {
        return size;
    }

    public int getNext() {
        return next;
    }
}
