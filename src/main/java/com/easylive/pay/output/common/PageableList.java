package com.easylive.pay.output.common;

import java.util.List;

public class PageableList<T> extends ScrollablePageList<T> {

    private long total;

    public PageableList(List<T> list, int page, int size, long total) {
        super(list, page, size);
        this.total = total;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
