package com.easylive.pay.output;

import com.easylive.pay.entity.CashoutLimitEntity;
import com.easylive.pay.entity.UserCoin;

/**
 * 用户资产
 * Created by c on 2015/12/14.
 */
public class Asset {

    private long barley;
    private long ecoin;
    private long riceroll;
    private long limitcash;
    private long cash;
    private int cashstatus;

    public Asset(UserCoin uc) {

        barley = uc.getBarley();
        ecoin = uc.getEcoin();
        riceroll = uc.getRiceroll();
        limitcash = 0;
        cash = 0;
        cashstatus = 0;
    }

    public Asset(UserCoin uc, CashoutLimitEntity limit) {
        barley = uc.getBarley();
        ecoin = uc.getEcoin();
        riceroll = uc.getRiceroll();
        CashoutInfo ci = new CashoutInfo(uc.getRiceroll(), limit);
        limitcash = ci.getDayAvailable();
        cash = ci.getBalance();
        cashstatus = ci.getStatus();
    }

    public Asset(UserCoin uc, CashoutLimitEntity limit, int appId) {
        barley = uc.getBarley();
        ecoin = uc.getEcoin();
        riceroll = uc.getRiceroll();
        CashoutInfo ci = new CashoutInfo(uc.getRiceroll(), limit, appId);
        limitcash = ci.getDayAvailable();
        cash = ci.getBalance();
        cashstatus = ci.getStatus();
    }

    public long getBarley() {
        return barley;
    }

    public void setBarley(long barley) {
        this.barley = barley;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public long getCash() {
        return cash;
    }

    public void setCash(long cash) {
        this.cash = cash;
    }

    public long getLimitcash() {
        return limitcash;
    }

    public void setLimitcash(long limitcash) {
        this.limitcash = limitcash;
    }

    public int getCashstatus() {
        return cashstatus;
    }

    public void setCashstatus(int cashstatus) {
        this.cashstatus = cashstatus;
    }
}
