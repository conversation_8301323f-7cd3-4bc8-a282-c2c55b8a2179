package com.easylive.pay.output;

import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.utils.CurrencyUtils;
import com.easylive.pay.utils.UAgentInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * Created by yangsong on 15/12/18.
 */
public class RechargeRecordPayload {
    private String orderId;
    private Date time;
    private long rmb;
    private long ecoin;
    private String description;

    public RechargeRecordPayload(RechargeRecord cor, UAgentInfo uai) {
        this.orderId = cor.getOrderId();
        this.time = cor.getCompleteTime();
        this.rmb = cor.getRmb();
        this.ecoin = cor.getEcoin() + cor.getPrize();
        BigDecimal bd = new BigDecimal(cor.getRmb());
        BigDecimal hundred = new BigDecimal(100);

        String success = "处理中";
        String enSuccess = "Doing";
        if ( cor.isPaid() ) {
            success = "成功";
            enSuccess = "Success";
        } else if (cor.getStatus() == OrderStatus.ORS_FAILED.getValue()) {
            success = "失败";
            enSuccess = "Failure";
        }
        String currency = getRechargeCurrency(cor);

        String amount;
        if (cor.getPlatform() == PayPlatform.PPF_GOOGLE.getValue()) {
            BigDecimal amountBd = new BigDecimal(cor.getAmount() == null ? 0 : cor.getAmount());
            BigDecimal million = new BigDecimal(1000000);
            amount = amountBd.divide(million, 2,  RoundingMode.HALF_UP).toString();
        } else {
            amount = bd.divide(hundred, RoundingMode.HALF_UP).toString();
        }

        if (UAgentInfo.OVERSEA.equals(uai.getAppName())) {
            this.description = "Recharge " + enSuccess;
        } else {
            this.description = "充值 " + amount + " " + currency + ", " + success;
        }
    }

    /**
     * 获取充值货币
     * @param cor
     * @return
     */
    private String getRechargeCurrency(RechargeRecord cor) {
        String currency = cor.getCurrency();
        if( currency == null ) {
            if (cor.getPlatform() == PayPlatform.PPF_PAYPAL.getValue())
                currency = "USD";
            else
                currency = "CNY";
        }
        return CurrencyUtils.getCurrencyDisplayName(currency);
    }

    public String getOrderId() {
        return orderId;
    }

    public Date getTime() {
        return time;
    }

    public long getRmb() {
        return rmb;
    }

    public long getEcoin() {
        return ecoin;
    }

    public String getDescription() {
        return description;
    }
}
