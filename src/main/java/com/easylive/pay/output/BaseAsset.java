package com.easylive.pay.output;

import com.easylive.pay.entity.UserCoin;
import com.fasterxml.jackson.annotation.JsonProperty;

public class BaseAsset {

    private long barley;
    private long ecoin;
    private long riceroll;

    @JsonProperty(value = "accumbarley")
    private long accumBarley;

    @JsonProperty(value = "accumecoin")
    private long accumEcoin;

    @JsonProperty(value = "accumriceroll")
    private long accumRiceroll;

    @JsonProperty(value = "costecoin")
    private long costEcoin;

    public BaseAsset(UserCoin uc) {
        barley = uc.getBarley();
        ecoin = uc.getEcoin();

        riceroll = uc.getRiceroll();
        accumBarley = uc.getAccumbarley();
        accumEcoin = uc.getAccumecoin();
        accumRiceroll = uc.getAccumriceroll();
        costEcoin = uc.getCostecoin();
    }

    public long getBarley() {
        return barley;
    }

    public void setBarley(long barley) {
        this.barley = barley;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public long getAccumBarley() {
        return accumBarley;
    }

    public void setAccumBarley(long accumBarley) {
        this.accumBarley = accumBarley;
    }

    public long getAccumEcoin() {
        return accumEcoin;
    }

    public void setAccumEcoin(long accumEcoin) {
        this.accumEcoin = accumEcoin;
    }

    public long getAccumRiceroll() {
        return accumRiceroll;
    }

    public void setAccumRiceroll(long accumRiceroll) {
        this.accumRiceroll = accumRiceroll;
    }

    public long getCostEcoin() {
        return costEcoin;
    }

    public void setCostEcoin(long costEcoin) {
        this.costEcoin = costEcoin;
    }
}
