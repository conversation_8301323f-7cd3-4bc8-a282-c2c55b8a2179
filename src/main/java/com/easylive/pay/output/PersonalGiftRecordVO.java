package com.easylive.pay.output;

import com.easylive.pay.entity.PersonalGiftRecord;
import com.easylive.pay.model.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 个人中心购买礼物记录
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonalGiftRecordVO {

    @ApiModelProperty("用户易播号")
    private String name;

    @ApiModelProperty("用户昵称")
    private String nickname;

    @ApiModelProperty("购买的商品id")
    private Long goodsId;

    @ApiModelProperty("购买的商品类型")
    private int goodsType;

    @ApiModelProperty("购买的商品名称")
    private String goodsName;

    @ApiModelProperty("礼物图标")
    private String goodsIcon;

    @ApiModelProperty("购买的商品数量")
    private Long goodsCount;

    public PersonalGiftRecordVO(PersonalGiftRecord recordProjection, String goodsName, String goodsIcon, User user) {
        this.name = user.getName();
        this.nickname = user.getNickname();
        this.goodsId = recordProjection.getGoodsId();
        this.goodsType = recordProjection.getGoodsType();
        this.goodsName = goodsName;
        this.goodsIcon = goodsIcon;
        this.goodsCount = recordProjection.getNumber();
    }

}
