package com.easylive.pay.output;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR>
 * @date 2019-05-24
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransferResult {

    private String orderId;
    private String tid;
    private long fromUid;
    private long toUid;
    private long ecoin;
    private long fromEcoinBalance;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public long getFromUid() {
        return fromUid;
    }

    public void setFromUid(long fromUid) {
        this.fromUid = fromUid;
    }

    public long getFromEcoinBalance() {
        return fromEcoinBalance;
    }

    public void setFromEcoinBalance(long fromEcoinBalance) {
        this.fromEcoinBalance = fromEcoinBalance;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public long getToUid() {
        return toUid;
    }

    public void setToUid(long toUid) {
        this.toUid = toUid;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }
}
