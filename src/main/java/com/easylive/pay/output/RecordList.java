package com.easylive.pay.output;

import java.util.List;

/**
 * Created by c on 2015/12/17.
 */
public class RecordList<E> {

    private int start;
    private int count;
    private int next;

    private List<E> recordlist;


    public RecordList(int start, List<E> list) {
        this.start = start;
        this.count = list.size();
        this.next = start + list.size();
        this.recordlist = list;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getNext() {
        return next;
    }

    public void setNext(int next) {
        this.next = next;
    }

    public List<E> getRecordlist() {
        return recordlist;
    }

    public void setRecordlist(List<E> recordlist) {
        this.recordlist = recordlist;
    }
}
