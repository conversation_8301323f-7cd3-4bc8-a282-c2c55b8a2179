package com.easylive.pay.output;

import com.easylive.pay.entity.RicerollContributorEntity;

/**
 * <AUTHOR>
 * @date 2019-04-19
 */
public class RicerollContributor {
    private long uid;
    private long riceroll;

    public RicerollContributor(RicerollContributorEntity entity) {
        this.uid = entity.getFromUid();
        this.riceroll = entity.getRiceroll();
    }

    public long getUid() {
        return uid;
    }

    public long getRiceroll() {
        return riceroll;
    }
}
