package com.easylive.pay.output;

import com.easylive.pay.entity.CashoutIdentityLimitEntity;
import com.easylive.pay.utils.DateUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资产限额
 */
@Data
@NoArgsConstructor
public class CashoutIdentityLimitBO {

    /**
     * 日提现额
     */
    private long dayCashout;

    /**
     * 日剩余提现额度
     */
    private long dayLimit;

    /**
     * 月提现额
     */
    private long monthCashout;

    /**
     * 月剩余提现额度
     */
    private long monthLimit;

    public CashoutIdentityLimitBO(CashoutIdentityLimitEntity identityLimit) {
        if (DateUtils.isToday(identityLimit.getCashoutDate())) {
            this.dayCashout = identityLimit.getDayCashout();
            this.dayLimit = getDayCashoutLimit() - identityLimit.getDayCashout() > 0 ? getDayCashoutLimit() - identityLimit.getDayCashout() : 0;
        } else {
            this.dayCashout = 0;
            this.dayLimit = getDayCashoutLimit();
        }

        if (DateUtils.isCurrentMonth(identityLimit.getCashoutDate())) {
            this.monthLimit = identityLimit.getMonthLimit() - identityLimit.getMonthCashout() > 0 ? identityLimit.getMonthLimit() - identityLimit.getMonthCashout() : 0;
            this.monthCashout = identityLimit.getMonthCashout();
        } else {
            this.monthLimit = identityLimit.getMonthLimit();
            this.monthCashout = 0;
        }
    }

    private long getDayCashoutLimit() {
        return 20000;
    }

}
