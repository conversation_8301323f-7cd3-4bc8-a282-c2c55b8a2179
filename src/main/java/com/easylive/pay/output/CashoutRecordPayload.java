package com.easylive.pay.output;

import com.easylive.pay.entity.CashoutRecord;
import com.easylive.pay.enums.OrderStatus;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by yangsong on 15/12/18.
 */
public class CashoutRecordPayload {
    private Date time;
    private long rmb;
    private long riceroll;
    private String description;

    public CashoutRecordPayload(CashoutRecord cor) {
        this.time = cor.getComplete_time();
        this.rmb = cor.getRmb();
        this.riceroll = cor.getRiceroll();
        BigDecimal bd = new BigDecimal(cor.getRmb());
        BigDecimal hundred = new BigDecimal(100);
        String success = "处理中";
        if (cor.getStatus() == OrderStatus.ORS_SUCCESS.getValue()) {
            success = "成功";
        } else if (cor.getStatus() == OrderStatus.ORS_FAILED.getValue()) {
            success = "失败";
        }
        this.description = "提现 " + bd.divide(hundred).toString() + " 元, " + success;
    }

    public Date getTime() {
        return time;
    }

    public long getRmb() {
        return rmb;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public String getDescription() {
        return description;
    }
}
