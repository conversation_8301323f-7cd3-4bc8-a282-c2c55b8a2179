package com.easylive.pay.output;

import com.easylive.pay.entity.CashoutOffline;
import com.easylive.pay.model.user.UserInfo;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-05-24
 */

public class CashoutOfflineInfo {

    private long uid;
    private String name;
    private String nickname;
    private int appId;
    private Date createTime;
    private Date lastTime;

    private long riceroll;
    private long balance;
    private long dayLimitOffline;
    private long dayLimit;

    private String phone;

    public CashoutOfflineInfo(CashoutOffline cashoutOffline, UserInfo user, Asset asset, CashoutInfo info) {
        this.uid = cashoutOffline.getUid();
        this.createTime = cashoutOffline.getCreateTime();
        this.lastTime = cashoutOffline.getLastTime();

        this.name = user.getName();
        this.nickname = user.getEdit().getNickname();
        this.appId = user.getAttr().getAppId();
        this.dayLimitOffline = info.getDayLimitOffline();
        this.dayLimit = info.getDayLimit();
        if (this.dayLimitOffline <= 0) {
            this.dayLimitOffline = this.dayLimit;
        }

        this.balance = info.getBalance();
        this.riceroll = asset.getRiceroll();
        this.phone = user.getIdentity().getPhone();
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getAppId() {
        return appId;
    }

    public CashoutOfflineInfo setAppId(int appId) {
        this.appId = appId;
        return this;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public long getBalance() {
        return balance;
    }

    public void setBalance(long balance) {
        this.balance = balance;
    }

    public long getDayLimitOffline() {
        return dayLimitOffline;
    }

    public void setDayLimitOffline(long dayLimitOffline) {
        this.dayLimitOffline = dayLimitOffline;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastTime() {
        return lastTime;
    }

    public void setLastTime(Date lastTime) {
        this.lastTime = lastTime;
    }

    public long getDayLimit() {
        return dayLimit;
    }

    public void setDayLimit(long dayLimit) {
        this.dayLimit = dayLimit;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

}
