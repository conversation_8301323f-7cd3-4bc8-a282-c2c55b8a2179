package com.easylive.pay.output;


import java.util.Date;
import java.util.List;

/**
 * Created by c on 2016/5/12.
 */
public class OpenRedpackList {
    private long value;
    private int best;
    private Date time;

    private RedpackInfo redpackInfo;

    private List<OpenRedpackUser> openList;

    public OpenRedpackList() {
        value = 0;
        best = 0;
        time = new Date();
        openList = null;
        redpackInfo = null;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public int getBest() {
        return best;
    }

    public void setBest(int best) {
        this.best = best;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public List<OpenRedpackUser> getOpenList() {
        return openList;
    }

    public void setOpenList(List<OpenRedpackUser> openList) {
        this.openList = openList;
    }

    public RedpackInfo getRedpackInfo() {
        return redpackInfo;
    }

    public void setRedpackInfo(RedpackInfo redpackInfo) {
        this.redpackInfo = redpackInfo;
    }
}
