package com.easylive.pay.output;

import com.easylive.pay.entity.GuardianOption;
import com.easylive.pay.entity.GuardianRelationEntity;
import com.easylive.pay.service.GuardianService;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-05-24
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class GuardianRelation {

    private long uid;
    private long anchorUid;
    private int type;
    private int level;
    private long exp;
    private Date endTime;


    public GuardianRelation(GuardianRelationEntity relation, GuardianService guardianService) {
        this.uid = relation.getUid();
        this.endTime = relation.getEndTime();
        this.anchorUid = relation.getAnchorUid();
        this.exp = relation.getExp();
        this.level = relation.getLevel();
        GuardianOption option = guardianService.getOptionById(relation.getGuardianId());
        if (option != null) {
            this.type = option.getType();
        }
    }

    public GuardianRelation(long uid, long anchorUid) {
        this.uid = uid;
        this.anchorUid = anchorUid;
    }


    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getAnchorUid() {
        return anchorUid;
    }

    public void setAnchorUid(long anchorUid) {
        this.anchorUid = anchorUid;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public long getExp() {
        return exp;
    }

    public void setExp(long exp) {
        this.exp = exp;
    }
}
