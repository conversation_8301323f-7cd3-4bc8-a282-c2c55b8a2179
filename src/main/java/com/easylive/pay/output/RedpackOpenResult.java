package com.easylive.pay.output;

import com.easylive.pay.entity.RedpackNumber;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-06-14
 */
public class RedpackOpenResult {

    private long value;
    private int best;
    private Date time = new Date();

    public RedpackOpenResult(RedpackNumber number) {
        if (number != null) {
            value = number.getValue();
            best = number.getBest();
            time = number.getTime();
        }
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public int getBest() {
        return best;
    }

    public void setBest(int best) {
        this.best = best;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }
}
