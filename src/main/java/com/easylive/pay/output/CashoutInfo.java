package com.easylive.pay.output;

import com.easylive.pay.common.Constants;
import com.easylive.pay.entity.CashoutLimitEntity;
import com.easylive.pay.enums.CashoutLimitForbid;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.utils.MathUtils;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class CashoutInfo {

    private long dayAvailable;
    private long balance;
    private int status;
    private long dayAmount;
    private long dayLimitOffline;
    private long dayLimit;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date lastDate;


    public CashoutInfo(long riceroll, CashoutLimitEntity limit) {
        //当前用户可提现的余额（分）
        balance = riceroll / Constants.RATE_RMB_TO_RICEROLL;

        if (limit.getIsForbid() == CashoutLimitForbid.YES) {
            //禁止提现
            status = 0;
            balance = 0;
        } else {
            //允许提现
            status = 1;
        }

        dayLimitOffline = limit.getDayCashoutLimit();
        dayLimit = getLimitCashout(balance);

        dayAmount = limit.getDayCashout();
        lastDate = limit.getCashoutDate();

        dayAvailable = getLimitCashout2();

//        AffirmCashoutWhite acw = SpecialUserService.getCashoutWhite(uc.getUid());
//        if (acw.isWhite()) {
//            dayAvailable = acw.getLimitRmb();
//        }

        if (DateUtils.isToday(lastDate)) {
            dayAvailable = dayAvailable - dayAmount;
        }

        dayAvailable = Math.min(dayAvailable, balance);
        if (dayAvailable < 0)
            dayAvailable = 0;
    }

    public CashoutInfo(long riceroll, CashoutLimitEntity limit, int appId) {
        balance = getBalance(riceroll, appId);

        if (limit.getIsForbid() == CashoutLimitForbid.YES) {
            //禁止提现
            status = 0;
            balance = 0;
        } else {
            //允许提现
            status = 1;
        }

        dayLimitOffline = limit.getDayCashoutLimit();
        dayLimit = getLimitCashout(balance);

        dayAmount = limit.getDayCashout();
        lastDate = limit.getCashoutDate();

        dayAvailable = getLimitCashout2();

        if (DateUtils.isToday(lastDate)) {
            dayAvailable = dayAvailable - dayAmount;
        }

        dayAvailable = Math.min(dayAvailable, balance);
        if (dayAvailable < 0) {
            dayAvailable = 0;
        }
    }

    /**
     * 不同APP不同的提现额度
     */
    public static long getBalance(long riceroll, int appId) {
        // 芙蓉直播
        if (appId == Constants.APP_ID_HIBI) {
            String ratio = MathUtils.numberFormat((double) Constants.HIBI_RATE_RMB_TO_RICEROLL / 100, 1);
            return Double.valueOf(Long.valueOf(riceroll).doubleValue()/Double.valueOf(ratio)).longValue();
        } else {
            return riceroll / Constants.RATE_RMB_TO_RICEROLL;
        }
    }

    private long getLimitCashout2() {
        return 20000;
    }

    private long getLimitCashout(long cash) {
        long limitCashout;
        if (cash > 6000000) {
            limitCashout = 200000;
        } else if (cash > 3000000) {
            limitCashout = 100000;
        } else if (cash > 1500000) {
            limitCashout = 50000;
        } else {
            limitCashout = 20000;
        }

        return limitCashout;
    }

    public long getDayAvailable() {
        return dayAvailable;
    }


    public long getBalance() {
        return balance;
    }


    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getDayAmount() {
        return dayAmount;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public long getDayLimitOffline() {
        return dayLimitOffline;
    }

    public long getDayLimit() {
        return dayLimit;
    }
}
