package com.easylive.pay.output;

import com.easylive.pay.model.User;

import java.util.Date;

/**
 * Created by c on 2016/5/12.
 */
public class OpenRedpackUser {
    private long value;                  // 易币数
    private int best;                   // 是否最佳
    private Date time;                  // 抢红包时间
    private String name;                // 易播号
    private String nickname;            // 昵称
    private String logourl;             // 用户头像

    public OpenRedpackUser() {
        value = 0;
        best = 0;
        time = new Date();
        name = null;
        nickname = null;
        logourl = null;
    }

    public OpenRedpackUser(long value, int best, Date time, User user) {
        this.value = value;
        this.best = best;
        this.time = time;
        this.name = user.getName();
        this.nickname = user.getNickname();
        this.logourl = user.getLogoUrl();
    }


    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public int getBest() {
        return best;
    }

    public void setBest(int best) {
        this.best = best;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getLogourl() {
        return logourl;
    }

    public void setLogourl(String logourl) {
        this.logourl = logourl;
    }
}
