package com.easylive.pay.output;

import com.easylive.pay.entity.TransferRecord;
import com.easylive.pay.model.User;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-05-24
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransferQueryResult {

    private String orderId;
    private long fromUid;
    private String fromName;
    private String fromNickname;

    private long toUid;
    private String toName;
    private String toNickname;
    private int ecoin;
    private Date createTime;

    public TransferQueryResult(TransferRecord record, User fromUser, User toUser) {
        this.orderId = record.getOrderId();
        this.createTime = record.getCreateTime();
        this.ecoin = record.getEcoin();

        this.fromUid = record.getFromUid();
        this.toUid = record.getToUid();

        if (fromUser != null) {
            this.fromName = fromUser.getName();
            this.fromNickname = fromUser.getNickname();
        }

        if (toUser != null) {
            this.toName = toUser.getName();
            this.toNickname = toUser.getNickname();
        }
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public long getFromUid() {
        return fromUid;
    }

    public void setFromUid(long fromUid) {
        this.fromUid = fromUid;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getFromNickname() {
        return fromNickname;
    }

    public void setFromNickname(String fromNickname) {
        this.fromNickname = fromNickname;
    }

    public long getToUid() {
        return toUid;
    }

    public void setToUid(long toUid) {
        this.toUid = toUid;
    }

    public String getToName() {
        return toName;
    }

    public void setToName(String toName) {
        this.toName = toName;
    }

    public String getToNickname() {
        return toNickname;
    }

    public void setToNickname(String toNickname) {
        this.toNickname = toNickname;
    }

    public int getEcoin() {
        return ecoin;
    }

    public void setEcoin(int ecoin) {
        this.ecoin = ecoin;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
