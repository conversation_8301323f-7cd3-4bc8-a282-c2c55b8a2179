package com.easylive.pay.output;

import com.easylive.pay.entity.GoodsRecord;
import com.easylive.pay.enums.GoodsType;

import java.util.Date;

/**
 * Created by c on 2015/12/16.
 */
public class BarleyRecord {

    //@JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss")
    private Date time;
    private long barley;
    private long goodsid;
    private String goodsname;
    private long goodstype;
    private long goodscount;
    private String description;

    public BarleyRecord() {
    }

    public BarleyRecord(GoodsRecord goodsRecord) {
        time = goodsRecord.getTime();
        barley = goodsRecord.getCost() * goodsRecord.getNumber();
        goodsid = goodsRecord.getGiftId();
        goodsname = goodsRecord.getGiftName();
        goodstype = goodsRecord.getGiftType();
        goodscount = goodsRecord.getNumber();

        if (goodstype == GoodsType.GT_BARLEY.getValue()) {
            description = "送出" + goodsname;
        } else if (goodstype == GoodsType.GT_EMOJI.getValue()) {
            description = "发出" + String.valueOf(goodsRecord.getNumber()) + "个表情“" + goodsname + "”";
        } else if (goodstype == GoodsType.GT_GIFT.getValue()) {
            description = "送出" + String.valueOf(goodsRecord.getNumber()) + "个礼物“" + goodsname + "”";
        } else {
            description = "";
        }
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public long getBarley() {
        return barley;
    }

    public void setBarley(long barley) {
        this.barley = barley;
    }

    public long getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(long goodsid) {
        this.goodsid = goodsid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public long getGoodstype() {
        return goodstype;
    }

    public void setGoodstype(long goodstype) {
        this.goodstype = goodstype;
    }

    public long getGoodscount() {
        return goodscount;
    }

    public void setGoodscount(long goodscount) {
        this.goodscount = goodscount;
    }
}
