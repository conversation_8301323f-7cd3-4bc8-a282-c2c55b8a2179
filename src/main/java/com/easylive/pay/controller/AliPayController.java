package com.easylive.pay.controller;

import com.alibaba.druid.util.StringUtils;
import com.easylive.pay.common.*;
import com.easylive.pay.entity.AppEntity;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.service.AppService;
import com.easylive.pay.service.UserService;
import com.easylive.pay.service.alipay.AliPayService;
import com.easylive.pay.service.alipay.AliPrepareOrder;
import com.easylive.pay.service.alipay.AliRequestOrder;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.utils.ServletUtils;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.Map;


@RestController
@RequestMapping(value = "/")
public class AliPayController {

    private static Logger logger = LoggerFactory.getLogger(AliPayController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private AliPayService aliPayService;

    @Autowired
    private AppService appService;

    /**
     * 支付宝WAP支付请求
     */
    @ApiIgnore
    @RequestMapping({"/pay/alipay", "/pay/recharge/ali/web/order", "/pay/recharge/ali/wap/order"})
    public ModelAndView alipayWebOrder(
            @RequestParam(value = "sessionid", required = false, defaultValue = "") String session,
            @RequestParam(value = "name", required = false, defaultValue = "") String name,
            @RequestParam(required = false, defaultValue = "yizhibo-wap") String app,
            @RequestParam(required = false) String resultUrl,
            @RequestParam(value = "appName", required = false) String appName,
            @RequestParam("amount") long amount,
            @RequestParam(value = "iosembed", required = false, defaultValue = "") String iosEmbed,
            HttpServletRequest request) {
        User fromUser;
        if (!session.isEmpty()) {
            fromUser = userService.getBySession(session);
        } else if (!name.isEmpty()) {
            fromUser = userService.ensureExists(name);
        } else {
            throw new ResponseException(ResponseError.E_PARAM);
        }
        long uid = fromUser.getId();
        String clientIp = ClientUtils.getClientIPAddr(request);
        UAgentInfo uai = new UAgentInfo(request);
        MobilePlatform platform = uai.getDeviceType();

        boolean ios = !StringUtils.isEmpty(iosEmbed);
        if (ios)
            app = "yizhibo-wap-ios";

        if( StringUtils.isEmpty(appName) )
            appName = uai.getAppName();


        logger.info("Recharge ali wap: appName: {}, Client IP: {}, Client Platform: {}, UID: [{}], Amount: [ {} ], iOS: {}, User Agent: {}, result url: {} ",
                appName,clientIp, platform.getValue(), uid, amount, iosEmbed, uai.getUserAgent(), resultUrl);
        String url = aliPayService.createWapOrder(appName, app, resultUrl, fromUser, amount, session, clientIp, platform, ios);
        RedirectView redirectView = new RedirectView();
        redirectView.setUrl(url);
        return new ModelAndView(redirectView);
    }


    /**
     * 支付宝App支付
     */
    @ApiIgnore
    @RequestMapping({"/pay/appalipay", "/pay/recharge/ali/app/order"})
    @SessionCheck
    @ResponseObjectBody
    public Map<String, String> aliPayAppOrder(
            @RequestAttribute User user,
            @RequestParam("amount") long amount,
            HttpServletRequest request) {
        long uid = user.getId();
        String clientIp = ClientUtils.getClientIPAddr(request);
        UAgentInfo uai = new UAgentInfo(request);
        MobilePlatform platform = uai.getDeviceType();

        logger.info("[Alipay][Recharge][AppOld][Start] Client IP: {}, Client Platform: {}, UID: [{}], Amount: [ {} ] ",
                clientIp, platform.getValue(), uid, amount);

        return aliPayService.createAppOrder(amount, uid, clientIp, platform);
    }

    /**
     * 支付宝App支付
     */
    @ApiIgnore
    @RequestMapping({"/pay/recharge/ali/app/order/v2"})
    @SessionCheck
    @ResponseObjectBody
    public Map aliPayAppOrderNew(
            @RequestAttribute User user,
            @RequestParam("amount") long amount,
            HttpServletRequest request) {
        long uid = user.getId();
        String clientIp = ClientUtils.getClientIPAddr(request);
        UAgentInfo uai = new UAgentInfo(request);
        MobilePlatform platform = uai.getDeviceType();

        logger.info("[Alipay][Recharge][App2][Start] Client IP: {}, Client Platform: {}, UID: [{}], Amount: [ {} ], UA:{} ",
                clientIp, platform.getValue(), uid, amount, uai.getUserAgent());

        String orderStr = aliPayService.createAppOrderNew(uai.getAppName(), amount, user.getId(), clientIp, platform);
        return GenericMap.toMap(
                Collections.singletonList("order"),
                Collections.singletonList(orderStr));
    }

    /**
     * 支付宝App支付,特殊充值渠道
     */
    @ApiIgnore
    @RequestMapping({"/pay/recharge/ali/app/order/sc"})
    @SessionCheck
    @ResponseObjectBody
    public Map aliPayAppOrderSpecialChannel(
            @RequestAttribute User user,
            @RequestParam long amount,
            HttpServletRequest request) {
        long uid = user.getId();
        String clientIp = ClientUtils.getClientIPAddr(request);
        UAgentInfo uai = new UAgentInfo(request);
        MobilePlatform platform = uai.getDeviceType();

        logger.info("[Alipay][Recharge][SpecialChannel][Start], Channel:{} Client IP: {}, Client Platform: {}, UID: [{}], Amount: [ {} ] ",
                user.getCid(), clientIp, platform.getValue(), uid, amount);

        String orderStr = aliPayService.createAppOrderSpecialChannel(amount, user, clientIp, platform);
        return GenericMap.toMap(
                Collections.singletonList("order"),
                Collections.singletonList(orderStr));
    }


    /**
     * 支付宝PC/WAP支付成功验证
     */
    @ApiIgnore
    @RequestMapping("/pay/recharge/ali/web/return")
    public ModelAndView aliPayWebReturn(HttpServletRequest request, HttpServletResponse response) {
        logger.info("[Alipay][WebReturn][Start]");
        Map<String, String> aliArgs = ServletUtils.getServletParameters(request);

        logger.info("[Alipay][WebReturn][Start] out_trade_no: [{}] ", aliArgs.get("out_trade_no"));
        String url = aliPayService.webReturnValid(aliArgs);

        RedirectView redirectView = new RedirectView();
        redirectView.setUrl(url);
        return new ModelAndView(redirectView);
    }


    /**
     * 支付宝回调
     */
    @ApiIgnore
    @RequestMapping("/pay/recharge/ali/notify/v2")
    public void aliPayRechargeNotifyNew(HttpServletRequest request, HttpServletResponse response) {

        logger.info("[Alipay]]Notify][V2][Start]");
        Map<String, String> aliArgs = ServletUtils.getServletParameters(request);

        logger.info("[Alipay][Notify][Start] out_trade_no: [{}], trade_status: [ {} ] ", aliArgs.get("out_trade_no"), aliArgs.get("trade_status"));
        String notifyResponse = "";
        if (aliPayService.rechargeNotifyNew(aliArgs)) {
            notifyResponse = "success";
        }

        try {
            logger.info("[Alipay] [Notify] [End] response with: " + notifyResponse);
            response.getWriter().write(notifyResponse);
        } catch (IOException e) {
            logger.error("[Alipay] [Notify] [Failed] response error", e);
        }
    }


    /**
     * 支付宝回调, 老接口
     */
    @ApiIgnore
    @RequestMapping({"/pay/aliasyncvalid", "/pay/recharge/ali/notify"})
    public void aliPayRechargeNotify(HttpServletRequest request, HttpServletResponse response) {
        logger.info("[Alipay]]Notify][Start], {}", request.getRequestURI());
        Map<String, String> aliArgs = ServletUtils.getServletParameters(request);

        logger.info("[Alipay] [Notify] [Start]out_trade_no: [{}], trade_status: [ {} ] ", aliArgs.get("out_trade_no"), aliArgs.get("trade_status"));
        String notifyResponse = "";
        if (aliPayService.rechargeNotify(aliArgs)) {
            notifyResponse = "success";
        }

        try {
            logger.info("[Alipay] [Notify] [End] response with: " + notifyResponse);
            response.getWriter().write(notifyResponse);
        } catch (IOException e) {
            logger.error("[Alipay] [Notify] [Failed] response error", e);
        }
    }





    @ApiOperation(value = "支付宝APP订单准备", notes = "支付宝APP订单准备")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回订单号")})
    @RequestMapping(value = {"/service/recharge/ali/app/order/prepare"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public AliPrepareOrder aliPayAppOrderPrepare(
            @ApiParam("用户UID") @RequestParam long uid,
            @ApiParam("充值金额,单位分") @RequestParam long amount,
            @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
            @ApiParam("优先使用HTTP的支付协议") @RequestParam(value = "preferHttp", required = false, defaultValue = "0") Integer preferHttp,
            @ApiParam("客户端UA") @RequestParam(value = "client_ua", required = false) String clientUserAgent,
            @ApiParam("中台APPID") @RequestParam(value = "appId", required = false) Integer mgsAppId
    ) {
        UAgentInfo uai = new UAgentInfo(clientUserAgent);
        MobilePlatform platform = uai.getDeviceType();
        logger.info("[Recharge][Alipay][App][Prepare] {},{},{},{},{},{},{}", mgsAppId, platform, uid, amount, clientIp, clientUserAgent, preferHttp);
        mgsAppId = appService.decideAppIdByIdAndName(mgsAppId, uai.getAppName());
        logger.info("[Recharge][Alipay][APP]: app id {} {},platform {}", mgsAppId, uai.getAppName(), platform);
        return aliPayService.prepareAppOrder(mgsAppId, amount, uid, clientIp, platform, preferHttp == 1);
    }


    @ApiOperation(value = "支付宝WAP订单准备", notes = "支付宝WAP订单准备")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回订单号")})
    @RequestMapping(value = {"/service/recharge/ali/wap/order/prepare"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public AliPrepareOrder aliPayWapOrderPrepare(
            @ApiParam("用户UID") @RequestParam long uid,
            @ApiParam("充值金额,单位分") @RequestParam long amount,
            @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
            @ApiParam("客户端UA") @RequestParam(value = "client_ua", required = false) String clientUserAgent,
            @ApiParam("中台APPID") @RequestParam(value = "appId", required = false) Integer mgsAppId
    ) {
        UAgentInfo uai = new UAgentInfo(clientUserAgent);
        MobilePlatform platform = uai.getDeviceType();
        logger.info("[Recharge][Alipay][WAP][Prepare] {},{},{},{},{},{}", mgsAppId, platform, uid, amount, clientIp, clientUserAgent);
        mgsAppId = appService.decideAppIdByIdAndName(mgsAppId, uai.getAppName());
        logger.info("[Recharge][Alipay][WAP]: app id {} {},platform {}", mgsAppId,uai.getAppName(), platform);
        return aliPayService.prepareWapOrder(mgsAppId, amount, uid, clientIp, platform);
    }

    /**
     * 支付宝App支付请求
     */
    @ApiOperation(value = "支付宝APP订单创建", notes = "支付宝APP订单请求")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "订单请求数据结果")})
    @RequestMapping(value = {"/service/recharge/ali/app/order/request"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public AliRequestOrder aliPayAppOrderRequest(@ApiParam("订单号") @RequestParam String orderId) {
        logger.info("[Recharge][Ali][APP][Order][Request]: {}", orderId);
        return aliPayService.requestAppOrder(orderId);
    }


    /**
     * 支付宝H5支付请求
     */
    @ApiOperation(value = "支付宝WAP支付订单请求", notes = "支付宝WAP订单请求")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "订单请求数据结果")})
    @RequestMapping(value = {"/service/recharge/ali/wap/order/request"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public AliRequestOrder aliPayWapOrderRequest(@ApiParam("订单号") @RequestParam String orderId) {
        logger.info("[Recharge][Ali][WAP][Order][Request]: {}", orderId);
        return aliPayService.requestWapOrder(orderId);
    }




    /**
     * 支付宝App支付
     */
    @ApiOperation(value = "支付宝APP订单创建", notes = "支付宝APP订单创建")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "order: 订单字符串，支付宝SDK可以直接使用此字符串调起支付界面")})
    @RequestMapping(value = {"/service/recharge/ali/app/order"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map aliPayAppOrderInternal(
            @ApiParam("用户UID") @RequestParam long uid,
            @ApiParam("充值金额，单位为分") @RequestParam long amount,
            @ApiParam("中台APP ID，以后可能不需要此此段，完全依靠用户属性来确定") @RequestParam(value = "appId", required = false, defaultValue = "1") int mgsAppId,
            HttpServletRequest request) {
        String clientIp = ClientUtils.getClientIPAddr(request);
        UAgentInfo uai = new UAgentInfo(request);
        MobilePlatform platform = uai.getDeviceType();

        logger.info("[Alipay][Recharge][App][Start] Client Platform: {}, UID: [{}], Amount: [ {} ] ",
                platform.getValue(), uid, amount);

        AppEntity app = appService.getAppById(mgsAppId);
        if (app == null)
            throw new ResponseException(ResponseError.E_RECHARGE_MGS_APPID, "Invalid mgs app id");

        String orderStr = aliPayService.createAppOrderNew(app.getName(), amount, uid, clientIp, platform);
        return GenericMap.toMap(
                Collections.singletonList("order"),
                Collections.singletonList(orderStr));
    }


    /**
     * 支付宝PC网站支付请求
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/ali/pc/order"})
    @ResponseObjectBody
    public Map alipayPCOrder(@RequestParam String name,
                             @RequestParam(defaultValue = "yizhibo") String app,
                             @RequestParam long amount,
                             HttpServletRequest request) {
        User user = userService.ensureExists(name);
        String clientIp = ClientUtils.getClientIPAddr(request);
        UAgentInfo uai = new UAgentInfo(request);
        MobilePlatform platform = uai.getDeviceType();
        return aliPayService.createPCOrder(app, user, amount, clientIp, platform);
    }


    @ApiIgnore
    @RequestMapping(value = "/service/recharge/ali/exception/list")
    @ResponseBody
    public String listAlipayExceptionalOrder(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate
    ) {
        StringBuilder sb = new StringBuilder();
        aliPayService.listExceptionalOrder(startDate, endDate).forEach(rechargeRecord -> {
            sb.append(rechargeRecord.getUid()).append(",").append(rechargeRecord.getRmb()).append(",").append(rechargeRecord.getOrderId()).append(",").append(rechargeRecord.getPlatformOrderId());
            sb.append("\n");
        });
        return sb.toString();
    }
}

