package com.easylive.pay.controller;

import com.easylive.pay.service.apple.ApplePayService;
import com.easylive.pay.service.apple.iap.notification.ResponseBodyV2;
import com.easylive.pay.service.factory.BankCashout;
import com.easylive.pay.service.huifu.HuifuPayService;
import com.easylive.pay.service.lianlian.LianLianPayService;
import com.easylive.pay.service.wx.WeixinService;
import com.easylive.pay.utils.ClientUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import springfox.documentation.annotations.ApiIgnore;
import com.easylive.pay.utils.ServletUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping(value = "/notify")
public class NotifyController {

    @Autowired
    private AliPayController aliPayController;

    @Autowired
    private WeixinService weixinService;

    @Autowired
    private OppoPayController oppoPayController;

    @Autowired
    private RechargeController rechargeController;

    @Autowired
    private GashPayController gashPayController;

    @Resource(name = "XinYunBankCashout")
    private BankCashout xinYunBankCashout;

    @Resource(name = "JiuHeBankCashout")
    private BankCashout jiuHeBankCashout;

    @Resource(name = "YZHCashout")
    private BankCashout yunzhanghuCashout;


    /**
     * 支付宝回调
     */
    @ApiIgnore
    @RequestMapping("/ali/notify")
    public void aliPayRechargeNotifyNew(HttpServletRequest request, HttpServletResponse response) {
        log.info("Alipay notify");
        aliPayController.aliPayRechargeNotifyNew(request, response);
        log.info("Alipay notify end");
    }

    /**
     * 支付宝PC/WAP支付成功验证
     */
    @ApiIgnore
    @RequestMapping("/ali/web-return")
    public ModelAndView aliPayWebReturn(HttpServletRequest request, HttpServletResponse response) {
        log.info("Alipay web return");
        return aliPayController.aliPayWebReturn(request, response);
    }

    /**
     * 微信支付订单回调通知
     */
    @ApiIgnore
    @RequestMapping({"/wx"})
    public String weixinOrderNotify(HttpServletRequest request) throws IOException {
        log.info("weixin pay notify");
        String xmlBody = IOUtils.toString(request.getInputStream());
        String s = weixinService.valid(xmlBody);
        log.info("weixin pay notify end");
        return s;
    }

    @ApiIgnore
    @RequestMapping(value = {"/oppo"}, method = RequestMethod.POST)
    public String notify(HttpServletRequest request) {
        return oppoPayController.notify(request);
    }


    @ApiIgnore
    @RequestMapping({"/reapal"})
    public void reapalWebNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        rechargeController.reapalWebNotify(request, response);
    }

    @ApiIgnore
    @RequestMapping("/gash/web-return")
    public void gashWebReturn(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String redirectUrl = gashPayController.gashWebReturn(request);
        response.sendRedirect(redirectUrl);
    }


    @Autowired
    private ApplePayService applePayService;

    @ApiIgnore
    @RequestMapping("/apple/notify")
    public void handleAppStoreNotification(@RequestBody ResponseBodyV2 responseBodyV2) {
        applePayService.handleAppStoreNotification(responseBodyV2);
    }

    /*@SneakyThrows
    @ApiIgnore
    @RequestMapping(value = "/xinyun",method = RequestMethod.POST)
    public void xinYunPayNotify(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> map = ServletUtils.getServletParameters(request);
        response.getWriter().write(xinYunBankCashout.payNotifyProcess(map));
    }*/

    @SneakyThrows
    @ApiIgnore
    @RequestMapping(value = "/jiuhe",method = RequestMethod.POST)
    public String jiuHePayNotify(@RequestBody Map<String, String> map) {
        log.info("[JiuHePayNotify] start !!!!");
        return jiuHeBankCashout.payNotifyProcess(map);
    }

    @Autowired
    private LianLianPayService lianLianPayService;
    @ApiIgnore
    @RequestMapping(value = "/lianlian", method = RequestMethod.POST)
    public String handleLianLianNotification(HttpServletRequest request) throws IOException {
        String data = IOUtils.toString(request.getInputStream());
        return lianLianPayService.validOrder(data);
    }

    @Autowired
    private HuifuPayService huifuPayService;
    @ApiIgnore
    @RequestMapping(value = "/huifu", method = RequestMethod.POST)
    public void handleHuifuNotification(HttpServletRequest request) {
        // print all params of request
        log.info("Huifu notification:");
        for (Map.Entry<String, String[]> entry : request.getParameterMap().entrySet()) {
            log.info(" key:{}, value:{}", entry.getKey(), entry.getValue());
        }
        huifuPayService.validOrder(request.getParameter("data"), request.getParameter("sign"));
    }

    @ApiIgnore
    @RequestMapping(value = "/yzh", method = RequestMethod.POST)
    public String handleYunzhanghuNotification(HttpServletRequest request) {
        Map<String, String> params = ServletUtils.getServletParameters(request);
        return yunzhanghuCashout.payNotifyProcess(params);
    }
}
