package com.easylive.pay.controller;

import com.easylive.pay.common.*;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.model.*;
import com.easylive.pay.output.*;
import com.easylive.pay.service.*;
import com.easylive.pay.service.reapal.ReapalService;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.utils.DateUtils;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/")
public class RechargeController {
    private static Logger logger = LoggerFactory.getLogger(RechargeController.class);

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private UserService userService;

    @Autowired
    private AssetService assetService;

    @Autowired
    private ReapalService reapalService;

    @Autowired
    private AppService appService;

    @Autowired
    private RechargeControlService rechargeControlService;

    /**
     * App获取充值总金额
     *
     * @param fromUser 用户信息
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/pay/rechargetotal", "/pay/recharge/user/total"})
    @ResponseObjectBody
    @SessionCheck
    public RechargeCount getUserRechargeTotal(@RequestAttribute User fromUser) {
        return userRechargeTotal(fromUser.getId());
    }

    /**
     * App端删除充值记录
     */
    @ApiIgnore
    @RequestMapping({"/pay/recharge/user/record/delete"})
    @ResponseObjectBody
    @SessionCheck
    public void deleteRechargeRecord(@RequestAttribute User fromUser, @RequestParam(value = "order_id") String orderId) {
        rechargeService.markRecordDeleted(fromUser.getId(), orderId);
    }

    /**
     * App获取充值记录
     *
     * @param fromUser 用户信息
     * @param start    开始
     * @param count    数量
     * @return RecordList
     */
    @ApiIgnore
    @RequestMapping({"/pay/rechargerecord", "/pay/recharge/user/record/list"})
    @ResponseObjectBody
    @SessionCheck
    public RecordList<RechargeRecordPayload> listRechargeRecordByUser(@RequestAttribute User fromUser,
                                                                      @RequestParam(required = false, defaultValue = "0") int start,
                                                                      @RequestParam(required = false, defaultValue = "20") int count,
                                                                      HttpServletRequest request) {
        return listUserRechargeRecord(fromUser.getId(), start, count, request);
    }


    /**
     * App端获取充值选项
     */
    @ApiIgnore
    @RequestMapping({"/pay/cashinoption", "/pay/recharge/option/list"})
    @SessionCheck
    @ResponseObjectBody
    public Map optionList(@RequestAttribute User user,
                          @RequestParam(value = "platform", required = false) Long platform,
                          HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        UAgentInfo uai = new UAgentInfo(request);
        int device = uai.getDeviceType().getValue();
        AppEntity app = appService.getAppByNameWithDefaultFallback(uai.getAppName());


        List<RechargeOption> list = new ArrayList<>();

        //platform != null，是新接口，走渠道溢价模式
        if (platform != null) {

            logger.debug("Recharge option: app: {} {} ,platform: {}", app.getId(), uai.getAppName(), platform);
            BigDecimal scale = BigDecimal.ONE;
            if (platform == PayPlatform.PPF_ALL.getValue() || platform == PayPlatform.PPF_WX_APP_PAY.getValue() || platform == PayPlatform.PPF_ALI_APP_PAY.getValue()) {
                Pair<BigDecimal, List<RechargeOption>> optionInfo = rechargeService.getOptionByUser(user, (int) app.getId());
                scale = optionInfo.getLeft();
                list = optionInfo.getRight();
                if (platform != PayPlatform.PPF_ALL.getValue()) {
                    list.forEach(option -> {
                        option.setPlatform(platform);
                    });
                }

            } else if (platform == PayPlatform.PPF_APPLE.getValue()) {
                //苹果充值特殊一点
                list = filterApplePayOption(rechargeService.getOptionListByPlatformAndAppId(platform, (int) app.getId()), uai.getAppName());
            } else if (platform == PayPlatform.PPF_PAYPAL.getValue()) {
                list = rechargeService.getOptionListByPlatformAndUser(PayPlatform.PPF_PAYPAL, user);
            }

            //fixed 客户端用这个比例用人民币换算易币，所以得*10 吴刚力
            map.put("scale", scale);
            map.put("basescale", rechargeService.getBaseRmbToEcoinScale(scale));


        } else {
            //老接口模式,根据客户端类型获取充值选项
            logger.info("List option: device type is {} , {}", device, request.getHeader("User-Agent"));
            list = rechargeService.getOptionListByDevice(device);

            if (device == MobilePlatform.MPF_IOS.getValue()) {
                list = filterApplePayOption(list, uai.getAppName());
            }
        }

        if (UAgentInfo.OVERSEA.equals(app.getName())) {
            rechargeService.setOptionsTagType(list, app.getId());
        }

        map.put("optionlist", rechargeService.fillRechargeOptionLotteryTime(user.getId(), list));
        return map;
    }


    /**
     * Web端ajax获取充值状态
     * TODO: 后续移除此接口，走统一的内网接口形式
     *
     * @param orderId 充值订单ID
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/pay/recharge/status"})
    @ResponseObjectBody
    public HashMap<String, Object> getRechargeStatus(
            @RequestParam(value = "orderid") String orderId) {
        RechargeRecord rechargeRecord = rechargeService.getRecordByOrderId(orderId);
        if (rechargeRecord == null) {
            logger.info("recharge order id " + orderId + " doest not exists");
            throw new ResponseException(ResponseError.E_PARAM, "order id not exists");
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("orderid", rechargeRecord.getOrderId());
        result.put("rmb", rechargeRecord.getRmb());
        result.put("ecoin", rechargeRecord.getEcoin());
        result.put("status", rechargeRecord.getStatus());
        result.put("complete_time", rechargeRecord.getCompleteTime());
        return result;
    }

    /**
     * 充值选项（目前PC充值页面,platform为9时扫码支付充值选项）
     * TODO: 后续移除此接口，走统一的内网接口形式
     *
     * @return 对应支付类型的充值选项列表
     */
    @ApiIgnore
    @RequestMapping({"/pay/recharge/option/list-by-platform"})
    @ResponseObjectBody
    public Map<String, List<RechargeOption>> listOptionByPlatform(@RequestParam(value = "platform", required = false, defaultValue = "9") long platform,
                                                                  @RequestParam(value = "appId", required = false, defaultValue = "1") int mgsAppId
    ) {
        List<RechargeOption> list = rechargeService.getOptionListByPlatform(platform);
        Map<String, List<RechargeOption>> map = new HashMap<>();
        map.put("optionlist", list);
        return map;
    }


    /**
     * 融宝支付回调
     */
    @ApiIgnore
    @RequestMapping({"/pay/recharge/reapal/web/notify"})
    public void reapalWebNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String[]> parameters = request.getParameterMap();
        String data = parameters.get("data")[0];
        String encryptKey = parameters.get("encryptkey")[0];
        String result = reapalService.reapalNotify(data, encryptKey);
        if ("success".equals(result)) {
            response.getWriter().write(result);
        }
    }


    /**
     * 易币回购选项
     *
     * @param user 用户信息
     * @return 对应设备类型的充值选项列表
     */
    @ApiIgnore
    @RequestMapping({"/pay/buybackoption", "/pay/buyback/option/list"})
    @SessionCheck
    @ResponseObjectBody
    public Map listBuybackOption(@RequestAttribute User user) {
        return GenericMap.toMap("optionlist", buybackOption(1));
    }


    /**
     * 易币回购
     *
     * @param user 用户信息
     * @return 对应设备类型的充值选项列表
     */
    @ApiIgnore
    @RequestMapping({"/pay/buyback", "/pay/buyback/transfer"})
    @SessionCheck
    @ResponseObjectBody
    public Map buybackTransfer(@RequestAttribute User user,
                               @RequestParam long rmb) {
        return buyback(user.getId(), rmb);
    }






    /*
    -----------------------------------------------------------------------------------------
     */


    @ApiIgnore
    @RequestMapping({"/service/recharge/order/status"})
    @ResponseObjectBody
    public RechargeRecordStatus getRechargeOrderStatus(
            @RequestParam(value = "orderId") String orderId) {
        return rechargeService.getRecordStatus(orderId);
    }


    @ApiOperation(value = "充值总额", notes = "充值总额")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "total 充值总额，单位分")})
    @RequestMapping(value = {"/service/recharge/user/total"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RechargeCount userRechargeTotal(@ApiParam("用户UID") @RequestParam long uid) {
        return rechargeService.getTotalRechargeCountByUid(uid);
    }

    @ApiOperation(value = "充值总额按照时间", notes = "充值总额按照时间")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "total 充值总额，单位分")})
    @RequestMapping(value = {"/service/recharge/user/total/time"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RechargeCount userRechargeTotal(@ApiParam("用户UID") @RequestParam long uid,
                                           @RequestParam @DateTimeFormat(pattern = "yyyyMMdd") Date start,
                                           @RequestParam @DateTimeFormat(pattern = "yyyyMMdd") Date end) {
        return rechargeService.getTotalRechargeCountByUidAndTime(uid, start, end);

    }


    @ApiOperation(value = "充值总额按照时间", notes = "充值总额按照时间")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "total 充值总额，单位分")})
    @RequestMapping(value = {"/service/recharge/user/total/date"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RechargeCount userRechargeTotalByDate(@ApiParam("用户UID") @RequestParam long uid,
                                           @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date start,
                                           @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date end) {
        return rechargeService.getTotalRechargeCountByUidAndTime(uid, start, end);

    }



    @RequestMapping(value = {"/service/recharge/record/search"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RechargeSearchResult searchRechargeRecord(@RequestBody RechargeSearchCriteria criteria) {
        return rechargeService.searchRechargeRecord(criteria);
    }

    @RequestMapping(value = {"/service/recharge/stat"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RechargeSearchStatResult statRechargeRecord(@RequestBody RechargeSearchCriteria criteria) {
        RechargeSearchResult.RechargeSearchStat success = rechargeService.statSuccessRechargeRecord(criteria);
        RechargeSearchResult.RechargeSearchStat total = rechargeService.statAllRechargeRecord(criteria);
        RechargeSearchStatResult result = new RechargeSearchStatResult();
        result.setEcoin(success.getEcoin());
        result.setRmb(success.getRmb());
        result.setSuccessCountByUser(success.getCountByUser());
        result.setSuccessCount(success.getCount());
        result.setTotalCount(total.getCount());
        return result;
    }

    @RequestMapping(value = {"/service/recharge/stat/group-by-platform"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public List<RechargeStatByPlatform> statRechargeRecordGroupByPlatform(@RequestBody RechargeSearchCriteria criteria) {
        return rechargeService.statSuccessRechargeRecordWithPlatformGroup(criteria);
    }


    @ApiOperation(value = "用户充值记录", notes = "查询用户充值记录")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "充值记录列表")})
    @RequestMapping(value = {"/service/recharge/user/record/list"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RecordList<RechargeRecordPayload> listUserRechargeRecord(
            @ApiParam("用户UID") @RequestParam long uid,
            @ApiParam("开始记录索引") @RequestParam(required = false, defaultValue = "0") int start,
            @ApiParam("记录数量") @RequestParam(required = false, defaultValue = "20") int count,
            HttpServletRequest request) {

        UAgentInfo uai = new UAgentInfo(request);

        List<RechargeRecord> list = rechargeService.listRecordByUid(uid, start, count);
        List<RechargeRecordPayload> rechargeList = new ArrayList<>();
        for (RechargeRecord tmp : list) {
            RechargeRecordPayload record = new RechargeRecordPayload(tmp, uai);
            rechargeList.add(record);
        }
        return new RecordList<>(start, rechargeList);
    }


    @ApiOperation(value = "易币回购选项", notes = "易币回购选项")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "选项列表，需要精简字段")})
    @RequestMapping(value = {"/service/buyback/option/list"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public List<RechargeOption> buybackOption(@ApiParam("中台APP ID") @RequestParam(value = "appId", required = false, defaultValue = "1") int mgsAppId) {

        //TODO: buyback option for MGS
        return rechargeService.getOptionListByPlatform((long) PayPlatform.PPF_BUY_BACK.getValue());
    }


    @ApiOperation(value = "易币回购", notes = "易币回购")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "ecoin: 回购易币数量")})
    @RequestMapping(value = {"/service/buyback/transfer"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map buyback(@ApiParam("用户UID") @RequestParam long uid,
                       @ApiParam("回购金额，人民币(分)") @RequestParam long rmb) {
//        throw new ResponseException(ResponseError.E_SERVICE,"Buyback not enabled");

        User user = userService.getById(uid);
        long ecoin = rechargeService.buyback(user, rmb);
        return GenericMap.toMap("ecoin", ecoin);
    }

    @ApiOperation(value = "苹果充值选项", notes = "苹果充值选项")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "选项列表，需要精简字段")})
    @RequestMapping(value = {"/service/recharge/option/apple"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public List<RechargeOption> listApplePayOption(@ApiParam("中台APP ID") @RequestParam(value = "appId", required = false, defaultValue = "1") int mgsAppId) {
        AppEntity app = appService.getAppById(mgsAppId);
        if (app == null)
            throw new ResponseException(ResponseError.E_RECHARGE_OPTION, "Invalid mgs app id");
        return filterApplePayOption(rechargeService.getOptionListByPlatform(PayPlatform.PPF_APPLE.getValue()), app.getName());
    }


    //TODO: epay h5充值页面使用，需要统一，走UID形式, 后续APPGate也对接此接口,走中台形式
    @ApiOperation(value = "充值选项", notes = "统一的充值选项(不包含苹果)")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "scale,溢价比例，optionlist: 选项列表，需要精简字段")})
    @RequestMapping(value = {"/service/recharge/option/list", "/service/recharge/option/all"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map listOption(@ApiParam("用户session id") @RequestParam(value = "sessionid", required = false) String sessionId,
                          @ApiParam("中台用户易播号") @RequestParam(value = "name", required = false) String name,
                          @ApiParam("中台AppId") @RequestParam(value = "appId", required = false, defaultValue = "0") int appId) {
        User user = null;

        if (sessionId != null) {
            user = userService.getBySession(sessionId);
        } else if (name != null) {
            user = userService.getByName(name);
        }

        if (user == null) {
            throw new ResponseException(ResponseError.E_PARAM, "SessionId or name must be provided");
        }

        AppEntity app = appService.getAppById(appId);

        Pair<BigDecimal, List<RechargeOption>> optionInfo = rechargeService.getOptionByUser(user, appId);
        List<RechargeOption> options = optionInfo.getRight();
        if (app != null && UAgentInfo.OVERSEA.equals(app.getName())) {
            rechargeService.setOptionsTagType(options, appId);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("scale", optionInfo.getLeft());
        map.put("basescale", rechargeService.getBaseRmbToEcoinScale(optionInfo.getLeft()));
        map.put("optionlist", options);
        return map;
    }

    private List<RechargeOptionItem> fillRechargeOptions(List<RechargeOption> options) {
        List<RechargeOptionItem> items = new ArrayList<>();

        options.forEach(option -> {
            RechargeOptionItem out = new RechargeOptionItem(option);
            items.add(out);
        });

        return items;
    }


    /**
     * 融宝三方支付充值订单
     *
     * @param uid
     * @param amount
     * @param memberIp
     * @param terminalType
     * @param terminalInfo
     * @param defaultBank
     * @param returnUrl
     * @return
     * @throws Exception
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/reapal/web/order"})
    @ResponseObjectBody
    public Map<String, String> createReapalOrder(@RequestParam("uid") long uid,
                                                 @RequestParam("amount") long amount,
                                                 @RequestParam("memberip") String memberIp,
                                                 @RequestParam("terminaltype") String terminalType,
                                                 @RequestParam("terminalinfo") String terminalInfo,
                                                 @RequestParam("defaultbank") String defaultBank,
                                                 @RequestParam("returnurl") String returnUrl) {
        return reapalService.createReapalOrder(uid, amount, memberIp, terminalType, terminalInfo, defaultBank, returnUrl);
    }


    @ApiIgnore
    @RequestMapping("/service/recharge/offline/order")
    @AssetBizCheck
    @ResponseObjectBody
    public Map<String, String> offlineOrder(HttpServletRequest request,
                                            @RequestParam long uid,
                                            @RequestParam long rmb,
                                            @RequestParam long ecoin,
                                            @RequestParam(required = false, defaultValue = "1") int appId,
                                            @RequestParam(required = false) String desc) {
        String clientIp = ClientUtils.getClientIPAddr(request);
        return rechargeService.offlinePay(clientIp, uid, rmb, ecoin, appId, desc);
    }

    /**
     * 获取每日的充值记录（运营系统）
     *
     * @param date  日期，格式为yyyy-MM-dd
     * @param start 开始项
     * @param count 总数目
     * @return 返回充值记录
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/record/list-by-date"})
    @ResponseObjectBody
    public RecordList<RechargeRecord> getDateRechargeRecord(@RequestParam String date,
                                                            @RequestParam(required = false, defaultValue = "0") int start,
                                                            @RequestParam(required = false, defaultValue = "20") int count) {
        logger.info("get date recharge record : date-" + date + " start-" + start + " count-" + count);
        List<RechargeRecord> list = rechargeService.listRecordByDate(date, start, count);
        return new RecordList<>(start, list);
    }

    /**
     * 根据商户订单号获取用户ID
     *
     * <AUTHOR>
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/order/info-by-platform-order"})
    @ResponseObjectBody
    public Long getUidByPlatformOrderId(
            @RequestParam(value = "orderId") String orderId
    ) {
        RechargeRecord record = rechargeService.getByPlatformOrderId(orderId);
        if (record == null) {
            throw new ResponseException(ResponseError.E_RECHARGE_INVALID_ORDER_ID, "order id not exists");
        }

        return record.getUid();
    }


    /**
     * 获取每日的易币回购记录（运营系统）
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/buyback/stat"})
    @ResponseObjectBody
    public Map buybackStat(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        logger.info("get buyback stat : date {}", date);
        int ecoin = 0;
        int riceroll = 0;
        int users = 0;
        int total = 0;
        Set<Long> userSet = new HashSet<>();
        List<BuybackRecord> records = rechargeService.getBuybackRecordByDate(date);
        for (BuybackRecord buybackRecord : records) {
            total++;
            ecoin += buybackRecord.getEcoin();
            riceroll += buybackRecord.getRiceroll();
            long uid = buybackRecord.getUid();
            if (!userSet.contains(uid)) {
                userSet.add(uid);
                users++;
            }
        }

        return GenericMap.toMap(
                Arrays.asList("total", "users", "ecoin", "riceroll"),
                Arrays.asList(total, users, ecoin, riceroll)
        );
    }

    /**
     * 获取当前的充值排行榜
     *
     * @return 充值排行榜
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/rank/list"})
    @ResponseObjectBody
    public RecordList<UserCoin> rechargeRank(@RequestParam(value = "start", required = false, defaultValue = "0") int start,
                                             @RequestParam(value = "count", required = false, defaultValue = "20") int count,
                                             @RequestParam(value = "order", required = false, defaultValue = "accumecoin") String order) {
        logger.info("get recharge rank by" + order);
        List<UserCoin> list = assetService.getRechargeRank(start, count, order);
        return new RecordList<>(0, list);
    }

    private Map rechargeStat(Date start, Date end) {
        Map<String, Object> map = new HashMap<>();
        logger.info("Query recharge day stat: {} {}", DateUtils.format(start), DateUtils.format(end));
        map.put("rmb", rechargeService.getTotalRechargeRmb(start, end));
        map.put("users", rechargeService.getTotalRechargePersons(start, end));
        map.put("count", rechargeService.getTotalRechargeCount(start, end));
        return map;
    }

    @ApiIgnore
    @RequestMapping({"/service/recharge/stat/day"})
    @ResponseObjectBody
    public Map rechargeStatByDate(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date start,
                                  @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date end) {
        return rechargeStat(start, end);
    }

    /**
     * 获取用户一段时间内充值记录
     *
     * @param uid
     * @param startDate
     * @param endDate
     * @return records
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/user/record/list-by-date"})
    @ResponseObjectBody
    public List<RechargeRecord> getUserRechargeRecordByDate(
            @RequestParam long uid,
            @RequestParam("start") String startDate,
            @RequestParam("end") String endDate) {
        logger.info("get user " + uid + " RechargeRecord between " + startDate + " and " + endDate);

        return rechargeService.listRecordByUserAndDate(uid, startDate, endDate);
    }

    /**
     * 根据订单号获取充值记录
     *
     * @param orderId
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/record/query"})
    @ResponseObjectBody
    public RechargeRecord getRechargeRecordByOrder(@RequestParam(value = "orderid") String orderId) {
        return rechargeService.getRecordByOrderId(orderId);
    }

    /**
     * 充值选项,
     *
     * @return 对应设备类型的充值选项列表
     */
    @ApiIgnore
    @RequestMapping({"/service/recharge/option/list-by-device"})
    @ResponseObjectBody
    public Map<String, List<RechargeOption>> listOptionByDevice(@RequestParam(value = "device", required = false, defaultValue = "4") long device) {

        //TODO: 这里难道不应该是 getOptionListByDevice ?
        List<RechargeOption> list = rechargeService.getOptionListByPlatform(device);
        Map<String, List<RechargeOption>> map = new HashMap<>();
        map.put("optionlist", list);
        return map;
    }


    @ApiIgnore
    @RequestMapping(value = "/service/recharge/rollback")
    @ResponseObjectBody
    public void rollbackRecharge(@RequestParam String orderId) {
        rechargeService.rollback(orderId);
    }


    @ApiIgnore
    @RequestMapping(value = "/service/recharge/complete")
    @ResponseObjectBody
    public RechargeResult completeOrder(@RequestParam String orderId) {
        return rechargeService.completeOrderById(orderId);
    }


    @ApiIgnore
    @RequestMapping({"/service/recharge/calc/by-uid"})
    @ResponseObjectBody
    public Long calcEcoinByUid(
            @RequestParam long uid,
            @RequestParam long rmb
    ) {
        return rechargeService.calcEcoinByUid(uid, rmb);
    }


    @ApiIgnore
    @RequestMapping({"/service/recharge/wx/idmap"})
    @ResponseObjectBody
    public int mapWeixinRechargeId( @RequestParam long uid,
                                     @RequestParam int appId,
                                     @RequestParam long amount) {
        User user = userService.ensureExists(uid);
        return rechargeControlService.decideWeixinRechargeId(user, appId, amount);
    }

    private List<RechargeOption> filterApplePayOption(List<RechargeOption> optionList, String appName) {

        AppEntity appEntity = appService.getAppByName(appName);
        if (appEntity == null) {
            logger.error("Invalid app name");
            throw new ResponseException(ResponseError.E_RECHARGE_OPTION, "Invalid app name");
        }

        String bundleId = appEntity.getBundleId() == null ? "" : appEntity.getBundleId().toLowerCase();

        List<RechargeOption> ret = new ArrayList<>();
        optionList.forEach(rechargeOption -> {
            if (rechargeOption.getProductId().contains(bundleId)) {
                ret.add(rechargeOption);
            }
        });
        return ret;
    }


    @ApiOperation(value = "使用金币加赠券获得的金币记入充值表", notes = "使用金币加赠券获得的金币记入充值表")
    @RequestMapping({"/service/recharge/prize"})
    @ResponseObjectBody
    public void rechargeBonus(
            @RequestParam String orderId,
            @RequestParam long prize
    ) {
        rechargeService.rechargePrize(orderId, prize);
    }

}
