package com.easylive.pay.controller;

import com.easylive.pay.common.*;
import com.easylive.pay.model.User;
import com.easylive.pay.service.CashoutService;
import com.easylive.pay.service.RechargeControlService;
import com.easylive.pay.service.TaxCollectService;
import com.easylive.pay.service.TestService;
import com.easylive.pay.service.wx.WeixinAppService;
import com.easylive.pay.utils.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.text.SimpleDateFormat;
import java.util.*;

@ApiIgnore
@RestController
@RequestMapping(value = "/test")
public class TestController {
    private static Logger logger = LoggerFactory.getLogger(TestController.class);

    @Autowired
    private TaxCollectService taxCollectService;

    private ResponseObject response = ResponseObject.SUCCESS_INSTANCE;

    @Autowired
    private CashoutService cashoutService;


    @Autowired
    private WeixinAppService weixinAppService;

    @InitBinder
    public void initBinder(ServletRequestDataBinder binder) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CustomDateEditor customDateEditor = new CustomDateEditor(sdf, true);
        binder.registerCustomEditor(Date.class, customDateEditor);
    }

    @RequestMapping(value = "test-session2")
    @SessionCheck
    public String testSession2(@RequestAttribute User user) {

        return String.valueOf(user.getId());
    }


    @RequestMapping(value = "test-session")
    @SessionCheck
    public String testSession(@RequestAttribute("sessionUser") User user) {
        return String.valueOf(user.getId());
    }

    @RequestMapping(value = "/test-string")
    public String testc1() {
        return "new";
    }

    @RequestMapping(value = "/sleep")
    public void testSleep(@RequestParam long time) throws InterruptedException {
        Thread.sleep(time * 1000L);
    }

    @RequestMapping(value = "/test-map")
    @ResponseObjectBody
    public Map<String, String> testMap() {
        HashMap<String, String> ss = new HashMap<String, String>();
        ss.put("CC", "HHHH");
        ss.put("11CC", "HHHH");

        return ss;
    }

    @RequestMapping(value = "/test-list")
    @ResponseObjectBody
    public List<Object> testList() {
        List<Object> ss = new ArrayList<Object>();
        ss.add("CCCC");
        ss.add(5);
        ss.add(ResponseObject.SUCCESS_INSTANCE);
        return ss;
    }


    @RequestMapping(value = "/test")
    @ResponseObjectBody
    public Object testxxx() throws ResponseException {
        int a = 0;
        if (a == 0) {
            logger.info("CCCCC");
            throw new ResponseException(ResponseError.E_PARAM);
        }
        return new Object();
    }

    @RequestMapping(value = "/test1")
    @ResponseObjectBody
    public Object test1() throws ResponseException {
        int a = 0;
        if (a == 0) {
            Object c = null;
            c.toString();
        }
        return new Object();
    }


    @RequestMapping(value = "/get")
    public String testGet() {
        logger.debug("HHHHHHHHHH");
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return "OK";
    }


    @RequestMapping(value = "/httptest")
    public String httptest() {
        return HttpUtils.doGet("http://www.baidu.com");
    }


    @Autowired
    private TestService testService;

    /**
     * 测试新政银企接口
     */
    @RequestMapping(value = "/withdraw")
    public void withdraw() {

        /**
         * 1.构造MAC参数
         * 2.构造XML，构造公共参数
         * 3.解析XML
         */
        Map<String, String> map = new TreeMap<>();
        Date now = new Date();
        map.put("TransDate", new SimpleDateFormat("yyyyMMdd").format(now));
        map.put("TransTime", new SimpleDateFormat("HHmmss").format(now));
        map.put("TransCode", String.valueOf(1120));
        map.put("ChnlType", "0001");
        map.put("TrcNo", "12345678901234567890");
        map.put("EnterpriseNo", "123");
        map.put("BankNo", "123");
        logger.info("[1] Convert Before Map={}", map);

        String xmlString = TestService.buildWithdrawBankXml(map);
        logger.info("[2] XML GBK ={}", xmlString);

        //请求接口
        testService.requestBankUrlWithXml(xmlString);

        Object obj = TestService.bankObjFromXml();
        logger.info("[3] Get Object From XML：Object={}", obj);

        String mac = TestService.getMac(map);
        logger.info("[4] MAC string={}", mac);

    }

    /**
     * 测试税筹API2.0接口
     */
    @RequestMapping(value = "/tax/sign")
    public boolean taxSign(@RequestParam String cardName,
                           @RequestParam String cardNo,
                           @RequestParam String idCard,
                           @RequestParam String mobile) {
        return taxCollectService.signContract(cardName, cardNo, idCard, mobile);
    }


    @Autowired
    private RechargeControlService rechargeControlService;

    @RequestMapping(value = "/recharge/control/map")
    public String testRechargeControl(@RequestParam int appId,
                                      @RequestParam int type,
                                      @RequestParam int subType,
                                      @RequestParam int amount
    ) {
        int rechargeId = rechargeControlService.getRechargeId(appId, type, subType, amount);
        return String.valueOf(rechargeId);
    }

    @RequestMapping(value = "/recharge/control/increase")
    public void testIncrease(@RequestParam int appId,
                                    @RequestParam int type,
                                    @RequestParam int rechargeId,
                                    @RequestParam int amount
    ) {
        rechargeControlService.increaseAmount(appId, type, rechargeId, amount);
    }



    @RequestMapping(value = "/recharge/wx/test")
    public String testGenerateScheme() {
        return weixinAppService.generateAppletScheme("wxc5060ccd0c57d8e1","pages/recharge/recharge","orderId=20240124094613462624682639356743","develop");
    }


    /**
     * 测试提现消息
     */
    @RequestMapping(value = "/cashout", method = {RequestMethod.GET, RequestMethod.POST})
    public Object testCashout(@RequestParam(required = false, defaultValue = "1") long id,
                              @RequestParam(required = false, defaultValue = "1") long uid,
                              @RequestParam(required = false, defaultValue = "200") long rmb,
                              @RequestParam(required = false, defaultValue = "500000") long riceroll,
                              @RequestParam(required = false, defaultValue = "4") int platform,
                              @RequestParam(required = false, defaultValue = "19") int appId,
                              @RequestParam(required = false, defaultValue = "") Date commitTime,
                              @RequestParam(required = false, defaultValue = "") Date completeTime) {
        cashoutService.emitCashout(id, uid, rmb, riceroll, platform, appId, commitTime, completeTime);
        return "success";
    }

}
