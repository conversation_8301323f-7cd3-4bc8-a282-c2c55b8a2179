package com.easylive.pay.controller;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ErrorAttributes;
import org.springframework.boot.autoconfigure.web.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.ServletRequestAttributes;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@ApiIgnore
@RestController
public class CustomizeErrorController implements ErrorController {

    private static final String PATH = "/error";
    private static Logger logger = LoggerFactory.getLogger(CustomizeErrorController.class);
    @Autowired
    private ErrorAttributes errorAttributes;

    @Override
    public String getErrorPath() {
        return PATH;
    }

    @RequestMapping(value = PATH)
    public String error(HttpServletRequest request, HttpServletResponse response) {
        //return new ResponseObject("E_SERVER", "Unknown Server Error");
        RequestAttributes requestAttributes = new ServletRequestAttributes(request);
        Map<String, Object> errors = this.errorAttributes.getErrorAttributes(requestAttributes, true);
        StringBuilder sb = new StringBuilder();
        sb.append(response.getStatus());
        if (response.getStatus() == HttpStatus.NOT_FOUND.value()) {
            return "{\"retval\":\"E_SERVICE\",\"reterr\":\"Service Not Found\"}";
        }

        for (Map.Entry<String, Object> entry : errors.entrySet()) {
            sb.append(entry.getKey() + ":" + entry.getValue());
        }
        logger.error(sb.toString());
        ObjectMapper mapper = new ObjectMapper();
        ResponseObject ro = new ResponseObject(ResponseError.E_SERVER, "Server Internal Error");
        try {
            return mapper.writeValueAsString(ro);
        } catch (Exception e) {
            return "{\"retval\":\"E_SERVER\",\"reterr\":\"Server Internal Error\"}";
        }
    }

}
