package com.easylive.pay.controller;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.config.GashConfig;
import com.easylive.pay.entity.RechargeOption;
import com.easylive.pay.enums.GashRechargeOption;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.service.UserService;
import com.easylive.pay.service.gash.GashPayService;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.vo.GashVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriUtils;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/")
@Slf4j
public class GashPayController {

    private static Logger logger = LoggerFactory.getLogger(GashPayController.class);

    @Autowired
    private GashPayService gashPayService;

    @Autowired
    private UserService userService;

    @Autowired
    private GashConfig gashConfig;

    @ApiOperation("获取gash充值选项")
    @RequestMapping(value = {"/pay/recharge/gash/option"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public Map optionList(@ApiParam("中台APPID") @RequestParam int appId) {
        List<RechargeOption> optionList = gashPayService.getRechargeOption(appId);
        return GenericMap.toMap("optionlist", optionList);
    }

    @ApiOperation("创建gash订单")
    @RequestMapping(value = {"/pay/recharge/gash/order"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public String createOrder(@ApiParam("用户Id") @RequestParam String name,
                              @ApiParam("订单金额") @RequestParam long amount,
                              @ApiParam("充值类型选项") @RequestParam int option,
                              @ApiParam("中台APPID") @RequestParam int appId,
                              @ApiParam("回传参数") @RequestParam String redirectUrl,
                              HttpServletRequest request) {

        GashRechargeOption gashOption = GashRechargeOption.of(option);
        if (gashOption == null) {
            throw new ResponseException(ResponseError.E_RECHARGE_PLATFORM);
        }

        User user = userService.getByName(name);
        if (user == null) {
            throw new ResponseException(ResponseError.E_USER_NOT_EXISTS);
        }

        String clientIp = ClientUtils.getClientIPAddr(request);
        return gashPayService.createOrder(user.getId(), appId, amount, MobilePlatform.MPF_H5, clientIp, redirectUrl, gashOption);
    }

    @ApiIgnore
    @RequestMapping(value = {"/pay/recharge/gash/web/return"}, method = RequestMethod.POST)
    public String gashWebReturn(HttpServletRequest request) {
        GashVO gashVO = new GashVO();
        try {
             gashPayService.operateOrder(request.getParameter("data"), gashVO);
        } catch (ResponseException e) {
            log.error("Gash notify valid fail. {}", e.getMessage());
        } catch (Exception e) {
            log.error("Gash notify exception.", e);
        }

        String url = gashVO.getExtra();
        try {
            url = UriUtils.decode(gashVO.getExtra(), StandardCharsets.UTF_8.toString());
        } catch (Exception ignored) {
        }

        return url + "?result=" + gashVO.getStatus().getCode();
    }

    @ApiIgnore
    @RequestMapping(value = {"/pay/recharge/gash/checkorder"}, method = RequestMethod.POST)
    public String checkOrder(
            @ApiParam("用户订单ID") @RequestParam String orderId,
            @ApiParam("订单金额") @RequestParam long amount,
            @ApiParam("平台支付ID") @RequestParam String paid
    ) {
        return gashPayService.checkOrder(orderId, amount, paid);
    }
}

