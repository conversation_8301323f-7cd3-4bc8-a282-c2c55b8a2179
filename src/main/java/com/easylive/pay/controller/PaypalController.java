package com.easylive.pay.controller;

import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.entity.RechargeOption;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.PayPlatform;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.PaypalOrder;
import com.easylive.pay.model.User;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.service.UserService;
import com.easylive.pay.service.paypal.PaypalService;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/")
public class PaypalController {

    @Autowired
    private PaypalService paypalService;

    @Autowired
    private RechargeService rechargeService;

    @Autowired
    private UserService userService;


    @ApiIgnore
    @ApiOperation("创建paypal订单")
    @RequestMapping(value = {"/pay/recharge/paypal/option"}, method = { RequestMethod.GET, RequestMethod.POST} )
    @ResponseObjectBody
    public Map optionList(@RequestParam(value = "sessionid", required = false, defaultValue = "") String sessionId,
                          @RequestParam(required = false, defaultValue = "") String name) {
        User user = userService.getBySessionOrName(sessionId, name);
        //TODO: paypal 溢价充值
        List<RechargeOption> list = rechargeService.getOptionListByPlatformAndUser(PayPlatform.PPF_PAYPAL,user);
        return GenericMap.toMap("optionlist", list);
    }

    @ApiIgnore
    @ApiOperation("APP创建paypal订单")
    @RequestMapping(value = {"/pay/recharge/paypal/order"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public PaypalOrder createOrder(@RequestParam(value = "sessionid", required = false, defaultValue = "") String sessionId,
                                   @RequestParam(required = false, defaultValue = "") String name,
                                   @ApiParam("支付选项名") @RequestParam String productId,
                                   @ApiParam("App名称") @RequestParam(required = false) String appName,
                                   HttpServletRequest request) {
        User user = userService.getBySessionOrName(sessionId, name);
        UAgentInfo ua = new UAgentInfo(request);
        MobilePlatform mobilePlatform = ua.getDeviceType();
        String clientIp = ClientUtils.getClientIPAddr(request);
        if( appName == null )
            appName = ua.getAppName();

        return paypalService.createOrder(user.getId(), appName, productId, mobilePlatform, clientIp);
    }

    /**
     * APP返回Paypal
     */
    @ApiOperation("APP提交Paypal交易")
    @RequestMapping(value = {"/pay/recharge/paypal/transaction"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public void createTransaction(@RequestParam(value = "sessionid", required = false, defaultValue = "") String sessionId,
                                  @RequestParam(required = false, defaultValue = "") String name,
                                  @ApiParam("订单号") @RequestParam String orderId,
                                  @ApiParam("Paypal Payment Nonce") @RequestParam String nonce) {
        User user = userService.getBySessionOrName(sessionId, name);
        paypalService.createTransaction(user.getId(), orderId, nonce);
    }

    /**
     * APP返回Paypal
     */
    @ApiOperation("查询Paypal交易")
    @RequestMapping(value = {"/service/recharge/paypal/transaction/info"}, method = RequestMethod.GET)
    public String getTransaction(@ApiParam("订单号") @RequestParam String orderId) {
        return paypalService.getTransactionInfo(orderId);
    }

}

