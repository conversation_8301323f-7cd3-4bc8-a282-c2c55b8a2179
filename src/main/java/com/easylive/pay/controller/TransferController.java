package com.easylive.pay.controller;

import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.output.TransferQueryResult;
import com.easylive.pay.output.TransferResult;
import com.easylive.pay.output.common.PageableList;
import com.easylive.pay.service.TransferService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/")
public class TransferController {


    @Autowired
    private TransferService transferService;


    @ApiOperation(value = "易币转账")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "用户资产信息")})
    @RequestMapping(value = {"/service/transfer/ecoin"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public TransferResult transferEcoin(@ApiParam("用户UID") @RequestParam long fromUid,
                                        @ApiParam("主播UID") @RequestParam long toUid,
                                        @ApiParam("价格") @RequestParam int ecoin) {
        return transferService.transfer(fromUid, toUid, ecoin);
    }

    @ApiOperation(value = "易币转账查询")
    @RequestMapping(value = {"/service/transfer/ecoin/query"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public PageableList<TransferQueryResult> transferEcoinQuery(@ApiParam("用户UID") @RequestParam(required = false) String fromName,
                                                                @ApiParam("主播UID") @RequestParam(required = false) String toName,
                                                                @ApiParam("页数") @RequestParam(required = false, defaultValue = "1") int page,
                                                                @ApiParam("页记录数") @RequestParam(required = false, defaultValue = "10") int size) {
        return transferService.query(fromName, toName, page, size);
    }

}
