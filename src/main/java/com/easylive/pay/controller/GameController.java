package com.easylive.pay.controller;


import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.config.GameConfig;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.enums.DdzUpdateCoinType;
import com.easylive.pay.model.User;
import com.easylive.pay.model.UserBeanUpdate;
import com.easylive.pay.service.AppGateService;
import com.easylive.pay.service.AssetService;
import com.easylive.pay.service.GameService;
import com.easylive.pay.service.UserService;
import com.easylive.pay.utils.Md5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.Map;

@ApiIgnore
@RestController
@RequestMapping(value = "/")
public class GameController {

    private static final long COINRATE = 1000;
    private static Logger logger = LoggerFactory.getLogger(GameController.class);
    @Autowired
    private AssetService assetService;

    @Autowired
    private AppGateService appGateService;

    @Autowired
    private UserService userService;

    @Autowired
    private GameService gameService;

    @Autowired
    private GameConfig gameConfig;

    @RequestMapping(value = "/service/game/buygift")
    @ResponseObjectBody
    public String buyGameGift(
            @RequestParam(value = "fromUid") Long fromUid,
            @RequestParam(value = "toUid") Long toUid,
            @RequestParam(value = "goodsId") Long gid,
            @RequestParam(value = "vid") String vid,
            @RequestParam(value = "beancost") Long cost,
            @RequestParam(value = "ecoin") Long ecoin,
            @RequestParam(value = "platformId") Long platformId,
            @RequestParam(value = "number") Integer number
    ) {
        User fromUser = userService.getById(fromUid);
        User toUser = userService.getById(toUid);

        return gameService.buyGameGift(fromUser, toUser, platformId, ecoin, cost, gid, number, vid);
    }

    /**
     * @param userName
     * @return 用户易币
     */
    @RequestMapping(value = "/pay/game/user/asset")
    @ResponseObjectBody
    public Map<String, String> getUserAsset(@RequestParam(value = "username", defaultValue = "0", required = false) String userName) {

        String defaultValue = "0";
        if (defaultValue.equals(userName)) {

            throw new ResponseException(ResponseError.E_PARAM);
        }
        HashMap<String, String> coin = new HashMap<>(16);

        try {

            User user = userService.ensureExists(userName);
            UserCoin uc = assetService.getByUid(user.getId());
            coin.put("userasset", String.valueOf(uc.getEcoin()));
            coin.put("name", user.getName());
        } catch (ResponseException e) {
            throw e;
        } catch (Exception e) {
            logger.error("[getUserAsset] runtime name:" + userName + "error:", e);
            throw new ResponseException(ResponseError.E_SERVER);
        }

        return coin;


    }

    /**
     * @param userName
     * @param type      1:增加 2:减少
     * @param coin
     * @param bean
     * @param timestamp
     * @param sign
     */
    @RequestMapping(value = {"/pay/game/update/ecoin", "/service/game/update/ecoin"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public void updateUserAsset(@RequestParam(value = "username", defaultValue = "0", required = false) String userName,
                                @RequestParam(value = "type", defaultValue = "0", required = false) Integer type,
                                @RequestParam(value = "coin", defaultValue = "0", required = false) long coin,
                                @RequestParam(value = "bean", defaultValue = "0", required = false) long bean,
                                @RequestParam(value = "timestamp", defaultValue = "0", required = false) String timestamp,
                                @RequestParam(value = "sign", defaultValue = "0", required = false) String sign) {

        Map<String, String> requestParams = new HashMap<>(16);
        requestParams.put("username", userName);
        requestParams.put("type", type.toString());
        requestParams.put("coin", String.valueOf(coin));
        requestParams.put("timestamp", timestamp);
        requestParams.put("sign", sign);
        requestParams.put("bean", String.valueOf(bean));

        logger.info("ddz [updateUserAsset] params=" + requestParams);

        if (type != DdzUpdateCoinType.DDZ_ECOIN_ADD.getValue() && type != DdzUpdateCoinType.DDZ_ECOIN_REDUCE.getValue()) {

            throw new ResponseException(ResponseError.E_PARAM);
        }

        String defaultValue = "0";
        if (defaultValue.equals(userName) || defaultValue.equals(timestamp) || defaultValue.equals(sign) || defaultValue.equals(String.valueOf(bean))) {

            throw new ResponseException(ResponseError.E_PARAM);
        }

        if ((coin * COINRATE) != bean) {
            logger.error("bean number error,coin: " + coin + " bean:" + bean);
            throw new ResponseException(ResponseError.E_PARAM);
        }

        if (coin <= 0) {
            logger.error("coin must greater than zero " + coin);
            throw new ResponseException(ResponseError.E_ASSET_ECOIN_NOT_ENOUGH);
        }


        try {

            String mySign = Md5Utils.buildMySign(requestParams, "&key=" + gameConfig.getKey());
            logger.info("[updateUserAsset] sign:" + sign + ",mySign=" + mySign);
            if (!sign.equals(mySign)) {
                logger.error("sign error! username:" + userName);
                throw new ResponseException(ResponseError.E_GAME_SIGN_FAILD);
            }
            User user = userService.ensureExists(userName);
            long uid = user.getId();
            logger.info("coin exchange starting [updateUserAsset] uid:" + uid + " username:" + userName + " type:" + type + " coin:" + String.valueOf(coin) + " bean:" + String.valueOf(bean));

            //游戏豆兑换易币 易币兑换游戏豆
            if (type == DdzUpdateCoinType.DDZ_ECOIN_ADD.getValue()) {
                gameService.exchangeBeanToEcoin(bean, coin, user, type);
            } else if (type == DdzUpdateCoinType.DDZ_ECOIN_REDUCE.getValue()) {
                gameService.exchangeEcoinToBean(bean, coin, user, type);
            }
        } catch (ResponseException e) {
            throw e;
        } catch (Exception e) {
            logger.error("[updateUserAsset] error: ", e);
            throw new ResponseException(ResponseError.E_SERVER);
        }


    }


    /**
     * @param userBeanUpdate
     */
    @RequestMapping(value = "/pay/game/user/bean/update", method = RequestMethod.POST)
    @ResponseObjectBody
    public void notifyDdzConsume(UserBeanUpdate userBeanUpdate) {

        Map<String, String> requestParams = new HashMap<>(16);
        requestParams.put("unames", userBeanUpdate.getUnames());
        requestParams.put("type", String.valueOf(userBeanUpdate.getType()));
        requestParams.put("timestamp", String.valueOf(userBeanUpdate.getTimestamp()));
        requestParams.put("sign", userBeanUpdate.getSign());
        requestParams.put("bean", String.valueOf(userBeanUpdate.getBean()));
        requestParams.put("gameid", String.valueOf(userBeanUpdate.getGameid()));
        String mySign = Md5Utils.buildMySign(requestParams, "&key=" + gameConfig.getKey());
        String sign = userBeanUpdate.getSign();
        logger.info("[notifyDdzConsume] user bean update params:" + userBeanUpdate);
        logger.info("[notifyDdzConsume] sign:" + sign + ",mySign=" + mySign);
        try {


            if (!sign.equals(mySign)) {
                logger.error("[notifyDdzConsume] sign error! username:" + userBeanUpdate.getUnames());
                throw new ResponseException(ResponseError.E_GAME_SIGN_FAILD);
            }
            int subtract = 1;
            int add = 2;


            if (userBeanUpdate.getType() == subtract) {

                appGateService.updateUserBean(1, userBeanUpdate.getUnames(), userBeanUpdate.getBean(), userBeanUpdate.getGameid());

            } else if (userBeanUpdate.getType() == add) {

                appGateService.updateUserBean(3, userBeanUpdate.getUnames(), userBeanUpdate.getBean(), userBeanUpdate.getGameid());

            }

        } catch (ResponseException e) {
            throw e;
        } catch (Exception e) {
            logger.error("[notifyDdzConsume] notify appgw error", e);
            throw new ResponseException(ResponseError.E_SERVER);
        }


    }


}
