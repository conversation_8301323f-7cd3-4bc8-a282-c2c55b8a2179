package com.easylive.pay.controller;

import com.easylive.pay.common.*;
import com.easylive.pay.entity.Goods;
import com.easylive.pay.entity.GoodsRecord;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.entity.UserGiftStat;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.output.Asset;
import com.easylive.pay.output.PersonalGiftRecordVO;
import com.easylive.pay.output.RecordList;
import com.easylive.pay.service.*;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/")
public class GiftController {

    private static Logger logger = LoggerFactory.getLogger(GiftController.class);

    @Autowired
    private AssetService assetService;

    @Autowired
    private GiftService giftService;

    @Autowired
    private ContributorService contributorService;

    @Autowired
    private UserService userService;

    @Autowired
    private AppService appService;


    @Value("#{'${goods.fans.group.gift.ids}'.split(',')}")
    private List<Long> goodsFansGroupGiftIds;


    /**
     * 购买商品送给主播
     *
     * @param fromUser 购买的用户
     * @param goodsId  商品号
     * @param vid      所在视频
     * @param name     主播的易播号
     * @param number   数量
     * @return Asset
     */
    @ApiIgnore
    @RequestMapping({"/pay/buy", "/pay/gift/buy"})
    @SessionCheck
    @ResponseObjectBody
    public Asset buy(@RequestAttribute User fromUser,
                     @RequestParam(value = "goodsid") long goodsId,
                     @RequestParam String vid,
                     @RequestParam String name,
                     @RequestParam(required = false, defaultValue = "1") int number,
                     @RequestParam(name = "pk_id", required = false) Long pkId,
                     HttpServletRequest request
    ) {
        UAgentInfo ua = new UAgentInfo(request);
        if (goodsFansGroupGiftIds.contains(goodsId)) {
            //粉丝团礼物不允许公网接口调用
            logger.error("Buy gift public is fans goods, invalid, goods id :{}",goodsId);
            throw new ResponseException(ResponseError.E_FREE_GOODS_NOT_ALLOW);
        }
        return giftService.buy(fromUser, goodsId, vid, name, number, pkId, ua);
    }

    /**
     * 个人中心/私信购买礼物
     *
     * @param fromUser
     * @param goodsId
     * @param vid
     * @param name
     * @param number
     * @param type
     * @param request
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/pay/gift/personal/buy"})
    @SessionCheck
    @ResponseObjectBody
    public Asset purchaseGift(@RequestAttribute User fromUser,
                              @RequestParam long goodsId,
                              @RequestParam String name,
                              @RequestParam String vid,
                              @RequestParam(required = false, defaultValue = "1") @Min(1) int number,
                              @RequestParam int type,
                              @RequestParam(required = false) String sender,
                              @RequestParam(required = false) String receiver,
                              HttpServletRequest request
    ) {
        UAgentInfo ua = new UAgentInfo(request);
        return giftService.buy(fromUser, goodsId, vid, name, number, null, type, ua, sender, receiver);
    }

    @RequestMapping({"/pay/gift/personal/record"})
    @ResponseObjectBody
    public List<PersonalGiftRecordVO> giftRecord(@RequestParam String name,
                                                 @RequestParam(required = false, defaultValue = "0") @Min(0) int start,
                                                 @RequestParam(required = false, defaultValue = "20") @Min(0) int count,
                                                 HttpServletRequest request
    ) {
        UAgentInfo ua = new UAgentInfo(request);
        int appId = appService.getAppIdByName(ua.getAppName());
        return giftService.giftRecord(name, appId);
    }

    /*
    -----------------------------------------------------------------------------------------------
     */


    /**
     * 获取商品列表,运营系统每天统计需要
     * TODO: 中台支持
     *
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/service/gift/goods/list"})
    @ResponseObjectBody
    public List<Goods> goods(@RequestParam(required = false, defaultValue = "1") long sourceId) {
        return giftService.getGoodsList();
    }


    /**
     * 获取当前的接收礼物前500的用户
     * TODO: 中台支持
     *
     * @return 接收礼物排行榜
     */
    @ApiIgnore
    @RequestMapping({"/service/gift/rank/receiver"})
    @ResponseObjectBody
    public RecordList<UserCoin> receiveGiftRank(@RequestParam(required = false, defaultValue = "1") long sourceId) {
        List<UserCoin> list = assetService.getReceiveGiftRank();
        return new RecordList<>(0, list);
    }

    /**
     * 获取当前的赠送礼物前500的用户
     * TODO: 中台支持
     *
     * @return 赠送礼物排行榜
     */
    @ApiIgnore
    @RequestMapping({"/service/gift/rank/sender"})
    @ResponseObjectBody
    public RecordList<UserCoin> sendGiftRank(@RequestParam(required = false, defaultValue = "1") long sourceId) {
        List<UserCoin> list = assetService.getSendGiftRank();
        return new RecordList<>(0, list);
    }


    /**
     * 获取用户的礼物记录
     *
     * @param uid
     * @param startDate
     * @param endDate
     * @param type      类型 0:收到  1:赠送
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/service/gift/record/list-by-user"})
    @ResponseObjectBody
    public List<GoodsRecord> getUserGoodsRecord(@RequestParam long uid,
                                                @RequestParam("start") String startDate,
                                                @RequestParam("end") String endDate,
                                                @RequestParam int type) {
        logger.info("get user " + uid + " GoodsRecord between " + startDate + " and " + endDate);

        return giftService.getGoodsRecordByUser(uid, startDate, endDate, type);
    }


    /**
     * 获取每日的礼物购买记录（运营系统统计）
     * TODO: 中台支持
     *
     * @param date  日期，格式为yyyy-MM-dd
     * @param start 开始项
     * @param count 总数目
     * @return 返回礼物购买记录
     */
    @ApiIgnore
    @RequestMapping({"/service/gift/record/list-by-date"})
    @ResponseObjectBody
    public RecordList<GoodsRecord> getDateGoodsRecord(@RequestParam(required = false, defaultValue = "1") long sourceId,
                                                      @RequestParam String date,
                                                      @RequestParam(required = false, defaultValue = "0") int start,
                                                      @RequestParam(required = false, defaultValue = "20") int count) {
        logger.info("get date goods record : date-" + date + " start-" + start + " count-" + count);
        List<GoodsRecord> list = giftService.getGoodsRecordByDate(date, start, count);
        return new RecordList<>(start, list);
    }


    /**
     * 购买商品送给主播，目前给web系统以及appgate使用
     * TODO: 统一接口，先让web系统和appgate统一，再改造
     *
     * @param fromUser 购买的用户
     * @param goodsId  商品号
     * @param vid      所在视频
     * @param name     主播的易播号
     * @param number   数量
     * @return Asset
     */
    @ApiOperation(value = "打赏礼物", notes = "打赏礼物")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sessionid", value = "用户session", dataType = "string", paramType = "query", required = true),
    })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "打赏之后的用户资产信息")})
    @RequestMapping(value = {"/service/buy", "/service/gift/buy"}, method = {RequestMethod.GET, RequestMethod.POST})
    @SessionCheck
    @ResponseObjectBody
    public Asset buyInternal(@RequestAttribute User fromUser,
                             @ApiParam("礼物ID") @RequestParam(value = "goodsid") long goodsId,
                             @ApiParam("房间/视频ID") @RequestParam String vid,
                             @ApiParam("主播易播号") @RequestParam String name,
                             @ApiParam("礼物数量") @RequestParam(required = false, defaultValue = "1") int number,
                             @ApiParam("PK记录ID") @RequestParam(name = "pk_id", required = false) Long pkId,
                             HttpServletRequest request
    ) {
        UAgentInfo ua = new UAgentInfo(request);
        return giftService.buy(fromUser, goodsId, vid, name, number, pkId, ua);
    }

    @ApiOperation(value = "内网打赏礼物", notes = "内网打赏礼物")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "打赏之后的用户资产信息")})
    @RequestMapping(value = {"/service/buy/gift/by-uid"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Asset buyInternal(@ApiParam("用户UID") @RequestParam long uid,
                             @ApiParam("礼物ID") @RequestParam(value = "goodsid") long goodsId,
                             @ApiParam("房间/视频ID") @RequestParam String vid,
                             @ApiParam("主播易播号") @RequestParam String name,
                             @ApiParam("礼物数量") @RequestParam(required = false, defaultValue = "1") int number,
                             @ApiParam("PK记录ID") @RequestParam(name = "pk_id", required = false) Long pkId,
                             HttpServletRequest request
    ) {
        UAgentInfo ua = new UAgentInfo(request);
        User fromUser = userService.ensureExists(uid);
        return giftService.buy(fromUser, goodsId, vid, name, number, pkId, ua);
    }

    @ApiIgnore
    @RequestMapping({"/service/gift/user/stat"})
    @ResponseObjectBody
    public Map giftStat(@RequestParam long uid) {
        UserGiftStat stat = giftService.getUserGiftStat(uid);
        return GenericMap.toMap(Arrays.asList("sendgiftcount", "recvgiftcount"),
                Arrays.asList(stat.getSendGiftCount(), stat.getRecvGiftCount()));
    }



    /*
     ------------------------------------------------------------------------------------------
     */


}