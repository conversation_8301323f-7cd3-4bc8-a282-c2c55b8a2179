package com.easylive.pay.controller;

import com.easylive.pay.common.*;
import com.easylive.pay.entity.GuardianRelationEntity;
import com.easylive.pay.model.User;
import com.easylive.pay.output.BuyGuardianOut;
import com.easylive.pay.output.GuardianRelation;
import com.easylive.pay.service.AppService;
import com.easylive.pay.service.GuardianService;
import com.easylive.pay.service.UserService;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/")
public class GuardianController {

    private static Logger logger = LoggerFactory.getLogger(GuardianController.class);

    @Autowired
    private GuardianService guardianService;

    @Autowired
    private UserService userService;

    @Autowired
    private AppService appService;
    /**
     * 购买守护给主播
     *
     * @param fromUser   购买用户
     * @param guardianId 守护id
     * @param toName     主播易播号
     */
    @ApiIgnore
    @RequestMapping({"/pay/buyguardian", "/pay/guardian/buy"})
    @SessionCheck
    @ResponseObjectBody
    public BuyGuardianOut buyGuardian(
            @RequestAttribute User fromUser,
            @RequestParam(value = "guardianid") int guardianId,
            @RequestParam(value = "to_user") String toName,
            @RequestParam(value = "vid") String vid,
            @ApiParam("开通月份") @RequestParam(required = false, defaultValue = "1") long month,
            HttpServletRequest request
    ) {
        UAgentInfo ua = new UAgentInfo(request);
        int appId = appService.getAppIdByName(ua.getAppName());
        return guardianService.buyGuardian(fromUser, toName, guardianId, vid, ua, month, appId);
    }


    @ApiOperation(value = "购买守护", notes = "购买守护")
    @RequestMapping(value = {"/service/guardian/buy"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public BuyGuardianOut buyGuardianInternal(
            @ApiParam("用户ID") @RequestParam long fromUid,
            @ApiParam("礼物ID") @RequestParam int guardianId,
            @ApiParam("主播易播号") @RequestParam String toName,
            @ApiParam("房间/视频ID") @RequestParam String vid,
            @ApiParam("开通月份") @RequestParam(required = false, defaultValue = "1") long month,
            HttpServletRequest request
    ) {
        User fromUser = userService.getById(fromUid);
        if (fromUser == null)
            throw new ResponseException(ResponseError.E_USER, "Invalid user id");
        UAgentInfo ua = new UAgentInfo(request);
        int appId = appService.getAppIdByName(ua.getAppName());
        return guardianService.buyGuardian(fromUser, toName, guardianId, vid, ua, month, appId);
    }


    @ApiOperation(value = "获取守护信息", notes = "获取两个用户之间的守护信息")
    @RequestMapping(value = {"/service/guardian/relation"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public GuardianRelation guardianRelation(@ApiParam("用户ID") @RequestParam long from,
                                             @ApiParam("主播ID") @RequestParam long to) {

        GuardianRelationEntity relation = guardianService.getRelation(from, to);
        if (relation != null) {
            return new GuardianRelation(relation, guardianService);
        }
        return new GuardianRelation(from, to);
    }

    @ApiOperation(value = "获取主播和用户守护信息", notes = "获取主播和多个用户的守护关系")
    @RequestMapping(value = {"/service/guardian/relation/multi"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public List<GuardianRelation> guardianRelation(@ApiParam("主播ID") @RequestParam long anchorUid,
                                                   @ApiParam("用户ID列表，逗号") @RequestParam String uids) {
        List<Long> uidList = new ArrayList<>();
        for (String s : uids.split(Constants.UID_SEPARATOR)) {
            try {
                long uid = Long.valueOf(s);
                uidList.add(uid);
            } catch (NumberFormatException e) {
                throw new ResponseException(ResponseError.E_PARAM, "Invalid uid: " + s);
            }
        }

        Map<Long, GuardianRelationEntity> relations = guardianService.getRelationOfAnchorAndUsers(anchorUid, uidList);
        List<GuardianRelation> ret = new ArrayList<>(relations.size());
        uidList.forEach(uid -> {
            GuardianRelationEntity relation = relations.get(uid);
            if (relation == null) {
                ret.add(new GuardianRelation(uid, anchorUid));
            } else {
                ret.add(new GuardianRelation(relation, guardianService));
            }
        });
        return ret;
    }


    /*
     ------------------------------------------------------------------------------------------
     */


}