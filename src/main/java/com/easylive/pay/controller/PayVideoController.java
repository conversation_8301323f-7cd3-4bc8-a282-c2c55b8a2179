package com.easylive.pay.controller;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.output.Asset;
import com.easylive.pay.output.BaseAsset;
import com.easylive.pay.service.PayVideoService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping(value = "/")
public class PayVideoController {


    @Autowired
    private PayVideoService payVideoService;


    /**
     * 付费视频
     *
     * @param uid
     * @param toUid
     * @param ecoin
     * @param vid
     * @return UserCoin
     */
    @ApiOperation(value = "购买付费视频", notes = "购买付费视频")
    @RequestMapping(value = {"/service/paylive/buy"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public BaseAsset payLive(
            @ApiParam("用户UID") @RequestParam(value = "uid") long uid,
            @ApiParam("主播UID") @RequestParam(value = "to_uid") long toUid,
            @ApiParam("价格") @RequestParam(value = "ecoin") long ecoin,
            @ApiParam("视频ID") @RequestParam(value = "vid") String vid,
            @ApiParam("是否是直播，1=是,0=否") @RequestParam(value = "living") long living,
            @ApiParam("价格比例调整(比如丝直播),不需要调整传0") @RequestParam(value = "ecoin_factor", required = false, defaultValue = "0") long ecoinFactor
    ) {
        if (ecoinFactor < 0 || ecoinFactor > 5) {
            throw new ResponseException(ResponseError.E_PARAM);
        }
        if (ecoinFactor > 0) {
            return payVideoService.pay(uid, ecoin, toUid, vid, living, ecoinFactor);
        } else {
            return payVideoService.pay(uid, ecoin, toUid, vid, living);
        }
    }


}
