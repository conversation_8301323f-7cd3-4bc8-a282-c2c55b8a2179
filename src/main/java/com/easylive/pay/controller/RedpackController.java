package com.easylive.pay.controller;

import com.easylive.pay.common.RequestAttribute;
import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.common.SessionCheck;
import com.easylive.pay.entity.Redpack;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.output.Asset;
import com.easylive.pay.output.OpenRedpackList;
import com.easylive.pay.output.RedpackOpenResult;
import com.easylive.pay.service.RedpackService;
import com.easylive.pay.service.UserService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = "/")
public class RedpackController {

    private static Logger logger = LoggerFactory.getLogger(RedpackController.class);

    @Autowired
    private RedpackService redpackService;

    @Autowired
    private UserService userService;


    @ApiIgnore
    @RequestMapping({"/pay/redpack", "/pay/redpack/room/open"})
    @SessionCheck
    @ResponseObjectBody
    public RedpackOpenResult openRoomRedpack(
            @RequestAttribute User fromUser,
            @RequestParam String vid,
            @RequestParam String code) {
        return new RedpackOpenResult(redpackService.openRedpackInRoom(fromUser, vid, code));
    }

    /**
     * 主播发送红包
     *
     * @param user  主播用户
     * @param vid   视频vid
     * @param ecoin 红包易币数
     * @param count 红包份数
     * @return Asset
     */
    @ApiIgnore
    @RequestMapping({"/pay/createredpack", "/pay/redpack/room/create"})
    @SessionCheck
    @ResponseObjectBody
    public Asset createRoomRedpack(@RequestAttribute User user,
                                   @RequestParam String vid,
                                   @RequestParam int ecoin,
                                   @RequestParam int count,
                                   @RequestParam(required = false, defaultValue = "恭喜发财") String title) {

        return redpackService.createRoomRedpack(user, vid, ecoin, count, title);
    }

    /**
     * 群组发送红包
     *
     * @param user  主播用户
     * @param ecoin 红包易币数
     * @param count 红包份数
     * @return Redpack
     */
    @ApiIgnore
    @RequestMapping({"/pay/groupredpack", "/pay/redpack/group/create"})
    @SessionCheck
    @ResponseObjectBody
    public Redpack createGroupRedpack(@RequestAttribute User user,
                                      @RequestParam int ecoin,
                                      @RequestParam int count,
                                      @RequestParam(required = false, defaultValue = "恭喜发财") String title) {
        return redpackService.createGroupRedpack(user, ecoin, count, title);
    }


    @ApiIgnore
    @RequestMapping({"/pay/openredpack", "/pay/redpack/group/open"})
    @SessionCheck
    @ResponseObjectBody
    public OpenRedpackList openGroupRedpack(@RequestAttribute User fromUser,
                                            @RequestParam String code,
                                            @RequestParam int open) {
        return redpackService.openGroupRedpack(fromUser, code, open);
    }


    /**
     * 获取红包状态(0,未抢完;1,抢完)
     *
     * @param code 红包代码
     * @return Map
     */
    @ApiIgnore
    @RequestMapping({"/pay/getredpackstatus", "/pay/redpack/status"})
    @ResponseObjectBody
    public Map getRedpackStatus(@RequestParam String code) {
        int status = redpackService.getRedpackStatusOfUser(code);
        Map<String, Object> ret = new HashMap<>();
        ret.put("code", code);
        ret.put("status", status);
        return ret;
    }




    /*
    ---------------------------------------------------------------------------
     */


    @ApiOperation(value = "主播发红包", notes = "主播发红包")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "发红包后的用户的资产信息")})
    @RequestMapping(value = {"/service/redpack/room/create"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Asset createRoomRedpackInternal(@ApiParam("主播UID") @RequestParam long uid,
                                           @ApiParam("房间VID") @RequestParam String vid,
                                           @ApiParam("红包易币总数") @RequestParam int ecoin,
                                           @ApiParam("红包数量") @RequestParam int count,
                                           @ApiParam("红包标题") @RequestParam(required = false, defaultValue = "恭喜发财") String title) {
        User user = userService.getById(uid);
        return redpackService.createRoomRedpack(user, vid, ecoin, count, title);
    }

    @ApiOperation(value = "PK红包", notes = "PK结束后触发红包")
    @RequestMapping(value = {"/service/redpack/pk/create"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public void createPKRedpack(@ApiParam("胜方主播UID") @RequestParam long uid,
                                @ApiParam("房间VID") @RequestParam String vid) {

        User user = userService.getById(uid);
        String title = String.format("恭喜%s获胜", user.getNickname());
        redpackService.createPKRedpack(user, vid, title);
    }

    @ApiOperation(value = "直播间开红包", notes = "直播间开红包")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "value:红包易币数量,为0表示没抽中  best: 是否手气最佳, time: 时间")})
    @RequestMapping(value = {"/service/redpack/room/open"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RedpackOpenResult openRoomRedpack(
            @ApiParam("易播号") @RequestParam String name,
            @ApiParam("房间VID") @RequestParam String vid,
            @ApiParam("红包代码") @RequestParam String code) {
        User user = userService.ensureExists(name);
        return new RedpackOpenResult(redpackService.openRedpackInRoom(user, vid, code));
    }


    @ApiOperation(value = "群组红包", notes = "群组红包")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "红包对象信息，需要精简字段")})
    @RequestMapping(value = {"/service/redpack/group/create"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Redpack createGroupRedpackInternal(@ApiParam("用户UID") @RequestParam long uid,
                                              @ApiParam("红包易币总数") @RequestParam int ecoin,
                                              @ApiParam("红包数量") @RequestParam int count,
                                              @ApiParam("红包标题") @RequestParam(required = false, defaultValue = "恭喜发财") String title) {
        User user = userService.getById(uid);
        return redpackService.createGroupRedpack(user, ecoin, count, title);
    }


    @ApiOperation(value = "打开群组红包", notes = "打开群组红包")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "红包信息")})
    @RequestMapping(value = {"/service/redpack/group/open"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public OpenRedpackList openGroupRedpackInternal(@ApiParam("用户UID") @RequestParam long uid,
                                                    @ApiParam("红包代码") @RequestParam String code,
                                                    @ApiParam("是否打开.0为查看，1为打开") @RequestParam int open) {
        User user = userService.getById(uid);
        return redpackService.openGroupRedpack(user, code, open);
    }


    @ApiOperation(value = "获取红包状态", notes = "获取红包状态")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "code,红包代码 status：红包状态 1已经抢完，0还未抢完")})
    @RequestMapping(value = {"/service/redpack/status"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map getRedpackStatusInternal(@ApiParam("红包代码") @RequestParam String code) {
        int status = redpackService.getRedpackStatusOfUser(code);
        return GenericMap.toMap(
                Arrays.asList("code", "status"),
                Arrays.asList(code, status)
        );
    }


    @ApiIgnore
    @RequestMapping({"/service/pay/redpack/refund"})
    @ResponseObjectBody
    public Redpack refundRedpack(@RequestParam String code) {
        return redpackService.refundRedpack(code);
    }
}
