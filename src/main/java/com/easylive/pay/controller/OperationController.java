package com.easylive.pay.controller;

import com.easylive.pay.common.ResponseError;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.ResponseObject;
import com.easylive.pay.dao.PersonGiftRecordMockRepository;
import com.easylive.pay.entity.PersonalGiftRecordMock;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.model.User;
import com.easylive.pay.output.TransferResult;
import com.easylive.pay.service.AssetService;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.service.TransferService;
import com.easylive.pay.service.UserService;
import com.easylive.pay.utils.TransformUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Random;

@ApiIgnore
@RestController
@RequestMapping(value = "/manage")
public class OperationController {

    @Autowired
    private TransferService transferService;

    @Autowired
    private AssetService assetService;

    @Autowired
    private UserService userService;

    @Autowired
    private PersonGiftRecordMockRepository personGiftRecordMockRepository;
    @Autowired
    private RechargeService rechargeService;

    @RequestMapping({"/ecoin/transfer"})
    @ResponseBody
    public TransferResult transferEcoin(@RequestParam String from, @RequestParam String to, @RequestParam int amount,
                                       @RequestParam String description) {
        User fromUser = userService.ensureExists(from);
        User toUser = userService.ensureExists(to);
        return transferService.transfer(fromUser.getId(), toUser.getId(), amount, description);
    }

    /**
     * 将某个用户所有ecoin转移到另一个用户
     * @param from 转出用户
     * @param to 转入用户
     * @param description 描述
     * @return 响应结果
     */
    @RequestMapping({"/ecoin/transfer/empty"})
    @ResponseBody
    public TransferResult transferEcoinAll(@RequestParam String from, @RequestParam String to, @RequestParam String description) {
        User fromUser = userService.ensureExists(from);
        User toUser = userService.ensureExists(to);
        UserCoin coin = assetService.getByUid(fromUser.getId());
        int ecoin = (int)coin.getEcoin();
        return transferService.transfer(fromUser.getId(), toUser.getId(), ecoin ,description);
    }


    /**
     * 充值错误订单的易币转回
     * @param orderId
     * @param to
     * @return
     */
    @RequestMapping({"/ecoin/transfer/recharge"})
    @ResponseBody
    public TransferResult transferEcoinForWrongRecharge(@RequestParam String orderId,
                                                        @RequestParam String to) {
        RechargeRecord rechargeRecord = rechargeService.ensureRecordByOrderId(orderId);

        //已经处理过的订单号不重复处理
        if ( rechargeService.isWrongTransferRecordExist(orderId) ) {
            throw new ResponseException(ResponseError.E_PARAM, "该订单已处理过");
        }

        //  充值订单状态要正确
        if( rechargeRecord.getStatus() != OrderStatus.ORS_SUCCESS.getValue()) {
            throw new ResponseException(ResponseError.E_PARAM, "充值订单状态不正确");
        }

        // 充值订单号不能超过一周
        if( rechargeRecord.getCompleteTime().before(new Date(System.currentTimeMillis() - 7 * 24 * 3600 * 1000)) ) {
            throw new ResponseException(ResponseError.E_PARAM, "充值订单号不能超过一周");
        }
        long fromUid = rechargeRecord.getUid();
        User fromUser = userService.ensureExists(fromUid);
        UserCoin coin = assetService.getByUid(fromUid);
        if (coin.getEcoin() < rechargeRecord.getEcoin()) {
            throw new ResponseException(ResponseError.E_PARAM, "易币不足");
        }

        User toUser = userService.ensureExists(to);
        int ecoin = (int)rechargeRecord.getEcoin();
        String description = String.format("充值错误订单的易币转回:%s",  orderId);
        TransferResult result = transferService.transfer(fromUser.getId(), toUser.getId(), ecoin ,description);
        rechargeService.addWrongTransferRecord(orderId, fromUid, toUser.getId(), ecoin);
        return result;
    }





    @RequestMapping({"/personal/gift/record/mock/list"})
    @ResponseBody
    public List<PersonalGiftRecordMock> listGiftRecordMock(@RequestParam String name){
        final User user = userService.getByName(name);
        return personGiftRecordMockRepository.findAllByToUid(user.getId());
    }

    @RequestMapping({"/personal/gift/record/mock/delete"})
    @ResponseBody
    public ResponseObject deleteGiftRecordMock(@RequestParam String ids){
        TransformUtils.parseLongIdList(ids).stream().forEach(item -> {
            personGiftRecordMockRepository.delete(item);
        });
        return ResponseObject.SUCCESS_INSTANCE;
    }

    @RequestMapping({"/personal/gift/record/mock/save"})
    @ResponseBody
    public ResponseObject saveGiftRecordMock(@RequestParam Long fromUid, @RequestParam Long toUid, @RequestParam String goodsIds, @RequestParam Integer number){
        TransformUtils.parseLongIdList(goodsIds).stream().forEach(item -> {
            PersonalGiftRecordMock personalGiftRecordMock = new PersonalGiftRecordMock(fromUid, toUid, item, 2, number, 0L);
            personGiftRecordMockRepository.save(personalGiftRecordMock);
        });
        return ResponseObject.SUCCESS_INSTANCE;
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping({"/personal/gift/record/mock/generate"})
    @ResponseBody
    public ResponseObject generate(@RequestParam String fromUids, @RequestParam String toUids, @RequestParam String goodsIds){
        List<Long> goodsIdList = TransformUtils.parseLongIdList(goodsIds);
        TransformUtils.parseLongIdList(toUids).stream().forEach(item -> {
            save(fromUids, item, goodsIdList);
        });
        return ResponseObject.SUCCESS_INSTANCE;
    }

    private void save(String fromUids, Long toUid, List<Long> candidateGoodsIdList){
        Collections.shuffle(candidateGoodsIdList);
        Integer goodsCount = new Random().nextInt(10);
        if (goodsCount < 5){
            goodsCount = 5;
        }
        final List<Long> fromUidList = TransformUtils.parseLongIdList(fromUids);
        candidateGoodsIdList.subList(0, goodsCount).stream().forEach(item -> {
            Collections.shuffle(fromUidList);
            PersonalGiftRecordMock personalGiftRecordMock = new PersonalGiftRecordMock(fromUidList.get(0), toUid, item, 2,
                    new Random().nextInt(10) + 3, 0L);
            personGiftRecordMockRepository.save(personalGiftRecordMock);
        });
    }


}