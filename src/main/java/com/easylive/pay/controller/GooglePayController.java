package com.easylive.pay.controller;

import com.easylive.pay.common.RequestAttribute;
import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.common.SessionCheck;
import com.easylive.pay.entity.RechargeOption;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.service.google.GooglePayService;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/")
public class GooglePayController {


    @Autowired
    private GooglePayService googlePayService;

    @RequestMapping(value = {"/pay/recharge/google/option"}, method = RequestMethod.POST)
    @SessionCheck
    @ResponseObjectBody
    public Map optionList(@RequestAttribute User user, @RequestParam(required = false) String app, HttpServletRequest request) {
        UAgentInfo ua = new UAgentInfo(request);
        if(StringUtils.isEmpty(app)) {
            app = ua.getAppName();
        }
        List<RechargeOption> options = googlePayService.getRechargeOption(app);
        return GenericMap.toMap("optionlist", options);
    }

    @ApiOperation("APP创建GooglePay订单")
    @RequestMapping(value = {"/pay/recharge/google/order"}, method = RequestMethod.POST)
    @SessionCheck
    @ResponseObjectBody
    public Map createOrder(@RequestAttribute User user,
                           @ApiParam("支付选项名") @RequestParam String productId,
                           @ApiParam("数量") @RequestParam long amount,
                           @ApiParam("货币") @RequestParam String currency,
                           HttpServletRequest request) {
        UAgentInfo ua = new UAgentInfo(request);
        MobilePlatform mobilePlatform = ua.getDeviceType();
        String clientIp = ClientUtils.getClientIPAddr(request);
        String orderId = googlePayService.createOrder(user.getId(), ua.getAppName(), productId, mobilePlatform, clientIp, amount, currency);
        return GenericMap.toMap("orderId", orderId);
    }

    @ApiOperation("APP提交验证客户端的支付信息")
    @RequestMapping(value = {"/pay/recharge/google/validate"}, method = RequestMethod.POST)
    @SessionCheck
    @ResponseObjectBody
    public void validateOrder(@RequestAttribute User user,
                              @ApiParam("订单号") @RequestParam String orderId,
                              @ApiParam("签名数据") @RequestParam String data,
                              @ApiParam("签名") @RequestParam String signature,
                              @ApiParam("app名称，目前只有liveu马甲包需要填入此值，后期liveu完全分离后此参数废弃") @RequestParam(required = false) String appName,
                              HttpServletRequest request) {
        UAgentInfo ua = new UAgentInfo(request);
        if (StringUtils.isEmpty(appName)){
            appName = ua.getAppName();
        }
        googlePayService.valid(user.getId(), appName, orderId, data, signature);
    }

}

