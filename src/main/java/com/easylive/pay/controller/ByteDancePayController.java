package com.easylive.pay.controller;

import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.model.ByteDanceOrderInfo;
import com.easylive.pay.service.bytedance.ByteDancePayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;



@RestController
@RequestMapping(value = "/")
public class ByteDancePayController {

    @Autowired
    private ByteDancePayService byteDancePayService;

    @RequestMapping(value = {"/service/recharge/bytedance/order"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public ByteDanceOrderInfo createOrder(@RequestParam long uid,
                                          @RequestParam long amount,
                                          @RequestParam String clientIp) {

        return byteDancePayService.createRechargeOrder(uid, amount, clientIp);
    }


}

