package com.easylive.pay.controller;

import com.easylive.pay.common.*;
import com.easylive.pay.entity.*;
import com.easylive.pay.enums.CashoutPlatform;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.model.user.IdentityInfo;
import com.easylive.pay.model.user.UserInfo;
import com.easylive.pay.model.user.UserInfoOption;
import com.easylive.pay.output.*;
import com.easylive.pay.service.*;
import com.easylive.pay.service.asset.WithdrawService;
import com.easylive.pay.service.huifu.HuifuPayService;
import com.easylive.pay.service.huifu.HuifuUser;
import com.easylive.pay.service.wx.WeixinService;
import com.easylive.pay.service.yzh.YunzhanghuService;
import com.easylive.pay.utils.AppStringUtils;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.vo.BindBankCard;
import com.easylive.pay.vo.YzhUserContractVo;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping(value = "/")
public class CashoutController {

    private static final Logger logger = LoggerFactory.getLogger(CashoutController.class);

    @Autowired
    private CashoutService cashoutService;

    @Autowired
    private WeixinService weixinService;

    @Autowired
    private UserService userService;

    @Autowired
    private AssetService assetService;

    @Autowired
    private SydService sydService;

    @Autowired
    private TaxCollectService taxCollectService;

    @Autowired
    private WithdrawService withdrawService;
    @Autowired
    private HuifuPayService huifuPayService;

    @Autowired
    private YunzhanghuService  yunzhanghuService;

    /**
     * 用户提现总金额
     *
     * @return String
     */
    @ApiIgnore
    @RequestMapping("/pay/cashout/payment/notify")
    public String paymentNotify() {
        logger.debug("[paymentNotify] 1. start");
        return "SUCCESS";
    }

    /**
     * 用户提现总金额
     *
     * @param fromUser 用户信息
     * @return Map
     */
    @ApiIgnore
    @RequestMapping({"/pay/cashouttotal", "/pay/cashout/user/total"})
    @ResponseObjectBody
    @SessionCheck
    public Map cashoutTotal(@RequestAttribute User fromUser) {
        long sum = cashoutService.getTotalByUid(fromUser.getId());
        return GenericMap.toMap("total", sum);
    }

    /**
     * 用户提现记录
     *
     * @param fromUser 用户
     * @param start    开始地址
     * @param count    数量
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/pay/cashoutrecord", "/pay/cashout/user/list"})
    @ResponseObjectBody
    @SessionCheck
    public RecordList<CashoutRecordPayload> listUserCashoutRecord(@RequestAttribute User fromUser, int start, int count) {
        return listUserCashoutRecordInternal(fromUser.getId(), start, count);
    }



    /*
     ----------------------------------------------------------------------------------------------------------
    */

    @ApiOperation(value = "提现总额", notes = "提现总额")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "total 提现总额，单位分")})
    @RequestMapping(value = {"/service/cashout/user/total"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map cashoutTotalInternal(@ApiParam("用户UID") @RequestParam long uid) {
        long sum = cashoutService.getTotalByUid(uid);
        return GenericMap.toMap("total", sum);
    }


    @ApiOperation(value = "提现记录", notes = "提现记录")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "提现列表,需要精简返回的字段")})
    @RequestMapping(value = {"/service/cashout/user/list"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RecordList<CashoutRecordPayload> listUserCashoutRecordInternal(
            @ApiParam("用户UID") @RequestParam long uid,
            @ApiParam("记录开始索引") @RequestParam int start,
            @ApiParam("记录数量") @RequestParam int count) {
        List<CashoutRecord> list = cashoutService.listByUid(uid, start, count);
        List<CashoutRecordPayload> cashoutList = new ArrayList<>();
        for (CashoutRecord cashoutRecord : list) {
            cashoutList.add(new CashoutRecordPayload(cashoutRecord));
        }
        return new RecordList<>(start, cashoutList);
    }



















    /*

     以下为运营系统接口------------------------------------
     */

    /**
     * 获取每日的提现记录（运营系统）
     *
     * @param date  日期，格式为yyyy-MM-dd
     * @param start 开始项
     * @param count 总数目
     * @return 返回提现记录
     */
    @ApiIgnore
    @RequestMapping({"/service/cashout/list-by-date"})
    @ResponseObjectBody
    public RecordList<CashoutRecord> listCashoutRecordByDate(String date, int start, int count) {
        logger.info("get date cashout record : date-" + date + " start-" + start + " count-" + count);
        List<CashoutRecord> list = cashoutService.getRecordByDate(date, start, count);
        return new RecordList<>(start, list);
    }

    /**
     * 获取用户的提现记录
     *
     * @param uid
     * @param start
     * @param end
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/service/cashout/list-by-user"})
    @ResponseObjectBody
    public List<CashoutRecord> getUserCashoutRecord(
            @RequestParam long uid,
            @RequestParam String start,
            @RequestParam String end) {
        logger.info("get user " + uid + " RechargeRecord between " + start + " and " + end);
        return cashoutService.getRecordByUser(uid, start, end);
    }


    /**
     * 禁止用户提现
     *
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/service/cashout/enable"})
    @ResponseObjectBody
    public void cashoutEnable(long uid, int enable) {
        cashoutService.enableCashout(uid, enable);
    }

    @ApiIgnore
    @RequestMapping({"/service/cashout/info"})
    @ResponseObjectBody
    public CashoutInfo cashoutStatus(long uid) {
        return cashoutService.getCashoutInfo(uid);
    }

    /**
     * 微信公众号红包提现接口
     *
     * @param uid
     * @param rmb     提现人民币，单位分
     * @param request
     * @return
     */
    @ApiIgnore
    @RequestMapping({"/service/cashout", "/service/cashout/wx/redpack"})
    @ResponseObjectBody
    public BaseAsset cashout(@RequestParam("uid") long uid,
                             @RequestParam(value = "openid") String openid,
                             @RequestParam("rmb") long rmb, HttpServletRequest request) {
        String clientIp = ClientUtils.getClientIPAddr(request);
        return weixinService.cashout(uid, rmb, openid, clientIp, CashoutPlatform.Weixin);
    }


    /**
     * 提现白名单用户调整
     *
     * @return Map
     */
    @ApiIgnore
    @RequestMapping({"/service/cashout/whitelist/set"})
    @ResponseObjectBody
    public Map<String, String> setCashoutWhiteList(@RequestParam long uid,
                                                   @RequestParam(name = "limit_rmb") int limit) {
        cashoutService.setWhiteList(uid, limit);
        return new HashMap<>();
    }

    /**
     * 删除提现白名单
     *
     * @param uid
     */
    @ApiIgnore
    @RequestMapping({"/service/cashout/whitelist/delete"})
    @ResponseObjectBody
    public Map<String, String> deleteCashoutWhiteList(@RequestParam long uid) {
        cashoutService.deleteWhiteList(uid);
        return new HashMap<>();
    }

    /**
     * 获取全部白名单列表
     *
     * @return Map
     */
    @ApiIgnore
    @RequestMapping({"/service/cashout/whitelist/list"})
    @ResponseObjectBody
    public List<CashoutWhiteEntity> listCashoutWhiteList() {
        return cashoutService.listWhiteList();
    }


    @ApiOperation(value = "开通线下结算提现")
    @RequestMapping(value = {"/service/cashout/offline/enable"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public void addToOfflineCashout(@ApiParam("用户UID") @RequestParam long uid,
                                    @ApiParam("开通或者关闭") @RequestParam boolean enable) {
        cashoutService.enableOfflineCashout(uid, enable);
    }

    @ApiOperation(value = "列表线下结算提现的用户")
    @RequestMapping(value = {"/service/cashout/offline/list"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public List<CashoutOfflineInfo> listOfflineCashout(@ApiParam("用户UID列表,逗号分隔.如果不传递，则获取全部") @RequestParam(required = false, defaultValue = "") String uids) {

        List<CashoutOffline> lst;
        if (StringUtils.isEmpty(uids)) {
            lst = cashoutService.listOfflineCashout();
        } else {
            List<Long> listUids = new ArrayList<>();
            for (String s : uids.split(",")) {
                try {
                    listUids.add(Long.parseLong(s));
                } catch (Exception e) {
                    throw new ResponseException(ResponseError.E_PARAM, "Invalid uid list");
                }
            }

            lst = cashoutService.listByUids(listUids);
        }

        Set<Long> uidSet = new HashSet<>();
        lst.forEach(cashoutOffline -> {
            uidSet.add(cashoutOffline.getUid());
        });

        if (uidSet.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> uidList = new ArrayList<>(uidSet);
        Map<Long, UserInfo> userInfoMap = userService.listUserInfoByUids(uidList,
                UserInfoOption.EDIT.name(),
                UserInfoOption.ATTR.name(),
                UserInfoOption.IDENTITY.name());
        Map<Long, CashoutLimitEntity> limits = cashoutService.getCashoutLimitByUids(uidList);
        Map<Long, UserCoin> assets = assetService.getByUids(uidList);
        List<CashoutOfflineInfo> result = new ArrayList<>(lst.size());
        lst.forEach(cashoutOffline -> {
            long uid = cashoutOffline.getUid();

            UserInfo userInfo = userInfoMap.get(uid);
            if (userInfo != null) {
                UserCoin uc = assets.get(uid);

                if (uc == null) {
                    uc = new UserCoin(uid);
                }

                CashoutLimitEntity limit = limits.get(uid);
                Asset asset = new Asset(uc, limit);
                CashoutInfo ci = new CashoutInfo(uc.getRiceroll(), limit, userInfo.getAttr().getAppId());
                CashoutOfflineInfo info = new CashoutOfflineInfo(cashoutOffline, userInfo, asset, ci);
                result.add(info);
            }

        });

        return result;
    }


    @ApiOperation(value = "线下提现")
    @RequestMapping(value = {"/service/cashout/offline/direct"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public String offlineDirectCashout(@ApiParam("用户易播号") @RequestParam String name,
                                       @ApiParam("用户姓名") @RequestParam String realName,
                                       @ApiParam("用户手机号") @RequestParam String phone,
                                       @ApiParam("用户身份证号") @RequestParam String idCard,
                                       @ApiParam("用户银行卡号") @RequestParam String bankCard,
                                       @ApiParam("提现金额(单位:分)") @RequestParam long amount) {
        logger.info("线下提现请求 name:{},amount:{},realName:{},phone:{},idCard:{},bankCard:{}", name, amount, realName, phone, idCard, bankCard);
        return cashoutService.offlineDirectCashout(name, amount, realName, phone, idCard, bankCard);
    }

    @ApiOperation(value = "线下公会提现结算")
    @RequestMapping(value = {"/service/cashout/guild/commit"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public String offlineGuildDirectCashout(@ApiParam("用户姓名") @RequestParam String realName,
                                            @ApiParam("用户手机号") @RequestParam String phone,
                                            @ApiParam("用户身份证号") @RequestParam String idCard,
                                            @ApiParam("用户银行卡号") @RequestParam String bankCard,
                                            @ApiParam("提现金额(单位:分)") @RequestParam long amount) {
        logger.info("公会结算提现请求 amount:{},realName:{},phone:{},idCard:{},bankCard:{}", amount, realName, phone, idCard, bankCard);
        return cashoutService.offlineGuildDirectCashout(amount, realName, phone, idCard, bankCard);
    }

    @ApiOperation(value = "添加提现公会用户")
    @RequestMapping(value = {"/service/cashout/guild/user/add"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public long addCashoutGuildUser(@ApiParam("用户姓名") @RequestParam String name,
                                       @ApiParam("用户身份证号") @RequestParam String idCard,
                                       @ApiParam("公会名称") @RequestParam String guildName,
                                       @ApiParam("描述") @RequestParam String description) {
        return cashoutService.addCashoutGuildUser(name, idCard, guildName, description);
    }

    @ApiOperation(value = "")
    @RequestMapping(value = {"/service/cashout/huifu/user/create"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public HuifuUser createHuifuUser(@ApiParam("用户易播号") @RequestParam String name,
                                     @ApiParam("用户姓名") @RequestParam String realName,
                                     @ApiParam("用户手机号") @RequestParam String phone,
                                     @ApiParam("用户身份证号") @RequestParam String idCard,
                                     @ApiParam("用户银行卡号") @RequestParam String bankCard) {
        long uid = userService.ensureExists(name).getId();
        return huifuPayService.getUser(uid, phone, realName, idCard, bankCard);
    }

    @ApiOperation(value = "线下结算提现")
    @RequestMapping(value = {"/service/cashout/offline/operate"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public Map offlineCashout(@ApiParam("用户UID") @RequestParam long uid,
                              @ApiParam("提现金额(单位:分)") @RequestParam long amount) {
        logger.info("线下提现结算请求 uid:{},amount:{}", uid, amount);
        Pair<String, Long> pair = cashoutService.doOfflineCashout(uid, amount);
        return GenericMap.toMap(Arrays.asList("orderId", "riceroll"),
                Arrays.asList(pair.getLeft(), pair.getRight()));
    }

    @ApiOperation(value = "设置提现限额")
    @RequestMapping(value = {"/service/cashout/offline/limit"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public void setCashoutLimit(@ApiParam("用户UID") @RequestParam long uid,
                                @ApiParam("提现额度(单位:分)") @RequestParam long limit) {
        cashoutService.setLimitCashout(uid, limit);
    }

    //================================APP提现接口开始=========================================

    /**
     * 测试提现接口
     */
 /*   @RequestMapping(value = {"/service/cashout/withdrawTest"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public boolean withdrawTest(@ApiParam("用户UID") @RequestParam long uid,
                                @ApiParam("提现金额(单位：分)") @RequestParam long amount) {
        return cashoutService.withdrawTest(uid, amount);
    }
  */


    /**
     * 同步新政银企签约
     */
    @RequestMapping(value = {"/service/cashout/xzyq/sign"}, method = {RequestMethod.GET})
    @ResponseObjectBody
    public boolean xzyqSyncSign(@ApiParam("操作类型 1-新增 2-修改 3-删除") @RequestParam(required = false, defaultValue = "1") int type,
                                @ApiParam("开始") @RequestParam int start,
                                @ApiParam("结束") @RequestParam int count) {
        return sydService.xzycSign(type, start, count);
    }

    /**
     * 同步之前的签约
     */
    @RequestMapping(value = {"/service/cashout/syncSign"}, method = {RequestMethod.GET})
    @ResponseObjectBody
    public boolean sydSyncSign() {
        return sydService.sydSyncSign();
    }

    /**
     * 同步最近有提现的到汇付
     */
    @RequestMapping(value = {"/service/cashout/huifu/sync/single"}, method = {RequestMethod.GET})
    @ResponseObjectBody
    public HuifuUser huifuSyncSignSingle(@RequestParam long uid) {
        UserBankCard bankCard = withdrawService.findUserBankCard(uid);
        String cardId;
        if (bankCard == null) {
            CashoutOfflineInfoEntity infoEntity = cashoutService.getCashoutOfflineInfoByUid(uid);
            if (infoEntity == null) {
                throw new ResponseException(ResponseError.E_PARAM, "用户未绑定银行卡");
            } else {
                cardId = infoEntity.getBankCardNo();
            }
        } else {
            cardId = bankCard.getAccountNo();
        }

        IdentityInfo identityInfo = userService.getIdentityByUid(uid);
        if (identityInfo == null) {
            throw new ResponseException(ResponseError.E_PARAM, "用户未实名认证");
        }
        return huifuPayService.getUser(uid, identityInfo.getPhone(), identityInfo.getRealName(), identityInfo.getIdCard(), cardId);
    }

    @RequestMapping(value = {"/service/cashout/huifu/sync/by-date"}, method = {RequestMethod.GET})
    @ResponseObjectBody
    public void huifuSyncSign(@RequestParam int days) {

        Date start = new DateTime().minusDays(days).toDate();
        Date end = new Date();
        List<UserBankCard> users = cashoutService.getAppCashoutUser(start, end);
        logger.info("{} users fetched", users.size());
        Map<Long, UserBankCard> userMap = new HashMap<>();
        users.forEach(user -> userMap.put(user.getUid(), user));
        List<Long> uids = new ArrayList<>(userMap.keySet());
        List<UserInfo> userInfos = userService.multiGetByUids(AppStringUtils.toIdString(uids), Set.of(UserInfoOption.IDENTITY));

        for (UserInfo info : userInfos) {
            long uid = info.getUid();
            IdentityInfo identityInfo = info.getIdentity();
            if (identityInfo == null) {
                continue;
            }
            UserBankCard card = userMap.get(uid);
            if (card == null) {
                continue;
            }
            huifuPayService.getUser(uid, identityInfo.getPhone(), identityInfo.getRealName(), identityInfo.getIdCard(), card.getAccountNo());
        }
    }


    /**
     * 同步之前的签约
     */
    @RequestMapping(value = {"/service/cashout/syncTax"}, method = {RequestMethod.GET})
    @ResponseObjectBody
    public boolean sydTaxSign(@ApiParam("开始") @RequestParam int start,
                              @ApiParam("结束") @RequestParam int count) {
        return taxCollectService.syncTaxSign(start, count);
    }

    /**
     * APP提现
     */
    @ApiOperation(value = "APP提现", notes = "APP提现")
    @RequestMapping(value = {"/service/cashout/withdraw"}, method = {RequestMethod.POST})
    @ResponseObjectBody
    public Asset withdraw(@ApiParam("用户UID") @RequestParam long uid,
                          @ApiParam("提款渠道") @RequestParam(required = false, defaultValue = "1") int fkType,
                          @ApiParam("银行卡ID") @RequestParam int cardId,
                          @ApiParam("提现金额(单位：分)") @RequestParam long amount,
                          @ApiParam("身份证号") @RequestParam String idNo,
                          @ApiParam("手机号码") @RequestParam(required = false) String mobile,
                          @ApiParam("APP ID") @RequestParam(required = false, defaultValue = "1") int appId,
                          HttpServletRequest request) {
        String clientIp = ClientUtils.getClientIPAddr(request);
        logger.info("APP提现请求: uid={}, fkType={}, cardId={}, amount={}, idNo={}, mobile={}, appId={}, clientIp={}", uid, fkType, cardId, amount, idNo, mobile, appId, clientIp);
        Asset asset = cashoutService.withdraw(uid, fkType, cardId, amount, clientIp, idNo, mobile, appId);
        if (asset == null) {
            throw new ResponseException(ResponseError.E_CASHOUT_BANK_CARD_BANK_SERVICE, "提现服务异常，等待确认提现结果");
        }
        return asset;
    }

    /**
     * 用户提现记录
     */
    @ApiIgnore
    @RequestMapping(value = {"/service/cashout/listRecord"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public RecordList<CashoutRecord> listRecord(@RequestParam long uid,
                                                @RequestParam(required = false, defaultValue = "0") int page,
                                                @RequestParam(required = false, defaultValue = "20") int size,
                                                @RequestParam(required = false, defaultValue = "0") int type) {
        List<CashoutRecord> list;
        if (type == 1) {
            list = cashoutService.listAllRecordByUid(uid, page, size);
        } else {
            list = cashoutService.listAppRecordByUid(uid, page, size);
        }
        return new RecordList<>(page, list);
    }

    /**
     * 用户提现记录详情
     */
    @ApiIgnore
    @RequestMapping(value = {"/service/cashout/getRecord"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public CashoutRecord getRecord(@RequestParam long uid, @RequestParam long id) {
        CashoutRecord record = cashoutService.getAppRecordById(uid, id);
        //如果是APP提现并且在1天内查询提现记录、异步从银行校准提现结果
        cashoutService.checkSuccessCashout(record);
        return record;
    }

    /**
     * 客户信息签约接口
     */
    @ApiIgnore
    @RequestMapping(value = {"/service/cashout/customer/sign"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public ResponseObject customerInfoSign(@ApiParam("用户uid") @RequestParam long uid,
                                           @ApiParam("操作类型") @RequestParam int operType,
                                           @ApiParam("证件号码") @RequestParam String idNo,

                                           @ApiParam("客户银行账号") @RequestParam(required = false) String accountNo,
                                           @ApiParam("银行账号户名") @RequestParam(required = false) String accountName,
                                           @ApiParam("收款银行行号") @RequestParam(required = false) String bankNo,
                                           @ApiParam("收款银行行名") @RequestParam(required = false) String bankName,

                                           @ApiParam("手机号码") @RequestParam(required = false) String cellPhone,

                                           @ApiParam("支付宝账号") @RequestParam(required = false) String zfbAccount,

                                           @ApiParam("身份证正面") @RequestParam(required = false) String front,
                                           @ApiParam("身份证反面") @RequestParam(required = false) String back,
                                           @ApiParam("平台标识ID") @RequestParam(required = false) String platformId

    ) {
        Object result = cashoutService.customerSign(uid, operType, accountNo, accountName, bankNo, bankName, idNo, cellPhone, zfbAccount, front, back, platformId);
        return new ResponseObject(result);
    }

    /**
     * 是否线下提现用户
     */
    @ApiIgnore
    @RequestMapping(value = {"/service/cashout/isOfflineUser"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map<String, Boolean> isOfflineUser(@ApiParam("用户uid") @RequestParam(required = true) long uid) {
        boolean result = cashoutService.isOfflineCashoutUser(uid);
        return new HashMap<String, Boolean>(1) {{
            put("result", result);
        }};
    }

    /**
     * 根据uid返回用户提现信息
     */
    @ApiOperation(value = "资产信息", notes = "获取用户资产信息")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "用户资产信息")})
    @RequestMapping(value = {"/service/cashout/appAsset"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Asset getAppAssetByUid(@ApiParam("用户UID") @RequestParam long uid,
                                  @ApiParam("appId") @RequestParam(required = false, defaultValue = "1") int appId) {
        UserCoin uc = assetService.getByUid(uid);
        CashoutLimitEntity cashoutLimit = cashoutService.getCashoutLimit(uid);
        return new Asset(uc, cashoutLimit, appId);
    }

    /**
     * 根据cardId返回用户提现信息
     */
    @ApiOperation(value = "根据cardId返回用户提现信息", notes = "根据cardId返回用户提现信息")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "根据cardId返回用户提现信息")})
    @RequestMapping(value = {"/service/cashout/assetLimit"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public CashoutIdentityLimitBO getAppAssetLimitByCardId(@ApiParam("身份证号") @RequestParam String idCard) {
        CashoutIdentityLimitEntity identityLimit = cashoutService.getOrCreateCashoutIdentityLimit(idCard);
        return new CashoutIdentityLimitBO(identityLimit);
    }

    /**
     * 绑定银行卡
     */
    @ApiOperation(value = "绑定银行卡", notes = "绑定银行卡")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "绑定银行卡")})
    @RequestMapping(value = {"/service/cashout/bankCard/bind"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    @Deprecated
    public UserBankCard bindBankCard(@ApiParam("银行卡信息") BindBankCard card) {
        return withdrawService.bindBankCard(card);
    }

    @ApiOperation(value = "解绑银行卡", notes = "解绑银行卡")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "绑定银行卡")})
    @RequestMapping(value = {"/service/cashout/bankCard/unbind"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public HashMap<String, Boolean> unbindBankCard(
            @ApiParam("uid") long uid,
            @ApiParam("解绑类型") int unBindType
    ) {
        boolean result = withdrawService.unbindBankCard(uid, unBindType);
        return new HashMap<String, Boolean>(16) {{
            put("result", result);
        }};
    }

    /**
     * 查询银行卡
     */
    @ApiOperation(value = "查询银行卡", notes = "查询银行卡")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "查询银行卡")})
    @RequestMapping(value = {"/service/cashout/bankCard/find"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public UserBankCard findBankCard(@ApiParam("用户UID") @RequestParam long uid) {
        UserBankCard ret = withdrawService.findUserBankCard(uid);
        if (ret == null) {
            throw new ResponseException(ResponseError.E_USER_BANK_CARD_NOT_EXISTS, "你还没有绑定银行卡");
        }
        return ret;
    }


    /**
     * 云账户获取合同信息接口
     */
    @ApiOperation(value = "云账户获取合同信息", notes = "云账户获取合同信息")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "云账户获取合同信息")})
    @RequestMapping(value = {"/service/cashout/yunzhanghu/contract"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public YzhUserContractVo getYunzhanghuContract() {
        return yunzhanghuService.getUserContract();
    }

    //================================APP提现接口结束=========================================
}
