package com.easylive.pay.controller;

import com.easylive.pay.common.RequestAttribute;
import com.easylive.pay.common.ResponseException;
import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.common.SessionCheck;
import com.easylive.pay.entity.RechargeOption;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.service.oppo.OppoOrder;
import com.easylive.pay.service.oppo.OppoNotifyRequest;
import com.easylive.pay.service.oppo.OppoPayService;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.utils.HttpUtils;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/")
public class OppoPayController {

    private static Logger logger = LoggerFactory.getLogger(OppoPayController.class);

    @Autowired
    private OppoPayService oppoPayService;


    @ApiOperation("APP创建OPPO订单")
    @RequestMapping(value = {"/pay/recharge/oppo/option"}, method = RequestMethod.POST)
    @SessionCheck
    @ResponseObjectBody
    public Map optionList(@RequestAttribute User user) {
        List<RechargeOption> optionList = oppoPayService.getRechargeOption();
        return GenericMap.toMap("optionlist", optionList);
    }

    @ApiOperation("APP创建OPPO订单")
    @RequestMapping(value = {"/pay/recharge/oppo/order"}, method = RequestMethod.POST)
    @SessionCheck
    @ResponseObjectBody
    public OppoOrder createOrder(@RequestAttribute User user,
                           @ApiParam("订单金额") @RequestParam long amount,
                           @ApiParam("OPPO SSOID") @RequestParam String ssoId,
                           @ApiParam("OPPO Token") @RequestParam String token,
                           HttpServletRequest request) {
        UAgentInfo ua = new UAgentInfo(request);
        MobilePlatform mobilePlatform = ua.getDeviceType();
        String clientIp = ClientUtils.getClientIPAddr(request);
        return oppoPayService.createOrder(user.getId(), ua.getAppName(), amount, ssoId, token, mobilePlatform, clientIp);
    }

    @ApiIgnore
    @RequestMapping(value = {"/pay/recharge/oppo/notify"}, method = RequestMethod.POST)
    public String notify(HttpServletRequest request) {
        String status = "FAIL";
        String resultMsg = "fail";
        try {
            OppoNotifyRequest data = new OppoNotifyRequest();
            data.setNotifyId(request.getParameter("notifyId"));
            data.setPartnerOrder(request.getParameter("partnerOrder"));
            data.setProductName(request.getParameter("productName"));
            data.setProductDesc(request.getParameter("productDesc"));
            data.setPrice(Integer.parseInt(request.getParameter("price")));
            data.setCount(Integer.parseInt(request.getParameter("count")));
            data.setAttach(request.getParameter("attach"));
            data.setSign((request.getParameter("sign")));
            oppoPayService.validOrder(data);
            status = "OK";
            resultMsg = "成功";
        } catch (ResponseException e) {
            resultMsg = e.getMessage();
        } catch (Exception e) {
            logger.error("Oppo pay recharge error:{}", e.getMessage());
        }

        Map<String, String> ret = new HashMap<>();
        ret.put("result", status);
        ret.put("resultMsg", resultMsg);

        String result = HttpUtils.toParamString(ret);
        logger.info("oppo recharge notify result:{}", result);

        return result;
    }

}

