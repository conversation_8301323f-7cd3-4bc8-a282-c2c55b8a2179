package com.easylive.pay.controller;

import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.entity.AdminTransferRecord;
import com.easylive.pay.entity.AssetBiz;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.enums.bank.BankType;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.output.TransferResult;
import com.easylive.pay.output.common.PageableList;
import com.easylive.pay.service.*;
import com.easylive.pay.service.alipay.AliPayService;
import com.easylive.pay.service.paypal.PaypalService;
import com.easylive.pay.service.wx.WeixinService;
import com.easylive.pay.service.yzh.YunzhanghuService;
import com.easylive.pay.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@ApiIgnore
@RestController
@RequestMapping(value = "/")
public class AdminController {

    @Autowired
    private ConfigService configService;

    @Autowired
    private SpecialUserService specialUserService;

    @Autowired
    private AssetService assetService;

    @Autowired
    private WeixinService weixinService;

    @Autowired
    private AliPayService aliPayService;

    @Autowired
    private PaypalService paypalService;

    @Autowired
    private YunzhanghuService yunzhanghuService;

    @Autowired
    private AppService appService;

    @Autowired
    private Executor taskExecutor;

    @Autowired
    private CashoutService cashoutService;

    @Autowired
    private TransferService transferService;

    @RequestMapping({"/admin/asset/special"})
    @ResponseBody
    public Map showAssetSpecialConfig() {
        return GenericMap.toMap(Arrays.asList("channels", "users"),
                Arrays.asList(specialUserService.getSpecialChannels(), specialUserService.getSpecialUsers()));
    }

    @RequestMapping({"/admin/config/show"})
    @ResponseBody
    public Map showConfig() {
        return configService.getAll();
    }

    @RequestMapping({"/admin/config/reload"})
    @ResponseBody
    public void reloadConfig() {
        configService.reload();
    }

    @RequestMapping({"/admin/asset/biz/list"})
    @ResponseBody
    public List<AssetBiz> assetBizObject() {
        return assetService.getAll();
    }


    /**
     * 返回用户资产原始信息， 即t_user_coin的信息ecoin
     *
     * @param uid
     * @return Ecoin
     */
    @RequestMapping({"/admin/asset/rawquery"})
    @ResponseObjectBody
    public UserCoin assetRawQuery(@RequestParam long uid) {
        return assetService.getByUid(uid);
    }


    @RequestMapping({"/admin/recharge/wx/reload"})
    @ResponseBody
    public void reloadWeixinApp() {
        weixinService.load();
    }


    @RequestMapping({"/admin/recharge/ali/reload"})
    @ResponseBody
    public void reloadAliApp() {
        aliPayService.load();
    }


    @RequestMapping({"/admin/recharge/paypal/reload"})
    @ResponseBody
    public void reloadPaypalApp() {
        paypalService.load();
    }


    @RequestMapping({"/admin/recharge/yzh/reload"})
    @ResponseBody
    public void reloadYzhApp() {
        yunzhanghuService.reload();
    }

    @RequestMapping({"/admin/recharge/yzh/queryOrder"})
    @ResponseBody
    public YunzhanghuService.OrderData yzhQueryOrder(@RequestParam String orderId) {
        return yunzhanghuService.buildQueryOrderData(orderId);
    }



    @RequestMapping({"/admin/app/list"})
    @ResponseBody
    public List listApps() {
        return appService.getAllApps();
    }


    @RequestMapping(value = {"/admin/system/threadPool/get"}, method = {RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> getThreadPoolState() {
        ThreadPoolExecutor executor = ((ThreadPoolTaskExecutor) taskExecutor).getThreadPoolExecutor();

        Map<String, Object> h = new LinkedHashMap<>();
        h.put("corePoolSize", executor.getCorePoolSize());
        h.put("poolSize", executor.getPoolSize());
        h.put("activeCount", executor.getActiveCount());
        h.put("largestPoolSize", executor.getLargestPoolSize());
        h.put("maximumPoolSize", executor.getMaximumPoolSize());
        h.put("taskCount", executor.getTaskCount());
        h.put("completedTaskCount", executor.getCompletedTaskCount());
        h.put("queueSize", executor.getQueue().size());
        h.put("queueRemainingCapacity", executor.getQueue().remainingCapacity());
        return h;
    }

    @RequestMapping(value = {"/admin/system/threadPool/set"}, method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> setThreadPoolState(@RequestParam(required = false) Integer corePoolSize,
                                                  @RequestParam(required = false) Integer maxPoolSize) {
        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) taskExecutor;
        if( corePoolSize != null ) {
            executor.setCorePoolSize(corePoolSize);
        }

        if( maxPoolSize != null )
            executor.setMaxPoolSize(maxPoolSize);

        return getThreadPoolState();
    }

    /**
     * 重新检查订单，注意：必须是app_status=3，如果app_status=4会导致二次退款
     */
    @RequestMapping(value = {"/service/admin/cashout/recheck"}, method = {RequestMethod.GET, RequestMethod.POST})
    public String recheckCashout(@RequestParam String ids) {
        cashoutService.updateCashoutQueueById(ids);
        return "success";
    }

    @RequestMapping(value = {"/service/admin/batch/cashout"}, method = {RequestMethod.GET, RequestMethod.POST})
    public String batchCheckRequestingOrder(@RequestParam Integer type) {
        cashoutService.batchCheckRequestingOrder(BankType.fromValue(type), DateUtils.getBeforeDateForDay(1));
        return "success";
    }

    /**
     * 运营转账-从错误充值的账户转到实际账户
     */
    @RequestMapping(value = {"/service/ecoin/transfer"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public TransferResult transferEcoin(@RequestParam String from,
                                        @RequestParam String to,
                                        @RequestParam String reason,
                                        @RequestParam String operateUser,
                                        @RequestParam String orderId
    ) {

        return transferService.adminTransfer(from, to, reason, operateUser, orderId);
    }

    @RequestMapping(value = {"/service/ecoin/transfer/list"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public PageableList<AdminTransferRecord> transferList(@RequestParam(required = false, defaultValue = "") String from,
                                                          @RequestParam(required = false, defaultValue = "") String to,
                                                          @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date startTime,
                                                          @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date endTime,
                                                          @RequestParam(required = false, defaultValue = "0") int start,
                                                          @RequestParam(required = false, defaultValue = "20") int size
    ) {
        return transferService.listAdminTransferRecord(from, to, startTime, endTime, start, size);
    }
}