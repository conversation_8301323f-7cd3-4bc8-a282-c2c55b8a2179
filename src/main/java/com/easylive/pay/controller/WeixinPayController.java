package com.easylive.pay.controller;

import com.easylive.pay.common.Constants;
import com.easylive.pay.common.RequestAttribute;
import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.common.SessionCheck;
import com.easylive.pay.entity.AppEntity;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.enums.OrderStatus;
import com.easylive.pay.model.User;
import com.easylive.pay.service.AppService;
import com.easylive.pay.service.RechargeService;
import com.easylive.pay.service.wx.WeixinPrepareOrder;
import com.easylive.pay.service.wx.WeixinService;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;


@RestController
@RequestMapping(value = "/")
public class WeixinPayController {

    private static final Logger logger = LoggerFactory.getLogger(WeixinPayController.class);
    @Autowired
    private WeixinService weixinService;

    @Autowired
    private AppService appService;

    @Autowired
    private RechargeService rechargeService;

    /**
     * 创建App上的微信充值订单
     */
    @ApiIgnore
    @RequestMapping({"/pay/unifiedorder", "/pay/recharge/wx/app/order"})
    @ResponseObjectBody
    @SessionCheck
    public Map createWeixinAppOrder(@RequestAttribute User fromUser,
                                    @RequestParam long amount,
                                    @RequestParam(value = "appid", required = false) String appId,
                                    HttpServletRequest request) {
        long uid = fromUser.getId();
        String clientIp = ClientUtils.getClientIPAddr(request);
        UAgentInfo uai = new UAgentInfo(request);
        MobilePlatform platform = uai.getDeviceType();
        String appName = uai.getAppName();

        logger.info("Create Weixin Pay App order {}, {}, {}, {}", uid, amount, appId, appName);
        AppEntity appEntity = appService.getAppByNameWithDefaultFallback(appName);

        return weixinService.createAppOrder(uid, amount, clientIp, platform, (int) appEntity.getId());
    }


    /**
     * 微信支付订单回调通知
     */
    @ApiIgnore
    @RequestMapping({"/pay/validorder", "/pay/recharge/wx/notify"})
    public String weixinOrderNotify(HttpServletRequest request) throws IOException {
        String xmlBody = IOUtils.toString(request.getInputStream());
        return weixinService.valid(xmlBody);
    }

    @ApiOperation(value = "微信APP充值订单创建", notes = "创建微信APP充值订单")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "微信支付准备数据")})
    @RequestMapping(value = {"/service/recharge/wx/app/order/prepare"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public WeixinPrepareOrder prepareWeixinAppOrder(@ApiParam("用户UID") @RequestParam long uid,
                                     @ApiParam("充值金额,单位分") @RequestParam long amount,
                                     @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
                                     @ApiParam("客户端UA") @RequestParam(value = "client_ua", required = false) String clientUserAgent,
                                     @ApiParam("中台APPID") @RequestParam(value = "appId", required = false) Integer mgsAppId) {
        logger.info("[Recharge][WX][APP][Prepare]: {}, {}, {}, {}, {} ", uid, mgsAppId, amount, clientIp, clientUserAgent);
        UAgentInfo uai = new UAgentInfo(clientUserAgent);
        MobilePlatform platform = uai.getDeviceType();
        mgsAppId = appService.decideAppIdByIdAndName(mgsAppId, uai.getAppName());
        logger.info("[Recharge][WX][APP][Prepare]: app {}, {} client:{}", mgsAppId, uai.getAppName(), platform);

        return weixinService.prepareAppOrder(uid, amount, clientIp, platform, mgsAppId);
    }

    @ApiIgnore
    @RequestMapping(value = {"/service/recharge/wx/h5/order/prepare"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public WeixinPrepareOrder prepareWeixinH5Order(
            @ApiParam("用户UID") @RequestParam long uid,
            @ApiParam("充值金额,单位分") @RequestParam long amount,
            @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
            @ApiParam("客户端UA") @RequestParam(value = "client_ua", required = false) String clientUserAgent,
            @ApiParam("中台APPID") @RequestParam(value = "appId", required = false) Integer mgsAppId) {
        logger.info("[Recharge][WX][H5][Prepare]: {}, {}, {}, {} ", uid, amount, clientIp, clientUserAgent);
        UAgentInfo uai = new UAgentInfo(clientUserAgent);
        mgsAppId = appService.decideAppIdByIdAndName(mgsAppId, uai.getAppName());
        MobilePlatform platform = uai.getDeviceType();
        logger.info("[Recharge][WX][H5]: app {}, {}, platform {}", mgsAppId, uai.getAppName(),  platform);
        return weixinService.prepareH5Order(uid, amount, clientIp, platform, mgsAppId);
    }

    @ApiIgnore
    @RequestMapping(value = {"/service/recharge/wx/mini/order/request"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map<String, String> weixinOrderRequestApplet(
            @ApiParam("订单号") @RequestParam(value = "orderId") String orderId,
            @ApiParam("小程序登录code") @RequestParam(value = "code") String code) {
        logger.info("[Recharge][WX][Order][Request]: {}, {} ", orderId, code);
        return weixinService.requestAppletOrder(orderId, code);
    }


    @ApiIgnore
    @RequestMapping(value = {"/service/recharge/wx/h5/order/request"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map<String, String> weixinOrderRequestH5(
            @ApiParam("订单号") @RequestParam(value = "orderId") String orderId) {
        logger.info("[Recharge][WX][H5][Order][Request]: {}", orderId);
        return weixinService.requestH5Order(orderId);
    }

    @ApiIgnore
    @RequestMapping(value = {"/service/recharge/wx/app/order/request"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map<String, String> weixinOrderRequestApp(
            @ApiParam("订单号") @RequestParam(value = "orderId") String orderId) {
        logger.info("[Recharge][WX][APP][Order][Request]: {}", orderId);
        return weixinService.requestAppOrder(orderId);
    }



    /**
     * TODO: 后续验证用户是否属于APP ID对应的中台
     */
    @ApiOperation(value = "微信APP充值", notes = "创建微信APP充值订单")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "微信统一下单后的数据，客户端一般直接使用")})
    @RequestMapping(value = {"/service/recharge/wx/app/order"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map createWeixinAppOrderInternal(@ApiParam("用户UID") @RequestParam long uid,
                                            @ApiParam("充值金额,单位分") @RequestParam long amount,
                                            @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
                                            @ApiParam("中台APPID") @RequestParam(value = "appId", required = false, defaultValue = "1") int mgsAppId,
                                            HttpServletRequest request) {
        UAgentInfo uai = new UAgentInfo(request);
        MobilePlatform platform = uai.getDeviceType();
        return weixinService.createAppOrder(uid, amount, clientIp, platform, mgsAppId);
    }


    /**
     * 创建H5微信充值订单, Web端使用
     * TODO: 后续迁到后面的接口
     */
    @ApiIgnore
    @RequestMapping(value = {"/service/wx/h5/order"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    @SessionCheck
    public Map createWeixinH5Order(@RequestAttribute User fromUser,
                                   @RequestParam long amount,
                                   @RequestParam(value = "client_ip") String clientIp,
                                   @RequestParam(value = "client_ua", required = false) String clientUserAgent,
                                   @RequestParam(value = "appId", required = false) Integer mgsAppId) {
        long uid = fromUser.getId();
        return this.createWeixinH5OrderNew(uid, amount, clientIp, clientUserAgent, mgsAppId);
    }


    @ApiOperation(value = "微信H5充值", notes = "创建微信H5充值订单")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "微信统一下单后的数据，客户端一般直接使用")})
    @RequestMapping(value = {"/service/recharge/wx/h5/order"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map createWeixinH5OrderNew(@ApiParam("用户UID") @RequestParam long uid,
                                      @ApiParam("充值金额,单位分") @RequestParam long amount,
                                      @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
                                      @ApiParam("客户端UA") @RequestParam(value = "client_ua", required = false) String clientUserAgent,
                                      @ApiParam("中台APPID") @RequestParam(value = "appId", required = false) Integer mgsAppId) {
        logger.info("[Recharge][WX][H5]: {}, {}, {}, {} ", uid, amount, clientIp, clientUserAgent);

        UAgentInfo uai = new UAgentInfo(clientUserAgent);
        if (mgsAppId == null) {
            AppEntity app = appService.getAppByName(uai.getAppName());
            if (app != null)
                mgsAppId = ((Long) app.getId()).intValue();
        }

        if (mgsAppId == null) {
            logger.warn("[Recharge][WX][H5]: invalid app name found: {}, use default app id", uai.getAppName());
            mgsAppId = Constants.DEFAULT_APP_ID;
        }

        logger.info("[Recharge][WX][H5]: app {}", mgsAppId);
        return weixinService.createH5Order(uid, amount, clientIp, mgsAppId);
    }


    @ApiOperation(value = "微信扫码充值", notes = "创建微信扫码充值订单")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "微信统一下单后的数据，客户端一般直接使用")})
    @RequestMapping(value = {"/service/recharge/wx/code/order"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map wxCodePay(@ApiParam("用户UID") @RequestParam("uid") long uid,
                         @ApiParam("充值金额,单位分") @RequestParam("amount") long amount,
                         @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
                         @ApiParam("中台APPID") @RequestParam(value = "appId", required = false, defaultValue = "1") int appId,
                         HttpServletRequest request) {
        return weixinService.createCodePayOrder(uid, amount, clientIp, appId);
    }

    /**
     * 小程序创建微信订单的接口
     *
     * @param uid      充值的用户id
     * @param amount   充值的金额，单位分
     * @param clientIp 充值用户的ip地址
     * @return Map
     */
    @ApiIgnore
    @RequestMapping(value = {"/service/recharge/wx/miniapp/order"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map<String, String> createWeixinMiniAppOrder(
            @RequestParam long uid,
            @RequestParam long amount,
            @RequestParam(value = "client_ip") String clientIp,
            @RequestParam(value = "open_id", required = false, defaultValue = "") String openId,
            @RequestParam(value = "mobileplatform") String mobilePlatform,
            @RequestParam(value = "appId", required = false, defaultValue = "1") int appId
    ) {
        MobilePlatform platform = weixinService.getPlatform(mobilePlatform);
        return weixinService.createMiniAppOrder(uid, amount, clientIp, platform, openId, appId);
    }


    @ApiOperation(value = "微信公众号充值", notes = "创建微信公众号充值订单")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "微信统一下单后的数据，客户端一般直接使用")})
    @RequestMapping(value = {"/service/recharge/wx/web/order", "/service/recharge/wx/mp/order"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Map createWeiXinMPOrder(
            @ApiParam("用户UID") @RequestParam long uid,
            @ApiParam("充值金额,单位分") @RequestParam long amount,
            @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
            @ApiParam("用户在公众号的open id") @RequestParam(value = "open_id") String openId,
            @ApiParam("客户端平台, ios/android") @RequestParam(value = "mobileplatform") String mobilePlatform,
            @ApiParam("中台APPID") @RequestParam(value = "appId", required = false, defaultValue = "1") int mgsAppId
    ) {
        MobilePlatform platform = weixinService.getPlatform(mobilePlatform);
        return weixinService.createMPOrder(uid, amount, clientIp, openId, platform, mgsAppId);
    }

    @ApiIgnore
    @RequestMapping({"/service/recharge/wx/order/status"})
    public String getOrderStatusByByPlatformOrderId(@RequestParam(value = "orderId") String orderId) {
        try {
            RechargeRecord record = rechargeService.getRecordByOrderId(orderId);
            if (record.getStatus() == OrderStatus.ORS_SUCCESS.getValue()) {
                return "OK";
            }
            return "FAIL";
        } catch (Exception ignored) {
            return "NA";
        }
    }

    public static void main(String[] args) {
        UAgentInfo ua = new UAgentInfo("funtime 6.18.0 rv:231221000 funtime Develop (iPhone 14ProMax; iOS 17.3.1; zh_CN)");
        System.out.println(ua.getAppName());
    }
}

