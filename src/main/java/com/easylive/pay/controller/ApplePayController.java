package com.easylive.pay.controller;

import com.easylive.pay.common.*;
import com.easylive.pay.entity.RechargeRecord;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.model.User;
import com.easylive.pay.service.AppService;
import com.easylive.pay.service.apple.ApplePayService;
import com.easylive.pay.service.apple.ApplePrepareOrder;
import com.easylive.pay.service.apple.AppleRequestOrder;
import com.easylive.pay.utils.ClientUtils;
import com.easylive.pay.utils.UAgentInfo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping(value = "/")
public class ApplePayController {

    private static Logger logger = LoggerFactory.getLogger(ApplePayController.class);

    @Autowired
    private ApplePayService applePayService;

    @Autowired
    private AppService appService;

    /**
     * 苹果支付的验证
     *
     * @param fromUser 用户信息
     * @param request  servlet,其中form形式包含receipt属性.
     * @return
     */
    @ApiIgnore
    @RequestMapping(value = {"/pay/applevalid"}, method = RequestMethod.POST)
    @ResponseObjectBody
    @SessionCheck
    public Map validAppleReceipt(@RequestAttribute User fromUser, HttpServletRequest request) {

        String receipt = request.getParameter("receipt");
        long uid = fromUser.getId();
        String clientIp = ClientUtils.getClientIPAddr(request);
        logger.info("ApplePayValid: UID: {}, Receipt:{} ", uid, receipt);
        UAgentInfo uai = new UAgentInfo(request);
        if (uai.getDeviceType() == MobilePlatform.MPF_IOS) {
            return applePayService.valid(receipt, uid, uai.getAppName(), clientIp, MobilePlatform.MPF_IOS);
        } else {
            logger.info("ApplePayValid: [Failed], Not IOS, UID: {} ", uid);
        }
        throw new ResponseException(ResponseError.E_APPLEPAY_VALID_NOTIOS);
    }

    /**
     * 苹果订阅的验证
     *
     * @param fromUser 用户信息
     * @param request  servlet,其中form形式包含receipt属性.
     * @return
     */
    @ApiIgnore
    @RequestMapping(value = {"/pay/sub/applevalid"}, method = RequestMethod.POST)
    @ResponseObjectBody
    @SessionCheck
    public Map validSubAppleReceipt(@RequestAttribute User fromUser, HttpServletRequest request) {

        String receipt = request.getParameter("receipt");
        String transactionId = request.getParameter("transactionId");

        long uid = fromUser.getId();
        logger.info("AppleSubPayValid: UID: {}, transactionId:{} ", uid, transactionId);
        UAgentInfo uai = new UAgentInfo(request);
        if (uai.getDeviceType() == MobilePlatform.MPF_IOS) {
            return applePayService.subValid(receipt, uid, uai.getAppName(), null, transactionId);
        } else {
            logger.info("AppleSubPayValid: [Failed], Not IOS, UID: {} ", uid);
        }
        throw new ResponseException(ResponseError.E_APPLEPAY_VALID_NOTIOS);
    }


    @ApiOperation(value = "验证苹果收据", notes = "客户端传递苹果的收据，如果验证成功，则充值成功。 目前服务器端不提前创建订单，验证成功才创建，后续可能会修改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "receipt", value = "收据数据", dataType = "string", paramType = "form"),
    })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "两个字段，status:, ecoin为充值易币数量")})
    @RequestMapping(value = {"/service/recharge/apple/valid"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public Map validAppleReceipt(@ApiParam("用户UID") @RequestParam long uid,
                                 HttpServletRequest request) {
        String receipt = request.getParameter("receipt");
        String clientIp = request.getParameter("client_ip");
        clientIp = clientIp != null ? clientIp : ClientUtils.getClientIPAddr(request);
        return applePayService.valid(receipt, uid, "", clientIp, MobilePlatform.MPF_IOS);
    }


    //TODO: 增加苹果支付订单创建,需要valid中支持对应的订单校验

    @ApiIgnore
    @RequestMapping({"/service/recharge/apple/order"})
    @ResponseObjectBody
    public String createAppleOrder(@RequestParam long uid, @RequestParam String productId,
                                   @RequestParam int appId) {
        RechargeRecord rechargeRecord = applePayService.createOrder(uid, appId, productId);
        return rechargeRecord.getOrderId();
    }

    /**
     * 是否存在有效订阅
     * @param user
     * @param transactionId
     * @return
     */
    @ApiIgnore
    @RequestMapping("/pay/sub/query")
    @SessionCheck
    public Map existsActiveSub(@RequestAttribute User user,
                               @RequestParam(required = false) String transactionId,
                               @RequestParam(required = false) String originalTransactionId
                               ) {
        return applePayService.getUserSub(transactionId, originalTransactionId);
    }


    @ApiOperation(value = "创建苹果订单", notes = "预创建订单")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "两个字段，status:, ecoin为充值易币数量")})
    @RequestMapping(value = {"/service/recharge/apple/order/prepare"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public ApplePrepareOrder appleOrderRequest(@ApiParam("用户UID") @RequestParam long uid,
                                               @ApiParam("充值ProductId") @RequestParam String productId,
                                               @ApiParam("客户端IP") @RequestParam(value = "client_ip") String clientIp,
                                               @ApiParam("客户端UA") @RequestParam(value = "client_ua", required = false) String clientUserAgent,
                                               @ApiParam("中台APPID") @RequestParam(value = "appId", required = false, defaultValue = "1") Integer mgsAppId) {
        logger.info("[Recharge][APPLE][Prepare]: {}, {}, {}, {}, {} ", uid, mgsAppId, productId, clientIp, clientUserAgent);
        UAgentInfo uai = new UAgentInfo(clientUserAgent);
        MobilePlatform platform = uai.getDeviceType();
        mgsAppId = appService.decideAppIdByIdAndName(mgsAppId, uai.getAppName());
        return applePayService.prepareOrder(mgsAppId, uid, productId, clientIp, platform);
    }

    @ApiOperation(value = "验证苹果收据", notes = "客户端传递苹果的收据，如果验证成功，则充值成功。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "receipt", value = "收据数据", dataType = "string", paramType = "form"),
    })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "两个字段，status:, ecoin为充值易币数量")})
    @RequestMapping(value = {"/service/recharge/apple/order/valid"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public AppleRequestOrder appleOrderRequest(@ApiParam("订单号") @RequestParam String orderId,
                                               HttpServletRequest request) {
        String receipt = request.getParameter("receipt");
        return applePayService.requestOrder(orderId, receipt);
    }
}

