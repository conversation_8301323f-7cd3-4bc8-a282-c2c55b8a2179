package com.easylive.pay.controller;

import com.easylive.pay.common.AssetBizCheck;
import com.easylive.pay.common.RequestAttribute;
import com.easylive.pay.common.ResponseObjectBody;
import com.easylive.pay.common.SessionCheck;
import com.easylive.pay.entity.AssetBiz;
import com.easylive.pay.entity.CashoutLimitEntity;
import com.easylive.pay.entity.UserCoin;
import com.easylive.pay.model.AssetOperation;
import com.easylive.pay.model.GenericMap;
import com.easylive.pay.model.User;
import com.easylive.pay.output.Asset;
import com.easylive.pay.output.BaseAsset;
import com.easylive.pay.service.AssetService;
import com.easylive.pay.service.CashoutService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/")
public class AssetController {

    private static Logger logger = LoggerFactory.getLogger(AssetController.class);


    @Autowired
    private AssetService assetService;

    @Autowired
    private CashoutService cashoutService;



    /**
     * 用户资产
     *
     * @param user
     * @return 返回 Asset
     */
    @ApiIgnore
    @RequestMapping({"/pay/asset", "/pay/asset/query"})
    @SessionCheck
    @ResponseObjectBody
    public Asset queryAssetOfUser(@RequestAttribute User user) {
        return this.getAssetByUid(user.getId(), user.getAppId());
    }


    /*
    --------------------------------------------------------------------------------------------------
     */

    /**
     * 返回用户资产基本信息
     *
     * @param uid
     * @return BaseAsset
     */
    @ApiOperation(value = "基本资产信息", notes = "基本资产信息")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "用户基本资产信息")})
    @RequestMapping(value = {"/service/asset/basequery"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public BaseAsset assetBaseQuery(@ApiParam("用户UID") @RequestParam long uid) {
        return new BaseAsset(assetService.getByUid(uid));
    }


    /**
     * 根据uid返回用户资产信息
     *
     * @param uid
     * @return Asset
     */
    @ApiOperation(value = "资产信息", notes = "获取用户资产信息")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "用户资产信息")})
    @RequestMapping(value = {"/service/asset/query"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseObjectBody
    public Asset getAssetByUid(@ApiParam("用户UID") @RequestParam long uid,
                               @ApiParam("APP ID") @RequestParam(required = false, defaultValue = "1") int appId) {
        UserCoin uc = assetService.getByUid(uid);
        CashoutLimitEntity limit = cashoutService.getCashoutLimit(uid);

        return new Asset(uc, limit, appId);
    }




    /**
     * 获取当前的所有资产总数
     *
     * @return Map
     */
    @ApiIgnore
    @RequestMapping({"/service/asset/stat/total"})
    @ResponseObjectBody
    public Map<String, Long> totalAsset() {
        logger.info("get total asset info");
        List<Object[]> list = assetService.getTotalAsset();
        HashMap<String, Long> ss = new HashMap<>();
        Object[] value = list.get(0);
        ss.put("ecoin", (Long) value[0]);
        ss.put("barley", (Long) value[1]);
        ss.put("riceroll", (Long) value[2]);
        return ss;
    }


    @ApiIgnore
    @RequestMapping({"/service/asset/operation/unary"})
    @AssetBizCheck
    @ResponseObjectBody
    public Map assetOperationUnary(@RequestAttribute AssetBiz assetBiz,
                                   @RequestParam long uid,
                                   @RequestParam long value,
                                   @RequestParam(value = "desc", defaultValue = "", required = false) String desc) {
        AssetOperation operation = new AssetOperation();
        operation.addOperand(uid, value);
        operation.setDesc(desc);

        String tid = assetService.operate(assetBiz, operation).getTid();
        return GenericMap.toMap("tid", tid);
    }


    @ApiIgnore
    @RequestMapping({"/service/asset/operation/binary"})
    @AssetBizCheck
    @ResponseObjectBody
    public Map assetOperationBinary(@RequestAttribute AssetBiz assetBiz,
                                    @RequestParam long uid1,
                                    @RequestParam long value1,
                                    @RequestParam long uid2,
                                    @RequestParam long value2,
                                    @RequestParam(value = "desc", defaultValue = "", required = false) String desc) {
        AssetOperation operation = new AssetOperation();
        operation.addOperand(uid1, value1);
        operation.addOperand(uid2, value2);
        operation.setDesc(desc);

        String tid = assetService.operate(assetBiz, operation).getTid();
        return GenericMap.toMap("tid", tid);
    }

    @ApiIgnore
    @RequestMapping({"/service/asset/operation/multi"})
    @AssetBizCheck
    @ResponseObjectBody
    public Map assetOperationMulti(@RequestAttribute AssetBiz assetBiz, @RequestBody AssetOperation operation) {
        String tid = assetService.operate(assetBiz, operation).getTid();
        return GenericMap.toMap("tid", tid);
    }

}