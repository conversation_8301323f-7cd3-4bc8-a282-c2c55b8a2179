package com.easylive.pay.controller;

import com.easylive.pay.common.*;
import com.easylive.pay.enums.MobilePlatform;
import com.easylive.pay.model.baidu.BaiduNotifyResultData;
import com.easylive.pay.model.baidu.BaiduResponse;
import com.easylive.pay.service.BaiduPayService;
import com.easylive.pay.model.baidu.BaiduSmallProgramOrderInfo;
import com.easylive.pay.utils.ServletUtils;
import com.easylive.pay.utils.UAgentInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@RestController
@RequestMapping(value = "/")
public class BaiduPayController {

    private static Logger logger = LoggerFactory.getLogger(BaiduPayController.class);

    @Autowired
    private BaiduPayService baiduPayService;

    @ApiIgnore
    @RequestMapping(value = {"/pay/recharge/baidu/notify"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public BaiduResponse<BaiduNotifyResultData> payNotify(HttpServletRequest request) {
        logger.info("[Baidu] recharge notify:");
        Map<String, String> args = ServletUtils.getServletParameters(request);
        BaiduResponse<BaiduNotifyResultData> ret = new BaiduResponse<>();
        BaiduNotifyResultData data = new BaiduNotifyResultData();
        data.setIsErrorOrder(0);
        data.setIsConsumed(1);
        ret.setData(data);

        try {
            baiduPayService.notifyValid(args);
            ret.setErrno(0);
            ret.setMsg("success");
            data.setIsConsumed(2);
        } catch (Exception e) {
            ret.setErrno(1);
            ret.setMsg(e.getMessage());
        }
        return ret;
    }


    @RequestMapping(value = {"/service/recharge/baidu/order"}, method = RequestMethod.POST)
    @ResponseObjectBody
    public BaiduSmallProgramOrderInfo createOrder(@RequestParam long uid,
                                                  @RequestParam long amount,
                                                  @RequestParam String userAgent,
                                                  @RequestParam String clientIp) {
        UAgentInfo uai = new UAgentInfo(userAgent);
        MobilePlatform platform = uai.getDeviceType();
        return baiduPayService.createSmallProgramOrder(uid, amount, platform, clientIp);
    }


}

