package com.easylive.pay.pub.event;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/1/26
 */
public class RechargeEvent {
    private long uid;
    private long amount;
    private long ecoin;
    private int platform;
    private String orderId;
    private String platformOrderId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date commitTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;
    private String currency;
    private String merchantCurrency;
    private String merchantId;
    private String tid;
    private int appId;
    private int userAppId = 1;
    private int userAppSourceId = 1;
    private int clientPlatform;

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getAmount() {
        return amount;
    }

    public void setAmount(long amount) {
        this.amount = amount;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPlatformOrderId() {
        return platformOrderId;
    }

    public void setPlatformOrderId(String platformOrderId) {
        this.platformOrderId = platformOrderId;
    }

    public Date getCommitTime() {
        return commitTime;
    }

    public void setCommitTime(Date commitTime) {
        this.commitTime = commitTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getMerchantCurrency() {
        return merchantCurrency;
    }

    public void setMerchantCurrency(String merchantCurrency) {
        this.merchantCurrency = merchantCurrency;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public int getAppId() {
        return appId;
    }

    public void setAppId(int appId) {
        this.appId = appId;
    }

    public int getUserAppId() {
        return userAppId;
    }

    public void setUserAppId(int userAppId) {
        this.userAppId = userAppId;
    }

    public int getUserAppSourceId() {
        return userAppSourceId;
    }

    public void setUserAppSourceId(int userAppSourceId) {
        this.userAppSourceId = userAppSourceId;
    }

    public int getClientPlatform() {
        return clientPlatform;
    }

    public void setClientPlatform(int clientPlatform) {
        this.clientPlatform = clientPlatform;
    }

    @Override
    public String toString() {
        return "RechargeEvent{" +
                "uid=" + uid +
                ", amount=" + amount +
                ", ecoin=" + ecoin +
                ", platform=" + platform +
                ", orderId='" + orderId + '\'' +
                ", platformOrderId='" + platformOrderId + '\'' +
                ", commitTime=" + commitTime +
                ", completeTime=" + completeTime +
                ", currency='" + currency + '\'' +
                ", merchantCurrency='" + merchantCurrency + '\'' +
                ", tid='" + tid + '\'' +
                ", appId=" + appId +
                ", userAppId=" + userAppId +
                ", userAppSourceId=" + userAppSourceId +
                ", clientPlatform=" + clientPlatform +
                '}';
    }
}
