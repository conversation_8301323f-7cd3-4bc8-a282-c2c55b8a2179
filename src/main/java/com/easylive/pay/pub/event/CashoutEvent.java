package com.easylive.pay.pub.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 提现事件
 * <AUTHOR>
 * @date 2021/9/15 14:10
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CashoutEvent {

    /**
     * 提现ID，保证消息幂等性
     */
    private long id;

    /**
     * UID
     */
    private long uid;

    /**
     * 提现金额（分）
     */
    private long rmb;

    /**
     * 提现扣除饭团
     */
    private long riceroll;

    /**
     * 提现平台，注意对应数据库字段`pfplatform`
     * @see com.easylive.pay.enums.CashoutPlatform
     */
    private int platform;

    /**
     * APP ID
     */
    private int appId;

    /**
     * 提现时间
     */
    private Date commitTime;

    /**
     * 提现成功时间
     */
    private Date completeTime;

    /**
     * 消息产生时间
     */
    private Date messageTime;

}
