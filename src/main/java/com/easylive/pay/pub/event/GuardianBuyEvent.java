package com.easylive.pay.pub.event;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class GuardianBuyEvent {
    private GuardianUser from;
    private GuardianUser to;

    private long ecoin;
    private int type;
    private long riceroll;
    private String vid;
    private String orderId;
    private Date time;

    public static class GuardianUser{
        private long uid;
        private String name;
        private String nickname;

        public long getUid() {
            return uid;
        }

        public void setUid(long uid) {
            this.uid = uid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        @Override
        public String toString() {
            return "GuardianUser{" +
                    "uid=" + uid +
                    ", name='" + name + '\'' +
                    ", nickname='" + nickname + '\'' +
                    '}';
        }
    }

    public GuardianUser getFrom() {
        return from;
    }

    public void setFrom(GuardianUser from) {
        this.from = from;
    }

    public GuardianUser getTo() {
        return to;
    }

    public void setTo(GuardianUser to) {
        this.to = to;
    }

    public long getEcoin() {
        return ecoin;
    }

    public void setEcoin(long ecoin) {
        this.ecoin = ecoin;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public String getVid() {
        return vid;
    }

    public void setVid(String vid) {
        this.vid = vid;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "GuardianBuyEvent{" +
                "from=" + from +
                ", to=" + to +
                ", ecoin=" + ecoin +
                ", type=" + type +
                ", riceroll=" + riceroll +
                ", orderId='" + orderId + '\'' +
                ", time=" + time +
                '}';
    }
}
