package com.easylive.pay.pub.event;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-06-13
 */
public class GiftEvent {
    private String orderId;

    private long giftId;
    private String giftName;
    private long giftType;
    private long giftExp;

    private long fromUid;
    private String fromName;
    private long fromAppSourceId;

    private long toUid;
    private String toName;
    private int toTopicId;
    private long toAppSourceId;

    private long cost;
    private long costType;
    private long riceroll;

    private long accumRiceroll;

    private long number;
    private String vid;
    private Boolean living;

    private long pkId;

    private Date time;

    /**
     * 强制跑道消息
     */
    private boolean showRunway;

    public String getGiftMsg() {
        return giftMsg;
    }

    public void setGiftMsg(String giftMsg) {
        this.giftMsg = giftMsg;
    }

    private String giftMsg;

    private String giftMsgTemplate;

    public long getGiftExp() { return giftExp; }

    public void setGiftExp(long giftExp) { this.giftExp = giftExp; }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public long getGiftId() {
        return giftId;
    }

    public void setGiftId(long giftId) {
        this.giftId = giftId;
    }

    public String getGiftName() {
        return giftName;
    }

    public void setGiftName(String giftName) {
        this.giftName = giftName;
    }

    public long getGiftType() {
        return giftType;
    }

    public void setGiftType(long giftType) {
        this.giftType = giftType;
    }

    public long getFromUid() {
        return fromUid;
    }

    public void setFromUid(long fromUid) {
        this.fromUid = fromUid;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public long getFromAppSourceId() {
        return fromAppSourceId;
    }

    public void setFromAppSourceId(long fromAppSourceId) {
        this.fromAppSourceId = fromAppSourceId;
    }

    public long getToUid() {
        return toUid;
    }

    public void setToUid(long toUid) {
        this.toUid = toUid;
    }

    public String getToName() {
        return toName;
    }

    public void setToName(String toName) {
        this.toName = toName;
    }

    public int getToTopicId() {
        return toTopicId;
    }

    public void setToTopicId(int toTopicId) {
        this.toTopicId = toTopicId;
    }

    public long getToAppSourceId() {
        return toAppSourceId;
    }

    public void setToAppSourceId(long toAppSourceId) {
        this.toAppSourceId = toAppSourceId;
    }

    public long getCost() {
        return cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }

    public long getCostType() {
        return costType;
    }

    public void setCostType(long costType) {
        this.costType = costType;
    }

    public long getRiceroll() {
        return riceroll;
    }

    public void setRiceroll(long riceroll) {
        this.riceroll = riceroll;
    }

    public long getAccumRiceroll() { return accumRiceroll; }

    public void setAccumRiceroll(long accumRiceroll) { this.accumRiceroll = accumRiceroll; }

    public long getNumber() {
        return number;
    }

    public void setNumber(long number) {
        this.number = number;
    }

    public String getVid() {
        return vid;
    }

    public void setVid(String vid) {
        this.vid = vid;
    }

    public long getPkId() {
        return pkId;
    }

    public void setPkId(long pkId) {
        this.pkId = pkId;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public boolean isShowRunway() {
        return showRunway;
    }

    public void setShowRunway(boolean showRunway) {
        this.showRunway = showRunway;
    }

    public String getGiftMsgTemplate() {
        return giftMsgTemplate;
    }

    public void setGiftMsgTemplate(String giftMsgTemplate) {
        this.giftMsgTemplate = giftMsgTemplate;
    }

    @Override
    public String toString() {
        return "GiftEvent{" +
                "orderId='" + orderId + '\'' +
                ", giftId=" + giftId +
                ", giftName='" + giftName + '\'' +
                ", giftType=" + giftType +
                ", giftExp=" + giftExp +
                ", fromUid=" + fromUid +
                ", fromName='" + fromName + '\'' +
                ", fromAppSourceId=" + fromAppSourceId +
                ", toUid=" + toUid +
                ", toName='" + toName + '\'' +
                ", toTopicId=" + toTopicId +
                ", toAppSourceId=" + toAppSourceId +
                ", cost=" + cost +
                ", costType=" + costType +
                ", riceroll=" + riceroll +
                ", accumRiceroll=" + accumRiceroll +
                ", number=" + number +
                ", vid='" + vid + '\'' +
                ", living=" + living +
                ", pkId=" + pkId +
                ", time=" + time +
                ", giftMsg='" + giftMsg + '\'' +
                ", giftMsgTemplate='" + giftMsgTemplate + '\'' +
                ", showRunway=" + showRunway +
                '}';
    }

    public Boolean getLiving() {
        return living;
    }

    public void setLiving(Boolean living) {
        this.living = living;
    }
}
