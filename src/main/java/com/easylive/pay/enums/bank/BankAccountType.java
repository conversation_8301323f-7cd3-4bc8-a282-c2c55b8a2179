package com.easylive.pay.enums.bank;

/**
 * 银行类型
 */
public enum BankAccountType {
    /**
     * 鹏劳
     */
    PN(0),

    /**
     * 河南省韶华人力资源服务有限公司
     */
    SH(1);

    private final int value;

    BankAccountType(final int newValue) {
        value = newValue;
    }

    public int getValue() {
        return value;
    }

    public static BankAccountType fromValue(int value) {
        for (BankAccountType type : BankAccountType.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }
}
