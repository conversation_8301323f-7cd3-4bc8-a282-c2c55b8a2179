package com.easylive.pay.enums.bank;

/**
 * 交易状态
 */
public enum TaxQueryStatus {
    /**
     * 交易状态
     * 0 – 交易成功
     * 1 – 交易失败
     * 2 – 处理处理中
     */
    SUCCESS("0"),
    FAILED("1"),
    WITHDRAW_DOING("2");

    private final String value;

    TaxQueryStatus(final String newValue) {
        value = newValue;
    }

    public String getValue() {
        return value;
    }

    public static TaxQueryStatus fromValue(String value) {
        for (TaxQueryStatus type : TaxQueryStatus.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
