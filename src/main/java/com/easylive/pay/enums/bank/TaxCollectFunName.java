package com.easylive.pay.enums.bank;

/**
 * 税优地
 */
public enum TaxCollectFunName {

    /**
     * 用户签约接口
     */
    USER_SIGN("userSign"),

    /**
     * 批量出款接口
     */
    BATCH_PAYMENT("batchPayment"),

    /**
     * 单笔出款结果查询接口
     */
    QUERY_PAYMENT("queryPayment");

    private final String value;

    TaxCollectFunName(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
