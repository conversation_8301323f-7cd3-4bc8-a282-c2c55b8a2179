package com.easylive.pay.enums.bank;

/**
 * 交易状态
 */
public enum PaymentQueryState {
    /**
     * 交易状态 0：已受理 1：处理中 2：提现中 3：成功 4：失败
     */
    ACCEPTED(0),
    DEALING(1),
    WITHDRAW_DOING(2),
    SUCCESS(3),
    FAIL(4);

    private final int value;

    PaymentQueryState(final int newValue) {
        value = newValue;
    }

    public int getValue() {
        return value;
    }

    public static PaymentQueryState fromValue(int value) {
        for (PaymentQueryState type : PaymentQueryState.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }
}
