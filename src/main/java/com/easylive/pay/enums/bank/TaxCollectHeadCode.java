package com.easylive.pay.enums.bank;

/**
 * 税筹
 */
public enum TaxCollectHeadCode {

    /**
     *     0001 验证签名失败
     *     0009 报文不能为空
     *     0010 报文消息头不能为空
     *     0011 报文字段校验错误
     *     0012 IP 没有访问权限
     *     0013 订单不存在
     *     0014 该记录已经申请过开票
     *     0015 API 系统内部配置异常
     *     0016 身份证号码和姓名一致性验证失败
     *     0017 订单异常
     *     0018 付款渠道不支持
     *     0098 商户未找到
     *     0099 报文解析异常
     *     9999 系统异常
     */

    /**
     * 用户签约接口
     */
    SUCCESS("0000"),

    /**
     * 验证签名失败
     */
    SIGN_FAILED("0001"),

    /**
     * 报文不能为空
     */
    BODY_EMPTY("0009"),

    /**
     * 报文消息头不能为空
     */
    HEADER_EMPTY("0010"),

    /**
     * 报文字段校验错误
     */
    BODY_VALID("0011"),

    /**
     * IP 没有访问权限
     */
    IP_PERMISSION("0012"),

    /**
     * 订单不存在
     */
    ORDER_NOT_EXISTS("0013"),

    /**
     * 该记录已经申请过开票
     */
    APPLY_EXISTS("0014"),

    /**
     * API 系统内部配置异常
     */
    API_CONFIG_EXCEPTION("0015"),

    /**
     * 身份证号码和姓名一致性验证失败
     */
    ID_NAME_NOT_SAME("0016"),

    /**
     * 订单异常
     */
    ORDER_EXPCEPTION("0017"),

    /**
     * 付款渠道不支持
     */
    CHANNEL_NOT_SUPPORT("0018"),

    /**
     * 商户未找到
     */
    MERCHANT_NOT_FOUND("0098"),

    /**
     * 报文解析异常
     */
    BODY_PARSE_EXCEPTION("0099"),

    /**
     * 系统异常，请求异常，交易暂停挂起，不允许做成功或失败处
     */
    SYSTEM_ERROR("9999");

    private final String value;

    TaxCollectHeadCode(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
