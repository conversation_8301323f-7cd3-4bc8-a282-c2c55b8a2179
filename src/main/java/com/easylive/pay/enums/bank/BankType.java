package com.easylive.pay.enums.bank;

/**
 * 银行类型
 */
public enum BankType {
    /**
     * 新政银企
     */
    XZYQ(0),

    /**
     * 税优地系统
     */
    SYD(1),

    /**
     * 税筹
     */
    TAX(2),

    /**
     * 薪云
     */
    XINYUN(3),

    /**
     * 玖合
     */
    JIUHE(4),

    /**
     * 汇付
     */
    HUIFU(5),

    /**
     * 云账户
     */
    YUNZHANGHU(6)
    ;

    private final int value;

    BankType(final int newValue) {
        value = newValue;
    }

    public int getValue() {
        return value;
    }

    public static BankType fromValue(int value) {
        for (BankType type : BankType.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }
}
