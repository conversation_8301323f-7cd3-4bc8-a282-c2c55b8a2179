package com.easylive.pay.enums.bank;

/**
 * 税优地
 */
public enum SydFunCode {

    /**
     * 批量付款接口
     */
    MULTI_PAY(6001, "6001"),

    /**
     * 批量付款查询接口
     */
    MULTI_PAY_QUERY(6002, "6002"),

    /**
     * 账户余额查询接口
     */
    ACCOUNT_QUERY(6003, "6003"),

    /**
     * SOHO签约接口
     */
    SIGN(6010, "6010"),

    /**
     * SOHO签约查询接口
     */
    SIGN_QUERY(6011, "6011"),

    /**
     * SOHO签约通知接口
     */
    SIGN_QUERY_NOTIFY(6011, "6011");

    private final int value;

    private final String data;

    SydFunCode(int value, String data) {
        this.value = value;
        this.data = data;
    }

    public int getValue() {
        return value;
    }

    public String getData() {
        return data;
    }

}
