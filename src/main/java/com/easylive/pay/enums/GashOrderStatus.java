package com.easylive.pay.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum GashOrderStatus {

    /**
     * gash 订单状态信息
     */
    SORS_CREATE("E", 0),
    SORS_SUCCESS("S", 1),
    SORS_INCOMPLETE("O", 2),
    SORS_WAIT("W", 3),
    SORS_FAILED("F",4),
    SORS_TIMEOUT("T", 5),
    SORS_CANCEL("C", 6),
    SORS_EXCEPTION("", 7),

    ;



    private final int value;
    private final String code;

    GashOrderStatus(final String code ,final int value) {
        this.code = code;
        this.value = value;
    }

    private static final Map<String, GashOrderStatus> MAP = new HashMap<>();

    static {
        for (GashOrderStatus status : GashOrderStatus.values()) {
            MAP.put(status.getCode(), status);
        }
    }

    public static GashOrderStatus of(String code) {
        return MAP.get(code);
    }

    public int getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }
}
