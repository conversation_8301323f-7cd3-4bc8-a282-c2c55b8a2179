package com.easylive.pay.enums;

public enum RankItemType {

    /**
     * Rank Item Type
     */
    ECOIN("coin"),
    RICEROLL("rice"),
    BARLEY("bar");

    private final String shortName;

    RankItemType(final String shortName) {
        this.shortName = shortName;
    }

    public static RankItemType of(String shortName) {
        for (RankItemType type : RankItemType.values()) {
            if (type.getShortName().equals(shortName))
                return type;
        }
        throw new IllegalArgumentException("Invalid rank type");
    }

    public static RankItemType fromAssetType(AssetType type) {
        if (type == AssetType.ECOIN)
            return ECOIN;
        else if (type == AssetType.RICEROLL)
            return RICEROLL;
        else if (type == AssetType.BARLEY)
            return BARLEY;

        throw new IllegalArgumentException("Invalid asset type");
    }

    public String getShortName() {
        return shortName;
    }

    @Override
    public String toString() {
        return shortName;
    }
}
