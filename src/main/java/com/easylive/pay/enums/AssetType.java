package com.easylive.pay.enums;

/**
 * Created by c on 2015/12/15.
 */
public enum AssetType {

    /**
     * 薏米,钻石
     */
    BARLEY(0),

    /**
     * 易币
     */
    ECOIN(1),

    /**
     * 饭团
     */
    RICEROLL(2),

    /**
     * 元宝
     */
    GCOIN(3);

    private final int value;

    AssetType(final int newValue) {
        value = newValue;
    }

    public static AssetType fromValue(int value) {
        for (AssetType assetType : AssetType.values()) {
            if (assetType.getValue() == value)
                return assetType;
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}
