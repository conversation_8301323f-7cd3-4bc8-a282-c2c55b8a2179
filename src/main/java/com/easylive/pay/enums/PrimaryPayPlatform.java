package com.easylive.pay.enums;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/8/4
 */
public enum PrimaryPayPlatform {
    OFFLINE(0, PayPlatform.PPF_OFFLINE_PAY),
    WEIXIN(1, PayPlatform.PPF_WX_APP_PAY, PayPlatform.PPF_WX_MP_PAY, PayPlatform.PPF_WX_CODE_PAY, PayPlatform.PPF_WX_MP_PAY, PayPlatform.PPF_WX_H5_PAY),
    ALIPAY(2, PayPlatform.PPF_ALI_APP_PAY, PayPlatform.PPF_ALI_WAP_PAY, PayPlatform.PPF_ALI_PC_PAY),
    APPLE(3, PayPlatform.PPF_APPLE, PayPlatform.PPF_SANDBOX),
    GOOGLE(4, PayPlatform.PPF_GOOGLE),
    PAYPAL(5, PayPlatform.PPF_PAYPAL);

    private final int value;
    private final Set<PayPlatform> platforms = new HashSet<>();
    private static final Map<Integer, PrimaryPayPlatform> valueMap = new HashMap<>();

    private static final Map<PayPlatform,PrimaryPayPlatform> maps = new HashMap<>();

    static {
        for(PrimaryPayPlatform p: PrimaryPayPlatform.values() ) {
            valueMap.put(p.getValue(), p);
            for( PayPlatform pp : p.getPlatforms() ) {
                maps.put( pp, p);
            }
        }
    }

    PrimaryPayPlatform(int value, PayPlatform... platforms) {
        this.value = value;
        this.platforms.addAll(Arrays.asList(platforms));
    }

    public int getValue() {
        return value;
    }

    public List<PayPlatform> getPlatforms() {
        return new ArrayList<>(platforms);
    }


    public static PrimaryPayPlatform fromSubPlatform(int subPlatform) {
        return maps.get(PayPlatform.fromValue(subPlatform));
    }

    public static PrimaryPayPlatform fromValue( int value) {
        return valueMap.get(value);
    }
}
