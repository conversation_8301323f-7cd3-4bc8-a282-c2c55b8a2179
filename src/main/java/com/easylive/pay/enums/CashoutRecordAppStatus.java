package com.easylive.pay.enums;

/**
 * APP提现状态
 *
 * <AUTHOR>
 * @date 2019/8/20
 */
public enum CashoutRecordAppStatus {

    /**
     * APP提现状态
     * 0 - 提现申请中
     * 1 - 提现成功，到账中
     * 2 - 提现失败，退款中
     * 3 - 到账成功
     * 4 - 退款成功
     * 5 - 转账请求已发起
     */
    INIT(0),
    INTO_ACCOUNTING(1),
    REFUNDING(2),
    ACCOUNT_SUCCESS(3),
    REFUND_SUCCESS(4),
    ACCOUNT_REQUESTING(5);

    private final int value;

    CashoutRecordAppStatus(final int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

}
