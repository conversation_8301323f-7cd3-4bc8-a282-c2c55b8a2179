package com.easylive.pay.enums;

public enum AliPayProtocol {

    /**
     * APP支付
     */
    APP(0),

    /**
     * 页面支付网关，包括官方WAP、PC支付
     */
    GATEWAY(1),

    /**
     * 二维码链接
     */
    QR_CODE(2)
    ;

    private final int value;

    AliPayProtocol(final int newValue) {
        value = newValue;
    }

    public static AliPayProtocol fromValue(int value) {
        for (AliPayProtocol protocol : AliPayProtocol.values()) {
            if (protocol.getValue() == value)
                return protocol;
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}
