package com.easylive.pay.enums;

import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public enum ConfigDataType {

    STRING(0),
    INTEGER(1),
    DECIMAL(2),
    DATETIME(3);

    private static final Map<Integer, ConfigDataType> maps = new HashMap<>();
    private static final Map<Integer, Class> classMap = new HashMap<>();

    static {
        maps.put(STRING.value, STRING);
        maps.put(INTEGER.value, INTEGER);
        maps.put(DECIMAL.value, DECIMAL);
        maps.put(DATETIME.value, DATETIME);

        classMap.put(STRING.value, String.class);
        classMap.put(INTEGER.value, BigInteger.class);
        classMap.put(DECIMAL.value, BigDecimal.class);
        classMap.put(DATETIME.value, Date.class);

    }

    private final int value;


    ConfigDataType(final int newValue) {
        value = newValue;
    }

    public static ConfigDataType fromValue(int value) {
        ConfigDataType configDataType = maps.get(value);
        return configDataType == null ? STRING : configDataType;
    }

    public static Class getDataTypeClass(int value) {
        Class classType = classMap.get(value);
        return classType == null ? String.class : classType;
    }

    public static Object convert(String value, int type) {
        return convert(value, fromValue(type));
    }

    public static Object convert(String value, ConfigDataType type) {
        if (value != null) {
            if (type == INTEGER) {
                return new BigInteger(value);
            } else if (type == DECIMAL) {
                return new BigDecimal(value);
            } else if (type == DATETIME) {
                return DateTime.parse(value);
            }
        }
        return value;
    }

    public int getValue() {
        return value;
    }

    public Class getValueClass() {
        return getDataTypeClass(value);
    }
}
