package com.easylive.pay.enums;

/**
 *
 * <AUTHOR>
 * @date 2023/9/14
 **/
public enum JiuHeOrderSettleStatusEnum {

    INIT("待提交"),
    COMMITTED("已提交，会有后续回调"),
    COMMITFAIL("提交失败"),
    TOPAY("待支付"),
    CONFIRMED("已确认，会有后续回调"),
    REMITTING("出款中，会有后续回调"),
    CANCELED("已取消"),
    SUCC("结算成功"),
    FAIL("结算失败"),
    PARTIAL("部分成功"),
    REFUNDED("已退票"),
    CLOSED("已关闭"),
    ;
    private String desc;



    JiuHeOrderSettleStatusEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

}