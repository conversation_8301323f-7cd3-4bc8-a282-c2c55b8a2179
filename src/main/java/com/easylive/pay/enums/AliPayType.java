package com.easylive.pay.enums;

public enum AliPayType {

    /**
     * APP支付
     */
    APP(0),

    /**
     * 手机WAP支付
     */
    WAP(1),

    /**
     * PC支付
     */
    PC(2),

    /**
     * 小程序支付
     */
    MINIAPP(3)
    ;

    private final int value;

    AliPayType(final int newValue) {
        value = newValue;
    }

    public static AliPayType fromValue(int value) {
        for (AliPayType assetType : AliPayType.values()) {
            if (assetType.getValue() == value)
                return assetType;
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}
