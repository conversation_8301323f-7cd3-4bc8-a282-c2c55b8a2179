package com.easylive.pay.enums;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @date 2020/7/21
 */
public enum SysSettingMetaKey {

    /**
     * 系统配置
     */
    WEEK_LIST_RANK_SWITCH(0, "week_list_rank_switch", "周榜礼物开关"),
    LIVE_DISPLAY_PERMISSION(1, "live_display_permissions", "开直播可选权限"),
    EXCHANGE_ANCHOR_TASK_PRICE(2, "exchange_anchor_task_price", "切换主播任务价格(易币)"),
    GAME_TESTER(3, "game_tester", "游戏内测人员"),
    NOBLE_IDENTIFY_PRICE(4, "nobel_identify_price", "贵族鉴定价格"),
    FIRST_RECHARGE_INFO(5, "recharge_first_setting", "首充奖励信息"),
    PROMOTER_SHARE_CONTENT(6, "promoter_share_content", "推广分享内容"),
    SIGN_TASK_SETTING(7, "new_sign_task_setting", "签到任务设置"),
    TASK_AWARD_SETTING(8, "new_task_award_setting", "任务奖励设置"),
    TASK_AWARD_GROUP_SETTING(9, "new_task_award_group_setting", "任务奖励分组设置"),
    PROMOTER_ANCHOR_REWARD(10, "promoter_anchor_reward", "推广主播奖励值"),
    LUCK_GIFT_EXPLOSION_DELAY(11, "luck_gift_explosion_delay", "幸运礼物爆奖延迟"),
    WITHDRAW_BANK(12, "withdraw_bank", "提现平台"),
    WITHDRAW_BANK_ACCOUNT(13, "withdraw_bank_account", "提现出款账户");

    private int code;
    private String value;
    private String valueCn;

    SysSettingMetaKey(int code, String value, String valueCn) {
        this.code = code;
        this.value = value;
        this.valueCn = valueCn;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueCn() {
        return valueCn;
    }

    public void setValueCn(String valueCn) {
        this.valueCn = valueCn;
    }

}
