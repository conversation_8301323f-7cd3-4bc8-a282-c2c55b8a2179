package com.easylive.pay.enums;

import java.util.ArrayList;
import java.util.List;

public enum WeixinPayType {

    /**
     * APP
     */
    APP("APP", PayPlatform.PPF_WX_APP_PAY),
    MP("JSAPI", PayPlatform.PPF_WX_MP_PAY),
    CODE("NATIVE", PayPlatform.PPF_WX_CODE_PAY),
    H5("MWEB", PayPlatform.PPF_WX_H5_PAY),
    MINIAPP("JSAPI", PayPlatform.PPF_WX_MINIAPP_PAY);

    private final String tradeType;
    private final PayPlatform payPlatform;

    WeixinPayType(final String tradeType, PayPlatform payPlatform) {
        this.tradeType = tradeType;
        this.payPlatform = payPlatform;
    }

    public static WeixinPayType fromValue(int value) {
        for (WeixinPayType type : WeixinPayType.values()) {
            if (type.ordinal() == value)
                return type;
        }
        return null;
    }

    public static List<Integer> fromTradeType(String tradeType) {
        List<Integer>  wxPayTypeList = new ArrayList<>();
        for (WeixinPayType type : WeixinPayType.values()) {
            if (type.getTradeType().equals(tradeType))
                wxPayTypeList.add(type.ordinal());
        }
        return wxPayTypeList;
    }

    public String getTradeType() {
        return tradeType;
    }

    public PayPlatform getPayPlatform() {
        return payPlatform;
    }
}
