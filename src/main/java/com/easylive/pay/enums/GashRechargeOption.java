package com.easylive.pay.enums;


public enum GashRechargeOption {

    /**
     * 点卡充值 - 测试环境
     */
    POINT_CARD(0, "COPGAM05"),

    /**
     * 银联卡
     */
    UNION_PAY(1, "BNK82204"),

    WEB_ATM(2, "BNK80801"),

    SMART_PAY(3, "BNK80802"),

    CREDIT_CARD(4, "BNK82201"),
    ;
    private final int value;
    private final String code;

    GashRechargeOption(final int value, final String code) {
        this.value = value;
        this.code = code;
    }

    public int getValue() {
        return value;
    }

    public String getCode() { return code;}

    public static GashRechargeOption of (int value){
        for (GashRechargeOption option: GashRechargeOption.values()){
            if(option.getValue() == value){
                return option;
            }
        }
        return null;
    }
}
