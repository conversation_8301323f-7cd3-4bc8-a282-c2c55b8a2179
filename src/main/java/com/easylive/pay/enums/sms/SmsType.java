package com.easylive.pay.enums.sms;

/**
 * 短信状态
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
public enum SmsType {

    /**
     * Register = 0;
     * ResetPassword = 1;
     * ThirdPartyRegister = 2;
     * WebRegister = 3;
     * AlipayVerify = 4;
     * BindPhone = 5;
     * BindBankCard = 6;
     */
    REGISTER(0, "REGISTER", "注册"),
    RESET_PASSWORD(1, "SENT", "重置密码"),
    THIRD_PARTY_REGISTER(2, "THIRD_PARTY_REGISTER", "三方注册"),
    WEB_REGISTER(3, "WEB_REGISTER", "网页注册"),
    ALIPAY_VERIFY(4, "ALIPAY_VERIFY", "阿里注册"),
    BIND_PHONE(5, "BIND_PHONE", "绑定手机"),
    BIND_BANK_CARD(6, "BIND_BANK_CARD", "绑定银行卡");

    private int code;
    private String value;
    private String valueCn;

    SmsType(int code, String value, String valueCn) {
        this.code = code;
        this.value = value;
        this.valueCn = valueCn;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueCn() {
        return valueCn;
    }

    public void setValueCn(String valueCn) {
        this.valueCn = valueCn;
    }

}
