package com.easylive.pay.enums.sms;

/**
 * 短信类型
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
public enum SmsStatus {


    /**
     * 0 - created,
     * 1 - sent,
     * 2 - closed,
     * 3 - send error,
     * 4 - timeout
     */
    CREATED(0, "CREATED", "已创建"),
    SENT(1, "SENT", "已发送"),
    COLOSED(2, "COLOSED", "已关闭"),
    SEND_ERROR(3, "SEND_ERROR", "发送错误"),
    TIMEOUT(4, "TIMEOUT", "超时");

    private int code;
    private String value;
    private String valueCn;

    SmsStatus(int code, String value, String valueCn) {
        this.code = code;
        this.value = value;
        this.valueCn = valueCn;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueCn() {
        return valueCn;
    }

    public void setValueCn(String valueCn) {
        this.valueCn = valueCn;
    }

}
