package com.easylive.pay.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 15/12/15
 */

public enum PayPlatform {

    /**
     * ALL
     */
    PPF_ALL(0),
    PPF_WX_APP_PAY(1),
    PPF_APPLE(2),
    PPF_SANDBOX(3),
    PPF_WX_MP_PAY(4),
    PPF_BUY_BACK(5),
    PPF_ALI_WAP_PAY(6),
    PPF_ALI_APP_PAY(7),
    PPF_APP_ALITBAO(8),
    PPF_WX_CODE_PAY(9),
    PPF_OFFLINE_PAY(10),
    PPF_BANK_PAY(11),
    /*
    PPF_MIDAS_PAY(12),
    PPF_YSDK_PAY(13),
    */
    PPF_WX_MINIAPP_PAY(14),
    PPF_ALI_PC_PAY(15),
    PPF_WX_H5_PAY(16),
    PPF_PAYPAL(17),
    PPF_BAIDU_PAY(18),
    PPF_GOOGLE(20),
    PPF_OPPO_PAY(21),
    PPF_UNION_PAY_ALI(22),
    PPF_GASH_PAY(23),
    ;

    private static final Map<Integer, PayPlatform> MAP = new HashMap<>();

    static {
        for (PayPlatform payPlatform : PayPlatform.values()) {
            MAP.put(payPlatform.getValue(), payPlatform);
        }
    }

    private final int value;

    PayPlatform(final int newValue) {
        value = newValue;
    }

    public static PayPlatform fromValue(int value) {
        return MAP.get(value);
    }

    public int getValue() {
        return value;
    }
}
