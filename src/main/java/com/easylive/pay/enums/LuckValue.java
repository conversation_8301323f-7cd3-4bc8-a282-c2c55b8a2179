package com.easylive.pay.enums;


public enum LuckValue {

    LV_REWARD_RATIO(2L),
    LV_ECOIN_TO_RICEROLL_RATIO(100L),
    LV_PRIZE_POOL_ID(1L),
    LV_MIN_PRIZE_POOL(300L),
    LV_MED_PRIZE_POOL(4000L),
    LV_MAX_PRIZE_POOL(50000L),
    LV_MIN_PRIZE_RATE(10L),
    LV_MED_PRIZE_RATE(100L),
    LV_MAX_PRIZE_RATE(1000L),
    LUCK_GIFT_DISCOUNT_LIMIT(1000L),
    EXPLOSION_STATUS_EXPLOSION(1L),
    EXPLOSION_STATUS_NOT_EXPLOSION(0L),
    EXPLOSION_NEXT_STAGE_VALUE(10000L),
    EXPLOSION_ONCE_POOL_VALUE(10000L);

    private final Long value;

    LuckValue(final Long newValue) {
        value = newValue;
    }

    public Long getValue() {
        return value;
    }
}
