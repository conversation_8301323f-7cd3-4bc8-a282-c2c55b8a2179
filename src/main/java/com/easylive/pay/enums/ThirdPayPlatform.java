package com.easylive.pay.enums;

public enum ThirdPayPlatform {

    /**
     * 官方，比如微信或者支付宝
     */
    OFFICIAL(0),

    /**
     * 连连支付
     */
    LIANLIAN(1),

    /**
     * 汇付天下支付
     */
    HUIFU(2);




    private final int value;

    ThirdPayPlatform(final int newValue) {
        value = newValue;
    }

    public static ThirdPayPlatform fromValue(int value) {
        for (ThirdPayPlatform assetType : ThirdPayPlatform.values()) {
            if (assetType.getValue() == value)
                return assetType;
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}
