package com.easylive.pay.enums;

/**
 * 道具类型
 */
public enum PackageToolsType {

    /**
     * 道具类型 1-礼物道具 2-靓号 3-坐骑 4-认证 5-观影券 6-置顶票 7-合成碎片 8-豌豆
     */
    GIFT(1),
    NICE_NUMBER(2),
    HORSE(3),
    CERT(5),
    MOVIE_TICKET(6),
    TOP_TICKET(7),
    COMPLEX_CHIP(7),
    PEA(8),
    ;

    private final int value;

    PackageToolsType(final int newValue) {
        value = newValue;
    }

    public int getValue() {
        return value;
    }
    
}
