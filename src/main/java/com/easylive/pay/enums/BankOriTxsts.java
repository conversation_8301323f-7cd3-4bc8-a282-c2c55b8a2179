package com.easylive.pay.enums;

/**
 * 新政银企原交易返回码
 *
 * <AUTHOR>
 * @date 2019/8/20
 */
public enum BankOriTxsts {
    /**
     * 交易状态:
     * 0 -  原交易失败；
     * 1 -	原交易成功；
     * 2 -	原交易已冲正；
     * 3 -	原交易已撤销；
     * 4 -	原交易已确认成功；
     * 5 -	原交易已发送待确认；
     * 6 -	原交易已确认失败；
     * 7 -	原交易处理中；
     */
    FAIL(0, "FAIL"),
    SUCCESS(1, "SUCCESS"),
    REVERSAL(2, "REVERSAL"),
    CANCEL(3, "CANCEL"),
    CONFIRM_SUCCESS(4, "CONFIRM_SUCCESS"),
    SEND_CONFIRM(5, "SEND_CONFIRM"),
    CONFIRM_FAILED(6, "CONFIRM_FAILED"),
    DEALING(7, "DEALING"),
    ;

    private final int code;
    private final String value;

    BankOriTxsts(final int code, final String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
