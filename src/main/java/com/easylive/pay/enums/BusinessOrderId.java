package com.easylive.pay.enums;

import lombok.Getter;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/25
 */
@Getter
public enum BusinessOrderId {
    PAY("",1,18),
    CASHOUT("000",1,10),
    XZYQ("001",0,10),
    HUIFU("002",1,10),
    HUIFU_REFUND("003", 1, 10);


    private final String prefix;
    private final int length;
    private final int type; // 0，日期， 1:时间日期
    private static final int FORMAT_DAY = 0;
    private static final int FORMAT_DATETIME = 1;


    BusinessOrderId(String prefix, int type, int length) {
        this.prefix = prefix;
        this.type = type;
        this.length = length;
    }

    private static final SimpleDateFormat dayFormat = new SimpleDateFormat("yyyyMMdd");
    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMddHHmmss");
    private static final SecureRandom random = new SecureRandom();

    public static String generateOrderId(BusinessOrderId biz) {
        SimpleDateFormat format = biz.getType() == FORMAT_DAY ? dayFormat : dateTimeFormat;
        return generateTradeNo(format, biz.getPrefix(), biz.getLength());
    }

    private static synchronized String generateTradeNo(SimpleDateFormat format, String prefix, int length) {
        if (length <= 0)
            throw new IllegalArgumentException("prefix or length is illegal");
        if (prefix == null) {
            prefix = "";
        }
        return prefix + format.format(new Date()) + new BigInteger(130, random).toString().substring(0, length);
    }
}
