package com.easylive.pay.enums;

public enum AssetBizType {

    /**
     * 0-消费型/充值型业务 1-运营类型 2-兑换型业务 3-其他
     */

    /**
     * 消费型/充值型业务
     */
    BUSINESS(0),

    /**
     * 运营类型
     */
    OPERATIONAL(1),

    /**
     * 兑换型业务
     */
    EXCHANGE(2),

    /**
     * 其他
     */
    OTHER(3);

    private final int value;

    AssetBizType(final int newValue) {
        value = newValue;
    }

    public static AssetBizType fromValue(int value) {
        for (AssetBizType authType : AssetBizType.values()) {
            if (authType.getValue() == value)
                return authType;
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}
