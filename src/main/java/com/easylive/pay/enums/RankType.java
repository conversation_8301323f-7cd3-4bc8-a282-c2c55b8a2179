package com.easylive.pay.enums;

public enum RankType {

    /**
     * Rank Type
     */
    CONTRIBUTOR("cont"),
    TOTAL("tot"),
    BUSINESS("biz"),
    ACTIVITY("act");

    private final String shortName;

    RankType(final String shortName) {
        this.shortName = shortName;
    }

    public static RankType of(String shortName) {
        for (RankType type : RankType.values()) {
            if (type.getShortName().equals(shortName))
                return type;
        }
        throw new IllegalArgumentException("Invalid rank item type");
    }

    public String getShortName() {
        return shortName;
    }

    @Override
    public String toString() {
        return shortName;
    }
}
