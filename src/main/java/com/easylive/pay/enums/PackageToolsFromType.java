package com.easylive.pay.enums;

/**
 * 道具类型
 */
public enum PackageToolsFromType {

    /**
     * 获取类型 1-购买 2-活动 3-抽奖 4-赠送 5-签到 6-推广 7-送礼物 8-PK竞猜
     */
    BUY(1),
    DIAMOND(2),
    LOTTERY(3),
    CONTRIBUTE(4),
    SIGN(5),
    PROMOTER(6),
    SEND_GIFT(7),
    PK_GUESS(8),
    ;

    private final int value;

    PackageToolsFromType(final int newValue) {
        value = newValue;
    }

    public int getValue() {
        return value;
    }

}
