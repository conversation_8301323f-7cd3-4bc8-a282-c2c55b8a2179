package com.easylive.pay.enums;

/**
 * 用户形象资源类型
 *
 * <AUTHOR>
 * @date 2019/11/20
 */
public enum IdentityType {
    /**
     * 0-身份证认证
     * 1-支付宝认证
     */
    ID_CARD(0, "ID_CARD", "身份证认证"),
    ALIYUN(1, "ALIY<PERSON>", "支付宝认证");

    private final int type;
    private final String value;
    private final String message;

    IdentityType(int type, String value, String message) {
        this.type = type;
        this.value = value;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

    public String getMessage() {
        return message;
    }
}
