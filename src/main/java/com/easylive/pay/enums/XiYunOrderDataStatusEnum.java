package com.easylive.pay.enums;

/**
 *
 * <AUTHOR>
 * @date 2023/9/14
 **/
public enum XiYunOrderDataStatusEnum {

    UNPROCESSED(1,"数据待调用"),
    PROCESSING(2,"数据处理中"),
    SUCCESS(3,"数据处理完成"),
    FAIL(4,"数据处理失败"),

    ;
    private int code;

    private String desc;


    XiYunOrderDataStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public static XiYunOrderDataStatusEnum fromValue(int code) {
        for (XiYunOrderDataStatusEnum en : XiYunOrderDataStatusEnum.values()) {
            if (en.getCode() == code) {
                return en;
            }
        }
        return null;
    }
}