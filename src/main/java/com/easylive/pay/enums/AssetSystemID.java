package com.easylive.pay.enums;


import com.easylive.pay.entity.AssetAuth;
import com.easylive.pay.entity.AssetBiz;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.easylive.pay.enums.AssetAuthType.ADD;
import static com.easylive.pay.enums.AssetAuthType.MINUS;
import static com.easylive.pay.enums.AssetBizType.*;
import static com.easylive.pay.enums.AssetType.*;

public enum AssetSystemID {

    /**
     * 1 - 充值
     * 20 - 未知使用，不能添加
     * 21 - 提现退款
     */
    RECHARGE(-1),
    CASHOUT(-2),
    GIFT(-3),
    BUY<PERSON>CK(-4),
    RED<PERSON>CK_OPEN(-5),
    REDPACK_ROOM(-6),
    REDPACK_GROUP(-7),
    PAY_VIDEO(-8),
    GUARDIAN(-9),
    GAME_BEAN_TO_ECOIN(-10),
    GAME_ECOIN_TO_BEAN(-11),
    GAME_GIFT(-12),
    BARLEY_GIFT(-13),
    LUCK_GIFT(-14),
    RECHARGE_ROLLBACK(-15),
    REDPACK_REFUND(-16),
    PACKAGE_GIFT(-17),
    ECOIN_TRANSFER(-18),
    OFFLINE_CASHOUT(-19),
    APP_CASHOUT_REFUND(-21),
    SURPLUS_REDPACK_RICEROLL(-22),
    SURPLUS_REDPACK_COIN(-23),
    ;

    static Map<AssetSystemID, AssetBiz> assetBizMap = new HashMap<>();

    static {
        //ecoin
        unary(RECHARGE, BUSINESS, ECOIN, ADD);
        unary(REDPACK_OPEN, EXCHANGE, ECOIN, ADD);
        unary(REDPACK_ROOM, EXCHANGE, ECOIN, MINUS);
        unary(REDPACK_GROUP, EXCHANGE, ECOIN, MINUS);
        unary(GAME_BEAN_TO_ECOIN, EXCHANGE, ECOIN, ADD);
        unary(GAME_ECOIN_TO_BEAN, EXCHANGE, ECOIN, MINUS);
        unary(LUCK_GIFT, EXCHANGE, ECOIN, ADD);
        unary(RECHARGE_ROLLBACK, OTHER, ECOIN, MINUS);
        unary(REDPACK_REFUND, EXCHANGE, ECOIN, ADD);
        unary(SURPLUS_REDPACK_COIN, EXCHANGE, ECOIN, ADD);

        //riceroll
        unary(CASHOUT, BUSINESS, RICEROLL, MINUS);
        unary(OFFLINE_CASHOUT, OPERATIONAL, RICEROLL, MINUS);
        unary(APP_CASHOUT_REFUND, OPERATIONAL, RICEROLL, ADD);

        binary(GIFT, BUSINESS, ECOIN, MINUS, RICEROLL, ADD);
        binary(BARLEY_GIFT, BUSINESS, BARLEY, MINUS, RICEROLL, ADD);
        binary(PAY_VIDEO, BUSINESS, ECOIN, MINUS, RICEROLL, ADD);
        binary(GUARDIAN, BUSINESS, ECOIN, MINUS, RICEROLL, ADD);

        AssetBiz gameGiftBiz = binary(GAME_GIFT, BUSINESS, ECOIN, MINUS, RICEROLL, ADD);
        gameGiftBiz.getAuths().get(0).setLowerBound(0L);
        gameGiftBiz.getAuths().get(0).setUpperBound(0L);

        AssetBiz redPackBiz = binary(SURPLUS_REDPACK_RICEROLL, BUSINESS, ECOIN, MINUS, RICEROLL, ADD);
        redPackBiz.getAuths().get(0).setLowerBound(0L);
        redPackBiz.getAuths().get(0).setUpperBound(0L);

        AssetBiz biz = binary(PACKAGE_GIFT, BUSINESS, ECOIN, MINUS, RICEROLL, ADD);
        biz.getAuths().get(0).setLowerBound(0L);
        biz.getAuths().get(0).setUpperBound(0L);

        binary(BUYBACK, EXCHANGE, RICEROLL, MINUS, ECOIN, ADD);
        binary(ECOIN_TRANSFER, EXCHANGE, ECOIN, MINUS, ECOIN, ADD);
    }

    private final int value;

    AssetSystemID(final int newValue) {
        value = newValue;
    }

    public static AssetBiz getAssetBiz(AssetSystemID systemID) {
        return assetBizMap.get(systemID);
    }

    private static void unary(AssetSystemID bid, AssetBizType bizType, AssetType assetType, AssetAuthType authType) {

        AssetBiz biz = createAssetBiz(bid, bizType);
        addAuth(biz, bid.getValue(), assetType, authType);
        assetBizMap.put(bid, biz);
    }

    private static AssetBiz binary(AssetSystemID bid, AssetBizType bizType, AssetType assetType1, AssetAuthType authType1, AssetType assetType2, AssetAuthType authType2) {
        AssetBiz biz = createAssetBiz(bid, bizType);

        addAuth(biz, bid.getValue(), assetType1, authType1);
        addAuth(biz, bid.getValue(), assetType2, authType2);
        assetBizMap.put(bid, biz);

        return biz;
    }

    private static AssetBiz createAssetBiz(AssetSystemID bid, AssetBizType bizType) {
        AssetBiz biz = new AssetBiz();
        biz.setId(bid.getValue());
        biz.setType(bizType.getValue());
        biz.setAuths(new ArrayList<>());
        return biz;
    }

    private static void addAuth(AssetBiz biz, long bid, AssetType assetType, AssetAuthType authType) {
        AssetAuth auth = new AssetAuth();
        auth.setAsset(assetType.getValue());
        auth.setAuth(authType.getValue());
        auth.setBid(bid);
        auth.setIdx(biz.getAuths().size());
        biz.getAuths().add(auth);
    }

    public int getValue() {
        return value;
    }
}
