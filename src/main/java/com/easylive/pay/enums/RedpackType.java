package com.easylive.pay.enums;

/**
 * <AUTHOR>
 * @date 2016/3/4
 */
public enum RedpackType {

    /**
     * 系统红包
     */
    SYSTEM(0),

    /**
     * 礼物红包
     */
    GIFT(1),

    /**
     * 主播红包
     */
    ANCHOR(2),

    /**
     * 私信红包
     */
    GROUP(3),

    /**
     * PK红包
     */
    PK(4);


    private final int value;

    RedpackType(final int newValue) {
        value = newValue;
    }

    public int getValue() {
        return value;
    }

}
