package com.easylive.pay.enums;

public enum RankInterval {

    /**
     * Rank Interval
     */
    HOUR("hour"),
    DAY("day"),
    WEEK("week"),
    MONTH("month"),
    YEAR("year"),
    ALL("all");

    private final String shortName;

    RankInterval(final String shortName) {
        this.shortName = shortName;
    }

    public static RankInterval of(String shortName) {
        for (RankInterval interval : RankInterval.values()) {
            if (interval.getShortName().equals(shortName))
                return interval;
        }
        throw new IllegalArgumentException("Invalid rank interval");
    }

    public String getShortName() {
        return shortName;
    }

    @Override
    public String toString() {
        return shortName;
    }
}
