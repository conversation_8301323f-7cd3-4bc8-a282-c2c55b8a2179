package com.easylive.pay.enums;

/**
 * 提现渠道类型
 * <AUTHOR>
 * @date 2023/9/14
 **/
public enum WithdrawChannelTypeEnum {

    /**
     * 个人银行卡（默认为1）
     */
    CARD(1),
    /**
     * 个人支付宝余额
     */
    ALIPAY(2),
    ;

    private int code;

    WithdrawChannelTypeEnum(Integer code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static WithdrawChannelTypeEnum fromValue(int code) {
        for (WithdrawChannelTypeEnum en : WithdrawChannelTypeEnum.values()) {
            if (en.getCode() == code) {
                return en;
            }
        }
        return null;
    }
}