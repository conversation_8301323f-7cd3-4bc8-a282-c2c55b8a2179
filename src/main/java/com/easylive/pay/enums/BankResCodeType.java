package com.easylive.pay.enums;

/**
 * 新政银企返回码
 *
 * <AUTHOR>
 * @date 2019/8/20
 */
public enum BankResCodeType {

    /**
     * 0000 - 成功
     * P066 - 账户可用余额不足
     */
    SUCCESS(0, "0000", "提现成功"),
    ACCOUNT_AMOUNT_EMPTY(1, "P066", "系统维护中，请稍候重试！"),
    TRADE_NOT_FOUND(2, "0025", "找不到原始交易");

    private final int code;
    private final String value;
    private final String message;

    BankResCodeType(final int code, final String value, final String message) {
        this.code = code;
        this.value = value;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getMessage() {
        return message;
    }
}
