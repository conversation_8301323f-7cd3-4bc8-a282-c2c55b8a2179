package com.easylive.pay.enums;

/**
 * 银行签名操作类型
 *
 * <AUTHOR>
 * @date 2019/8/27
 */
public enum BankSignOperateType {
    /**
     * APP提现状态
     * 1 - 新增
     * 2 - 修改
     * 3 - 删除
     */
    ADD(1, "ADD", "新增"),
    UPDATE(2, "UPDATE", "修改"),
    DELETE(3, "DELETE", "删除");

    private int code;
    private String value;
    private String valueCn;

    BankSignOperateType(int code, String value, String valueCn) {
        this.code = code;
        this.value = value;
        this.valueCn = valueCn;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueCn() {
        return valueCn;
    }

    public void setValueCn(String valueCn) {
        this.valueCn = valueCn;
    }
}
