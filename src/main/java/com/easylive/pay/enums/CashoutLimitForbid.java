package com.easylive.pay.enums;

/**
 * APP提现禁止
 *
 * <AUTHOR>
 * @date 2019/9/20
 */
public enum CashoutLimitForbid {
    /**
     * 是否禁止提现：0 - 否 1 - 是
     */
    NO(0),
    YES(1);

    private final int value;

    CashoutLimitForbid(final int newValue) {
        value = newValue;
    }

    public static AssetType fromValue(int value) {
        for (AssetType assetType : AssetType.values()) {
            if (assetType.getValue() == value) {
                return assetType;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}