package com.easylive.pay.enums;

/**
 *
 * <AUTHOR>
 * @date 2023/9/14
 **/
public enum XiYunOrderStatusEnum {


    NON(1,"待付款"),
    PAID(2,"已付款"),
    FAIL(3,"付款失败"),
    PAYING(4,"付款中"),
    REJECT(5,"驳回付款"),
    SYSCARTON(7,"系统卡单"),

    ;
    private int code;

    private String desc;


    XiYunOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public static XiYunOrderStatusEnum fromValue(int code) {
        for (XiYunOrderStatusEnum en : XiYunOrderStatusEnum.values()) {
            if (en.getCode() == code) {
                return en;
            }
        }
        return null;
    }
}