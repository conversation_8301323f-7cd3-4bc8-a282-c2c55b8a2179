package com.easylive.pay.enums;

/**
 * 新政银企返回码
 *
 * <AUTHOR>
 * @date 2019/8/20
 */
public enum BankTxSts {
    /**
     * 交易状态:
     *  0 -  交易失败；
     * 1 -	交易成功；
     */
    FAIL(0, "FAIL"),
    SUCCESS(1, "SUCCESS");

    private final int code;
    private final String value;

    BankTxSts(final int code, final String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
