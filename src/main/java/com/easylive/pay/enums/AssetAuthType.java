package com.easylive.pay.enums;

public enum AssetAuthType {

    /**
     * Add
     */
    ADD(0),
    MINUS(1);

    private final int value;

    AssetAuthType(final int newValue) {
        value = newValue;
    }

    public static AssetAuthType fromValue(int value) {
        for (AssetAuthType authType : AssetAuthType.values()) {
            if (authType.getValue() == value)
                return authType;
        }
        return null;
    }

    public int getValue() {
        return value;
    }
}
