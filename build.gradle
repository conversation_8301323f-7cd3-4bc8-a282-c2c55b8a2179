group = 'com.easylive'
version = '1.0-SNAPSHOT'

apply plugin: 'java'
//apply plugin: 'jetty'
apply plugin: 'eclipse'
apply plugin: 'idea'
apply plugin: 'spring-boot'
apply plugin: 'war'
apply plugin: 'application'
mainClassName = 'com.easylive.pay.Application'




war {
    baseName = 'pay'
    version =  '1.0-SNAPSHOT'
}


sourceCompatibility = 1.9

repositories {
    maven{ url = 'http://maven.aliyun.com/nexus/content/groups/public/' }
    maven{ url = 'http://repo2.maven.org/maven2/' }
    mavenCentral()
}


buildscript {
    ext {
        springBootVersion = '1.3.8.RELEASE'
    }
    repositories {
        maven{ url = "http://maven.aliyun.com/nexus/content/groups/public/" }
        maven{ url = "http://repo2.maven.org/maven2/" }
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath 'io.spring.gradle:dependency-management-plugin:0.6.1.RELEASE'
    }
}

dependencies {
    compile fileTree(dir: 'libs', include: ['*.jar'])
    compile 'commons-io:commons-io:2.4'
    compile "org.apache.httpcomponents:httpclient:4.5.1"
    compile "org.apache.httpcomponents:httpcore:4.4.4"
    compile "org.apache.httpcomponents:httpasyncclient:4.1.1"
    compile 'dom4j:dom4j:1.6.1'
    compile "org.springframework.boot:spring-boot-starter-web:${springBootVersion}"
    compile "org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}"
    compile "org.springframework.boot:spring-boot-starter:${springBootVersion}"
    compile "org.springframework.boot:spring-boot-starter-redis:${springBootVersion}"
    compile "org.springframework.boot:spring-boot-starter-test:${springBootVersion}"
    //compile "org.springframework.kafka:spring-kafka:1.3.9.RELEASE"
    compile "org.apache.kafka:kafka-clients:2.3.1"
    compile 'mysql:mysql-connector-java:8.0.11'
    compile 'com.google.code.gson:gson:2.5'
    compile 'org.aspectj:aspectjrt:1.7.4'
//    compile 'org.slf4j:slf4j-api:1.7.13'
//    compile 'org.slf4j:jcl-over-slf4j:1.7.13'
    compile 'ch.qos.logback:logback-classic:1.1.7'
    compile 'joda-time:joda-time:2.9.1'
    compile 'javax.el:javax.el-api:2.2.5'
    compile "io.springfox:springfox-swagger2:2.8.0"
    compile "io.springfox:springfox-swagger-ui:2.8.0"
    compile "com.google.guava:guava:20.0"
    compile 'com.google.collections:google-collections:1.0'
    compile group: 'com.google.zxing', name: 'core', version: '3.2.1'
    compile group: 'com.google.zxing', name: 'javase', version: '3.2.1'
    compile group: 'org.codehaus.janino', name: 'commons-compiler', version: '3.0.6'
    compile group: 'org.codehaus.janino', name: 'janino', version: '3.0.6'
    compile 'org.apache.commons:commons-lang3:3.4'
    compile "io.github.openfeign:feign-core:10.2.3"
    compile "io.github.openfeign:feign-httpclient:10.2.3"
    compile "io.github.openfeign:feign-jackson:10.2.3"
    compile "io.github.openfeign.form:feign-form:3.8.0"
    compile "commons-codec:commons-codec:1.13"
//    compile 'com.fasterxml.jackson.core:jackson-core:2.6.4'
//    compile 'com.fasterxml.jackson.core:jackson-databind:2.6.4'
//    compile 'com.fasterxml.jackson.core:jackson-annotations:2.6.4'
    compile 'com.alibaba:druid:1.1.9'
    compile 'com.alibaba:fastjson:1.2.7'
    compile group: 'com.alipay.sdk', name: 'alipay-sdk-java', version: '4.39.246.ALL'
    compile group: 'org.bouncycastle', name: 'bcprov-jdk15on', version: '1.60'
    compile group: 'org.yaml', name: 'snakeyaml', version: '1.16'
    compile group: 'com.braintreepayments.gateway', name: 'braintree-java', version: '2.105.0'

    compile ("org.apache.zookeeper:zookeeper:3.5.6") {
        exclude group: 'log4j', module: 'log4j'
        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    }
    compile "org.apache.curator:curator-recipes:4.2.0"

    // https://mvnrepository.com/artifact/com.thoughtworks.xstream/xstream
    compile group: 'com.thoughtworks.xstream', name: 'xstream', version: '1.4.10'
    compileOnly 'org.projectlombok:lombok:1.18.10'
    annotationProcessor 'org.projectlombok:lombok:1.18.10'
    compile group: 'javax.xml.bind', name: 'jaxb-api', version: '2.3.0'

    compile 'org.apache.tomcat:tomcat-jdbc:8.0.28'
    compile "org.springframework.boot:spring-boot-starter-tomcat:${springBootVersion}"
    testCompile group: 'junit', name: 'junit', version: '4.11'
    testCompile 'org.projectlombok:lombok:1.18.10'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.10'

    compile 'com.ctrip.framework.apollo:apollo-client:1.5.0'

    compile 'com.liferay:javax.xml.ws:2.3.0.LIFERAY-PATCHED-1'
    implementation 'com.sun.xml.ws:jaxws-rt:2.2.10'

    implementation 'com.auth0:java-jwt:4.2.1'
    implementation 'com.huifu.adapay:adapay-java-sdk:1.2.10'
    implementation 'com.yunzhanghu.openapi:sdk:1.4.25-RELEASE'

//    implementation 'com.fasterxml.jackson.core:jackson-databind:********'
//    compile 'io.github.zhongzichang:spring-iap:1.0'




    tasks.withType(JavaCompile) {
        options.compilerArgs << '-Xlint:unchecked'
        options.deprecation = true
    }
}
targetCompatibility = JavaVersion.VERSION_1_9
