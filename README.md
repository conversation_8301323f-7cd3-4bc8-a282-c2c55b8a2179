# 支付系统pay
## 1 系统概述
支付系统（pay）是java语言版的支付相关系统，主要完成微信、支付宝等充值、发礼物、发红包、拆红包等于支付相关的功能。
pay在整个系统中的位置在以下的系统架构中所示。
### 1.1 系统架构

![](img/topo.png)

### 1.2 主要数据表说明

* t_user_coin 用户资产表，记录用户的资产信息，如所剩易币，充值总金额，剩余饭团等
* t_goods_record  用户发送礼物记录，记录所有用户的礼物来往信息，包括礼物名称，数量、金额、时间、类型等信息。
* t_redpack 发送红包信息，记录用户和系统生成的红包信息，如红包码、金额、数量、状态、时间、发送者、所在视频房间等。
* t_redpack_number 拆红包信息，记录红包码、拆红包金额、拆红包者uid、时间、最佳等信息。发红包后拆红包信息预先生成好，每个人抢红包update拆红包者uid和时间即可。
* t_recharge_record 充值记录表。记录充值uid、时间、金额、易币、订单号、平台、状态等信息。
* t_cashout_record 提现记录表。记录提现者uid、饭团、金额、时间、订单号、平台、ip等信息。

## 2 接口描述
pay监听端口是8080，但是一般不会直接访问pay服务，而是通过nginx反向代理来访问pay服务，nginx反向代理有两部分，一部分是pay.conf内描述的内部接口，一部分是appgw.conf内描述的对外接口。

从上面的架构图可以看出，支付系统的接口可以分为内部接口和外部接口两部分、内部接口可以直接通过http://pay.yizhibo.tv:8888 访问，外部接口需要通过appgw的nginx反向代理进行转发，即对外不会暴露pay.yizhibo.tv域名，访问方式为 http://appgw.yizhibo.tv/v2/pay。

接口调用方式：

* 服务域名

	线上:
	 
	   * 对外接口  http://appgw.yizhibo.tv/v2/pay 
	   * 内部接口  http://pay.yizhibo.tv:8888  
	   
	测试: 
	
	   * 对外接口  http://dev.yizhibo.tv/dev/appgw/pay
	   * 内部接口  http://dev.yizhibo.tv/dev/pay/

* 通信协议
	HTTP
	
* 请求方式
	GET、POST
* 字符编码
	UTF-8

* [接口wiki详情](http://wiki.yizhibo.tv/interface/pay)




## 3.运行环境

### 3.1依赖

* gradle
* java

### 3.2更新配置

在src/main/resources/目录下将合适候选的application.properties拷贝成application.properties


### 3.3构建

``` gradle clean build ```

### 3.4运行


``` java -jar build/libs/pay-1.0-SNAPSHOT.war ```



## 4 运维相关
### 4.1 基本信息
| 名称 | 内容 | 
|:--|:--| 
| 代码仓库     | ******************:backend/pay.git |
| 服务器部署     | appgw1,appgw2 |
| 部署目录     | /local/source/pay/ |
| 自动部署地址 | http://jenkins.yizhibo.tv/job/%E6%98%93%E7%9B%B4%E6%92%AD/job/backend/job/pay/ |
| 操作权限 | zebra |
| 开发人员 | 李卫东[<EMAIL>]  |

### 4.2 运行日志

info级别日志

* /zebra/logs/pay/

所有日志搜集

* log1:/data/logs/pay

### 4.3 自动部署

进入Jenkins自动部署页面 http://jenkins.yizhibo.tv/job/%E6%98%93%E7%9B%B4%E6%92%AD/job/backend/job/pay/ ，点击立即构建即可

