-- ----------------------------
-- Table structure for t_cashout_yzh_app
-- ----------------------------
DROP TABLE IF EXISTS `t_cashout_yzh_app`;
CREATE TABLE `t_cashout_yzh_app` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(12) NOT NULL COMMENT '应用名称, 用于区分不同的云账户配置',
  `dealer_id` varchar(16) NOT NULL COMMENT '云账户商户ID',
  `broker_id` varchar(16) NOT NULL COMMENT '云账户经纪商ID',
  `app_key` varchar(32) NOT NULL COMMENT '云账户App Key',
  `des_key` varchar(32) NOT NULL COMMENT '云账户3DES Key',
  `description` varchar(24) DEFAULT NULL COMMENT '备注',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态: 0:禁用, 1:启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云账户应用配置表';

-- ----------------------------
-- Table structure for t_cashout_yzh_member
-- ----------------------------
DROP TABLE IF EXISTS `t_cashout_yzh_member`;
CREATE TABLE `t_cashout_yzh_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `uid` bigint(20) NOT NULL COMMENT '用户ID',
  `app_id` bigint(20) NOT NULL COMMENT '签约时使用的账户, 关联到t_cashout_yzh_app的id',
  `real_name` varchar(255) DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(255) DEFAULT NULL COMMENT '身份证号',
  `sign_status` varchar(20) NOT NULL COMMENT '签约状态 (eg: UNSIGNED, SIGNED, FAILED, PENDING)',
  `signed_at` datetime DEFAULT NULL COMMENT '签约时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uid_app_id` (`uid`, `app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云账户用户签约信息表';


-- ----------------------------
-- Table structure for t_cashout_yzh_record
-- ----------------------------
DROP TABLE IF EXISTS `t_cashout_yzh_record`;
CREATE TABLE `t_cashout_yzh_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` varchar(64) NOT NULL COMMENT '平台订单号',
  `yzh_order_id` varchar(64) DEFAULT NULL COMMENT '云账户订单号',
  `bank_order_id` varchar(64) DEFAULT NULL COMMENT '云账户订单号',
  `uid` bigint(20) NOT NULL COMMENT '用户ID',
  `app_id` bigint(20) NOT NULL COMMENT '提现时使用的APP ID, 关联到t_cashout_yzh_app',
  `amount` bigint(20) NOT NULL COMMENT '提现金额 (元),单位分',
  `real_name` varchar(50) NOT NULL COMMENT '收款人真实姓名',
  `card_no` varchar(50) NOT NULL COMMENT '收款银行卡号',
  `phone` varchar(20) NOT NULL COMMENT '收款人手机号',
  `status` int NOT NULL COMMENT '云账户订单状态',
  `status_detail` int DEFAULT NULL COMMENT '云账户详细状态码',
  `status_msg` varchar(256) DEFAULT NULL COMMENT '云账户返回的消息',
  `status_msg_detail` varchar(256) DEFAULT NULL COMMENT '云账户返回详细消息',
  `complete_time` datetime NULL COMMENT '订单完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云账户提现记录表'; 